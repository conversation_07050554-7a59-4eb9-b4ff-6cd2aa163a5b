<template>
    <!-- 模板导入 -->
    <el-dialog 
        v-model="importDialogDispaly.normal" 
        :title="jType === '1010' ? '一键导入': '模板导入'" 
        center 
        width="500" 
        class="custom-confirm dialogDrag" 
        modal-class="modal-class"
    >
        <div class="importShow-content" v-dialogDrag>
            <div class="importShow-main">
                <div style="margin-top: 30px"><span>1.请选择下面任意一种方式导入日记账</span></div>
                <div style="margin-top: 5px">
                    <span>&nbsp;&nbsp;(1)在日记账中导出所需数据（单账户），确认后直接导入</span>
                </div>
                <div style="margin-top: 5px">
                    <span>&nbsp;&nbsp;(2)在柠檬云进销存-收款/付款单列表，导出所需数据，确认后直接导入</span>
                </div>
                <div style="margin-top: 5px">
                    <span>
                        &nbsp;&nbsp;(3)点击下载日记账模板，按照模板格式进行数据整理再导入
                        <a class="link ml-10" @click="downloadTemplate">下载模板</a>
                    </span>
                </div>
                <div style="margin-top: 20px; margin-bottom: 8px; display: flex; align-items: center">
                    <span>2.选择文件导入</span>
                    <label class="file-button ml-20 mr-20">
                        <input @change="onFileSelected('normal', $event)" type="file" accept=".xls,.xlsx" ref="normalFileRef" />
                        <a class="link">选取文件</a>
                    </label>
                    <span class="file-name">{{ normalFileInfo.name }}</span>
                </div>
            </div>
            <div class="buttons" :class="{ base: !isErp }">
                <a class="button solid-button" style="width: 86px" @click="uploadFile('normal')">导入</a>
                <a class="button ml-10" style="width: 86px" @click="importDialogDispaly.normal = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 全部账户导入 -->
    <el-dialog v-model="importDialogDispaly.all" title="导入日记账" center width="500" class="custom-confirm dialogDrag" modal-class="modal-class">
        <div class="importTipShow-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="importTipShow-main">
                <div class="tip-label">{{ "导入的" + (jType === "1010" ? "现金" : "银行") + "账户：" }}</div>
                <div class="tip-select">
                    <Select v-model="account" :teleported="false">
                        <Option
                            v-for="item in cdAccountList"
                            :key="item.ac_id"
                            :value="item.ac_id"
                            :label="item.ac_no + ' - ' + item.ac_name"
                        ></Option>
                    </Select>
                </div>
            </div>
            <div class="buttons" :class="{ base: !isErp }">
                <a class="button" style="width: 86px" @click="handleImportTipCancel">取消</a>
                <a class="button solid-button" style="width: 86px" @click="handleImportTipSure">确定</a>
            </div>
        </div>
    </el-dialog>
    <!-- 银行对账单(Excel)导入 -->
    <el-dialog
        v-model="importDialogDispaly.bank"
        title="银行对账单(Excel)导入"
        center
        width="500"
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
    >
        <div class="importBankShow-content" v-dialogDrag>
            <div class="importBankShow-main">
                <div style="margin-top: 20px"><span>1.请选择下面任意一种方式导入银行对账单数据</span></div>
                <div style="margin-top: 10px; margin-left: 50px">
                    <span>(1)直接导入在银行网站下载的银行对账单或流水，无需手动整理数据。</span>
                </div>
                <div style="margin-top: 10px; margin-left: 50px">
                    <span>(2)点击下载对账单模板，按照模板格式要求手动整理数据再导入。</span>
                </div>
                <div style="margin-top: 10px; margin-left: 50px">
                    <a class="link" @click="downloadBankTemplate">下载模板</a>
                </div>
                <div style="margin-top: 20px; margin-bottom: 8px">
                    <span>2.选择文件导入</span>
                </div>
                <div style="margin-bottom: 8px; display: flex; align-items: center">
                    <label class="file-button ml-10 mr-20">
                        <input @change="onFileSelected('bank', $event)" type="file" ref="bankFileRef" />
                        <a class="link">选取文件</a>
                    </label>
                    <span class="file-name">{{ bankFileInfo.name }}</span>
                </div>
            </div>
            <div class="buttons" :class="{ base: !isErp }">
                <a class="button solid-button" style="width: 86px" @click="uploadFile('bank')">导入</a>
                <a class="button ml-10" style="width: 86px" @click="() => (importDialogDispaly.bank = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 银行对账单导入结果 -->
    <el-dialog
        v-model="importBankResultShow"
        title="导入结果"
        center
        width="580"
        class="custom-confirm import-result-dialog dialogDrag"
        modal-class="modal-class"
    >
        <div class="import-result" v-dialogDrag>
            <div class="import-result-content">
                <p>
                    抱歉，导入银行对账单时遇到了问题。
                    <br />
                    {{ resultErrorMsg }}
                    <br />
                    请检查数据格式是否正确，或者联系我们的客服团队获取帮助。感谢您的耐心和理解~
                </p>
                <img v-if="isErp" class="qrcode" src="@/assets/Invoice/fetchInvoice_erp.png" />
                <div v-if="isProSystem && !isErp">
                    <img v-if="!isAccountingAgent" class="qrcode" src="@/assets/Aside/pro-kfewm.png" />
                    <img v-if="isAccountingAgent" class="qrcode" src="@/assets/Aside/aapro-kfewm.png" />
                </div>
                <div v-if="!isProSystem && !isErp">
                    <img v-if="!isAccountingAgent" class="qrcode" src="@/assets/Aside/kfewm.png" />
                    <img v-if="isAccountingAgent" class="qrcode" src="@/assets/Aside/aa-kfewm.png" />
                </div>
            </div>
        </div>
        <div class="buttons">
            <a class="button solid-button" style="width: 86px" @click="() => (importBankResultShow = false)">确认</a>
        </div>
    </el-dialog>
    <!-- 支付宝导入 -->
    <el-dialog v-model="importDialogDispaly.alipay" title="支付宝导入" center width="440" class="custom-confirm dialogDrag" modal-class="modal-class">
        <div class="importAlipayShow-content" v-dialogDrag>
            <ul class="importAlipayShow-top">
                <li v-for="item in alipayList" :key="item.value" @click="() => (alipaytab = item.value)" style="cursor: pointer">
                    <span :class="alipaytab === item.value ? 'active' : ''">{{ item.label }}</span>
                    <span :class="alipaytab === item.value ? 'active line' : 'line'"></span>
                </li>
            </ul>
            <div class="importAlipayShow-main" v-show="alipaytab === 'business'">
                <div class="left">
                    <img src="@/assets/Icons/alipay-business.png" />
                </div>
                <div class="right">&nbsp;&nbsp;&nbsp;授权导入仅支持支付宝授权柠檬云调用您的交易数据，不会对您的资金安全产生任何影响</div>
            </div>
            <div class="importAlipayShow-main" v-show="alipaytab === 'personal'">
                <div class="left">
                    <img src="@/assets/Icons/alipay-user.png" />
                </div>
                <div class="right">
                    <div class="import-line">
                        <a class="link" @click="globalWindowOpen('https://consumeprod.alipay.com/record/advanced.htm')">
                            1、登录支付宝个人版
                        </a>
                    </div>
                    <div class="import-line">2、下载"交易记录"的账单(excel格式)</div>
                    <div class="import-line">3、解压文件(解压后才可以导入系统)</div>
                    <div class="import-line">
                        <label class="file-button">
                            <input @change="onFileSelected('alipay', $event)" type="file" ref="alipayFileRef" />
                            <a class="link">4、选取文件</a>
                        </label>
                        <span class="file-name">{{ alipayFileInfo.name }}</span>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="solid-button blue" style="width: 86px" v-show="alipaytab === 'business'" @click="alipayImportAuth">授权导入</a>
                <a class="solid-button blue" style="width: 86px" v-show="alipaytab === 'personal'" @click="uploadFile('alipay')">
                    文件导入
                </a>
            </div>
        </div>
    </el-dialog>
    <!-- 微信导入 -->
    <el-dialog v-model="importDialogDispaly.wechat" title="微信导入" center width="440" class="custom-confirm dialogDrag" modal-class="modal-class">
        <div class="importWechatShow-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="importWechatShow-main">
                <div class="switch-tabs">
                    <div
                        style="cursor: pointer"
                        @click="() => (wechatShow = 'business')"
                        :class="wechatShow === 'business' ? 'active' : ''"
                    >
                        导入商家账号数据
                        <span class="tab-line"></span>
                    </div>
                    <div
                        style="cursor: pointer"
                        @click="() => (wechatShow = 'personal')"
                        :class="wechatShow === 'personal' ? 'active' : ''"
                    >
                        导入个人账号数据
                        <span class="tab-line"></span>
                    </div>
                </div>
                <div class="first" v-show="wechatShow === 'business'">
                    <div class="left">
                        <img src="@/assets/Cashier/wechat.png" />
                    </div>
                    <div class="right">
                        <div class="line">
                            <a
                                class="link"
                                @click="
                                    globalWindowOpen(
                                        'https://pay.weixin.qq.com/index.php/core/home/<USER>'
                                    )
                                "
                            >
                                1、登录微信商户平台
                            </a>
                        </div>
                        <div class="line">2、下载"资金账单"的业务明细账单</div>
                        <div class="line">
                            <label class="file-button">
                                <input @change="onFileSelected('wechatBusiness', $event)" type="file" />
                                <a class="link">3、选取文件</a>
                            </label>
                            <span class="file-name">{{ wechatBusinessFileInfo.name }}</span>
                        </div>
                    </div>
                </div>
                <div class="second" v-show="wechatShow === 'personal'">
                    <div class="left">
                        <img src="@/assets/Cashier/wechatps.png" />
                    </div>
                    <div class="right">
                        <div class="line">1、打开微信-我的-服务-钱包-账单</div>
                        <div class="line">2、在右上角"常见问题"中下载账单</div>
                        <div class="line">3、解压文件(解压后才可以导入系统)</div>
                        <div class="line">
                            <label class="file-button">
                                <input @change="onFileSelected('wechatPersonal', $event)" type="file" ref="wechatPersonalFlieRef" />
                                <a class="link">4、选取文件</a>
                            </label>
                            <span class="file-name">{{ wechatPersonalFileInfo.name }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" style="width: 86px" @click="uploadFile(wechatShow === 'personal' ? 'wechatPs' : 'wechatBs')">
                    文件导入
                </a>
            </div>
        </div>
    </el-dialog>
    <!-- 企微导入 -->
    <el-dialog
        v-model="importDialogDispaly.enterpriseWechat"
        title="企微导入"
        center
        width="440"
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
    >
        <div class="importEnterpriseWechatShow-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="content-left">
                <img src="@/assets/Cashier/wechat.png" />
            </div>
            <div class="content-right">
                <div class="import-line">
                    <a
                        class="link maincolor"
                        @click="globalWindowOpen('https://work.weixin.qq.com/wework_admin/frame#/payBill/independent/v2/fundFlow')"
                    >
                        1、登录企业微信后台下载资金流水
                    </a>
                </div>
                <div class="import-line">2、导出“资金流水”的业务明细账单</div>
                <div class="import-line">
                    <label class="file-button">
                        <input @change="onFileSelected('enterpriseWechat', $event)" type="file" ref="enterpriseWechatFileRef" />
                        <a class="link">3、选取文件</a>
                    </label>
                    <span class="file-name">{{ enterpriseWechatFileInfo.name }}</span>
                </div>
            </div>
            <div class="buttons">
                <a class="solid-button" style="width: 86px" @click="uploadFile('enterpriseWechat')"> 文件导入 </a>
            </div>
        </div>
    </el-dialog>
    <!-- 明细账导入 -->
    <el-dialog
        v-model="importDialogDispaly.subsidiaryLedger"
        title="明细账导入日记账"
        center
        width="750"
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
        @closed="cashierSubjectClose"
    >
        <div class="subsidiaryLedger-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="subsidiaryLedger-main">
                <div class="main-top">
                    <div class="line-item-title">会计期间：</div>
                    <div class="line-item-field">
                        <!-- <el-select v-model="period_s" style="width: 132px" :teleported="false" :fit-input-width="true">
                            <el-option v-for="item in periodData" :key="item.pid" :label="getPeriodInfo(item)" :value="item.pid" />
                        </el-select>
                        <div class="period-separative">至</div>
                        <el-select v-model="period_e" style="width: 132px" :teleported="false" :fit-input-width="true">
                            <el-option v-for="item in periodData" :key="item.pid" :label="getPeriodInfo(item)" :value="item.pid" />
                        </el-select> -->
                        <DatePicker
                            v-model:startPid="startMonth"
                            v-model:endPid="endMonth"
                            :clearable="false"
                            :editable="false"
                            :dateType="'month'"
                            :value-format="'YYYYMM'"
                            :label-format="'YYYY年MM月'"
                            :isPeriodList="true"
                            @getActPid="getActPid"
                        />
                    </div>
                </div>
                <div class="main-body">
                    <Table
                        row-key="asubId"
                        :data="cashierSubjectData"
                        height="218px"
                        :columns="cashierSubjectColumns"
                        :page-is-show="false"
                        ref="cashierSubjectRef"
                        @selection-change="selectCashierSubject"
                        :cellClassName="cellClassName"
                    >
                        <template #selection>
                            <el-table-column
                                type="selection"
                                width="30"
                                align="center"
                                header-align="left"
                                :reserve-selection="true"
                                :selectable="canSelect"
                            />
                        </template>
                        <template #acName>
                            <el-table-column prop="acName" label="导入的账户名称" align="left" header-align="left" :resizable="false">
                                <template #default="scope">
                                    <span v-if="scope.row.acId != 0">{{ scope.row.acName }}</span>
                                    <span v-else class="accountRelation" @click="setAccountRelation">设置科目账户关联</span>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                    <div class="tips mt-20">
                        <div class="highlight-red">说明：</div>
                        <div class="mt-10">
                            <span>1.需要将</span>
                            <span id="subjectTip" class="highlight-red">{{ (props.jType === "1010" ? "现金" : "银行") + "科目" }}</span>
                            <span>与</span>
                            <span id="cdAccountTip" class="highlight-red">{{ (props.jType === "1010" ? "现金" : "银行") + "账户" }}</span>
                            <span>进行</span>
                            <span class="highlight-red">关联</span><br />
                        </div>
                        <div class="mt-10">2.如果对应期间有已经生成凭证的日记账数据，则会覆盖导入</div>
                    </div>
                </div>
            </div>
            <div class="buttons" :class="{ base: !isErp }">
                <a class="button solid-button mr-10" style="width: 86px" @click="importSubsidiaryLedger">导入</a>
                <a class="button" style="width: 86px" @click="cashierSubjectClose">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 银行回单(PDF)导入导入 -->
    <el-dialog
        v-model="importDialogDispaly.receipt"
        title="银行回单(PDF)导入"
        center
        width="500"
        class="custom-confirm dialogDrag"
        top="5vh"
        modal-class="modal-class"
    >
        <div class="recognize-content" v-dialogDrag>
            <div class="tip-content">
                <div class="tip-line">1.支持网银下载银行回单（PDF格式）一键导入系统，自动生成流水及关联回单附件；</div>
                <div class="tip-line">
                    2.确保导入回单能正确识别流水及收支方向，请前往
                    <span class="link" @click="changeCurrentRoute">【资金-账户设置】</span>
                    维护银行账户的银行卡号信息；
                </div>
                <div class="tip-line">
                    3.如您不清楚回单如何下载，请查看各银行回单下载
                    <span class="link" @click="handleToHelp"> 帮助文档 </span>
                    或联系
                    <span class="link service" :class="currentSystem"> 在线客服 </span>
                </div>
            </div>
            <div class="recognize-drag">
                <el-upload
                    class="upload-demo"
                    drag
                    multiple
                    :auto-upload="true"
                    :show-file-list="false"
                    action="#"
                    :on-change="onChange"
                    :http-request="uoloadReceiptFile"
                >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">点击或拖拽上传</div>
                    <template #tip>
                        <div class="el-upload__tip">注：回单文件不超过20M（PDF不超过{{ limitCount }}份）</div>
                    </template>
                </el-upload>
                <div class="mt-10" v-show="receiptImportResultDialogInfo.lastResultShow">
                    <a class="link" @click="checkLastResult">查看导入结果</a>
                </div>
                <div v-show="recognizeInfo.fileList.length">
                    <el-scrollbar class="file-list-wrap" :always="true" :max-height="150">
                        <ul class="file-list">
                            <li class="file-list-item" v-for="item in recognizeInfo.fileList" :key="item.file.uid">
                                <span> {{ item.file.name }} </span>
                                <div class="file-list-oprate">
                                    <span @click="deleteFile(item.file.uid)">删除</span>
                                    <span @click="downloadFile(item)">下载</span>
                                </div>
                            </li>
                        </ul>
                    </el-scrollbar>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button ml-10" @click="comfirmRecognizeImport">识别导入</a>
                <a class="button ml-10" @click="cancelRecognizeImport">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 银行回单导入结果 -->
    <el-dialog
        v-model="receiptImportResultDialogInfo.display"
        title="银行回单导入结果"
        center
        width="600"
        class="custom-confirm dialogDrag"
        modal-class="modal-class"
        @close="receiptImportResultClose"
    >
        <div class="import-result-content" v-dialogDrag>
            <div class="import-result-main">
                <div class="import-date mt-10 mb-10">{{ "导入日期：" + receiptImportResultDialogInfo.importDate }}</div>
                <Table
                    ref="importResultTableRef"
                    :data="receiptImportResultDialogInfo.data"
                    :columns="importResultColumns"
                    :page-is-show="false"
                    :height="isErp ? 310 : 259"
                    :scrollbar-show="true"
                >
                    <template #remarks>
                        <el-table-column
                            prop="remarks"
                            label="原因"
                            align="left"
                            header-align="left"
                            min-width="160px"
                            :show-overflow-tooltip="false"
                            :resizable="false"
                        >
                            <template #default="{ row }: { row: IReceiptImportList }">
                                <template v-if="row.remarks.includes('前往查看设置')">
                                    {{ row.remarks.slice(0, getLinkIndex(row.remarks).start) }}
                                    <span class="link" style="font-size: 12px" @click="toAccount">前往查看设置</span>
                                    {{ row.remarks.slice(getLinkIndex(row.remarks).end) }}
                                </template>
                                <template v-else>
                                    {{ row.remarks }}
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="buttons">
                <a
                    class="button solid-button mr-10"
                    v-show="receiptImportResultDialogInfo.checkDiaplay"
                    @click="checkJournalFromReceiptImport"
                >
                    查看
                </a>
                <a class="button" @click="closeImportResultDialog">关闭</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { ElNotify } from "@/util/notify";
import { useLoading } from "@/hooks/useLoading";
import { getCookie, setCookie } from "@/util/cookie";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalExport, globalWindowOpen, globalWindowOpenPage, setTopLocationhref } from "@/util/url";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { getAACompanyId, getGlobalToken } from "@/util/baseInfo";
import { alipayWebsite, alipayAppid, AliPaySystem } from "../utils";
import { getPeriodsApi, type IPeriod } from "@/api/period";
import { checkPermission } from "@/util/permission";

import type { ICashierSubject, IJournalImportResult, ImportType } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { dayjs, type UploadRequestOptions } from "element-plus";
import type { ICDAccountItem } from "@/views/Cashier/components/types";

import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import { initStartOrEndMonth } from "@/components/DatePicker/utils";

const isErp = ref(window.isErp);
const isProSystem = ref(window.isProSystem);
const isAccountingAgent = ref(window.isAccountingAgent);

const trialStatusStore = useTrialStatusStore();
const props = defineProps<{
    cdAccount: string;
    jType: "1010" | "1020";
    cdAccountList: Array<ICDAccountItem>;
}>();
const emit = defineEmits<{
    (event: "import-success"): void;
    (event: "edit-date", _s: string, _e: string): void;
    (event: "change-acid", id: number | string): void;
    (event: "change-show-disabled", showDisabled: boolean): void;
}>();

interface IFileInfo {
    file: File | null;
    name: string;
}

const importDialogDispaly = reactive({
    normal: false,
    alipay: false,
    wechat: false,
    bank: false,
    enterpriseWechat: false,
    all: false,
    receipt: false,
    subsidiaryLedger: false,
});
const alipayFileRef = ref();
const wechatPersonalFlieRef = ref();
const wechatBusinessFlieRef = ref();
const bankFileRef = ref();
const enterpriseWechatFileRef = ref();
const normalFileRef = ref();

const normalFileInfo = reactive<IFileInfo>({ file: null, name: "" });
const bankFileInfo = reactive<IFileInfo>({ file: null, name: "" });
const alipayFileInfo = reactive<IFileInfo>({ file: null, name: "" });
const wechatBusinessFileInfo = reactive<IFileInfo>({ file: null, name: "" });
const wechatPersonalFileInfo = reactive<IFileInfo>({ file: null, name: "" });
const enterpriseWechatFileInfo = reactive<IFileInfo>({ file: null, name: "" });

let importType: ImportType = "";
function selectImportMethod(type: ImportType) {
    if (props.cdAccount === "all") {
        account.value = props.cdAccountList[0].ac_id;
        const length = props.cdAccountList.filter((item) => item.state === "0").length;
        if (length === 1) {
            showImportOption(type);
        } else {
            importDialogDispaly.all = true;
            importType = type;
        }
    } else {
        showImportOption(type);
    }
}
function onFileSelected(type: "normal" | "bank" | "alipay" | "wechatBusiness" | "wechatPersonal" | "enterpriseWechat", event: Event) {
    let file: File | null = ((event.target as HTMLInputElement).files as FileList)[0];
    !file && (file = null);
    const name: string = file ? file.name : "";
    switch (type) {
        case "normal":
            normalFileInfo.file = file;
            normalFileInfo.name = name;
            break;
        case "bank":
            bankFileInfo.file = file;
            bankFileInfo.name = name;
            break;
        case "alipay":
            alipayFileInfo.file = file;
            alipayFileInfo.name = name;
            break;
        case "wechatBusiness":
            wechatBusinessFileInfo.file = file;
            wechatBusinessFileInfo.name = name;
            break;
        case "wechatPersonal":
            wechatPersonalFileInfo.file = file;
            wechatPersonalFileInfo.name = name;
            break;
        case "enterpriseWechat":
            enterpriseWechatFileInfo.file = file;
            enterpriseWechatFileInfo.name = name;
            break;
    }
}
let canImport = true;
function uploadFile(type: "normal" | "bank" | "alipay" | "wechatBs" | "wechatPs" | "enterpriseWechat") {
    if (!canImport) return;
    canImport = false;

    let file: File | null = null;
    let urlPath = "/api/Journal/";
    switch (type) {
        case "normal":
            file = normalFileInfo.file;
            urlPath += props.jType === "1010" ? "ImportCashWithNormal" : "ImportDepositWithNormal";
            break;
        case "bank":
            file = bankFileInfo.file;
            urlPath += props.jType === "1010" ? "ImportCashWithBank" : "ImportDepositWithBank";
            break;
        case "wechatBs":
            file = wechatBusinessFileInfo.file;
            urlPath += props.jType === "1010" ? "ImportCashWithWeChat" : "ImportDepositWithWeChat";
            break;
        case "wechatPs":
            file = wechatPersonalFileInfo.file;
            urlPath += props.jType === "1010" ? "ImportCashWithPersonalWeChat" : "ImportDepositWithPersonalWeChat";
            break;
        case "alipay":
            file = alipayFileInfo.file;
            urlPath += props.jType === "1010" ? "ImportCashWithAlipay" : "ImportDepositWithAlipay";
            break;
        case "enterpriseWechat":
            file = enterpriseWechatFileInfo.file;
            urlPath += props.jType === "1010" ? "ImportCashWithEnterpriseWeChat" : "ImportDepositWithEnterpriseWeChat";
            break;
    }
    if (!file) {
        ElNotify({ type: "warning", message: "请选择文件" });
        canImport = true;
        return;
    }
    const params = getUrlSearchParams({
        cdAccount: props.cdAccount === "all" ? account.value : props.cdAccount,
        jType: props.jType,
    });
    urlPath = urlPath + "?" + params;
    const formData = new FormData();
    formData.append("file", file);
    useLoading().enterLoading("努力导入中，请稍后...");
    request({
        url: urlPath,
        data: formData,
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
    })
        .then((res: IResponseModel<any>) => {
            if (res.state != 1000) return;
            if (res.data.result === "Success") {
                ElNotify({ type: "success", message: "导入成功！" });
                if (type === "alipay") {
                    setCookie("alipayType", "user", "d1");
                    importDialogDispaly.alipay = false;
                    alipayFileInfo.name = "";
                    alipayFileInfo.file = null;
                    alipayFileRef.value && (alipayFileRef.value!.value = "");
                } else if (type === "wechatBs") {
                    importDialogDispaly.wechat = false;
                    wechatBusinessFileInfo.name = "";
                    wechatBusinessFileInfo.file = null;
                    wechatBusinessFlieRef.value && (wechatBusinessFlieRef.value!.value = "");
                } else if (type === "wechatPs") {
                    importDialogDispaly.wechat = false;
                    wechatPersonalFileInfo.name = "";
                    wechatPersonalFileInfo.file = null;
                    wechatPersonalFlieRef.value && (wechatPersonalFlieRef.value!.value = "");
                } else if (type == "bank") {
                    importDialogDispaly.bank = false;
                    bankFileInfo.name = "";
                    bankFileInfo.file = null;
                    bankFileRef.value && (bankFileRef.value!.value = "");
                } else if (type === "enterpriseWechat") {
                    importDialogDispaly.enterpriseWechat = false;
                    enterpriseWechatFileInfo.name = "";
                    enterpriseWechatFileInfo.file = null;
                    enterpriseWechatFileRef.value && (enterpriseWechatFileRef.value!.value = "");
                } else {
                    importDialogDispaly.normal = false;
                    normalFileInfo.name = "";
                    normalFileInfo.file = null;
                    normalFileRef.value && (normalFileRef.value!.value = "");
                }
                if (res.data.startDate && res.data.endDate) {
                    emit("edit-date", res.data.startDate, res.data.endDate);
                }
                setTimeout(() => {
                    if (props.cdAccount === res.data.acid + "") {
                        emit("import-success");
                    } else {
                        const acItem = props.cdAccountList.find((item) => item.ac_id === res.data.acid.toString());
                        if (!acItem) {
                            if (res.data.acid === 0) {
                                emit("import-success");
                            } else if (props.cdAccount !== props.cdAccountList[0].ac_id + "") {
                                emit("change-acid", props.cdAccountList[0].ac_id);
                            } else {
                                emit("import-success");
                            }
                        } else {
                            emit("change-acid", res.data.acid);
                        }
                    }
                }, 300);
            } else {
                if (type === "alipay") {
                    if (res.data.error.indexOf("您输入的日期早于账套启用日期！") > -1) {
                        ElNotify({ type: "warning", message: "您的交易创建时间列存在早于账套启用日期的记录！" });
                    } else if (res.data.error.indexOf("您输入的日期已结账！") > -1) {
                        ElNotify({ type: "warning", message: "您的交易创建时间列存在已结账的记录！" });
                    } else {
                        ElNotify({ type: "warning", message: res.data.error });
                    }
                } else if (type === "wechatBs" || type === "wechatPs") {
                    if (res.data.error.indexOf("您输入的日期早于账套启用日期！") > -1) {
                        ElNotify({ type: "warning", message: "您的记账时间列存在早于账套启用日期的记录！" });
                    } else if (res.data.error.indexOf("您输入的日期已结账！") > -1) {
                        ElNotify({ type: "warning", message: "您的记账时间列存在已结账的记录！" });
                    } else {
                        ElNotify({ type: "warning", message: res.data.error });
                    }
                } else if (type === "bank") {
                    if (res.data.error) {
                        resultErrorMsg.value = res.data.error;
                    } else {
                        resultErrorMsg.value = "亲，出错了，请使用模板再次尝试！";
                    }
                    importBankResultShow.value = true;
                } else {
                    ElNotify({ type: "warning", message: res.data.error });
                }
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "出错了，请使用模板再次尝试！" });
        })
        .finally(() => {
            useLoading().quitLoading();
            canImport = true;
        });
}

// 模板导入
const account = ref("");
function downloadTemplate() {
    const params = props.cdAccount === "all" ? account.value : props.cdAccount;
    const url = "/api/Journal/ExportImportTemplate?cdAccount=" + params;
    globalExport(url);
}
// 全部账户导入
async function showImportOption(type: ImportType) {
    if (type === "normal") {
        importDialogDispaly.normal = true;
    } else if (type === "bank") {
        importDialogDispaly.bank = true;
    } else if (type === "alipay") {
        importDialogDispaly.alipay = true;
    } else if (type === "wechat") {
        importDialogDispaly.wechat = true;
    } else if (type === "enterpriseWechat") {
        importDialogDispaly.enterpriseWechat = true;
    } else if (type === "receipt") {
        resetImportResult();
        const receiptImportInfo = await getReceiptImportInfo();
        const hasWaiting = receiptImportInfo.records.some((item) => item.importStatus === ImportStatus.Waiting);
        if (hasWaiting) {
            closeDialog = true;
            receiptImportResultDialogInfo.display = true;
            setRecepitImportResult(receiptImportInfo);
            checkInImportStack();
        } else {
            if (receiptImportInfo.records.length > 0) {
                receiptImportResultDialogInfo.lastResultShow = true;
                setRecepitImportResult(receiptImportInfo);
            } else {
                receiptImportResultDialogInfo.lastResultShow = false;
            }
            const acid = props.cdAccount === "all" ? "all_" + account.value : props.cdAccount;
            if (recognizeInfo.acid !== acid) {
                _list.length = 0;
                recognizeInfo.acid = acid;
                recognizeInfo.fileList.length = 0;
            }
            importDialogDispaly.receipt = true;
        }
    } else if (type === "subsidiaryLedger") {
        loadCashierSubjects();
    }
}
function handleImportTipSure() {
    showImportOption(importType);
    handleImportTipCancel();
}
function handleImportTipCancel() {
    importDialogDispaly.all = false;
    importType = "";
}
// 银行对账单(Excel)
function downloadBankTemplate() {
    globalExport("/api/Journal/ExportImportBankTemplate");
}
const importBankResultShow = ref(false);
const resultErrorMsg = ref("");
// 支付宝导入
const alipayList: Array<{ label: string; value: "business" | "personal" }> = [
    { label: "导入商家账号数据", value: "business" },
    { label: "导入个人账号数据", value: "personal" },
];
const alipaytab = ref<"business" | "personal">(!isErp.value ? "business" : "personal");
function alipayImportAuth() {
    const appAuthToken = getCookie("appAuthToken");
    const alipayCDAccount = props.cdAccount === "all" ? account.value : props.cdAccount;
    const alipayParams = "cdAccount=" + alipayCDAccount + "&jType=" + props.jType;
    if (appAuthToken == undefined || appAuthToken == "") {
        if (isAccountingAgent.value) {
            const journalImportFromType = isProSystem.value ? AliPaySystem.AgentPro : AliPaySystem.AgentFree;
            document.cookie = `journalImportFromType=${journalImportFromType};path=/;Domain=.ningmengyun.com`;
            document.cookie = `AACompanyId=${getAACompanyId()};path=/;Domain=.ningmengyun.com`;
        }
        if (isErp.value) {
            document.cookie = `journalImportFromType=${AliPaySystem.Erp};path=/;Domain=.ningmengyun.com`;
        } else if (isProSystem.value) {
            document.cookie = `journalImportFromType=${AliPaySystem.Pro};path=/;Domain=.ningmengyun.com`;
        } else {
            document.cookie = `journalImportFromType=${AliPaySystem.Acc};path=/;Domain=.ningmengyun.com`;
        }
        document.cookie = `alipayImportAuthAppasid=${getGlobalToken()};path=/;Domain=.ningmengyun.com`;
        const url = window.jFreeH5Host + "/Cashier/AlipayImportJournalRedirectRoute?stay=true&" + alipayParams;
        const href = alipayWebsite + "/oauth2/appToAppAuth.htm?app_id=" + alipayAppid + "&redirect_uri=" + encodeURIComponent(url);
        setTopLocationhref(href, true);
    } else {
        let url = "/Cashier/AlipayImportJournal?stay=true&" + alipayParams;
        if (!isErp.value) {
            url += "&appasid=" + getGlobalToken();
        } else {
            url = "/#" + url
        }
        setTopLocationhref(url, true);
    }
}
// 微信导入
const wechatShow = ref<"business" | "personal">("business");
// 明细账导入
const cashierSubjectRef = ref<InstanceType<typeof Table>>();
const period_s = ref(0); // 会计期间开始
const period_e = ref(0); // 会计期间结束
const startMonth = ref("");
const endMonth = ref("");
const periodData = ref(new Array<any>());
const cashierSubjectData = ref<Array<ICashierSubject>>([]);
const cashierSubjectSelection = ref<Array<ICashierSubject>>([]);
const cashierSubjectColumns = computed<Array<IColumnProps>>(() => {
    const columns: Array<IColumnProps> = [
        { slot: "selection", width: 36, headerAlign: "center", align: "center", reserveSelection: true },
        { label: (props.jType === "1010" ? "现金" : "银行") + "科目", prop: "asubName", align: "left", headerAlign: "left" },
        { slot: "acName" },
    ];
    if (props.jType === "1020") {
        columns.push({ label: "银行账号", prop: "bankAccount", align: "left", headerAlign: "left" });
    }
    return columns;
});
async function loadPeriodInfo() {
    await getPeriodsApi().then((res: IResponseModel<Array<IPeriod>>) => {
        if (res.state != 1000) return;
        periodData.value = res.data.reverse().filter((item) => item.status !== 3);
        periodData.value.forEach((item) => {
            item.time = item.year + "" + String(item.sn).padStart(2, "0");
        });
        const defaultPid = periodData.value[0].pid || 0;
        period_s.value = defaultPid;
        period_e.value = defaultPid;
        let result = initStartOrEndMonth(periodData.value, Number(period_s.value), Number(period_e.value));
        startMonth.value = result.startMonth;
        endMonth.value = result.endMonth;
    });
}
const getActPid = (start: number, end: number) => {
    period_s.value = start;
    period_e.value = end;
}
async function loadCashierSubjects() {
    await loadPeriodInfo();
    request({
        url: `/api/CDAccount/GetCashierImportSubjects?jType=${props.jType}&startPeriod=${period_s.value}&endPeriod=${period_e.value}`,
        method: "post",
    }).then((res: IResponseModel<Array<ICashierSubject>>) => {
        if (res.state != 1000) return;
        cashierSubjectData.value = res.data;
        importDialogDispaly.subsidiaryLedger = true;
    });
}
function cashierSubjectClose() {
    importDialogDispaly.subsidiaryLedger = false;
    cashierSubjectRef.value?.clearSelection();
}
function getPeriodInfo(period: IPeriod) {
    return period.year + "年" + period.sn + "月";
}
function selectCashierSubject(selection: Array<ICashierSubject>) {
    cashierSubjectSelection.value = selection;
}
function cellClassName(data: { row: ICashierSubject; columnIndex: number }) {
    const { row, columnIndex } = data;
    const baseClass = row.acId === 0 ? "disabled" : "";
    return columnIndex === 1 ? baseClass + " asubName" : baseClass;
}
function canSelect(row: ICashierSubject) {
    return row.acId !== 0;
}
function setAccountRelation() {
    if (window.isErp) {
        globalWindowOpenPage("/Erp/AsubRelationSettings", "科目关联设置");
    } else {
        globalWindowOpenPage("/Cashier/CDAccount", "账户设置");
    }
}
function importSubsidiaryLedger() {
    if (!canImport) {
        ElNotify({ type: "warning", message: "正在导入中，请稍后再试！" });
        return;
    }
    if (cashierSubjectSelection.value.length === 0) {
        ElNotify({ type: "warning", message: "请选择需要导入的科目和账户！" });
        return;
    }
    canImport = false;
    let journalType = props.jType === "1010" ? "ImportCashFromVoucher" : "ImportDepositFromVoucher";
    let asubIds = cashierSubjectSelection.value.map((item) => item.asubId).join(",");
    request({
        url: `/api/Journal/${journalType}?asubId=${asubIds}&startPeriod=${period_s.value}&endPeriod=${period_e.value}`,
        method: "post",
    })
        .then((res: IResponseModel<IJournalImportResult>) => {
            if (res.state != 1000) {
                ElNotify({ type: "warning", message: res.msg || "导入失败！" });
                return;
            }
            if (res.data.result !== "Success") {
                ElNotify({ type: "warning", message: res.data.error });
                return;
            }
            ElNotify({ type: "success", message: "导入成功！" });
            cashierSubjectClose();
            emit("edit-date", res.data.startDate, res.data.endDate);
            if (res.data.acstate > 0) {
                emit("change-show-disabled", true);
            }
            if (props.cdAccount === res.data.acid + "") {
                emit("import-success");
            } else {
                emit("change-acid", res.data.acid);
            }
        })
        .catch(() => {
            ElNotify({ type: "error", message: "出错了，请稍后再试！" });
        })
        .finally(() => {
            canImport = true;
        });
}
// 银行回单导入
const recognizeInfo = reactive<{
    fileList: Array<UploadRequestOptions>;
    delCount: number;
    totalCount: number;
    errCount: number;
    currentErr: number;
    rightCount: number;
    currentRight: number;
    moreSizeCount: number;
    acid: string;
}>({
    fileList: [],
    delCount: 0,
    totalCount: 0,
    errCount: 0,
    currentErr: 0,
    rightCount: 0,
    currentRight: 0,
    moreSizeCount: 0,
    acid: "",
});
function resetRecognizeInfo() {
    recognizeInfo.delCount = 0;
    recognizeInfo.errCount = 0;
    recognizeInfo.currentErr = 0;
    recognizeInfo.rightCount = 0;
    recognizeInfo.currentRight = 0;
    recognizeInfo.moreSizeCount = 0;
}
const currentSystem = computed(() => (isErp.value ? "erp" : isProSystem.value ? "pro" : "acc"));
function changeCurrentRoute() {
    const promission = isErp.value ? "Accounts-查看" : "cdaccount-canview";
    if (!checkPermission([promission])) {
        ElNotify({ type: "warning", message: "您无账户设置的权限，请联系管理员添加" });
        return;
    }
    if (isErp.value) {
        globalWindowOpenPage("/Accounts", "账户管理");
    } else {
        globalWindowOpenPage("/Cashier/CDAccount?editAcType=1020", "账户设置");
    }
}
function handleToHelp() {
    globalWindowOpen("https://help.ningmengyun.com/#/jz/commonPro?subMenuId=*********");
}
const uoloadReceiptFile: any = function (file: UploadRequestOptions) {
    if (!/(\.pdf)$/i.test(file.file.name)) {
        recognizeInfo.errCount++;
        recognizeInfo.currentErr++;
    } else if (file.file.size > 20 * 1024 * 1024) {
        recognizeInfo.moreSizeCount++;
        recognizeInfo.errCount++;
        recognizeInfo.currentErr++;
    } else {
        recognizeInfo.rightCount++;
        recognizeInfo.currentRight++;
        recognizeInfo.fileList.push(file);
    }
    if (recognizeInfo.errCount + recognizeInfo.rightCount === recognizeInfo.totalCount) {
        if (recognizeInfo.currentErr > 0) {
            const message =
                recognizeInfo.currentRight > 0 ? "PDF格式的文件上传成功,已跳过不支持的文件类型" : "选择的文件中没有PDF格式的文件";
            tipErrorFileInfo(message);
        } else if (recognizeInfo.currentRight > 0) {
            ElNotify({ message: "PDF格式的文件上传成功", type: "success" });
        }
        recognizeInfo.currentErr = 0;
        recognizeInfo.currentRight = 0;
    }
};
function tipErrorFileInfo(message: string) {
    if (recognizeInfo.moreSizeCount > 0) {
        if (recognizeInfo.moreSizeCount === recognizeInfo.currentErr) {
            ElNotify({ type: "warning", message: "上传的回单文件大小不能超过20M" });
        } else {
            ElNotify({ message: "PDF格式的文件上传成功，已跳过大小超20M的文件", type: "warning" });
        }
    } else {
        ElNotify({ message, type: "warning" });
    }
}
let _list: Array<any> = [];
function onChange(file: any, list: Array<any>) {
    _list = list;
    if (!recognizeInfo.delCount) {
        recognizeInfo.totalCount = list.length;
    } else {
        recognizeInfo.totalCount = list.length - recognizeInfo.delCount;
    }
}
function deleteFile(uid: number) {
    recognizeInfo.delCount++;
    recognizeInfo.rightCount--;
    recognizeInfo.fileList = recognizeInfo.fileList.filter((item) => item.file.uid !== uid);
}
function downloadFile(file: UploadRequestOptions) {
    const blob = new Blob([file.file], { type: file.file.type });
    let a = document.createElement("a");
    a.download = file.file.name;
    a.href = URL.createObjectURL(blob);
    a.style.display = "none";
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(a.href);
    document.body.removeChild(a);
}
let isUpload = false;
const limitCount = computed(() => 50);
function comfirmRecognizeImport() {
    if (isUpload) return;
    isUpload = true;
    if (recognizeInfo.fileList.length > limitCount.value) {
        ElNotify({ message: `一次最多上传${limitCount.value}个文件`, type: "warning" });
        isUpload = false;
        return;
    }
    if (!recognizeInfo.fileList.length) {
        ElNotify({ message: "未上传识别导入的文件，请上传PDF格式的银行回单文件后再操作", type: "warning" });
        isUpload = false;
        return;
    }
    const fileData = new FormData();
    recognizeInfo.fileList.forEach((file) => {
        fileData.append("file", file.file);
    });
    const acid = props.cdAccount === "all" ? account.value : props.cdAccount;
    request({
        url: window.jReceiptImportHost + "/api/Journal/ImportReceiptFile?acId=" + acid,
        method: "post",
        data: fileData,
        headers: { "Content-Type": "multipart/form-data" },
    })
        .then(async (res: IResponseModel<boolean>) => {
            isUpload = false;
            if (res.state === 1000 && res.data) {
                resetRecognizeInfo();
                recognizeInfo.totalCount = 0;
                _list.length = 0;
                recognizeInfo.fileList.length = 0;
                closeDialog = true;
                receiptImportResultDialogInfo.display = true;
                const receiptImportInfo = await getReceiptImportInfo();
                setRecepitImportResult(receiptImportInfo);
                cancelRecognizeImport();
                checkInImportStack();
                recognizeInfo.fileList.length = 0;
            } else {
                ElNotify({ message: res.msg || "PDF文件上传失败，请稍后再试！", type: "warning" });
                return;
            }
        })
        .catch(() => {
            ElNotify({ message: "PDF文件上传失败，请稍后再试！", type: "warning" });
            isUpload = false;
        });
}
function setRecepitImportResult(receiptImportInfo: IReceiptImportBack) {
    receiptImportResultDialogInfo.data = receiptImportInfo.records || [];
    receiptImportResultDialogInfo.importDate = receiptImportInfo.importDate;
    receiptImportResultDialogInfo.startDate = receiptImportInfo.startDate;
    receiptImportResultDialogInfo.endDate = receiptImportInfo.endDate;
    receiptImportResultDialogInfo.checkDiaplay = receiptImportResultDialogInfo.data.length > 0 && checkShowCheck();
}
function checkShowCheck() {
    let show = true;
    let err = 0;
    for (let i = 0; i < receiptImportResultDialogInfo.data.length; i++) {
        if (receiptImportResultDialogInfo.data[i].importStatus === ImportStatus.Waiting) {
            show = false;
            break;
        } else if (receiptImportResultDialogInfo.data[i].importStatus !== ImportStatus.Success) {
            err++;
        }
    }
    return show && err !== receiptImportResultDialogInfo.data.length;
}
let closeDialog = true;
function checkLastResult() {
    closeDialog = false;
    receiptImportResultDialogInfo.display = true;
    importDialogDispaly.receipt = false;
    checkInImportStack();
}
enum ImportStatus {
    None = 0,
    Success = 1,
    Waiting = 2,
    Failed = 3,
}
interface IReceiptImportList {
    asId: number;
    acId: number;
    recordId: number;
    fileName: string;
    importStatus: ImportStatus;
    remarks: string;
    importDate: string;
    importBy: number;
    startDate: string | null;
    endDate: string | null;
}
interface IReceiptImportBack {
    records: Array<IReceiptImportList> | null;
    startDate: string;
    endDate: string;
    importDate: string;
}
async function getReceiptImportInfo() {
    let records: Array<IReceiptImportList> = [];
    let importDate = "";
    let startDate = "";
    let endDate = "";
    const acid = props.cdAccount === "all" ? account.value : props.cdAccount;
    await request({ url: "/api/Journal/ReceiptRecord?acid=" + acid }).then(async (res: IResponseModel<IReceiptImportBack>) => {
        if (res.state !== 1000 || !res.data.records) return;
        records = res.data.records;
        importDate = dayjs(res.data.importDate.split(" ")[0]).format("YYYY-MM-DD");
        startDate = res.data.startDate;
        endDate = res.data.endDate;
    });
    return { records, importDate, startDate, endDate };
}
function cancelRecognizeImport() {
    importDialogDispaly.receipt = false;
}
// 银行回单导入结果
const receiptImportResultDialogInfo = reactive<{
    data: Array<IReceiptImportList>;
    display: boolean;
    lastResultShow: boolean;
    importDate: string;
    checkDiaplay: boolean;
    startDate: string;
    endDate: string;
}>({
    data: [],
    display: false,
    lastResultShow: true,
    importDate: "",
    checkDiaplay: false,
    startDate: "",
    endDate: "",
});
function resetImportResult() {
    receiptImportResultDialogInfo.data.length = 0;
    receiptImportResultDialogInfo.importDate = "";
    receiptImportResultDialogInfo.checkDiaplay = false;
    receiptImportResultDialogInfo.startDate = "";
    receiptImportResultDialogInfo.endDate = "";
    closeDialog = true;
    clearTimeout(timer);
}
function receiptImportResultClose() {
    if (!closeDialog) {
        importDialogDispaly.receipt = true;
    } else {
        resetImportResult();
    }
}
function getLinkIndex(remarks: string) {
    const start = remarks.indexOf("前往查看设置");
    const end = start + 6;
    return { start, end };
}
function toAccount() {
    if (!isErp.value) {
        if (!checkPermission(["cdaccount-canview"])) {
            ElNotify({ type: "warning", message: "您无账户设置的权限，请联系管理员添加" });
            return;
        }
        globalWindowOpenPage("/Cashier/CDAccount?editAcType=1020", "账户设置");
    } else {
        if (!checkPermission(["Accounts-查看"])) {
            ElNotify({ type: "warning", message: "您无账户设置的权限，请联系管理员添加" });
            return;
        }
        globalWindowOpenPage("/Accounts", "账户管理");
    }
}
let timer: any;
function checkInImportStack() {
    const hasLoading = receiptImportResultDialogInfo.data.some((item) => item.importStatus === ImportStatus.Waiting);
    if (!hasLoading) return;
    timer = setTimeout(async () => {
        const receiptImportInfo = await getReceiptImportInfo();
        setRecepitImportResult(receiptImportInfo);
        checkInImportStack();
    }, 5000);
}
const importResultColumns = ref<Array<IColumnProps>>([
    {
        label: "序号",
        minWidth: 40,
        align: "left",
        headerAlign: "left",
        formatter(_r, _c, _v, index: number) {
            return index + 1 + "";
        },
    },
    { label: "文件名称", prop: "fileName", minWidth: 100, align: "left", headerAlign: "left" },
    {
        label: "导入状态",
        prop: "importStatus",
        minWidth: 80,
        align: "left",
        headerAlign: "left",
        formatter(row, column, val: ImportStatus) {
            if (val === ImportStatus.Success) {
                return "已导入";
            } else if (val === ImportStatus.Waiting) {
                return "导入加载中...";
            } else if (val === ImportStatus.Failed) {
                return "导入失败";
            } else {
                return "未导入";
            }
        },
    },
    { slot: "remarks" },
]);
function checkJournalFromReceiptImport() {
    closeDialog = true;
    closeImportResultDialog();
    if (receiptImportResultDialogInfo.startDate && receiptImportResultDialogInfo.endDate) {
        emit("edit-date", receiptImportResultDialogInfo.startDate, receiptImportResultDialogInfo.endDate);
    }
    const timer = setTimeout(() => {
        emit("import-success");
        clearTimeout(timer);
    });
}
function closeImportResultDialog() {
    receiptImportResultDialogInfo.display = false;
}

defineExpose({ selectImportMethod });
</script>

<style scoped lang="less">
@import "@/style/Cashier/JournalImport.less";
</style>
