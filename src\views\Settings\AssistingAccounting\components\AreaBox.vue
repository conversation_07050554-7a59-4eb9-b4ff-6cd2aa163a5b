<template>
    <el-popover placement="bottom-start" :width="300" trigger="click" ref="areaRef" @show="handleShow" @hide="handleHide">
        <template #reference>
            <div class="input-box">
                <el-tooltip :content="inputVal" effect="light" placement="right" :visible="visible">
                    <input type="text" v-model="inputVal" class="input input-ellipsis" @mouseenter="visible = inputVal.length>13" @mouseleave="visible = false"/>
                </el-tooltip>
            </div>
        </template>
        <div>
            <div class="tab">
                <div class="tab-item" :class="{ 'tab-item_selected': selected === 'pro' }" @click="tabClick('pro')">省份</div>
                <div class="tab-item" :class="{ 'tab-item_selected': selected === 'city' }" @click="tabClick('city')">城市</div>
                <div class="tab-item" :class="{ 'tab-item_selected': selected === 'area' }" @click="tabClick('area')">区县</div>
            </div>
            <div class="content-box">
                <div class="left" :class="firstPage ? 'first' : ''" @click="changeShow('last')"></div>
                <div class="area-box">
                    <ul v-if="selected === 'pro'">
                        <li
                            :class="areaInfo.adLevel1 === item.id ? 'selected' : ''"
                            v-for="item in state.proList"
                            :key="item.id"
                            @click="proClick(item.id, item.name)"
                        >
                            {{ item.name }}
                        </li>
                    </ul>
                    <ul v-if="selected === 'city'">
                        <li
                            :class="areaInfo.adLevel2 === item.id ? 'selected' : ''"
                            v-for="item in state.cityList"
                            :key="item.id"
                            @click="cityClick(item.id)"
                        >
                            {{ item.name }}
                        </li>
                    </ul>
                    <ul v-if="selected === 'area'">
                        <li
                            :class="areaInfo.adLevel3 === item.id ? 'selected' : ''"
                            v-for="item in state.areaList"
                            :key="item.id"
                            @click="areaClick(item)"
                        >
                            {{ item.areaName }}
                        </li>
                    </ul>
                </div>
                <div class="right" :class="lastPage ? 'last' : ''" @click="changeShow('next')"></div>
            </div>
        </div>
    </el-popover>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElPopover } from "element-plus";

interface IAreaInfo {
    adLevel1: string;
    adLevel2: string;
    adLevel3: string;
}
const props = defineProps<{
    selectedAreaInfo: IAreaInfo;
}>();
const visible=ref(false)
interface provinceBack {
    name: string;
    id: string;
}
interface cityBack {
    name: string;
    id: string;
    provinceId: string;
    cityPinyin: string;
    hotCity: boolean;
    cityShortPY: string;
}
interface areaBack {
    areaName: string;
    cityId: string;
    cityName: string;
    id: string;
    pinYin: string;
    pinYinChar: string;
    provinceId: string;
}
let proName = "";
const areaRef = ref<InstanceType<typeof ElPopover>>();
const inputVal = ref("");
const selected = ref<"pro" | "city" | "area">("pro");
const currentProindex = ref(0);
const currentCityindex = ref(0);
const currentAreaindex = ref(0);
const emit = defineEmits(["address"]);
const state = reactive<any>({
    proList: [] as provinceBack[],
    cityList: [] as cityBack[],
    areaList: [] as areaBack[],
});
const showList = reactive({
    porvinceArr: [] as provinceBack[],
    cityArr: [] as cityBack[],
    areaArr: [] as areaBack[],
});
const areaInfo = reactive({
    adLevel1: "",
    adLevel2: "",
    adLevel3: "",
});
const handleHide = () => {
    areaInfo.adLevel1 = "";
    areaInfo.adLevel2 = "";
    areaInfo.adLevel3 = "";
    selected.value = "pro";
};
const handleShow = () => {
    const { adLevel1, adLevel2, adLevel3 } = props.selectedAreaInfo;
    areaInfo.adLevel1 = adLevel1;
    areaInfo.adLevel2 = adLevel2;
    areaInfo.adLevel3 = adLevel3;
};
const getCities = (proId: string) => {
    request({ url: "/api/City/CityList?proId=" + proId }).then((res: IResponseModel<cityBack[]>) => {
        showList.cityArr = chunkArray(
            res.data.map((item: cityBack) => {
                item.name = filterProName(item.name, "city");
                return item;
            })
        );
        state.cityList = showList.cityArr[0];
        firstPage.value = true;
        lastPage.value = showList.cityArr.length === 1;
    });
};
const getArea = (cityId: string) => {
    request({ url: "/api/City/GetArea?cityId=" + cityId, method: "post" }).then((res: IResponseModel<areaBack[]>) => {
        showList.areaArr = chunkArray(
            res.data.map((item: areaBack) => {
                item.areaName = filterProName(item.areaName, "area");
                return item;
            })
        );
        state.areaList = showList.areaArr[0];
        firstPage.value = true;
        lastPage.value = showList.areaArr.length === 1;
    });
};
const proClick = (proId: string, name: string) => {
    proName = name;
    areaInfo.adLevel1 = proId;
    getCities(proId);
    selected.value = "city";
};
const cityClick = (cityId: string) => {
    areaInfo.adLevel2 = cityId;
    getArea(cityId);
    selected.value = "area";
};
const areaClick = (data: areaBack) => {
    inputVal.value = proName + "-" + data.cityName + "-" + data.areaName;
    areaInfo.adLevel3 = data.id;
    areaRef.value?.hide();
    emit("address", {
        adLevel1: data.provinceId,
        adLevel2: data.cityId,
        adLevel3: data.id,
    });
};
const editInputVal = (val: string) => (inputVal.value = val);
const tabClick = (area: "pro" | "city" | "area") => {
    if (area === "city" && state.cityList.length === 0) return;
    if (area === "area" && state.areaList.length === 0) return;
    if (area === "pro") {
        firstPage.value = currentProindex.value === 0;
        lastPage.value = currentProindex.value === showList.porvinceArr.length - 1;
    } else if (area === "city") {
        firstPage.value = currentCityindex.value === 0;
        lastPage.value = currentCityindex.value === showList.cityArr.length - 1;
    } else if (area === "area") {
        firstPage.value = currentAreaindex.value === 0;
        lastPage.value = currentAreaindex.value === showList.areaArr.length - 1;
    }
    selected.value = area;
};
const firstPage = ref(true);
const lastPage = ref(true);
const changeShow = (type: string) => {
    if (selected.value === "pro") {
        if (type === "next") {
            if (currentProindex.value === showList.porvinceArr.length - 1) {
                lastPage.value = true;
                firstPage.value = showList.porvinceArr.length === 1;
                return;
            }
            currentProindex.value = currentProindex.value + 1;
            firstPage.value = false;
            lastPage.value = currentProindex.value === showList.porvinceArr.length - 1;
        } else {
            if (currentProindex.value === 0) {
                firstPage.value = true;
                lastPage.value = showList.porvinceArr.length === 1;
                return;
            }
            currentProindex.value = currentProindex.value - 1;
            firstPage.value = currentProindex.value === 0;
            lastPage.value = currentProindex.value === showList.porvinceArr.length - 1;
        }
        state.proList = showList.porvinceArr[currentProindex.value];
    } else if (selected.value === "city") {
        if (type === "next") {
            if (currentCityindex.value === showList.cityArr.length - 1) {
                lastPage.value = true;
                firstPage.value = showList.cityArr.length === 1;
                return;
            }
            currentCityindex.value = currentCityindex.value + 1;
            firstPage.value = false;
            lastPage.value = currentCityindex.value === showList.cityArr.length - 1;
        } else {
            if (currentCityindex.value === 0) {
                firstPage.value = true;
                lastPage.value = showList.cityArr.length === 1;
                return;
            }
            currentCityindex.value = currentCityindex.value - 1;
            firstPage.value = currentCityindex.value === 0;
            lastPage.value = currentCityindex.value === showList.cityArr.length - 1;
        }
        state.cityList = showList.cityArr[currentCityindex.value];
    } else if (selected.value === "area") {
        if (type === "next") {
            if (currentAreaindex.value === showList.areaArr.length - 1) {
                lastPage.value = true;
                firstPage.value = showList.areaArr.length === 1;
                return;
            }
            currentAreaindex.value = currentAreaindex.value + 1;
            firstPage.value = false;
            lastPage.value = currentAreaindex.value === showList.areaArr.length - 1;
        } else {
            if (currentAreaindex.value === 0) {
                firstPage.value = true;
                lastPage.value = showList.areaArr.length === 1;
                return;
            }
            currentAreaindex.value = currentAreaindex.value - 1;
            firstPage.value = currentAreaindex.value === 0;
            lastPage.value = currentAreaindex.value === showList.areaArr.length - 1;
        }
        state.areaList = showList.areaArr[currentAreaindex.value];
    }
};
watch(
    () => state.cityList,
    () => {
        state.areaList = [];
    }
);
const changeAreaInfo = (adLevel1: string, adLevel2: string, adLevel3: string) => {
    areaInfo.adLevel1 = adLevel1;
    areaInfo.adLevel2 = adLevel2;
    areaInfo.adLevel3 = adLevel3;
};
defineExpose({ editInputVal, changeAreaInfo });
const handleInit = () => {
    request({ url: "/api/City/GetProvinces", method: "post" }).then((res: IResponseModel<provinceBack[]>) => {
        showList.porvinceArr = chunkArray(
            res.data.map((item: provinceBack) => {
                item.name = filterProName(item.name, "pro");
                return item;
            })
        );
        state.proList = showList.porvinceArr[0];
        firstPage.value = true;
        lastPage.value = showList.porvinceArr.length === 1;
    });
};
handleInit();
// 工具函数
const filterProName = (name: string, type: string) => {
    if (type === "pro") {
        if (name.length <= 3 || name.includes("自治区") || name.includes("特别")) {
            name = name.includes("内蒙古") ? name.slice(0, 3) : name.slice(0, 2);
        } else {
            name = name.slice(0, 3);
        }
    } else if (type === "city" || type === "area") {
        name = name.slice(0, 4);
    }
    return name;
};
const chunkArray = (arr: any) => {
    var chunks = [];
    while (arr.length) {
        chunks.push(arr.splice(0, 12));
    }
    return chunks;
};
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/Settings/AssistingAccounting.less";
.tab {
    display: flex;
    .tab-item {
        flex: 1;
        width: 78px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        cursor: pointer;
        font-size: 12px;
    }
    .tab-item_selected {
        background: var(--main-color);
        color: #fff;
    }
}
.content-box {
    display: flex;
    align-items: center;
    height: 130px;
    box-sizing: border-box;
    padding: 10px;
    font-size: 12px;
    position: relative;
    .left,
    .right {
        height: 109px;
        width: 29px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 10px;
        background-color: pink;
    }
    .left {
        left: 0px;
        background: url("@/assets/Settings/left_can.png") no-repeat center center;
        &.first {
            background: url("@/assets/Settings/left.png") no-repeat center center;
        }
    }
    .right {
        right: 0px;
        background: url("@/assets/Settings/right_can.png") no-repeat center center;
        &.last {
            background: url("@/assets/Settings/right.png") no-repeat center center;
        }
    }
    .area-box {
        flex: 1;
        height: 100%;
        ul {
            width: 100%;
            height: 100%;
            display: flex;
            align-content: flex-start;
            flex-wrap: wrap;
            list-style: none;
            margin: 0px;
            padding: 0px;
            box-sizing: border-box;
            padding-top: 11px;
            li {
                list-style: none;
                margin: 0px;
                padding: 0px;
                width: 33.33%;
                cursor: pointer;
                display: flex;
                justify-content: center;
                align-content: center;
                height: 16px;
                padding: 3px 0px;
                color: var(--font-color);
                &:hover {
                    color: var(--main-color);
                    text-decoration: underline;
                }
                &.selected {
                    color: var(--red);
                    text-decoration: none;
                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}
.input-box {
    & > .input {
        background: url("@/assets/Icons/down-black.png") no-repeat;
        background-position: bottom 10px right 5px;
        .detail-original-input(188px, 30px);
        &:focus {
            background-image: url("@/assets/Icons/up-black.png");
        }
    }
}
</style>
