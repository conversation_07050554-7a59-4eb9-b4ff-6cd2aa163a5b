<template>
    <div class="tabs-pane-content">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <DatePicker
                    class="small"
                    v-model:start-pid="searchInfo.startPid"
                    v-model:end-pid="searchInfo.endPid"
                    :clearable="true"
                    :disabled-date-start="disabledDateStart"
                    :disabled-date-end="disabledDateEnd"
                />
                <span class="ml-10">账户：</span>
                <CDAccountFilter
                    ref="CDAccountFilterRef"
                    v-model:accountOptions="accountOptions"
                    :options="options"
                    v-model:selectedList="selectedList"
                    :width="'170px'"
                    :maxWidth="138"
                    @change-switch-status="changeSwitchStatus"
                />
                <span class="ml-10" v-if="fcList.length > 2">币别：</span>
                <CurrencyFilter
                    v-if="fcList.length > 2"
                    v-model:fcid="searchInfo.fcid" 
                    :fcList="fcList" 
                    :width="'100px'"
                ></CurrencyFilter>
                <span class="ml-10">主类别：</span>
                <el-select 
                    v-model="searchInfo.mainItem" 
                    :fit-input-width="true" 
                    style="width: 100px"
                    :filterable="true"
                    :filter-method="mainTypeFilterMethod"
                >
                    <el-option 
                        v-for="item in showCombineTypesMain" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
                <span class="ml-10">次类别：</span>
                <el-select 
                    v-model="searchInfo.subItem" 
                    :fit-input-width="true" 
                    style="width: 100px" 
                    :teleported="false"
                    :filterable="true"
                    :filter-method="subTypeFilterMethod"
                >
                    <el-option 
                        v-for="item in showCombineTypesSub" 
                        :key="item.value" 
                        :label="item.label" 
                        :value="item.value" 
                    />
                </el-select>
                <a class="solid-button ml-10" @click="handleSearch">查询</a>
            </div>
            <div class="main-tool-right">
                <el-checkbox v-model="searchInfo.showMainTotal" @change="handleQuickSearch">显示主类型合计</el-checkbox>
                <a class="button ml-10" v-permission="['report-canprint']" @click="handlePrintOrExport('print')">打印</a>
                <a class="button ml-10" v-permission="['report-canexport']" @click="handlePrintOrExport('export')">导出</a>
                <RefreshButton></RefreshButton>
            </div>
        </div>
        <div class="multi-combine" v-if="isErp && showMoreTable">
            <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                <Table 
                    :columns="defaultLayoutColumns" 
                    :data="tableData" 
                    empty-text="暂无数据" 
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.name, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column
                            label="收入总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column label="收入总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column
                            label="支出总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="expend_standard"
                            :width="getColumnWidth(setModule, 'expend_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column label="支出总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expendFc')"
                                align="right"
                                header-align="right"
                                prop="expendFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expend')"
                                align="right"
                                header-align="right"
                                prop="expend"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #operate>
                        <el-table-column label="操作" min-width="128px" align="left" header-align="left">
                            <template #default="{ row }: { row: IAAJournalTable }">
                                <span @click="toCombineJournal(row)" v-if="row.name !== '' && row.second_name !== ''" class="link">
                                    查看明细
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
                <div class="mb-10"></div>
                <Table 
                    :columns="seaondLayoutColumns" 
                    :data="vendorData" 
                    empty-text="暂无数据" 
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.name, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column
                            label="收入总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column label="收入总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column
                            label="支出总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="expend_standard"
                            :width="getColumnWidth(setModule, 'expend_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column label="支出总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expendFc')"
                                align="right"
                                header-align="right"
                                prop="expendFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expend')"
                                align="right"
                                header-align="right"
                                prop="expend"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #operate>
                        <el-table-column label="操作" min-width="128px" align="left" header-align="left">
                            <template #default="{ row }: { row: IAAJournalTable }">
                                <span @click="toCombineJournal(row)" v-if="row.name !== '' && row.second_name !== ''" class="link">
                                    查看明细
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
                <div class="mb-10"></div>
                <Table 
                    :columns="thirdLayoutColumns" 
                    :data="employeeData" 
                    empty-text="暂无数据" 
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.name, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column
                            label="收入总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column label="收入总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column
                            label="支出总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="expend_standard"
                            :width="getColumnWidth(setModule, 'expend_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column label="支出总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expendFc')"
                                align="right"
                                header-align="right"
                                prop="expendFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expend')"
                                align="right"
                                header-align="right"
                                prop="expend"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #operate>
                        <el-table-column label="操作" min-width="128px" align="left" header-align="left">
                            <template #default="{ row }: { row: IAAJournalTable }">
                                <span @click="toCombineJournal(row)" v-if="row.name !== '' && row.second_name !== ''" class="link">
                                    查看明细
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
                <div class="mb-10"></div>
                <Table
                    :show-header="false"
                    :columns="thirdLayoutColumns"
                    :data="totalData"
                    empty-text="暂无数据"
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.name, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column
                            label="收入总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column label="收入总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column
                            label="支出总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="expend_standard"
                            :width="getColumnWidth(setModule, 'expend_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column label="支出总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expendFc')"
                                align="right"
                                header-align="right"
                                prop="expendFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expend')"
                                align="right"
                                header-align="right"
                                prop="expend"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #operate>
                        <el-table-column label="操作" min-width="128px" align="left" header-align="left"> </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="main-bottom">
                <div style="color: var(--red)">提示：</div>
                <div class="mt-10">
                    <span>内部转账，不计入往来</span>
                    <a v-permission="['transfer-canview']" class="link ml-10" @click="redirectToTransfer">点击查看</a>
                </div>
            </div>
        </div>
        <template v-else>
            <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                <Table
                    :columns="defaultLayoutColumns"
                    :data="tableData"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :show-overflow-tooltip="true"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.name, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column
                            label="收入总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column label="收入总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column
                            label="支出总额"
                            :min-width="216"
                            align="right"
                            header-align="right"
                            prop="expend_standard"
                            :width="getColumnWidth(setModule, 'expend_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column label="支出总额" header-align="center">
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expendFc')"
                                align="right"
                                header-align="right"
                                prop="expendFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expend')"
                                align="right"
                                header-align="right"
                                prop="expend"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expend_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #operate>
                        <el-table-column label="操作" min-width="128px" align="left" header-align="left" :resizable="false">
                            <template #default="{ row }: { row: IAAJournalTable }">
                                <span @click="toCombineJournal(row)" v-if="row.name !== '' && row.second_name !== ''" class="link">
                                    查看明细
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="main-bottom">
                <div style="color: var(--red)">提示：</div>
                <div class="mt-10">
                    <span>内部转账，不计入往来</span>
                    <a v-permission="['transfer-canview']" class="link ml-10" @click="redirectToTransfer">点击查看</a>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, watchEffect, nextTick } from "vue";
import { getUrlSearchParams, globalExport, globalPrint } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { CashAAType } from "@/views/Cashier/CashOrDepositJournal/types";
import { checkReportTableEmpty, combineTypes, getColumns } from "../utils";
import { globalWindowOpenPage } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { getATagParams, formatCombineParams, getSearchInfoCD, getFcCode } from "../utils";
import { dayjs } from "element-plus";
import { getGlobalLodash } from "@/util/lodash";
import { usePagination } from "@/hooks/usePagination";
import { getGlobalToken } from "@/util/baseInfo";

import { Option } from "@/components/SelectCheckbox/types";
import type { IDateInfo, IAAJournalBack, IAAJournalTable, ISCurrcy } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import CDAccountFilter from "@/views/Cashier/components/CDAccountFilter.vue";
import CurrencyFilter from "@/views/Cashier/components/CurrencyFilter.vue";
import { formatMoney } from "@/util/format";

const setModule = "CashReportCombine";
const isErp = ref(window.isErp);
const accountSetStore = useAccountSetStore();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination("Combine", true);

const props = defineProps<{ 
    options: Array<IBankAccountItem>; 
    dateInfo: IDateInfo;
    fcList: ISCurrcy[];
}>();

const emit = defineEmits<{
    (event: "change-switch-status", args: boolean): void;
}>();
const changeSwitchStatus = (val: boolean) => {
    emit("change-switch-status", val);
}

const accountOptions = ref<Option[]>([]);
const cacheKey = "combineReport-showTotal-" + getGlobalToken();
const selectedList = ref<Array<number>>([]);
const searchInfo = reactive({
    startPid: "",
    endPid: "",
    CD_ACC_IDS: "ALL",
    mainItem: CashAAType.IEType,
    subItem: CashAAType.Project,
    showMainTotal: localStorage.getItem(cacheKey) === "true",
    fcid: -1,
});

watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});

const loading = ref(false);
const defaultLayoutColumns = ref<Array<IColumnProps>>([]);
const seaondLayoutColumns = ref<Array<IColumnProps>>([]);
const thirdLayoutColumns = ref<Array<IColumnProps>>([]);
const tableData = ref<Array<IAAJournalTable>>([]);
const vendorData = ref<Array<IAAJournalTable>>([]);
const employeeData = ref<Array<IAAJournalTable>>([]);
const totalData = ref<Array<IAAJournalTable>>([]);
const showMoreTable = ref(false);
function handleQuickSearch(check: any) {
    const showMainTotal = !!check;
    localStorage.setItem(cacheKey, showMainTotal + "");
    handleSearch(false);
}
function calcColumns() {
    const [defaultColumns, secondColumns, thirdColumns] = getColumns(searchInfo.mainItem, searchInfo.subItem, searchInfo.fcid);
    defaultLayoutColumns.value = defaultColumns;
    showMoreTable.value = searchInfo.mainItem === CashAAType.Unit || searchInfo.subItem === CashAAType.Unit;
    if (isErp.value && showMoreTable.value) {
        seaondLayoutColumns.value = secondColumns;
        thirdLayoutColumns.value = thirdColumns;
    }
}
function handleSearch(toFirthPage?: any) {
    if (searchInfo.mainItem === searchInfo.subItem) {
        ElNotify({ type: "warning", message: "主、次类别选项不能为相同选项，请重新选择" });
        return;
    }
    calcColumns();
    if (toFirthPage === true) {
        paginationData.currentPage !== 1 ? (paginationData.currentPage = 1) : loadTableData();
    } else {
        loadTableData();
    }
}
function handleGetParams() {
    return {
        tabType: "COMBINATION",
        startDate: searchInfo.startPid,
        endDate: searchInfo.endPid,
        cdAccIds: searchInfo.CD_ACC_IDS,
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
        firstType: formatCombineParams(searchInfo.mainItem),
        secondType: formatCombineParams(searchInfo.subItem),
        firstTotal: searchInfo.showMainTotal,
        showDisableAccount: getShowDisabledAccount("cashReport"),
        fcid: searchInfo.fcid,
    };
}
const currentFcid = ref(-1);
async function loadTableData() {
    loading.value = true;
    const params = handleGetParams();
    await request({ url: "/api/CashierReport/PagingListForAA?" + getUrlSearchParams(params) })
        .then((res: IResponseModel<IAAJournalBack>) => {
            if (res.state == 1000) {
                const list = res.data.rows;
                if (isErp.value && showMoreTable.value) {
                    formatTableData(list);
                } else {
                    paginationData.total = res.data.total;
                    const rows = [...list];
                    const pushTotal =
                        (paginationData.currentPage - 1) * paginationData.pageSize <= res.data.total &&
                        paginationData.currentPage * paginationData.pageSize >= res.data.total;
                    if (res.data.totalRows && res.data.totalRows.length > 0 && pushTotal) {
                        rows.push(...res.data.totalRows);
                    }
                    tableData.value = rows;
                }
            }
            loading.value = false;
            currentFcid.value = searchInfo.fcid;
        })
        .catch(() => {
            loading.value = false;
        });
}
const handlePrintOrExport = (type: "print" | "export") => {
    const erpShowMoreTableLength = tableData.value.length + vendorData.value.length + employeeData.value.length;
    const length = isErp.value && showMoreTable.value ? erpShowMoreTableLength : tableData.value.length;
    if (!checkReportTableEmpty(length, isErp.value && showMoreTable.value ? 4 : 2)) return;
    if (searchInfo.startPid == "" || searchInfo.endPid == "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    if (searchInfo.mainItem === searchInfo.subItem) {
        ElNotify({ type: "warning", message: "主、次类别选项不能为相同选项，请重新选择" });
        return;
    }
    const urlPath = type === "print" ? "PrintForAA?" : "ExportForAA?";
    const url = "/api/CashierReport/" + urlPath + getUrlSearchParams(handleGetParams());
    type === "print" ? globalPrint(url) : globalExport(url);
};
const CDAccountFilterRef = ref();
function handleInit() {
    const { start, end } = props.dateInfo;
    searchInfo.startPid = start;
    searchInfo.endPid = end;
    nextTick(() => {
        CDAccountFilterRef.value?.handleInit();
        selectedList.value = accountOptions.value.map((i) => i.id);
        searchInfo.CD_ACC_IDS = getSearchInfoCD(selectedList.value, props.options);
        handleSearch();
    });
}
defineExpose({ handleInit });

watch(selectedList, () => {
    searchInfo.CD_ACC_IDS = getSearchInfoCD(selectedList.value, props.options);
});

function disabledDateStart(time: Date) {
    const endDate = dayjs(searchInfo.endPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    return time.getTime() > endDate || time.getTime() < asStartDate;
}
function disabledDateEnd(time: Date) {
    const startDate = dayjs(searchInfo.startPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    const minTime = Math.max(startDate, asStartDate);
    return time.getTime() < minTime;
}
function redirectToTransfer() {
    globalWindowOpenPage("/Cashier/Transfer", "内部转账");
}
const { cloneDeep } = getGlobalLodash();
function formatTableData(table: Array<IAAJournalTable>) {
    const data = cloneDeep(table) as Array<IAAJournalTable>;
    const totalRow = getTotalRow(data);
    const customerList: Array<IAAJournalTable> = [];
    const vendorList: Array<IAAJournalTable> = [];
    const employeeList: Array<IAAJournalTable> = [];
    for (let i = 0; i < data.length; i++) {
        const item = data[i];
        if (item.aaType === "10001") {
            customerList.push(item);
        } else if (item.aaType === "10002") {
            vendorList.push(item);
        } else if (item.aaType === "10003") {
            employeeList.push(item);
        }
    }
    const customerTotalRow = getTotalRow(customerList, "客户");
    const vendorTotalRow = getTotalRow(vendorList, "供应商");
    const employeeTotalRow = getTotalRow(employeeList, "职员");
    customerList.push(customerTotalRow);
    vendorList.push(vendorTotalRow);
    employeeList.push(employeeTotalRow);
    totalData.value = [totalRow];
    tableData.value = customerList;
    vendorData.value = vendorList;
    employeeData.value = employeeList;
}
function getTotalRow(data: Array<any>, totalName = "组合") {
    const totalRow: IAAJournalTable = {
        name: "",
        code: "",
        second_name: totalName + "合计",
        second_code: "",
        income_standard: 0,
        expend_standard: 0,
        incomeCount: 0,
        expendCount: 0,
        income: 0,
        expend: 0,
    };
    data.forEach((i) => {
        if (i.second_name && i.second_name !== "组合合计") {
            totalRow.income += i.income;
            totalRow.expend += i.expend;
            totalRow.incomeCount += i.incomeCount;
            totalRow.expendCount += i.expendCount;
            totalRow.income_standard = totalRow.income_standard + i.income_standard;
            totalRow.expend_standard = totalRow.expend_standard + i.expend_standard;
        }
    });
    return totalRow;
}
function toCombineJournal(row: IAAJournalTable) {
    const mainParams = { ...getATagParams(row.name) };
    const subParams = { ...getATagParams(row.second_name || "") };
    const params: any = {
        cd_ids: mainParams.CD_IDS,
        date_s: mainParams.Date_s,
        date_e: mainParams.Date_e,
        mian_item: searchInfo.mainItem === CashAAType.IEType ? "IE_TYPE" : mainParams.AA_Type,
        sub_item: searchInfo.subItem === CashAAType.IEType ? "IE_TYPE" : subParams.AA_Type,
        main_id: searchInfo.mainItem === CashAAType.IEType ? mainParams.IE_ID : mainParams.AA_Value,
        sub_id: searchInfo.subItem === CashAAType.IEType ? subParams.IE_ID : subParams.AA_Value,
        fcid: searchInfo.fcid,
        showDisabled: getShowDisabledAccount("cashReport"),
        ran: Math.random()
    };
    if (isErp.value) {
        params.Page = "COMBINATION";
    }
    const path = isErp.value ? "/Cashier/Journal" : "/Cashier/CombineJournal";
    globalWindowOpenPage(path + "?" + getUrlSearchParams(params), "组合类别日记账");
}

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    loadTableData();
});

function headerDragend(width: number, prop: string) {
    calcColumns();
}

const showCombineTypesMain = ref<Array<any>>([]);
const showCombineTypesSub = ref<Array<any>>([]);
watchEffect(() => {
    showCombineTypesMain.value = JSON.parse(JSON.stringify(combineTypes));
    showCombineTypesSub.value = JSON.parse(JSON.stringify(combineTypes));
});
function mainTypeFilterMethod(value: string) {
    showCombineTypesMain.value = commonFilterMethod(value, combineTypes, 'label');
}
function subTypeFilterMethod(value: string) {
    showCombineTypesSub.value = commonFilterMethod(value, combineTypes, 'label');
}
</script>

<style scoped lang="less">
@import "@/style/Functions.less";
@import "@/style/Cashier/Report.less";
.small {
    .detail-el-date-picker(118px, 32px);
}
.main-tool-left {
    > div {
        flex-shrink: 0;
    }
    > span {
        flex-shrink: 0;
    }
    .solid-button {
        width: 70px;
    }
}
.main-tool-right {
    .button {
        width: 70px;
    }
}
</style>
