import { useVisitedRoutesStore } from "@/store/modules/visitedRoutes"
import router from "@/router"
import { ElNotify } from "@/utils/notify"

export const getUrlAllowParams = () => {
  const allowParams = ["appasid"]
  return allowParams
}
const checkHasCustomUrlParams = (query: any) => {
  let checkHasCustomUrlParams = false
  const allowParams = getUrlAllowParams()
  for (const key in query) {
    if (!allowParams.includes(key)) {
      checkHasCustomUrlParams = true
      break
    }
  }
  return checkHasCustomUrlParams
}
export const tryClearCustomUrlParams = (route: any) => {
  if (!checkHasCustomUrlParams(route.query)) return
  const allowParams = getUrlAllowParams()
  const params = { ...route.query }
  for (const key in params) {
    if (!allowParams.includes(key)) {
      delete params[key]
    }
  }
  // 将剩余的参数拼接到path中
  const fullPath = route.path + (Object.keys(params).length ? `?${new URLSearchParams(params).toString()}` : "")

  useVisitedRoutesStore().replaceCurrentRouter(fullPath)
}

export const getQueryStringByName = (name: string) => {
  const result = location.search.match(new RegExp(new RegExp(`[?&]${name}=([^&#]*)`, "i")))
  if (result === null || result.length < 1) {
    return ""
  }
  return result[1]
}

export const getAppasid = () => {
  return getQueryStringByName("appasid")
}

export const carryAppasidParam = (url: string): string => {
  const separator = url.includes("?") ? "&" : "?"
  const appasid = getAppasid()
  return `${url}${separator}appasid=${appasid}`
}

export const globalWindowOpenPage = (href: string, title: string) => {
  const routerArrayStore = useVisitedRoutesStore()
  if (
    routerArrayStore.routerArray.length >= 20 &&
    !routerArrayStore.routerArray.find((item) => item.path.toLowerCase() === href.toLowerCase().split("?")[0])
  ) {
    ElNotify({ message: "页签达到上限，可右键快速关闭其他页", type: "warning" })
    return
  }

  router.push(carryAppasidParam(href)).then(() => {
    document.title = title + " - 智能报税"
    routerArrayStore.routerArray.find((item) => item.alive)!.title = title
  })
}

export const globalWindowOpen = (href: string) => {
  if (!href.startsWith("http")) {
    href = carryAppasidParam(href)
  }
  if (href.startsWith(window.jAccH5Url)) {
    href = carryAppasidParam(href)
  }
  window.open(href)
}
