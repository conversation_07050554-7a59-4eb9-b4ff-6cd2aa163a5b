<template>
    <!-- 新增收支类别 -->
    <el-dialog
        title="添加收支类别"
        center
        v-model="ieTypeInfo.dispaly"
        width="440px"
        :destroy-on-close="true"
        @closed="resetIETypeInfo"
        class="custom-confirm dialogDrag"
    >
        <div class="add-info-content" :class="isErp ? 'erp' : ''" v-dialogDrag>
            <div class="add-info-line-item required">
                <div class="add-info-title">类别：</div>
                <div class="add-info-field">
                    <el-select v-model="ieTypeInfo.ieType" :teleported="false" @change="changeIETypeSelectList">
                        <el-option :value="10050" label="收入" />
                        <el-option :value="10060" label="支出" />
                    </el-select>
                </div>
            </div>
            <div class="add-info-line-item required">
                <div class="add-info-title">编码：</div>
                <div class="add-info-field">
                    <input
                        type="text"
                        v-model="ieTypeInfo.code"
                        @input="handleAAInput(ieCodeLimitSize, $event, '编码', 'code', changeIETypeData)"
                        @paste="handleAAPaste(ieCodeLimitSize, $event)"
                    />
                </div>
            </div>
            <div class="add-info-line-item required">
                <div class="add-info-title">名称：</div>
                <div class="add-info-field">
                    <input
                        type="text"
                        v-model="ieTypeInfo.name"
                        @input="handleAAInput(ieNameLimitSize, $event, '名称', 'name', changeIETypeData)"
                        @paste="handleAAPaste(ieNameLimitSize, $event)"
                    />
                </div>
            </div>
            <div class="add-info-line-item required">
                <div class="add-info-title">上级类别：</div>
                <div class="add-info-field">
                    <Select  
                        v-model="ieTypeInfo.parentId" 
                        :filterable="true"
                        :filter-method="ietypeFilterMethod"
                    >
                        <!-- <Option key="0" value="0" label="全部类别" /> -->
                        <Option v-for="item in filterIEType" :key="item.subkey" :value="item.subkey" :label="item.value2" />
                    </Select>
                </div>
            </div>
            <div class="add-info-line-item" v-if="cashFlowShow">
                <div class="add-info-title">关联现金流：</div>
                <div class="add-info-field">
                    <Select
                        v-model="ieTypeInfo.cashflow"
                        placeholder="请选择"
                        :fit-input-width="true"
                        :clearable="true"
                        IconClearRight="26px"
                        :filterable="true"
                        :filter-method="cashFlowFilterMethod"
                    >
                        <Option v-for="item in cashFlowShowList" :key="item.value" :label="item.label" :value="item.value" />
                    </Select>
                </div>
            </div>
            <div class="add-info-line-item">
                <div class="add-info-title">智能匹配关键字：</div>
                <div class="add-info-field">
                    <div class="filed-textarea">
                        <el-input v-model="ieTypeInfo.keyword" placeholder=" " type="textarea" :rows="5" resize="none" />
                    </div>
                </div>
            </div>
            <div class="add-info-buttons">
                <a class="button solid-button" @click="handleIETypeSave">保存</a>
                <a class="button ml-10" @click="hideIETypeDialog">取消</a>
            </div>
        </div>
    </el-dialog>
    <!-- 新增项目 / 部门 -->
    <AddAAEntryDialog ref="AddAAEntryDialogRef" :autoAddName="autoAddName" @save="handleAddAssistingAccounting" />
    <!-- 业财新增部门 -->
    <AddAssistingAccountingEntryDialog
        title="新增部门"
        :close-on-save-fail="false"
        ref="addAssistingAccountingEntryDialogRef"
        @save-success="erpAAESaveSuccess"
    ></AddAssistingAccountingEntryDialog>
    <!-- 选择 / 新增往来单位（客户，供应商，职员） -->
    <DialogUnit ref="dialogUnitRef" :trySyncCompany="trySyncCompany" :canAdd="true" @closed="handleUnitClosed" @handle-sure="handleUnitConfirm" />
    <!-- 新增 / 编辑 支付方式 -->
    <el-dialog
        v-model="payMethodInfo.display"
        :title="payMethodInfo.title"
        center
        :width="isErp ? '500px' : '400px'"
        :destroy-on-close="true"
        @closed="resetPayMethodInfo"
        class="custom-confirm dialogDrag"
    >
        <div class="pay-method-content" v-dialogDrag>
            <div class="pay-method-main" :class="isErp ? 'erp' : 'acc'">
                <template v-if="isErp">
                    <div class="row">
                        <span class="pay-label">编码：</span>
                        <el-input style="width: 340px" v-model="payMethodInfo.code" />
                    </div>
                    <div class="row">
                        <span class="pay-label">名称：</span>
                        <el-input 
                            style="width: 340px" 
                            v-model="payMethodInfo.method" 
                            @input="InputMaxLength"
                            @keypress="PressMaxLength" 
                            />
                    </div>
                </template>
                <template v-else>
                    <span class="pay-label">结算方式：</span>
                    <el-input 
                        style="width: 180px" 
                        v-model="payMethodInfo.method" 
                        placeholder="请输入结算方式" 
                        @input="InputMaxLength"
                        @keypress="PressMaxLength" 
                    />
                </template>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleSavePayMethod">{{ isErp ? "保存" : "确定" }}</a>
                <a class="button ml-20" @click="closePayMethodDialog">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, inject, computed, toRef, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import { getNextIECode } from "@/Views/Cashier/IEType/utils";
import { ElConfirm } from "@/util/confirm";
import { ieTypeKey } from "../utils";
import { CashAAType, CashIEType, type IUnitUpdateParams } from "../types";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import AddAAEntryDialog from "./AddAAEntryDialog.vue";
import type { IIETypeItem } from "@/views/Cashier/type";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import DialogUnit from "./DialogUnit.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const isErp = ref(window.isErp);
const accountingStandard = useAccountSetStore().accountSet!.accountingStandard as unknown as AccountStandard;
const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();

const props = defineProps<{
    jType: "1010" | "1020";
    autoAddName: string;
    trySyncCompany: (aaList: Array<{ aaeId: number, aaName: string }>) => void;
}>();
const emit = defineEmits<{
    (event: "save-ietype", type: CashIEType, no: string, name: string): void;
    (event: "save-aa", aaType: CashAAType.Project | CashAAType.Department, code: string, name: string): void;
    (event: "unit-closed"): void;
    (event: "unit-change", params: IUnitUpdateParams | null, aaType: number): void;
    (event: "save-pay-method", name: string): void;
}>();

// 收支类别
const ieTypeInfo = reactive<{
    dispaly: boolean;
    ieType: CashIEType;
    code: string;
    name: string;
    keyword: string;
    parentId: string;
    cashflow: string;
}>({
    dispaly: false,
    ieType: CashIEType.Income,
    code: "",
    name: "",
    keyword: "",
    parentId: "0",
    cashflow: "",
});
const ieCodeLimitSize = isErp.value ? LimitCharacterSize.Default : LimitCharacterSize.categoryCode;
const ieNameLimitSize = isErp.value ? LimitCharacterSize.Default : LimitCharacterSize.Name;

const IETypeList = ref<IIETypeItem[]>([]);
const getIEType = inject(ieTypeKey) as () => Array<IIETypeItem>;
const cashFlowShow = computed(() => {
    const allows = [AccountStandard.LittleCompanyStandard, AccountStandard.CompanyStandard, AccountStandard.FolkComapnyStandard];
    return allows.includes(accountingStandard);
});
function showIETypeDialog() {
    ieTypeInfo.dispaly = true;
    ieTypeInfo.ieType = CashIEType.Income;
    changeIETypeSelectList(ieTypeInfo.ieType);
    ieTypeInfo.name = props.autoAddName.slice(0, ieNameLimitSize);
    if (props.autoAddName.length > ieNameLimitSize) {
        ElNotify({ type: "warning", message: "亲，名称不能超过" + ieNameLimitSize + "个字符!" });
    }
}
function hideIETypeDialog() {
    ieTypeInfo.dispaly = false;
}
function resetIETypeInfo() {
    ieTypeInfo.code = "";
    ieTypeInfo.name = "";
    ieTypeInfo.keyword = "";
    ieTypeInfo.parentId = "0";
    ieTypeInfo.cashflow = "";
    ieTypeInfo.ieType = CashIEType.Income;
}
function changeIETypeSelectList(ieType: CashIEType) {
    ieTypeInfo.parentId = "0";
    setNextIETypeCode(ieType);
    const list = getIEType();
    if (ieType === CashIEType.Income) {
        IETypeList.value = list.filter((item) => item.value2.startsWith("收") && Number(item.num3) === 0);
    } else {
        IETypeList.value = list.filter((item) => item.value2.startsWith("支") && Number(item.num3) === 0);
    }
}
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingList");
const initialCashAAEID =
    accountingStandard === AccountStandard.CompanyStandard || accountingStandard === AccountStandard.FolkComapnyStandard ? 31 : 24;
const cashFlowSelectList = computed(() =>
    assistingAccountingList.value
        .filter((item) => item.aatype === 10007 && item.aaeid !== initialCashAAEID)
        .map((item) => ({ label: item.aaname, value: item.aaeid.toString() }))
);
function checkCanSaveIEType() {
    if ((ieTypeInfo.code + "").trim() === "") {
        ElNotify({ type: "warning", message: "编码不能为空" });
        return false;
    }
    if (window.isErp) {
        const reg = /^[0-9a-zA-Z]{1,64}$/;
        if (!reg.test((ieTypeInfo.code + "").trim())) {
            ElNotify({ type: "warning", message: "亲，编码只能输入三位及以下的数字字母组合" });
            return false;
        }
    } else {
        const reg = /^[0-9a-zA-Z]{1,4}$/;
        if (!reg.test((ieTypeInfo.code + "").trim())) {
            ElNotify({ type: "warning", message: "亲，编码只能输入四位及以下的数字字母组合" });
            return false;
        }
    }
    if (ieTypeInfo.name.trim() === "") {
        ElNotify({ type: "warning", message: "名称不能为空" });
        return false;
    }
    return true;
}
const handleIETypeSave = () => {
    if (!checkCanSaveIEType()) return;
    let requestParams: any = {};
    const params: any = {
        ie_type: ieTypeInfo.ieType,
        ie_no: ieTypeInfo.code,
        ie_name: ieTypeInfo.name,
        ie_keyword: ieTypeInfo.keyword,
        parentID: ieTypeInfo.parentId,
        cashAAEID: ~~ieTypeInfo.cashflow,
    };
    if (!isErp.value) {
        requestParams = {
            url: "/api/IEType",
            method: "post",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            data: params,
        };
    } else {
        requestParams = {
            url: "/api/BaseDataForErp/SaveRpType",
            method: "post",
            data: params,
        };
    }
    const ieTypeItem = IETypeList.value.find((item) => item.subkey === ieTypeInfo.parentId);
    let hasData = false;
    request({
        url: "/api/IEType/PreCheckAdd?parentIeId=" + ieTypeInfo.parentId,
        method: "post",
    }).then((res: any) => {
        if (res.data === "ALERT") {
            ieTypeInfo.dispaly = false;
            ElConfirm(
                `上级收支类别已使用，保存后，将同时新增同名下级类别 "${ieTypeItem?.value2.slice(2) + "1"}", 代替您确定继续新增吗？`
            ).then((r: boolean) => {
                if (r) {
                    hasData = true;
                    SaveRequest();
                }
            });
        } else if (res.data === "SUCCESS") {
            SaveRequest();
        } else if (res.data === "DisableIEType") {
            ElNotify({ type: "warning", message: "上级收支类别已被禁用，请刷新页面重试！" });
        } else {
            ElNotify({ type: "warning", message: "亲，保存失败了，请刷新页面重试！" });
        }
    });
    function SaveRequest() {
        request(requestParams)
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: res.msg || "亲，保存失败了，请联系侧边栏客服" });
                    return;
                }
                if (!isErp.value) {
                    if (res.data == "Success") {
                        ElNotify({ type: "success", message: "保存成功！" });
                        window.dispatchEvent(
                            new CustomEvent("reloadIETypeList", {
                                detail: { type: ieTypeInfo.ieType.toString(), journalType: props.jType },
                            })
                        );
                        hideIETypeDialog();
                        resetIETypeInfo();
                        if (hasData) {
                            emit("save-ietype", params.ie_type, String(Number(params.ie_no) + 1), params.ie_name);
                        } else {
                            emit("save-ietype", params.ie_type, params.ie_no, params.ie_name);
                        }
                    } else if (res.data == "NO") {
                        ElNotify({ type: "warning", message: "亲，编码重复了" });
                    } else if (res.data == "Name") {
                        ElNotify({ type: "warning", message: "亲，类别名称重复了" });
                    } else {
                        ElNotify({ type: "warning", message: "亲，保存失败了，请联系侧边栏客服" });
                    }
                } else {
                    if (res.msg === "") {
                        ElNotify({ type: "success", message: "保存成功！" });
                        hideIETypeDialog();
                        resetIETypeInfo();
                        emit("save-ietype", params.ie_type, params.ie_no, params.ie_name);
                    } else {
                        ElNotify({ type: "warning", message: res.msg });
                    }
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "亲，保存失败了，请联系侧边栏客服" });
            });
    }
};
async function setNextIETypeCode(value: CashIEType.Income | CashIEType.Expenditure) {
    await getNextIECode(value.toString()).then((res: string) => {
        ieTypeInfo.code = res;
    });
}
function changeIETypeData(key: string, val: string) {
    if (key === "code") {
        ieTypeInfo.code = val;
    } else if (key === "name") {
        ieTypeInfo.name = val;
    }
}

// 往来单位
const dialogUnitRef = ref<InstanceType<typeof DialogUnit>>();
function handleUnitClosed() {
    emit("unit-closed");
}
function handleUnitConfirm(params: IUnitUpdateParams | null, aaType: number) {
    emit("unit-change", params, aaType);
}
let erpAAESaveSuccess = (data: any) => {
    console.log(data);
};

// 支付方式
const payMethodInfo = reactive({
    display: false,
    method: "",
    id: "",
    title: "",
    code: "",
});
function InputMaxLength(value: string) {
    if (value.length > 30) {
        ElNotify({ type: "warning", message: "亲，结算方式不能超过30个字符！" });
        payMethodInfo.method = value.slice(0, 30);
        return;
    }
}
function PressMaxLength(e: any) {
    const { value, selectionStart,selectionEnd } = e.target as HTMLInputElement;
    if (value.length > 30 - 1) {
        if (selectionStart === selectionEnd) {
            e.preventDefault();
            ElNotify({ type: "warning", message: "亲，结算方式不能超过30个字符！" });
        }
    }
}
let isSaving = false;
function handleSavePayMethod() {
    if (isSaving) return;
    if (!payMethodInfo.method.trim()) {
        ElNotify({ type: "warning", message: "结算方式不能为空" });
        return;
    }
    isSaving = true;
    const url = props.jType === "1010" ? "/api/Payment/Cash" : "/api/Payment/Deposit";
    const method = payMethodInfo.id ? "put" : "post";
    const params: any = { payment_name: payMethodInfo.method };
    if (method === "put") params.payment_id = payMethodInfo.id;
    if (isErp.value) params.payment_no = payMethodInfo.code;
    request({ url, method, data: params })
        .then((res: IResponseModel<boolean>) => {
            isSaving = false;
            if (res.state === 1000 && res.data) {
                ElNotify({ type: "success", message: "亲，保存成功啦" });
                payMethodInfo.display = false;
                emit("save-pay-method", params.payment_name);
            } else {
                ElNotify({ type: "warning", message: res.msg || "亲，保存失败了，请联系侧边栏客服" });
            }
        })
        .catch(() => {
            isSaving = false;
            ElNotify({ type: "warning", message: "亲，保存失败了，请联系侧边栏客服" });
        });
}
function closePayMethodDialog() {
    payMethodInfo.display = false;
}
function resetPayMethodInfo() {
    payMethodInfo.method = "";
    payMethodInfo.id = "";
    payMethodInfo.title = "";
    payMethodInfo.code = "";
}
async function getNextPaymentCode() {
    await request({ url: "/api/Payment/NextPayMethodNo", method: "get" }).then((res: IResponseModel<string>) => {
        res.state === 1000 && (payMethodInfo.code = res.data);
    });
}

let doing = false;
const AddAAEntryDialogRef = ref<InstanceType<typeof AddAAEntryDialog>>();
async function openAADialog(aatype: CashAAType, payMethodId = "", payMethodName = "") {
    if (doing) return;
    if (isErp.value && aatype === CashAAType.Department) {
        addAssistingAccountingEntryDialogRef.value?.showAADialog(aatype, props.autoAddName);
        erpAAESaveSuccess = (data: any) => {
            emit("save-aa", aatype, data.departmentCode, data.departmentName);
        };
        return;
    }
    if (aatype === CashAAType.IEType) {
        showIETypeDialog();
        return;
    }
    if (aatype === CashAAType.Unit) {
        dialogUnitRef.value?.changeDialogShow(true);
        dialogUnitRef.value?.handleInit();
        return;
    }
    if (aatype === CashAAType.Department || aatype === CashAAType.Project) {
        doing = true;
        AddAAEntryDialogRef.value?.showAADialog(aatype);
        doing = false;
        return;
    }
    if (aatype === CashAAType.PayMethod) {
        doing = true;
        payMethodInfo.display = true;
        payMethodInfo.method = payMethodName ? payMethodName : props.autoAddName.slice(0, 30);
        payMethodInfo.id = payMethodId;
        payMethodInfo.title = payMethodId ? "编辑结算方式" : "新增结算方式";
        if (props.autoAddName.length > 30) {
            ElNotify({ type: "warning", message: "亲，结算方式不能超过30个字符！" });
        }
        isErp.value && !payMethodId && (await getNextPaymentCode());
        doing = false;
        return;
    }
}
const handleAddAssistingAccounting = (aaType: number, code: string, name: string) => {
    emit("save-aa", aaType, code, name);
};
function checkDialogUnitDisplay() {
    return !!dialogUnitRef.value?.checkDialogUnitDisplay();
}
defineExpose({ openAADialog, checkDialogUnitDisplay });

//下拉组件拼音首字母搜索
const IETypeListAll = ref<Array<IIETypeItem>>([]);
const filterIEType = ref<Array<IIETypeItem>>([]);
const cashFlowShowList = ref<any[]>([]);
watchEffect(() => { 
    IETypeListAll.value = JSON.parse(JSON.stringify(IETypeList.value)); 
    IETypeListAll.value.unshift({ 
        subkey: "0", 
        value1: "0", 
        value2: "全部类别",
        value3: "",
        subsubkey: "",
        haschild: 0,
        num3: "0",
    })
    filterIEType.value = JSON.parse(JSON.stringify(IETypeListAll.value)); 
    cashFlowShowList.value = JSON.parse(JSON.stringify(cashFlowSelectList.value)); 
});
function ietypeFilterMethod(value: string) {
    filterIEType.value = commonFilterMethod(value, IETypeListAll.value, 'value2');
}
function cashFlowFilterMethod(value: string) {
    cashFlowShowList.value = commonFilterMethod(value, cashFlowSelectList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.add-info-content {
    padding: 20px 0;
    text-align: center;
    .add-info-line-item {
        margin-right: 40px;
        &.required {
            .add-info-title:before {
                content: "*";
                color: var(--red);
            }
        }
        & + .add-info-line-item {
            margin-top: 16px;
        }
        .add-info-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            display: inline-block;
            vertical-align: middle;
            width: 120px;
            text-align: right;
        }
        .add-info-field {
            display: inline-block;
            vertical-align: middle;
            text-align: left;
            .detail-el-select(200px, 32px);
            & > input {
                .detail-original-input(200px, 32px);
            }
        }
        .filed-textarea {
            width: 198px;
            :deep(.el-textarea) {
                .detail-el-textarea-scroll-thumb();
            }
        }
    }
    .add-info-buttons {
        margin-top: 24px;
    }

    &.erp {
        padding-bottom: 0px;
        .add-info-buttons {
            height: 30px;
            display: block;
            box-sizing: content-box;
            border-top: 1px solid var(--border-color);
            text-align: right;
            padding: 16px 20px 16px 0;
            & a {
                text-align: center;
                display: block;
                float: right;
                width: 70px;
                min-width: 70px;
                height: 30px;
                border-radius: 2px;
                margin-left: 20px;
                line-height: 30px;
            }
            & button {
                box-sizing: border-box;
                padding: 0 12px;
                font-size: var(--font-size);
            }
        }
    }
}
.pay-method-content {
    .pay-method-main {
        :deep(.el-input) {
            height: 28px;
        }
        &.acc {
            padding: 40px 70px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        &.erp {
            padding: 20px 32px;
            .row {
                padding: 8px 24px;
                height: 30px;
                line-height: 20px;
                text-align: left;
                display: flex;
                white-space: nowrap;
                align-items: center;
                .pay-label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
