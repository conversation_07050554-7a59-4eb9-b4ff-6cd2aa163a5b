
export interface ITableDataItem {
    asid: number;
    expand: number;
    fold: number;
    lineID: number;
    lineNumber: number;
    lineType: number;
    note: string;
    proName: string;
    statementId: number;
    colums: IColum[];
    beforeyearNoteArr?: string[];
    children?:ITableDataItem[];
    indentation?:boolean;
}

export interface IColum {
    assitEntry: IAssitEntry;
    data: {
        Amount: number;
        InitialAmount: number;
        beforeyearInitialAmount?: number;
    };
}

export interface IAssitEntry {
    aaname: string;
    aanum: string;
    aatype: number;
    aaeid: number;
    asid: number;
    createdBy: number;
    createdDate: string;
    preName: string;
    status: number;
    uscc: string;
    value01: any;
}
