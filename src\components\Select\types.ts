import type { Placement } from "element-plus"

export interface SelectProps {
  modelValue: any
  teleported?: boolean
  placeholder?: string
  fitInputWidth?: boolean
  filterable?: boolean
  allowCreate?: boolean
  clearable?: boolean
  automaticDropdown?: boolean
  disabled?: boolean
  placement?: string
  tooltipPlacement?: Placement
  class?: string
  popperClass?: string
  style?: Record<string, any>
  multiple?: boolean
  remote?: boolean
  noDataText?: string
  noMatchText?: string
  iconClearRight?: string
  defaultFirstOption?: boolean
  popperOptions?: Record<string, any>
  filterMethod?: (value: string) => void
}

export interface SelectEmits {
  (e: "update:modelValue", value: any): void
  (e: "change", value: any): void
  (e: "focus", event: FocusEvent): void
  (e: "blur", event: FocusEvent): void
  (e: "visible-change", visible: boolean): void
  (e: "clear"): void
  (e: "keyupEnter"): void
  (e: "keydownEnter"): void
  (e: "keyup", event: KeyboardEvent): void
  (e: "input", value: string): void
  (e: "click", event: Event): void
}
