import type { RendererElement, RendererNode, VNode } from "vue";

export type FilterOrder = "number" | "code" | "date" | "text" | "multiSelect" | "";

export interface IColumnProps {
    label?: string;
    prop?: string;
    width?: number | string;
    minWidth?: number | string;
    align?: "left" | "center" | "right";
    headerAlign?: "left" | "center" | "right";
    slot?: string;
    type?: string;
    fixed?: "left" | "right";
    useHtml?: boolean;
    reserveSelection?: boolean;
    children?: Array<IColumnProps>;
    className?: string;
    col?: number; // 列数
    resizable?: boolean;
    filterOrder?: FilterOrder; // 表头筛选
    headerSort?: boolean; // 表头排序
    alias?: string; // 别名
    defaultNotSelectAll?: boolean; // 默认全选
    formatter?: (
        row: any,
        column: any,
        cellValue: any,
        index: number
    ) =>
        | string
        | VNode<
              RendererNode,
              RendererElement,
              {
                  [key: string]: any;
              }
          >;
}
