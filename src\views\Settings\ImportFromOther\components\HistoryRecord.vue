<template>
    <div class="history-record">
        <div class="header-tool-bar">
            <div class="header-tool-bar-left">
                导账日期：
                <DatePicker
                    v-model:startPid="searchInfo.startDate"
                    v-model:endPid="searchInfo.endDate"
                    :disabled-date-start="disabledDateStart"
                    :disabled-date-end="disabledDateEnd"
                    :clearable="true"
                />
                <el-select v-model="searchInfo.importType" placeholder="导账方式" class="ml-20" style="width: 100px">
                    <el-option label="全部" :value="-1" />
                    <el-option label="科目余额表导账" :value="2" />
                    <el-option label="在线导账" :value="3" />
                </el-select>
                <el-select v-model="searchInfo.importStatus" placeholder="导账状态" class="ml-20" style="width: 100px">
                    <el-option label="全部" :value="-1" />
                    <el-option label="导账成功" :value="1" />
                    <el-option label="部分导入" :value="2" />
                    <el-option label="导账失败" :value="3" />
                </el-select>
                <a class="button solid-button ml-20" @click="handleSearch">查询</a>
                <a class="reset-btn ml-5" @click="handleResetSearch"></a>
            </div>
            <div class="header-tool-bar-right">
                <div class="search-box">
                    <input
                        v-model="searchInfo.searchKeyWord"
                        type="text"
                        class="search-ipt"
                        placeholder="请输入关键词搜索"
                        @keyup.enter="handleSearch"
                    />
                    <div @click="handleSearch" class="search-icon"></div>
                </div>
            </div>
        </div>
        <div class="history-record-table">
            <Table
                ref="accountSetTable"
                :loading="loading"
                :width="1100"
                :data="props.historyData"
                :columns="tableColumns"
                :scrollbarShow="true"
                :page-is-show="true"
                :page-sizes="paginationData.pageSizes"
                :page-size="paginationData.pageSize"
                :total="props.historyTotal"
                :currentPage="paginationData.currentPage"
                :empty-text="emptyText"
                :showOverflowTooltip="true"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                @refresh="handleRerefresh"
                :tableName="setModule"
            >
                <template #status>
                    <el-table-column label="状态" min-width="210" align="left" headerAlign="center" :show-overflow-tooltip="false" :resizable="false">
                        <template #default="scope">
                            <div v-if="scope.row.status === 0" class="status-box">
                                <img src="@/assets/Settings/loading-icon.png" />导账中...
                            </div>
                            <div v-if="scope.row.status === 1" class="status-box">
                                <img src="@/assets/Settings/correct-icon.png" />
                                导账成功
                            </div>
                            <div v-if="scope.row.status === 2" class="status-box">
                                <img src="@/assets/Settings/warnning-yellow.png" />
                                部分导入
                                <a
                                    v-if="scope.row.error_msg !== ''"
                                    class="link pop-trigger"
                                    @click.prevent="showErrorMsg(scope.row)"
                                >
                                    点击查看原因
                                </a>
                            </div>
                            <div v-if="scope.row.status === 3" class="status-box">
                                <img src="@/assets/Settings/warn-red.png" />
                                导账失败
                                <el-popover placement="right" :width="170" trigger="hover" popper-class="'import-status-list-popper'">
                                    <template #reference>
                                        <a class="link pop-trigger">查看原因</a>
                                    </template>
                                    <div class="pop-content">
                                        {{ getErrorMsg(scope.row.error_msg) }}
                                        <div class="pop-tips" style="color: #fa7c27; margin-top: 10px">可以联系客服帮您看看哦~</div>
                                    </div>
                                </el-popover>
                            </div>
                        </template>
                    </el-table-column>
                </template>
            </Table>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click.prevent="() => emits('changeImportMode', lastMode)">返回</a>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import dayjs from "dayjs";
import DatePicker from "@/components/DatePicker/index.vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ImportModeType, IHistoryListItem, IErrorMsg, IHistoryParams, IErrorReasonParams } from "../types";
import { usePagination } from "@/hooks/usePagination";
import { getErrorMsg } from "../utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useAccountSetStore } from "@/store/modules/accountSet";

const setModule = "ImportHistoryRecord";
const props = withDefaults(
    defineProps<{
        historyData: IHistoryListItem[];
        historyTotal: number;
        loading: boolean;
        importMode: ImportModeType;
    }>(),
    {
        historyData: () => [],
        historyTotal: 0,
        loading: false,
    }
);

const emits = defineEmits<{
    (e: "changeImportMode", val: ImportModeType, paramsData?: IErrorReasonParams): void;
    (e: "getHistory", params: IHistoryParams): boolean;
}>();
const currentAsId = useAccountSetStore().accountSet?.asId || 0;
const lastMode = ref<ImportModeType>("init");
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

// 导账方式列表
const importTypeList: Record<any, string> = {
    0: "未知",
    1: "导账工具",
    2: "科目余额表导入",
    3: "在线导账",
};

// 表格数据
const tableColumns: IColumnProps[] = [
    {
        label: "软件名",
        prop: "softCompanyName",
        minWidth: 160,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "softCompanyName")
    },
    {
        label: "账套名称",
        prop: "as_name",
        minWidth: 350,
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "as_name")
    },
    {
        label: "导账方式",
        prop: "importSourceType",
        minWidth: 170,
        align: "left",
        headerAlign: "left",
        formatter: (row, column, value) => {
            return importTypeList[value];
        },
        width: getColumnWidth(setModule, "importSourceType")
    },
    {
        label: "导账日期",
        prop: "created_date",
        minWidth: 120,
        align: "center",
        headerAlign: "center",
        formatter: (row, column, value) => {
            return value.replace("T", " ");
        },
        width: getColumnWidth(setModule, "created_date")
    },
    {
        slot: "status",
    },
];

const emptyText = ref("暂无数据");

// 搜索信息
const searchInfo = reactive<IHistoryParams>({
    startDate: "",
    endDate: "",
    // 导账来源类型2:科目余额表;3:在线导账
    importType: -1,
    // 状态0:正在导入；1:导入成功；2：部分导入；3：导入失败
    importStatus: -1,
    searchKeyWord: "",
    currentPage: paginationData.currentPage,
    pageSize: paginationData.pageSize,
});

// 禁用日期
function disabledDateStart(time: Date) {
    if (!searchInfo.endDate) return false;
    const endDate = dayjs(searchInfo.endDate).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    if (!searchInfo.startDate) return false;
    const startDate = dayjs(searchInfo.startDate).valueOf();
    return time.getTime() < startDate;
}
  // 查看错误信息
  const showErrorMsg = (row: IHistoryListItem) => {
    const { error_msg, appAsId, as_name } = row;
    const errMsgData = {
      appAsId,
      asName: as_name,
      errorMsgList: JSON.parse(error_msg),
    };
    emits("changeImportMode", "statusError", errMsgData);
  };


// 重置查询条件
const handleReset = () => {
    searchInfo.startDate = "";
    searchInfo.endDate = "";
    searchInfo.importType = -1;
    searchInfo.importStatus = -1;
    searchInfo.searchKeyWord = "";
    paginationData.currentPage = 1;
};

const accountSetTable = ref();

// 查询历史记录
const getTableData = () => {
    emits("getHistory", searchInfo);

    // 回到顶部
    const routerContainer = document.querySelector(".router-container");
    routerContainer!.scrollTo(0, 0);
    accountSetTable.value.tableScrollTo(0, 0);
};

// 手动搜索
const handleSearch = () => {
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
    } else {
        getTableData();
    }
};

// 重置并搜索
const handleResetSearch = async () => {
    await handleReset();
    getTableData();
};

// 需要刷新
const needRefresh = computed(
    () => props.importMode === "historyRecord" && props.historyData.length && props.historyData.some((item) => item.status === 0)
);

let timer: number;

watch(
    () => needRefresh.value,
    (val) => {
        if (val) {
            // 当前页有未完成的每隔20s刷新一次
            timer = setInterval(() => {
                emits("getHistory", searchInfo);
            }, 20000);
        } else {
            clearInterval(timer);
        }
    }
);

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    searchInfo.currentPage = paginationData.currentPage;
    searchInfo.pageSize = paginationData.pageSize;
    getTableData();
});

watch(
    () => props.importMode,
    (val, oldVal) => {
        if (val === "historyRecord" && oldVal !== "statusError") {
            lastMode.value = oldVal;
            handleResetSearch();
        }
    }
);

onMounted(() => {
    getTableData();
});
</script>
<script lang="ts"></script>
<style lang="less" scoped>
.history-record {
    width: 1100px;
    padding-top: 24px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .header-tool-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 32px;
        margin-bottom: 20px;
        .header-tool-bar-left {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .reset-btn {
                display: inline-block;
                height: 20px;
                width: 32px;
                background: url(@/assets/Erp/page-refresh.png) no-repeat;
                background-size: 20px 20px;
                cursor: pointer;
            }
        }
        .header-tool-bar-right {
            .search-box {
                position: relative;
                .search-ipt {
                    width: 200px;
                    height: 32px;
                    line-height: 28px;
                    padding: 0 16px;
                    background: #ffffff;
                    border-radius: 2px;
                    border: 1px solid #e6e6e6;
                    box-sizing: border-box;
                }
                .search-icon {
                    position: absolute;
                    top: 6px;
                    right: 12px;
                    content: "";
                    width: 15px;
                    height: 16px;
                    background: url(@/assets/Settings/search-icon.png) no-repeat 0 0;
                    background-size: 15px 16px;
                }
            }
        }
    }
    .status-box {
        display: flex;
        align-items: center;
        img {
            width: 18px;
            margin: 5px;
        }
        .pop-trigger {
            margin-left: 5px;
        }
    }
    .history-record-table {
        flex: 1;
        min-height: 0;
    }
}
</style>
