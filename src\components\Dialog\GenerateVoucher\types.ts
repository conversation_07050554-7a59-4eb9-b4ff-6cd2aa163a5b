import type { VoucherTemplateLogicEnum } from "@/api/voucherTemplate";
export interface IVoucherTempalteLine {
    asubId: string;
    lockAsub?: boolean | undefined;
    direction: number;
    logicEnum?: VoucherTemplateLogicEnum;
    valueType?: number;
    valueId?: string;
    index?: number;
}

export interface IVoucherTemplateModel {
    vgId: number;
    vtId: number;
    voucherTemplateLines: Array<IVoucherTempalteLine>;
}
