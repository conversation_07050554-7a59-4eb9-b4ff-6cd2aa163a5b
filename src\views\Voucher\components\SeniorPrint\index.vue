<template>
    <div class="seniorPrint content" :class="{ erpSeniorPrint: isErp }">
        <div class="print-top">
            <div class="print-tool-left">
                <a class="solid-button mr-10" @click="saveSetting(0, 1)">保存</a>
                <a class="button mr-10" @click="preview">预览</a>
                <a class="button mr-10" @click="resetSetting">重置</a>
                <a class="button mr-10" style="width: 120px" @click="syncDialogShow = true">同步到其他账套</a>
                <a class="button mr-20" style="width: 134px" @click="setCover">设置封面打印样式</a>
                <div class="video-guide mr-20" @click="helpManual">
                    <img v-if="!isErp" src="@/assets/Icons/acc-help-book.png" alt="视频" style="height: 16px; margin-right: 5px" />
                    <img v-if="isErp" src="@/assets/Icons/help-book.png" alt="视频" style="height: 16px; margin-right: 5px" />
                    <a>帮助手册 </a>
                </div>
                <div class="video-guide mr-20" @click="helpVideo">
                    <img v-if="!isErp" src="@/assets/Icons/video.png" alt="视频" style="height: 16px; margin-right: 5px" />
                    <img v-if="isErp" src="@/assets/Icons/video-blue.png" alt="视频" style="height: 16px; margin-right: 5px" />
                    <a> 操作视频 </a>
                </div>
                <div class="video-guide" @click="updateTipShow = true">
                    <img v-if="!isErp" src="@/assets/Icons/setting-service.png" alt="视频" style="height: 16px; margin-right: 5px" />
                    <img v-if="isErp" src="@/assets/Icons/setting-service-blue.png" alt="视频" style="height: 16px; margin-right: 5px" />
                    <a>功能引导图 </a>
                </div>
            </div>
            <div class="zoom-btn">
                <div class="zoom-icon" style="padding-top: 4px">
                    <el-icon @click="subZoom" class="el-icon-minus"><Minus /></el-icon>
                    <el-icon @click="addZoom" class="el-icon-plus"><Plus /></el-icon>
                </div>

                <el-select v-model="zoom" style="width: 100px; height: 28px; line-height: 28px">
                    <el-option v-for="item in zoomList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </div>
            <div class="print-tool-right">
                <div
                    v-if="!isLemonClient()"
                    :class="['screen-btn', fullScreen ? (isErp ? 'full-screen erp' : 'full-screen') : 'exit-full-screen']"
                    @click="fullScreen = !fullScreen"
                >
                    <span> {{ !fullScreen ? "全屏" : "退出全屏" }}</span>
                </div>
            </div>
        </div>
        <div class="print-content" style="height: calc(100%-120px)">
            <div class="arrowLeftNav" v-show="leftNavShow" @click="() => (leftNavShow = !leftNavShow)"></div>
            <div class="expandLeftNav" v-show="!leftNavShow" @click="() => (leftNavShow = !leftNavShow)"></div>
            <div class="arrowRightNav" v-show="rightNavShow" @click="() => (rightNavShow = !rightNavShow)"></div>
            <div class="expandRightNav" v-show="!rightNavShow" @click="() => (rightNavShow = !rightNavShow)"></div>
            <div class="print-left" v-show="leftNavShow">
                <div class="print-pageOption">
                    <div class="nav-name">打印选择</div>
                    <div class="print-pageOption-inner">
                        <div class="page-item">
                            <span>打印纸张：</span>
                            <el-select style="width: 140px" v-model="printInfo.printType" :teleported="false" @change="changePagerSize">
                                <el-option v-for="item in printTypeList" :value="item.value" :label="item.label" :key="item.value" />
                            </el-select>
                            <el-tooltip popper-class="el-option-tool-tip" effect="light" placement="right-start">
                                <template #content>
                                    <div style="cursor: pointer">
                                        凭证封面的纸张会与凭证纸张保持一致，当切换纸张时，为保证封面的打印效果，可点击<span
                                            style="color: var(--link-color)"
                                            @click="setCover"
                                            >设置封面打印样式</span
                                        >去调整封面样式哦
                                    </div></template
                                >
                                <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px; margin-left: 3px" />
                            </el-tooltip>
                        </div>
                        <div class="page-item" style="padding-top: 8px">
                            <span>打印方向：</span
                            ><el-radio-group v-model="printInfo.direction" :disabled="directionDisabled" @change="changePagerDirection">
                                <el-radio label="Z">纵向</el-radio>
                                <el-radio label="H">横向</el-radio>
                            </el-radio-group>
                            <el-tooltip popper-class="el-option-tool-tip wider" effect="light" placement="right-start">
                                <template #content>
                                    <div style="cursor: pointer">
                                        纸张为B5、A5、A4宽12*21cm、发票版14*24cm且方向为纵向时，为保证打印效果，右侧模板编辑区仍为横向，但实际打印时仍按纵向打印，您可以预览查看效果哦~
                                    </div></template
                                >
                                <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px; margin-left: 6px" />
                            </el-tooltip>
                        </div>
                    </div>
                </div>
                <div class="nav-tool">
                    <div class="nav-name">工具</div>
                    <div class="tool-list">
                        <div class="tool text-icon" @dragend="navSetting('text', $event)" @dblclick="navSetting('text')" draggable="true">
                            <i></i>
                            <div class="tool-name">文字</div>
                        </div>
                        <div class="tool img-icon" @dragend="addImg($event)" @dblclick="addImg()" draggable="true">
                            <i></i>
                            <div class="tool-name">图片</div>
                            <input
                                type="file"
                                @change="handleFileSelect"
                                id="selfSealInput"
                                multiple="multiple"
                                accept="image/png, image/jpeg, image/jpg, image/bmp"
                                style="display: none"
                            />
                        </div>
                        <div
                            class="tool square-icon"
                            @dragend="navSetting('rectangle', $event)"
                            @dblclick="navSetting('rectangle')"
                            draggable="true"
                        >
                            <i></i>
                            <div class="tool-name">矩形</div>
                        </div>
                        <div class="tool line-icon" @dragend="navSetting('line', $event)" @dblclick="navSetting('line')" draggable="true">
                            <i></i>
                            <div class="tool-name">线条</div>
                        </div>
                    </div>
                </div>
                <div class="nav-table">
                    <el-scrollbar :always="true">
                        <div class="nav-table-collapse">
                            <el-collapse accordion v-model="collapseIndex">
                                <el-collapse-item
                                    v-for="moudleItem in seniorModuleList"
                                    :key="moudleItem.order"
                                    :title="moudleItem.moudleName"
                                    :name="moudleItem.order"
                                >
                                    <template #title>
                                        <div class="nav-title">
                                            {{ moudleItem.moudleName
                                            }}<el-icon v-if="moudleItem.moudleName === '表体信息（中）'" @click="bodySet($event)"
                                                ><Setting
                                            /></el-icon>
                                        </div>
                                    </template>
                                    <div
                                        v-for="btnItem in moudleItem.seniorFieldList"
                                        :key="btnItem.fieldName"
                                        :class="{ 'nav-btn': true, 'is-selected': btnItem.checked, isErp: isErp }"
                                        @click="changeSeniorMoudle(btnItem, moudleItem.order)"
                                    >
                                        {{ btnItem.order === 0 ? "凭证字" : btnItem.fieldName }}
                                    </div>
                                </el-collapse-item>
                                <el-collapse-item title="打印设置" :name="5">
                                    <template #title>
                                        <div class="nav-title">打印设置</div>
                                    </template>
                                    <template #default>
                                        <h6>页边距</h6>
                                        <div
                                            class="flex-center"
                                            :class="{ isErp: isErp }"
                                            style="flex-wrap: wrap; justify-content: space-between"
                                        >
                                            <div class="page-margin">
                                                <span class="page-direction">上</span>
                                                <el-input-number
                                                    v-model="printInfo.marginTop"
                                                    :min="0"
                                                    :max="maxTBMargin - minBottomMargin"
                                                    controls-position="right"
                                                    @change="
                                                        handleMarginTop(printInfo.marginTop);
                                                        addHistory();
                                                    "
                                                    @keyup="handleMargin($event, 'top')"
                                                />
                                                mm
                                            </div>
                                            <div class="page-margin">
                                                <span class="page-direction">下</span>
                                                <el-input-number
                                                    v-model="printInfo.marginBottom"
                                                    :disabled="true"
                                                    :min="minBottomMargin"
                                                    :max="maxTBMargin"
                                                    controls-position="right"
                                                />
                                                mm
                                            </div>
                                            <div class="page-margin">
                                                <span class="page-direction">左</span>
                                                <el-input-number
                                                    v-model="printInfo.marginLeft"
                                                    :min="0"
                                                    :max="35"
                                                    controls-position="right"
                                                    @change="
                                                        handleMarginLeft(printInfo.marginLeft);
                                                        addHistory();
                                                    "
                                                    @keyup="handleMargin($event, 'left')"
                                                />
                                                mm
                                            </div>
                                            <div class="page-margin">
                                                <span class="page-direction">右</span>
                                                <el-input-number
                                                    v-model="printInfo.marginRight"
                                                    :disabled="true"
                                                    :min="0"
                                                    :max="35"
                                                    controls-position="right"
                                                />
                                                mm
                                            </div>
                                        </div>
                                        <h6 style="margin-top: 4px">打印设置</h6>
                                        <div>
                                            <el-checkbox
                                                v-model="printInfo.isPrintFileNum"
                                                label="无附件时打印“0”"
                                                @change="addHistory"
                                            /><br />
                                            <el-checkbox
                                                v-model="printInfo.isShowSummaryPrint"
                                                label="汇总打印"
                                                @change="addHistory"
                                            /><br />
                                            <div class="sub-level" v-show="printInfo.isShowSummaryPrint">
                                                <span style="margin-right: 4px">将科目汇总到</span>
                                                <el-select
                                                    v-model="printInfo.summaryAsubLevel"
                                                    :teleported="false"
                                                    style="width: 50px"
                                                    @change="addHistory"
                                                >
                                                    <el-option :value="1" label="1" />
                                                    <el-option :value="2" label="2" />
                                                    <el-option :value="3" label="3" />
                                                    <el-option :value="4" label="4" />
                                                </el-select>
                                                <span style="margin-left: 4px">级科目</span>
                                                <el-checkbox
                                                    v-model="printInfo.isSummaryByDirection"
                                                    label="按借贷方向分别汇总"
                                                    @change="addHistory"
                                                />
                                                <el-checkbox
                                                    v-model="printInfo.isHideZero"
                                                    label="汇总金额为零不显示"
                                                    @change="addHistory"
                                                />
                                                <el-checkbox
                                                    v-model="printInfo.isShowAssitItem"
                                                    label="显示核算项目"
                                                    @change="addHistory"
                                                />
                                            </div>
                                            <el-checkbox
                                                v-model="printInfo.isPrintFrontCover"
                                                label="同时打印封面"
                                                @change="addHistory"
                                            /><br />
                                            <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start">
                                                <template #content>
                                                    <div>仅支持打印jpg、png、pdf格式文件，其他文件将跳过</div>
                                                </template>
                                                <template #default>
                                                    <div style="width: 100px">
                                                        <el-checkbox
                                                            style="margin-right: 4px"
                                                            v-model="printInfo.printFile"
                                                            label="打印附件"
                                                            @change="addHistory"
                                                        />
                                                        <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px" />
                                                    </div>
                                                </template>
                                            </el-tooltip>
                                            <div class="sub-level" v-show="printInfo.printFile">
                                                <el-checkbox
                                                    v-model="printInfo.simultaneouslyPrintFileList"
                                                    label="同时打印附件清单"
                                                    @change="addHistory"
                                                /><br />
                                                <el-checkbox
                                                    v-model="printInfo.continuousFiles"
                                                    label="附件连续打印"
                                                    @change="addHistory"
                                                />
                                            </div>
                                            <el-checkbox
                                                v-model="printInfo.isShowPrintDate"
                                                label="显示打印日期"
                                                @change="limitMarginBottom"
                                            />
                                            <el-checkbox
                                                v-model="printInfo.isShowPageNumber"
                                                label="显示页码"
                                                @change="limitMarginBottom"
                                            />
                                            <el-checkbox
                                                v-model="printInfo.isShowSplitLine"
                                                label="显示分割线"
                                                v-show="[1, 2].includes(printInfo.printType)"
                                                @change="addHistory"
                                            />
                                            <!-- <el-checkbox v-model="printInfo.isShowPrintDate" label="连续打印" /> -->
                                            <el-tooltip popper-class="print-voucher-tip" effect="light" placement="right-start">
                                                <template #content>
                                                    <div>勾选即同一个凭证号会连续打印，不同凭证号则另起一页打印</div>
                                                </template>
                                                <template #default>
                                                    <div style="width: 154px">
                                                        <el-checkbox
                                                            style="margin-right: 4px"
                                                            v-model="printInfo.isSplitPageByVNum"
                                                            label="按凭证号分页打印"
                                                            @change="addHistory"
                                                        />
                                                        <img class="img-question" src="@/assets/Icons/question.png" style="height: 16px" />
                                                    </div>
                                                </template>
                                            </el-tooltip>
                                            <el-checkbox v-model="printInfo.isHideEmpty" label="空行不打印" @change="addHistory" />
                                            <el-checkbox
                                                v-model="printInfo.isLineHeightAdaptive"
                                                label="表格行高自适应"
                                                @change="addHistory"
                                            />
                                        </div>
                                    </template>
                                </el-collapse-item>
                            </el-collapse>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
            <div class="print-center" v-loading="loading" element-loading-text="正在加载数据...">
                <div class="main-drag-box" :style="{ ...getPagerWH }">
                    <!-- @dblclick="dblAddModule('text', $event)" -->
                    <div
                        class="main-drag-content"
                        :style="{
                            position: 'absolute',
                            top: `${mmToPx(printInfo.marginTop) * zoom}px`,
                            left: `${mmToPx(printInfo.marginLeft) * zoom}px`,
                            right: `${mmToPx(printInfo.marginRight) * zoom}px`,
                            bottom: `${mmToPx(printInfo.marginBottom) * zoom}px`,
                            ...getPageContentWH,
                        }"
                        :key="printInfo.printType + printInfo.direction + zoom"
                        @dragover="allowDrop"
                    >
                        <Vue3DraggableResizable
                            :class="{ active: item.active, unactive: !item.active, isErp: isErp }"
                            v-for="item in seniorModuleData.filter((item) => item.positionType !== 3)"
                            :style="overLapId.includes(item.id) ? 'border:1px solid #44b449' : ''"
                            :key="item.id"
                            :initW="item.width"
                            :initH="item.height"
                            v-model:x="item.left"
                            v-model:y="item.top"
                            v-model:w="item.width"
                            v-model:h="item.height"
                            v-model:active="item.active"
                            :minH="10"
                            :handles="item.sticks"
                            :draggable="!item.inputable"
                            :resizable="!item.inputable"
                            :parent="true"
                            @activated="activeDrag($event, item, 'unTable')"
                            @deactivated="($event: any)=>deactivated($event,item)"
                            @drag-start="mouduleDrag($event, 'drag-start', item)"
                            @resize-start="mouduleDrag($event, 'resize-start')"
                            @dragging="mouduleDrag($event, 'dragging', item)"
                            @resizing="mouduleDrag($event, 'resizing')"
                            @drag-end="mouduleDrag($event, 'drag-end', item)"
                            @resize-end="mouduleDrag($event, 'resize-end', item)"
                            @dblclick="caseOnDrop(item)"
                            @mousedown.stop="
                                bodyDown($event, item);
                                selectStyle(item.style);
                            "
                        >
                            <template v-if="item.type === 'text'">
                                <div
                                    class="drag-box"
                                    v-if="!item.inputable"
                                    @dblclick="() => (item.inputable = true)"
                                    :style="{
                                        width: item.width + 'px',
                                        height: item.height + 'px',
                                        ...getStyle(item, item.style, item.height),
                                        zIndex: 2,
                                    }"
                                >
                                    <!-- 处理斜体显示不全 -->
                                    <span
                                        v-if="item.label"
                                        class="drag-box-inner"
                                        :style="{
                                            width: item.style.fontStyle === 'italic' ? item.width - 3 + 'px' : item.width + 'px',
                                            maxHeight: item.height + 'px',
                                        }"
                                    >
                                        {{ item.dataSource ? `${item.label}[=${item.text}]` : item.label || "请输入文字" }}</span
                                    >
                                    <span
                                        v-else
                                        class="drag-box-inner"
                                        :style="{ width: item.width + 'px', color: '#666666', maxHeight: item.height + 'px' }"
                                        >请输入文字</span
                                    >
                                </div>
                                <el-input
                                    v-else
                                    v-model="item.label"
                                    type="textarea"
                                    resize="none"
                                    :style="{
                                        width: item.width + 'px',
                                        height: item.height + 'px',
                                        textAlign: 'left',
                                        boxSizing: 'border-box',
                                        ...getStyle(item, item.style, item.height),
                                        overflow: 'unset',
                                    }"
                                    :input-style="{
                                        height: item.height + 'px',
                                        minHeight: item.height + 'px',
                                        padding: '0px 1px',
                                        fontStyle: item.style.fontStyle,
                                        fontWeight: item.style.fontWeight,
                                        overflow: 'unset',
                                    }"
                                    :ref="
                                        (el) => {
                                            moudleInputRef[item.id] = el;
                                        }
                                    "
                                    @keydown="inputKeyDown($event, item)"
                                    @blur="blurInput($event, item)"
                                    @change="moduleInputChange"
                                />
                            </template>
                            <template v-else-if="item.type === 'textNoLabel'">
                                <div
                                    class="drag-box"
                                    :style="{
                                        width: item.width + 'px',
                                        height: item.height + 'px',
                                        ...getStyle(item, item.style, item.height),
                                        zIndex: 2,
                                    }"
                                >
                                    <span
                                        class="drag-box-inner"
                                        :style="{
                                            width:
                                                item.style.fontStyle === 'italic' && ['justify', 'right'].includes(item.style.textAlign)
                                                    ? item.width - 3 + 'px'
                                                    : item.width + 'px',
                                            maxHeight: item.height + 'px',
                                        }"
                                    >
                                        <!-- 针对表头凭证字特殊处理 -->
                                        {{
                                            item.dataSource
                                                ? item.id === 0
                                                    ? `[=${item.text}]`
                                                    : `${item.label}[=${item.text}]`
                                                : item.label || "请输入文字"
                                        }}</span
                                    >
                                </div>
                            </template>
                            <template v-else-if="item.type === 'img'">
                                <img style="zindex: 2" draggable="false" v-if="item.dataSource" :src="item.dataSource" class="avatar" />
                            </template>
                            <template v-else-if="item.type === 'rectangle'">
                                <div style="border: 1px solid #999; height: 100%; width: 100%; box-sizing: border-box"></div>
                            </template>
                            <template v-else-if="item.type === 'line'">
                                <div
                                    style="
                                        border-top: 1px solid rgb(153, 153, 153);
                                        margin: 0 auto;
                                        height: 1px;
                                        position: relative;
                                        top: 9px;
                                    "
                                    :style="{ top: item.height / 2 + 'px' }"
                                ></div>
                            </template>
                        </Vue3DraggableResizable>
                        <Vue3DraggableResizable
                            v-if="seniorTableData"
                            class="table-drag-box"
                            :key="Number(loading) + tableContentList.length"
                            :class="{ active: seniorTableData.active, unactive: !seniorTableData.active, isErp: isErp }"
                            :style="
                                overLapId.includes(seniorTableData.id)
                                    ? !isErp
                                        ? 'border:1px solid #44b449'
                                        : 'border:1px solid #3d7fff'
                                    : ''
                            "
                            :initW="seniorTableData.width"
                            :initH="seniorTableData.height"
                            v-model:x="seniorTableData.left"
                            v-model:y="seniorTableData.top"
                            v-model:w="seniorTableData.width"
                            v-model:h="seniorTableData.height"
                            v-model:active="seniorTableData.active"
                            :handles="seniorTableData.sticks"
                            :draggable="!seniorTableData.inputable"
                            :resizable="!seniorTableData.inputable"
                            :parent="true"
                            :minH="60"
                            :minW="tableContentList.length * 20"
                            @activated="activeDrag($event, seniorTableData, 'table')"
                            @deactivated="deactivated($event, seniorTableData)"
                            @drag-start="tableDrag($event, 'drag-start', seniorTableData)"
                            @resize-start="tableDrag($event, 'resize-start')"
                            @dragging="tableDrag($event, 'dragging', seniorTableData)"
                            @resizing="tableDrag($event, 'resizing')"
                            @drag-end="tableDrag($event, 'drag-end', seniorTableData)"
                            @resize-end="tableDrag($event, 'resize-end', seniorTableData)"
                            @dblclick="caseOnDrop(seniorTableData)"
                            @mousedown="tableMouseDown($event, seniorTableData)"
                        >
                            <!-- @mousedown.stop="bodyDown($event, seniorTableData)" -->
                            <div
                                class="table-drag-content"
                                :style="{
                                    width: seniorTableData.width + 'px',
                                }"
                            >
                                <div
                                    v-show="seniorTableData.inputable && !tableListDragging"
                                    class="drag-h-line"
                                    :style="{ top: tableContentList[0].headerHeight - 4 + 'px' }"
                                    @mousedown="resizeHeight($event, 'headerHeight')"
                                >
                                    <div></div>
                                </div>
                                <div
                                    v-show="seniorTableData.inputable && !tableListDragging"
                                    class="drag-h-line"
                                    :style="{ bottom: tableContentList[0].footerHeight - 4 + 'px' }"
                                    @mousedown="resizeHeight($event, 'bodyHeight')"
                                >
                                    <div></div>
                                </div>
                                <div
                                    v-show="seniorTableData.inputable && !tableListDragging"
                                    class="drag-h-line"
                                    id="lastHLine"
                                    :style="{ bottom: '-4px' }"
                                    @mousedown="resizeHeight($event, 'footerHeight')"
                                >
                                    <div></div>
                                </div>
                                <Container
                                    v-if="seniorTableData.inputable"
                                    :orientation="'horizontal'"
                                    :drag-class="'card-ghost'"
                                    :animation-duration="50"
                                    @drop="tableListDrag"
                                    @drag-start="dragStart"
                                >
                                    <!-- flex: item.width ? '1 1 ' + item.width + 'px' : '1 1 auto', -->
                                    <Draggable
                                        v-for="(item, index) in tableContentList"
                                        :key="item.id"
                                        :style="{
                                            width: `${item.width}px`,
                                            height: `calc(100% - ${tableContentList[0].footerHeight}px)`,
                                            borderRight: index === tableContentList.length - 1 ? '' : '1px solid #999',
                                        }"
                                    >
                                        <!-- left: index === tableContentList.length - 1 ? '-1px' : '', -->

                                        <div class="draggable-item" style="width: 100%">
                                            <div
                                                class="table-body"
                                                style="width: 100%"
                                                :style="index === tableContentList.length - 1 ? 'border:none' : ''"
                                            >
                                                <div class="table-lable" style="width: 100%" :style="{ height: item.headerHeight + 'px' }">
                                                    <div
                                                        class="table-lable-div"
                                                        v-if="!item.inputable"
                                                        :style="{ ...getStyle(item, item.headerStyle, item.headerHeight, true) }"
                                                        @click="selectStyle(item.headerStyle)"
                                                        @dblclick="clickTableLable(item)"
                                                    >
                                                        <span
                                                            :style="{
                                                                width:
                                                                    item.headerStyle.fontStyle === 'italic'
                                                                        ? item.width - 3 + 'px'
                                                                        : item.width + 'px',
                                                            }"
                                                        >
                                                            {{ item.label }}</span
                                                        >
                                                    </div>
                                                    <el-input
                                                        class="table-lable-div"
                                                        v-else
                                                        :ref="
                                                            (el) => {
                                                                tableLableInputRefs[item.id] = el;
                                                            }
                                                        "
                                                        v-model="item.label"
                                                        :style="{ ...getStyle(item, item.headerStyle, item.headerHeight, true) }"
                                                        @blur="blurTableLable($event, item)"
                                                        @change="tableLableChange"
                                                    />
                                                </div>
                                                <div
                                                    class="table-content"
                                                    :style="{
                                                        height: item.bodyHeight + 'px',
                                                        ...getStyle(item, item.bodyStyle, item.bodyHeight, true),
                                                    }"
                                                    @click="selectStyle(item.bodyStyle)"
                                                >
                                                    <span
                                                        :style="{
                                                            width:
                                                                item.headerStyle.bodyStyle === 'italic'
                                                                    ? item.width - 3 + 'px'
                                                                    : item.width + 'px',
                                                        }"
                                                    >
                                                        {{ item.text }}</span
                                                    >
                                                </div>
                                                <div
                                                    class="table-debit-amount"
                                                    v-if="item.id == 12"
                                                    :style="{
                                                        width: `${item.width}px`,
                                                        height: item.footerHeight + 'px',
                                                        position: 'absolute',
                                                        bottom: `-${item.footerHeight}px`,
                                                        borderLeft: index !== 0 && tableContentList[index-1].id !== 13  && lastTotalIndex + 1 !== index  ?'1px solid #999':'',
                                                        borderRight:index === tableContentList.length - 1 ? '':'1px solid #999',
                                                        ...getStyle(item,item.footerStyle!, item.footerHeight,true),
                                                    }"
                                                    @click="selectStyle(item.footerStyle!)"
                                                >
                                                    <span
                                                        :style="{
                                                            width:
                                                                item.headerStyle.footerStyle === 'italic'
                                                                    ? item.width - 3 + 'px'
                                                                    : item.width + 'px',
                                                        }"
                                                        >[=借方金额合计]</span
                                                    >
                                                </div>
                                                <div
                                                    class="table-credit-amount"
                                                    v-if="item.id == 13"
                                                    :style="{
                                                        width: `${ item.width}px`,
                                                        height: item.footerHeight + 'px',
                                                        position: 'absolute',
                                                        bottom: `-${item.footerHeight}px`,
                                                        borderLeft: index !== 0 && tableContentList[index-1].id !== 12 && lastTotalIndex + 1 !== index ?'1px solid #999':'',
                                                        borderRight:index === tableContentList.length - 1 ? '':'1px solid #999',
                                                        ...getStyle(item,item.footerStyle!, item.footerHeight,true),
                                                    }"
                                                    @click="selectStyle(item.footerStyle!)"
                                                >
                                                    <span
                                                        :style="{
                                                            width:
                                                                item.headerStyle.footerStyle === 'italic'
                                                                    ? item.width - 3 + 'px'
                                                                    : item.width + 'px',
                                                        }"
                                                        >[=贷方金额合计]</span
                                                    >
                                                </div>
                                            </div>
                                            <div
                                                v-show="!tableListDragging"
                                                @mousedown.stop="resizeWidth($event, item)"
                                                class="drag-w-line"
                                                :style="{
                                                    height:
                                                        (index !== tableContentList.length - 1 &&
                                                            (tableContentList[index].id === 12 ||
                                                                tableContentList[index].id === 13 ||
                                                                tableContentList[index + 1].id === 12 ||
                                                                tableContentList[index + 1].id === 13)) ||
                                                        index === tableContentList.length - 1 ||
                                                        index === lastTotalIndex
                                                            ? seniorTableData.height + 'px'
                                                            : '100%',
                                                    width: index !== tableContentList.length - 1 ? '4px' : '4px',
                                                }"
                                            >
                                                <div></div>
                                            </div>
                                        </div>
                                    </Draggable>
                                </Container>
                                <div
                                    class="noDraggableTable"
                                    v-else
                                    style="display: flex; height: 100%; overflow: hidden"
                                    :style="{
                                        width: `${seniorTableData.width}px`,
                                    }"
                                >
                                    <!-- flex: item.width ? '1 1 ' + item.width + 'px' : '1 1 auto', -->
                                    <!-- style="width: 100%" -->
                                    <div
                                        v-for="(item, index) in tableContentList"
                                        :key="item.id"
                                        class="draggable-item"
                                        :style="{
                                            width: `${item.width}px`,
                                            height: `calc(100% - ${tableContentList[0].footerHeight}px)`,
                                            position: 'relative',
                                        }"
                                    >
                                        <!-- left: index === tableContentList.length - 1 ? '-1px' : '', -->

                                        <div
                                            class="table-body"
                                            :style="{
                                                border: index === tableContentList.length - 1 ? 'none' : '',
                                                width: `${item.width}px`,
                                            }"
                                        >
                                            <div
                                                class="table-lable"
                                                :style="{
                                                    height: item.headerHeight + 'px',
                                                    ...getStyle(item, item.headerStyle, item.headerHeight, true),
                                                }"
                                            >
                                                <span
                                                    :style="{
                                                        width:
                                                            item.headerStyle.fontStyle === 'italic'
                                                                ? item.width - 3 + 'px'
                                                                : item.width + 'px',
                                                    }"
                                                >
                                                    {{ item.label }}</span
                                                >
                                            </div>
                                            <!-- flex: item.bodyHeight ? '1 1 ' + item.bodyHeight + 'px' : '1 1 auto', -->
                                            <div
                                                class="table-content"
                                                :style="{
                                                    height: item.bodyHeight + 'px',
                                                    ...getStyle(item, item.bodyStyle, item.bodyHeight, true),
                                                }"
                                            >
                                                <span
                                                    :style="{
                                                        width:
                                                            item.bodyStyle.fontStyle === 'italic'
                                                                ? item.width - 3 + 'px'
                                                                : item.width + 'px',
                                                    }"
                                                    >{{ item.text }}</span
                                                >
                                            </div>
                                            <div
                                                class="table-debit-amount"
                                                v-if="item.id == 12"
                                                :style="{
                                                    width: `${item.width}px`,
                                                    height: item.footerHeight + 'px',
                                                    position: 'absolute',
                                                    bottom: `-${item.footerHeight}px`,
                                                    borderLeft: index !== 0 && tableContentList[index-1].id !== 13 && lastTotalIndex + 1 !== index ?'1px solid #999':'',
                                                    borderRight:index === tableContentList.length - 1 ? '':'1px solid #999',
                                                    ...getStyle(item,item.footerStyle!, item.footerHeight,true),
                                                }"
                                            >
                                                <span
                                                    :style="{
                                                        width:
                                                            item.bodyStyle.fontStyle === 'italic'
                                                                ? item.width - 3 + 'px'
                                                                : item.width + 'px',
                                                    }"
                                                    >[=借方金额合计]</span
                                                >
                                            </div>
                                            <div
                                                class="table-credit-amount"
                                                v-if="item.id == 13"
                                                :style="{
                                                    width: `${item.width}px`,
                                                    height: item.footerHeight + 'px',
                                                    position: 'absolute',
                                                    bottom: `-${item.footerHeight}px`,
                                                    borderLeft: index !== 0 && tableContentList[index-1].id !== 12 && lastTotalIndex + 1 !== index ?'1px solid #999':'',
                                                    borderRight:index === tableContentList.length - 1 ? '':'1px solid #999',
                                                    ...getStyle(item,item.footerStyle!, item.footerHeight,true),
                                                }"
                                            >
                                                <span
                                                    :style="{
                                                        width:
                                                            item.bodyStyle.fontStyle === 'italic'
                                                                ? item.width - 3 + 'px'
                                                                : item.width + 'px',
                                                    }"
                                                    >[=贷方金额合计]</span
                                                >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div
                                    class="table-total"
                                    :style="{
                                        height: tableContentList[0].footerHeight + 'px',
                                        bottom: '0px',
                                    }"
                                >
                                    <div
                                        :style="{
                                            height: tableContentList[0].footerHeight + 'px',
                                            width: calcTotalWidth + 'px',
                                            borderRight:'1px solid #999',
                                            boxSizing: 'border-box',
                                            ...getStyle(tableContentList[0],tableContentList[0]!.footerStyle, tableContentList[0].footerHeight,true),
                                        }"
                                        @click="seniorTableData.inputable ? selectStyle(tableContentList[0].footerStyle) : ''"
                                    >
                                        <span
                                            :style="{
                                                width:
                                                    tableContentList[0].footerStyle.fontStyle === 'italic'
                                                        ? calcTotalWidth - 3 + 'px'
                                                        : calcTotalWidth + 'px',
                                            }"
                                            >合计：</span
                                        >
                                    </div>
                                </div>
                            </div>
                        </Vue3DraggableResizable>
                    </div>
                    <!-- 显示打印时间 -->
                    <div
                        v-show="printInfo.isShowPrintDate"
                        class="print-time"
                        :style="{
                            left: mmToPx(printInfo.marginLeft) * zoom + 'px',
                            bottom: relativeBottom * zoom + 'px',
                            fontSize: 10 * zoom + 'px',
                        }"
                    >
                        打印日期：{{ printInfo.printDateText ? printInfo.printDateText : getFormatterDate("", "-") }}
                    </div>
                    <!-- 显示打印页码 -->
                    <div
                        v-show="printInfo.isShowPageNumber"
                        class="page-number"
                        :style="{
                            right: mmToPx(printInfo.marginRight) * zoom + 'px',
                            bottom: relativeBottom * zoom + 'px',
                            fontSize: 10 * zoom + 'px',
                        }"
                    >
                        第1/1页，共1页
                    </div>
                    <!-- 页边距限制角标 -->
                    <div
                        class="top-left-subscript"
                        :style="{
                            top: `${mmToPx(printInfo.marginTop) * zoom - 24 * zoom}px`,
                            left: `${mmToPx(printInfo.marginLeft) * zoom - 24 * zoom}px`,
                            width: `${24 * zoom}px`,
                            height: `${24 * zoom}px`,
                        }"
                    ></div>
                    <div
                        class="top-right-subscript"
                        :style="{
                            top: `${mmToPx(printInfo.marginTop) * zoom - 24 * zoom}px`,
                            right: `${mmToPx(printInfo.marginRight) * zoom - 24 * zoom}px`,
                            width: `${24 * zoom}px`,
                            height: `${24 * zoom}px`,
                        }"
                    ></div>
                    <div
                        class="bottom-left-subscript"
                        :style="{
                            bottom: `${mmToPx(printInfo.marginBottom) * zoom - 24 * zoom}px`,
                            left: `${mmToPx(printInfo.marginLeft) * zoom - 24 * zoom}px`,
                            width: `${24 * zoom}px`,
                            height: `${24 * zoom}px`,
                        }"
                    ></div>
                    <div
                        class="bottom-right-subscript"
                        :style="{
                            bottom: `${mmToPx(printInfo.marginBottom) * zoom - 24 * zoom}px`,
                            right: `${mmToPx(printInfo.marginRight) * zoom - 24 * zoom}px`,
                            width: `${24 * zoom}px`,
                            height: `${24 * zoom}px`,
                        }"
                    ></div>
                </div>
            </div>
            <div class="print-right" v-show="rightNavShow">
                <div class="rich-text" style="height: 90px">
                    <div class="rich-text-name">字体</div>
                    <div class="typeFace">
                        <div style="display: flex">
                            <el-select
                                v-model="selectFontType"
                                style="width: 100px"
                                :teleported="false"
                                @change="changeBaseStyle('fontFamily', selectFontType)"
                            >
                                <el-option
                                    v-for="item in fontTypeList"
                                    class="font-family-option"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                    :style="{ fontFamily: item.value }"
                                />
                            </el-select>
                            <el-select
                                v-model="selectFontSize"
                                style="width: 60px"
                                :teleported="false"
                                @change="changeBaseStyle('fontSize', selectFontSize)"
                            >
                                <el-option v-for="item in fontSizeList" :key="item.value" :label="item.label" :value="item.value" />
                            </el-select>
                        </div>
                        <div style="margin-top: 9px">
                            <span
                                @click="changeBold(!isBold)"
                                class="rich-text-btn"
                                :class="{ 'is-active': isBold, 'is-grey': !barCanEdit }"
                                style="font-weight: bold"
                                >B</span
                            >
                            <span
                                @click="changeUnderline(!isUnderline)"
                                class="rich-text-btn"
                                :class="{ 'is-active': isUnderline, 'is-grey': !barCanEdit }"
                                style="text-decoration: underline"
                                >U</span
                            >
                            <span
                                @click="changeItalic(!isItalic)"
                                class="rich-text-btn"
                                :class="{ 'is-active': isItalic, 'is-grey': !barCanEdit }"
                                style="font-style: italic"
                                >I</span
                            >
                        </div>
                    </div>
                </div>
                <div class="rich-text" style="height: 68px">
                    <div class="rich-text-name">文本对齐</div>
                    <div class="rich-text-col">
                        <div
                            class="cell"
                            :class="{ 'is-active': selectTextAlign == 'left', 'is-grey': !barCanEdit }"
                            @click="changeTextAlign('left')"
                        >
                            <img v-if="selectTextAlign === 'left' && !isErp" src="@/assets/Voucher/text-left-active.png" />
                            <img v-else-if="selectTextAlign === 'left' && isErp" src="@/assets/Voucher/text-left-blue.png" />
                            <img v-else src="@/assets/Voucher/text-left.png" />
                            <span>左对齐</span>
                        </div>
                        <div
                            class="cell"
                            :class="{ 'is-active': selectTextAlign == 'center', 'is-grey': !barCanEdit }"
                            @click="changeTextAlign('center')"
                        >
                            <img v-if="selectTextAlign === 'center' && !isErp" src="@/assets/Voucher/text-center-active.png" />
                            <img v-else-if="selectTextAlign === 'center' && isErp" src="@/assets/Voucher/text-center-blue.png" />
                            <img v-else src="@/assets/Voucher/text-center.png" />
                            <span>水平居中</span>
                        </div>
                        <div
                            class="cell"
                            :class="{ 'is-active': selectTextAlign == 'justify', 'is-grey': !barCanEdit }"
                            @click="changeTextAlign('justify')"
                        >
                            <img v-if="selectTextAlign === 'justify' && !isErp" src="@/assets/Voucher/text-justify-active.png" />
                            <img v-else-if="selectTextAlign === 'justify' && isErp" src="@/assets/Voucher/text-justify-blue.png" />
                            <img v-else src="@/assets/Voucher/text-justify.png" />
                            <span>两端对齐</span>
                        </div>
                        <div
                            class="cell"
                            :class="{ 'is-active': selectTextAlign == 'right', 'is-grey': !barCanEdit }"
                            @click="changeTextAlign('right')"
                        >
                            <img v-if="selectTextAlign === 'right' && !isErp" src="@/assets/Voucher/text-right-active.png" />
                            <img v-else-if="selectTextAlign === 'right' && isErp" src="@/assets/Voucher/text-right-blue.png" />
                            <img v-else src="@/assets/Voucher/text-right.png" />
                            <span>右对齐</span>
                        </div>
                    </div>
                </div>
                <div class="rich-text" style="height: 60px">
                    <div class="rich-text-name">文本超长</div>
                    <div class="rich-text-col">
                        <div
                            class="cell"
                            :class="{ 'is-active': selectWhiteSpace == 'Wrap', 'is-grey': !barCanEdit }"
                            @click="changeWhiteSpace('Wrap')"
                        >
                            <img v-if="selectWhiteSpace === 'Wrap' && !isErp" src="@/assets/Voucher/text-line-break-active.png" />
                            <img v-else-if="selectWhiteSpace === 'Wrap' && isErp" src="@/assets/Voucher/text-line-break-blue.png" />
                            <img v-else src="@/assets/Voucher/text-line-break.png" />
                            <span>文本折行</span>
                        </div>
                        <div
                            class="cell"
                            :class="{ 'is-active': selectWhiteSpace == 'Truncation', 'is-grey': !barCanEdit }"
                            @click="changeWhiteSpace('Truncation')"
                        >
                            <img v-if="selectWhiteSpace === 'Truncation' && !isErp" src="@/assets/Voucher/text-cut-active.png" />
                            <img v-else-if="selectWhiteSpace === 'Truncation' && isErp" src="@/assets/Voucher/text-cut-blue.png" />
                            <img v-else src="@/assets/Voucher/text-cut.png" />
                            <span>截断</span>
                        </div>
                        <div
                            class="cell"
                            :class="{ 'is-active': selectWhiteSpace == 'Reduce', 'is-grey': !barCanEdit }"
                            @click="changeWhiteSpace('Reduce')"
                        >
                            <img v-if="selectWhiteSpace === 'Reduce' && !isErp" src="@/assets/Voucher/text-scale-active.png" />
                            <img v-else-if="selectWhiteSpace === 'Reduce' && isErp" src="@/assets/Voucher/text-scale-blue.png" />
                            <img v-else src="@/assets/Voucher/text-scale.png" />
                            <span>缩小</span>
                        </div>
                    </div>
                </div>
                <div class="rich-text" style="height: 110px">
                    <div class="rich-text-name">控件对齐</div>
                    <div class="rich-text-col">
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="changeBoxAlign('left')">
                            <!-- 'is-active': selectTextAlign == 'left' -->
                            <!-- <img v-if="selectBoxAlign.has('left')" src="@/assets/Voucher/cell-left-active.png" /> -->
                            <img src="@/assets/Voucher/cell-left.png" />
                            <span>左对齐</span>
                        </div>
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="changeBoxAlign('xCenter')">
                            <!-- 'is-active': selectTextAlign == 'center' -->
                            <!-- <img v-if="selectBoxAlign.has('xCenter')" src="@/assets/Voucher/cell-center-active.png" /> -->
                            <img src="@/assets/Voucher/cell-center.png" />
                            <span>水平居中</span>
                        </div>
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="changeBoxAlign('right')">
                            <!-- 'is-active': selectTextAlign == 'right', -->
                            <!-- <img v-if="selectBoxAlign.has('right')" src="@/assets/Voucher/cell-center-active.png" /> -->
                            <img src="@/assets/Voucher/cell-right.png" />
                            <span>右对齐</span>
                        </div>
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="changeBoxAlign('top')">
                            <!-- 'is-active': selectTextAlign == 'right', -->
                            <!-- <img v-if="selectBoxAlign.has('top')" src="@/assets/Voucher/cell-top-active.png" /> -->
                            <img src="@/assets/Voucher/cell-top.png" />
                            <span>顶对齐</span>
                        </div>
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="changeBoxAlign('yCenter')">
                            <!-- 'is-active': selectTextAlign == 'right', -->
                            <!-- <img v-if="selectBoxAlign.has('yCenter')" src="@/assets/Voucher/cell-center-active.png" /> -->
                            <img src="@/assets/Voucher/cell-center.png" />
                            <span>垂直居中</span>
                        </div>
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="changeBoxAlign('bottom')">
                            <!-- 'is-active': selectTextAlign == 'right', -->
                            <!-- <img v-if="selectBoxAlign.has('bottom')" src="@/assets/Voucher/cell-bottom-active.png" /> -->
                            <img src="@/assets/Voucher/cell-bottom.png" />
                            <span>底对齐</span>
                        </div>
                    </div>
                </div>
                <div class="rich-text" style="height: 60px">
                    <div class="rich-text-name">操作</div>
                    <div class="rich-text-col">
                        <div class="cell" :class="{ 'is-grey': !barCanEdit }" @click="historyIndex >= 0 ? preHistory() : ''">
                            <img v-if="historyIndex >= 0" src="@/assets/Voucher/prevStep.png" />
                            <img v-else src="@/assets/Voucher/pre-grey.png" />
                            <span>上一步</span>
                        </div>
                        <div
                            class="cell"
                            :class="{ 'is-grey': !barCanEdit }"
                            @click="historyArr.length !== historyIndex + 1 && historyArr.length != 0 ? nextHistory() : ''"
                        >
                            <img
                                v-if="historyArr.length !== historyIndex + 1 && historyArr.length != 0"
                                src="@/assets/Voucher/nextStep.png"
                            />
                            <img v-else src="@/assets/Voucher/next-grey.png" />
                            <span>下一步</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <bodySetting v-model:bodySettingShow="bodySettingShow" :bodySetCheck="bodySetCheck" @update="updateBodySet"></bodySetting>
    <SyncToOtherAccountSet v-model:syncDialogShow="syncDialogShow" @confirmSync="syncSettings" />
    <el-dialog title="提示" center width="450px" class="custom-confirm" v-model="noticeChange">
        <template v-if="noticeChangeType === 1">
            <div class="tips-content" v-dialogDrag>
                <div class="selected-table-text">
                    <div style="cursor: pointer">
                        凭证封面的纸张会与凭证纸张保持一致，当切换纸张时，为保证封面的打印效果，可点击<span
                            style="color: var(--link-color)"
                            @click="setCover"
                            >设置封面打印样式</span
                        >去调整封面样式哦
                    </div>
                    <div class="mt-10 ml-10">
                        <el-checkbox label="不再提示" v-model="checkChangePage"></el-checkbox>
                    </div>
                </div>
            </div>
            <div class="buttons borderTop mt-20">
                <a
                    class="button solid-button mr-10"
                    @click="
                        setCover();
                        confirmNoticeChange(1);
                    "
                    >去调整</a
                >
                <a class="button" @click="cancelNoticeChange">取消</a>
            </div>
        </template>
        <template v-else>
            <div class="tips-content" v-dialogDrag>
                <div class="selected-table-text">
                    <div>
                        纸张为B5、A5、A4宽12*21cm、发票版14*24cm且方向为纵向时，为保证打印效果，右侧模板编辑区仍为横向，但实际打印时仍按纵向打印，您可以预览查看效果哦~
                    </div>
                </div>
                <div class="mt-10 ml-10">
                    <el-checkbox label="不再提示" v-model="checkChangeDirection"></el-checkbox>
                </div>
            </div>
            <div class="buttons borderTop mt-20">
                <a class="button solid-button mr-10" @click="confirmNoticeChange(2)">确定</a>
            </div>
        </template>
    </el-dialog>
    <el-dialog
        class="functionUpdate dialogDrag"
        v-model="updateTipShow"
        width="1350"
        title="功能引导图"
        center
        :show-close="false"
        top="5vh"
    >
        <img
            src="@/assets/Common/close-icon.png"
            style="position: absolute; left: 1350px; top: -35px; width: 32px; height: 32px"
            @click="updateTipShow = false"
        />
        <div v-dialogDrag>
            <img v-if="isErp" src="@/assets/Voucher/print-guide-blue.png" style="height: 628px; width: 100%" />
            <img v-else src="@/assets/Voucher/print-guide.png" style="height: 628px; width: 100%" />
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "SeniorPrint",
};
</script>

<script setup lang="ts">
import SyncToOtherAccountSet from "@/components/CoverPrint/components/SyncToOtherAccountSet.vue";
import bodySetting from "./bodySetting.vue";
import type {
    IpagerSize,
    IStyle,
    IModuleData,
    ITableList,
    ModuleType,
    ISavePrintData,
} from "@/components/CoverPrint/types";
import { TextAlignmentType, VoucherPrintDataType } from "@/components/CoverPrint/types";
import {
    getKeyByValue,
    transformTableData,
    splitTableObject,
    moduleList,
    judgeOverFlow,
    pageSizeOptions,
    zoomList,
    fontSizeList,
    typeTransform,
    canTextFitInDiv,
} from "@/components/CoverPrint/utils";
import { NOTICE_PAGE_SETTINGS_SUFFIX, NOTICED_DIR_SETTINGS_SUFFIX, SPACE_BETWEEN, MIN_CELL_SIZE, MIN_CELL_WIDTH } from "./constants";
import { isLemonClient } from "@/util/lmClient";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { printTypeList, getMaxMargin } from "@/views/Voucher/utils";
import { getVoucherSeniorPrintInfo, printParams, setVoucherSeniorPrintInfo, type ISeniorPrintInfo } from "@/components/Voucher/utils";
import { ElNotify } from "@/util/notify";
import { globalWindowOpenPage, getUrlSearchParams, globalWindowOpen, tryClearCustomUrlParams } from "@/util/url";
import { getFormatterDate } from "@/util/date";
import { request } from "@/util/service";
import { ElAlert } from "@/util/confirm";
import useBoxSelection from "@/hooks/useBoxSelection";
import { useLoading } from "@/hooks/useLoading";
import { ref, toRef, computed, onMounted, onBeforeUnmount, type Ref, watch, nextTick, onActivated, onUnmounted, onDeactivated } from "vue";
import { useRoute } from "vue-router";
import { Container, Draggable } from "vue3-smooth-dnd";
import _ from "lodash";
import { getGlobalToken } from "@/util/baseInfo";
import { getServiceId } from "@/util/proUtils";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useVoucherSettingsStore } from "@/store/modules/voucherSettings";

const updateTipShow = ref(localStorage.getItem("updateTipShow") === "true");
localStorage.setItem("updateTipShow", "false");
const routerArrayStore = useRouterArrayStoreHook();
const accountsetStore = useAccountSetStore();
function setCover() {
    globalWindowOpenPage(
        `/Voucher/SeniorCoverPrint?printType=${printInfo.value.printType}&direction=${printInfo.value.direction}`,
        "封面打印设置"
    );
}

function generateKey(type: number) {
    const userSn = accountsetStore.userInfo?.userSn;
    const token = getGlobalToken();
    return `${userSn}-${token}-${type === 1 ? NOTICE_PAGE_SETTINGS_SUFFIX : NOTICED_DIR_SETTINGS_SUFFIX}`;
}

function setLocalStorage(type: number, check: boolean) {
    const key = generateKey(type);
    localStorage.setItem(key, check.toString());
}

function checkNoticeSettings(type: number) {
    const key = generateKey(type);
    return localStorage.getItem(key);
}

function confirmNoticeChange(type: number) {
    noticeChange.value = false;
    setLocalStorage(type, type === 1 ? checkChangePage.value : checkChangeDirection.value);
}
function cancelNoticeChange() {
    noticeChange.value = false;
}

const isErp = ref(window.isErp);
const syncDialogShow = ref(false);

function helpManual() {
    let url = "";
    if (isErp.value) {
        url = "https://help.ningmengyun.com/#/yyc/HandleForYYC?subMenuId=*********";
    } else {
        url = "https://help.ningmengyun.com/#/jz/handle?subMenuId=********* ";
    }
    globalWindowOpen(url);
}

function helpVideo() {
    let url = "";
    if (isErp.value) {
        url = "https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=130150111";
    } else {
        url = "https://help.ningmengyun.com/#/jz/videoPlayer?qType=130150161";
    }
    globalWindowOpen(url);
}

const noticeChange = ref(false);
//1代表纸张变化提示 2代表方向变化提示
const noticeChangeType = ref(0);
// const noticeChangeDirection = ref(false);
const checkChangePage = ref(false);
const checkChangeDirection = ref(false);
const fullScreen = toRef(useFullScreenStore(), "fullScreen");
const loading = ref(true);
const printInfo = ref(_.cloneDeep(getVoucherSeniorPrintInfo()));
//纸张大小 按照pt计算
// A4   595, 842
// A5   420, 595
// B5   498, 708
// Invoice  680, 397
// A4宽 595, 340
//返回的设置是0，与默认不符合
watch(
    () => printInfo.value.isShowSummaryPrint,
    (val) => {
        if (val && printInfo.value.summaryAsubLevel === 0) {
            printInfo.value.summaryAsubLevel = 1;
        }
    },
    {
        immediate: true,
    }
);
const pagerSizeList = ref(_.cloneDeep(pageSizeOptions));

// 当前选择的纸张宽高
const selectPagerSize: Ref<IpagerSize> = computed(() => {
    return pagerSizeList.value.find((item) => item.value === printInfo.value.printType)!;
});
//当前缩放比例
const zoom = ref(1.3);
const oldzoom = ref(1);
function addZoom() {
    let index = zoomList.findIndex((i) => i.value === zoom.value);
    if (index < zoomList.length - 1) {
        zoom.value = zoomList[index + 1].value;
    }
}
function subZoom() {
    let index = zoomList.findIndex((i) => i.value === zoom.value);
    if (index > 0) {
        zoom.value = zoomList[index - 1].value;
    }
}
const commonProps = ["width", "height", "top", "left"];
const tableProps = ["width", "height", "footerHeight", "bodyHeight", "headerHeight"];
function zoomChange(oldVal: number, newVal: number, dragBoxArr: any, tableList: any) {
    let { mainWidth, mainHeight } = getPagerWhNumber();
    mainWidth = mainWidth * newVal;
    mainHeight = mainHeight * newVal;

    const adjustItem = (item: any, properties: string[]) => {
        properties.forEach((prop) => {
            //可能超出页面，所以不能四舍五入
            item[prop] = Math.round((item[prop] / oldVal) * newVal);
        });
    };

    dragBoxArr.forEach((item: IModuleData) => {
        adjustItem(item, commonProps);
    });
    historySeniorModuleData.value &&
        historySeniorModuleData.value.forEach((item: IModuleData) => {
            adjustItem(item, commonProps);
        });
    tableList.forEach((item: ITableList) => {
        adjustItem(item, tableProps);
    });
}
watch(zoom, (newVal, oldVal) => {
    oldzoom.value = oldVal;
    zoomChange(oldVal, newVal, seniorModuleData.value, tableContentList.value);
});

// 日期、页码 相对底部间距
const relativeBottom = computed(() => {
    return [2, 3].includes(printInfo.value.printType) ? 10 : 20;
});
const getPagerWH = computed(() => {
    //目前是只允许默认纵向的受方向控制，其他的不受控制
    const defaultVertical = [1, 2, 6].includes(printInfo.value.printType);
    const isVertical = printInfo.value.direction === "Z" && defaultVertical;
    return isVertical
        ? { width: `${selectPagerSize.value.width * zoom.value}px`, height: `${selectPagerSize.value.height * zoom.value}px` }
        : { width: `${selectPagerSize.value.height * zoom.value}px`, height: `${selectPagerSize.value.width * zoom.value}px` };
});
// 当前选择的纸张内容宽高
const getPageContentWH = computed(() => {
    //目前是只允许默认纵向的受方向控制，其他的不受控制
    const defaultVertical = [1, 2, 6].includes(printInfo.value.printType);
    const isVertical = printInfo.value.direction === "Z" && defaultVertical;
    const { marginTop, marginBottom, marginLeft, marginRight } = printInfo.value;
    const marginX = Math.ceil(mmToPxWithDigit(marginLeft + marginRight));
    const marginY = Math.ceil(mmToPxWithDigit(marginTop + marginBottom));
    return isVertical
        ? {
              width: `${(selectPagerSize.value.width - marginX) * zoom.value}px`,
              height: `${(selectPagerSize.value.height - marginY) * zoom.value}px`,
          }
        : defaultVertical
        ? {
              width: `${(selectPagerSize.value.height - marginY) * zoom.value}px`,
              height: `${(selectPagerSize.value.width - marginX) * zoom.value}px`,
          }
        : {
              width: `${(selectPagerSize.value.height - marginX) * zoom.value}px`,
              height: `${(selectPagerSize.value.width - marginY) * zoom.value}px`,
          };
});

function getPagerWhNumber() {
    //getPagerWH
    // let { width: mainWidth, height: mainHeight } = selectPagerSize.value;
    let { width, height } = getPageContentWH.value;
    let mainWidth = 0;
    let mainHeight = 0;
    mainWidth = Number(width.replace("px", ""));
    mainHeight = Number(height.replace("px", ""));
    // if (printInfo.value.direction === "H") {
    //     [mainWidth, mainHeight] = [mainHeight, mainWidth];
    // }
    return { mainWidth, mainHeight };
}
//根据选择的纸张大小和方向改变拖拽盒子限制
function setDragSize() {
    let width = Number(getPageContentWH.value.width.replace("px", ""));
    let height = Number(getPageContentWH.value.height.replace("px", ""));
    let marginLeft = mmToPx(printInfo.value.marginLeft);
    let marginRight = mmToPx(printInfo.value.marginRight);
    let marginTop = mmToPx(printInfo.value.marginTop);
    let marginBottom = mmToPx(printInfo.value.marginBottom);
    seniorModuleData.value.forEach((item) => {
        if (!["table", "tableHeader", "tableBody", "tableFooter"].includes(item.type)) {
            if (item.width > width) {
                item.width = width;
                item.left = 0;
            }
            if (item.height > height) {
                item.height = height;
                item.top = 0;
            }
            if (item.left + item.width > width) {
                item.left = width - item.width;
            }
            if (item.top + item.height > height) {
                item.top = height - item.height;
            }
        } else if (item.type === "table") {
            //
            if (item.width > width) {
                item.width = width;
                item.left = 0;
                tableCellWidth();
            }
            if (item.height > height) {
                item.height = height;
                item.top = 0;
                let calcHeight = height / 3;
                tableContentList.value.forEach((item) => {
                    item.headerHeight = calcHeight;
                    item.bodyHeight = calcHeight;
                    item.footerHeight = calcHeight;
                });
            }
            if (item.left + item.width > width) {
                item.left = width - item.width;
            }
            if (item.top + item.height > height) {
                item.top = height - item.height;
            }
        }
    });
    tableContentList.value.forEach((item) => {
        let tableHeight = seniorTableData.value.height;
        if (item.width > width - marginLeft - marginRight) {
            item.width = width - marginLeft - marginRight;
        }
        if (tableHeight > height - marginTop - marginBottom) {
            tableHeight = height - marginTop - marginBottom;
            let cellHeight = tableHeight / 3;
            item.headerHeight = item.footerHeight = item.bodyHeight = cellHeight;
        }
    });
    addHistory();
}
// 平分表格列宽
function tableCellWidth() {
    let width = seniorTableData.value.width;
    let length = tableContentList.value.length;
    let cellWidth = Math.floor(width / length);
    let lastCellWidth = width - cellWidth * (length - 1);
    tableContentList.value.forEach((item, index) => {
        item.width = index === length - 1 ? lastCellWidth : cellWidth;
    });
}
const bodySettingShow = ref(false);
// isLineHeightAdaptive: false,
// isHideEmpty: false,
const bodySetCheck = ref({
    isMergeSubject: false,
    isShowLeaf: false,
    isMergeAAE: false,
    isHideAAECode: false,
    isHideAAECashflow: false,
    isShowFC: false,
    isShowQuantity: false,
});

// 有2种更新，一种是更新设置；一种是增加或者减少字段
// 先假设更新设置时不需要传参数进来。 如果产生了分隔列，则放到最后。合并列一种是合并到科目，一种是合并到摘要
// 增删列的时候，可以根据传入的列，来进行特殊的处理
function updateBodySetPlus(columnName?: any, targetIndex: number = -1, restoreEle: any = null) {
    let selectSeniorTableData = seniorModuleList.value[1].seniorFieldList.filter((i) => i.checked);
    const { isMergeSubject, isMergeAAE, isShowFC, isShowQuantity } = bodySetCheck.value;

    let subjectName = selectSeniorTableData.findIndex((i) => i.fieldName === "科目名称");
    let abstract = selectSeniorTableData.findIndex((i) => i.fieldName === "摘要");

    if (!columnName) {
        // 存在科目才需要处理科目合并的相关逻辑
        if (subjectName > -1) {
            let tableSubjectName = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_NAME");
            let subjectCode = selectSeniorTableData.findIndex((i) => i.fieldName === "科目编码");
            let tableSubjectCode = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_CODE");

            // 合并科目和科目编码
            if (isMergeSubject) {
                // 科目编码和科目名称都存在
                if (subjectCode > -1 && tableSubjectCode > -1) {
                    //删除科目编码，科目名称变为科目
                    if (tableContentList.value[tableSubjectName].label === "科目名称") {
                        tableContentList.value[tableSubjectName].label = "科目";
                    }
                    tableContentList.value[tableSubjectName].text = "[=科目编码+科目名称]";
                    tableContentList.value[tableSubjectName].width += tableContentList.value[tableSubjectCode].width;

                    if (tableSubjectName > tableSubjectCode) {
                        tableContentList.value[tableSubjectName].footerStyle = tableContentList.value[tableSubjectCode].footerStyle;
                        tableContentList.value[tableSubjectName].footerHeight = tableContentList.value[tableSubjectCode].footerHeight;

                        [tableContentList.value[tableSubjectName], tableContentList.value[tableSubjectCode]] = [
                            tableContentList.value[tableSubjectCode],
                            tableContentList.value[tableSubjectName],
                        ];
                        [tableSubjectName, tableSubjectCode] = [tableSubjectCode, tableSubjectName];
                    }
                    tableContentList.value.splice(tableSubjectCode, 1);
                }
            } else {
                // 科目编码和科目名称都存在
                if (subjectCode > -1 && tableSubjectCode === -1) {
                    //删除科目编码，科目名称变为科目
                    if (tableContentList.value[tableSubjectName].label === "科目") {
                        tableContentList.value[tableSubjectName].label = "科目名称";
                    }
                    tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                        "科目编码+科目名称",
                        "科目名称"
                    );
                    tableContentList.value[tableSubjectName].width -= MIN_CELL_WIDTH;
                    addTableContent(selectSeniorTableData[subjectCode]);
                }
            }

            tableSubjectName = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_NAME");
            let tableAssistingAccounting = tableContentList.value.findIndex((i) => i.dataSource === "#AA_NAME");
            let assistingAccounting = selectSeniorTableData.findIndex((i) => i.fieldName === "辅助核算");

            if (isMergeAAE) {
                // 辅助核算存在
                if (assistingAccounting > -1 && tableAssistingAccounting > -1) {
                    //删除辅助核算，科目变为科目+辅助核算
                    // tableContentList.value[tableSubjectName].label = "科目";
                    tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                        "]",
                        "+辅助核算]"
                    );
                    tableContentList.value[tableSubjectName].width += tableContentList.value[tableAssistingAccounting].width;

                    if (tableSubjectName > tableAssistingAccounting) {
                        tableContentList.value[tableSubjectName].footerStyle = tableContentList.value[tableAssistingAccounting].footerStyle;
                        tableContentList.value[tableSubjectName].footerHeight =
                            tableContentList.value[tableAssistingAccounting].footerHeight;

                        [tableContentList.value[tableSubjectName], tableContentList.value[tableAssistingAccounting]] = [
                            tableContentList.value[tableAssistingAccounting],
                            tableContentList.value[tableSubjectName],
                        ];
                        [tableSubjectName, tableAssistingAccounting] = [tableAssistingAccounting, tableSubjectName];
                    }
                    tableContentList.value.splice(tableAssistingAccounting, 1);
                }
            } else {
                // 辅助核算存在
                if (assistingAccounting > -1 && tableAssistingAccounting === -1) {
                    //删除辅助核算，科目变为科目+辅助核算
                    tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace("+辅助核算", "");
                    tableContentList.value[tableSubjectName].width -= MIN_CELL_WIDTH;
                    addTableContent(selectSeniorTableData[assistingAccounting]);
                }
            }
        }

        if (abstract > -1) {
            let tableAbstract = tableContentList.value.findIndex((i) => i.dataSource === "#DESCRIPTION");
            let foreignCurrency = selectSeniorTableData.findIndex((i) => i.fieldName === "外币核算");
            let tableForeignCurrency = tableContentList.value.findIndex((i) => i.dataSource === "#AA_FC");
            if (isShowFC) {
                if (foreignCurrency > -1 && tableForeignCurrency > -1) {
                    tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("]", "+外币核算]");
                    tableContentList.value[tableAbstract].width += tableContentList.value[tableForeignCurrency].width;

                    if (tableAbstract > tableForeignCurrency) {
                        tableContentList.value[tableAbstract].footerStyle = tableContentList.value[tableForeignCurrency].footerStyle;
                        tableContentList.value[tableAbstract].footerHeight = tableContentList.value[tableForeignCurrency].footerHeight;

                        [tableContentList.value[tableAbstract], tableContentList.value[tableForeignCurrency]] = [
                            tableContentList.value[tableForeignCurrency],
                            tableContentList.value[tableAbstract],
                        ];
                        [tableAbstract, tableForeignCurrency] = [tableForeignCurrency, tableAbstract];
                    }
                    tableContentList.value.splice(tableForeignCurrency, 1);
                }
            } else {
                if (foreignCurrency > -1 && tableForeignCurrency === -1) {
                    addTableContent(selectSeniorTableData[foreignCurrency]);
                    tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("+外币核算", "");
                    tableContentList.value[tableAbstract].width -= MIN_CELL_WIDTH;
                }
            }

            tableAbstract = tableContentList.value.findIndex((i) => i.dataSource === "#DESCRIPTION");
            let quantity = selectSeniorTableData.findIndex((i) => i.fieldName === "数量核算");
            let tableQuantity = tableContentList.value.findIndex((i) => i.dataSource === "#AA_QUANTITY");
            if (isShowQuantity) {
                if (quantity > -1 && tableQuantity > -1) {
                    tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("]", "+数量核算]");
                    tableContentList.value[tableAbstract].width += tableContentList.value[tableQuantity].width;

                    if (tableAbstract > tableQuantity) {
                        tableContentList.value[tableAbstract].footerStyle = tableContentList.value[tableQuantity].footerStyle;
                        tableContentList.value[tableAbstract].footerHeight = tableContentList.value[tableQuantity].footerHeight;

                        [tableContentList.value[tableAbstract], tableContentList.value[tableQuantity]] = [
                            tableContentList.value[tableQuantity],
                            tableContentList.value[tableAbstract],
                        ];
                        [tableAbstract, tableQuantity] = [tableQuantity, tableAbstract];
                    }
                    tableContentList.value.splice(tableQuantity, 1);
                }
            } else {
                if (quantity > -1 && tableQuantity === -1) {
                    addTableContent(selectSeniorTableData[quantity]);
                    tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("+数量核算", "");
                    tableContentList.value[tableAbstract].width -= MIN_CELL_WIDTH;
                }
            }
        }
    } else {
        let selectSeniorData = seniorModuleList.value[1].seniorFieldList.find((i) => i.fieldName === columnName);
        switch (columnName) {
            case "科目编码":
                if (subjectName > -1 && isMergeSubject) {
                    let tableSubjectName = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_NAME");
                    let tableSubjectCode = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_CODE");
                    if (selectSeniorData?.checked) {
                        // 科目编码和科目名称都存在
                        if (tableSubjectCode > -1) {
                            //删除科目编码，科目名称变为科目
                            if (tableContentList.value[tableSubjectName].label === "科目名称") {
                                tableContentList.value[tableSubjectName].label = "科目";
                            }

                            tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                                "[=科目名称",
                                "[=科目编码+科目名称"
                            );
                            tableContentList.value[tableSubjectName].width += tableContentList.value[tableSubjectCode].width;
                            tableContentList.value.splice(tableSubjectCode, 1);
                        }
                    } else {
                        // 科目编码和科目名称都存在
                        if (tableSubjectCode === -1) {
                            //删除科目编码，科目名称变为科目
                            if (tableContentList.value[tableSubjectName].label === "科目") {
                                tableContentList.value[tableSubjectName].label = "科目名称";
                            }
                            tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                                "[=科目编码+科目名称",
                                "[=科目名称"
                            );
                            tableContentList.value[tableSubjectName].width -= MIN_CELL_WIDTH;
                            seniorTableData.value.width -= MIN_CELL_WIDTH;
                        }
                    }
                }
                break;
            case "科目名称":
            case "科目":
                if (selectSeniorData?.checked) {
                    // 添加科目名称列
                    let tableSubjectName = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_NAME");
                    let tableSubjectCode = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_CODE");

                    if (tableSubjectCode > -1 && isMergeSubject) {
                        // 科目编码和科目名称都存在
                        tableContentList.value[tableSubjectName].label = "科目";
                        tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                            "[=科目名称",
                            "[=科目编码+科目名称"
                        );
                        tableContentList.value[tableSubjectName].width += tableContentList.value[tableSubjectCode].width;
                        tableContentList.value[tableSubjectName].headerStyle = tableContentList.value[tableSubjectCode].headerStyle;
                        tableContentList.value[tableSubjectName].bodyStyle = tableContentList.value[tableSubjectCode].bodyStyle;
                        if (tableSubjectCode < tableSubjectName) {
                            tableContentList.value[tableSubjectName].footerStyle = tableContentList.value[tableSubjectCode].footerStyle;
                            tableContentList.value[tableSubjectName].footerHeight = tableContentList.value[tableSubjectCode].footerHeight;

                            [tableContentList.value[tableSubjectName], tableContentList.value[tableSubjectCode]] = [
                                tableContentList.value[tableSubjectCode],
                                tableContentList.value[tableSubjectName],
                            ];
                            [tableSubjectName, tableSubjectCode] = [tableSubjectCode, tableSubjectName];
                        }
                        tableContentList.value.splice(tableSubjectCode, 1);
                    }
                    tableSubjectName = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_NAME");
                    let tableAssistingAccounting = tableContentList.value.findIndex((i) => i.dataSource === "#AA_NAME");
                    if (tableAssistingAccounting > -1 && isMergeAAE) {
                        // 辅助核算存在
                        tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                            "]",
                            "+辅助核算]"
                        );
                        tableContentList.value[tableSubjectName].width += tableContentList.value[tableAssistingAccounting].width;

                        if (tableAssistingAccounting < tableSubjectName) {
                            tableContentList.value[tableSubjectName].footerStyle =
                                tableContentList.value[tableAssistingAccounting].footerStyle;
                            tableContentList.value[tableSubjectName].footerHeight =
                                tableContentList.value[tableAssistingAccounting].footerHeight;

                            [tableContentList.value[tableSubjectName], tableContentList.value[tableAssistingAccounting]] = [
                                tableContentList.value[tableAssistingAccounting],
                                tableContentList.value[tableSubjectName],
                            ];
                            [tableSubjectName, tableAssistingAccounting] = [tableAssistingAccounting, tableSubjectName];
                        }
                        tableContentList.value.splice(tableAssistingAccounting, 1);
                    }
                } else {
                    // 减少科目名称列。如果勾选了科目编码和辅助核算，并且在合并的情况下，则增加列
                    let subjectCode = selectSeniorTableData.findIndex((i) => i.fieldName === "科目编码");
                    let assistingAccounting = selectSeniorTableData.findIndex((i) => i.fieldName === "辅助核算");
                    if (subjectCode > -1 && isMergeSubject) {
                        // restoreEle.width -= 50;
                        addTableContent(selectSeniorTableData[subjectCode], targetIndex, restoreEle);
                    }
                    if (assistingAccounting > -1 && isMergeAAE) {
                        // restoreEle.width -= 50;
                        addTableContent(selectSeniorTableData[assistingAccounting], targetIndex, restoreEle);
                    }
                }
                break;
            case "辅助核算":
                if (subjectName > -1 && isMergeAAE) {
                    let tableSubjectName = tableContentList.value.findIndex((i) => i.dataSource === "#ASUB_NAME");
                    let tableAssistingAccounting = tableContentList.value.findIndex((i) => i.dataSource === "#AA_NAME");
                    if (selectSeniorData?.checked) {
                        // 辅助核算存在
                        if (tableAssistingAccounting > -1) {
                            //删除辅助核算，科目变为科目+辅助核算
                            // tableContentList.value[tableSubjectName].label = "科目";
                            tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                                "]",
                                "+辅助核算]"
                            );
                            tableContentList.value[tableSubjectName].width += tableContentList.value[tableAssistingAccounting].width;
                            tableContentList.value.splice(tableAssistingAccounting, 1);
                        }
                    } else {
                        //删除辅助核算，科目变为科目+辅助核算
                        tableContentList.value[tableSubjectName].text = tableContentList.value[tableSubjectName].text.replace(
                            "+辅助核算",
                            ""
                        );
                        tableContentList.value[tableSubjectName].width -= MIN_CELL_WIDTH;
                        seniorTableData.value.width -= MIN_CELL_WIDTH;
                    }
                }
                break;
            case "摘要":
                if (selectSeniorData?.checked) {
                    let tableAbstract = tableContentList.value.findIndex((i) => i.dataSource === "#DESCRIPTION");
                    let foreignCurrency = selectSeniorTableData.findIndex((i) => i.fieldName === "外币核算");
                    let tableForeignCurrency = tableContentList.value.findIndex((i) => i.dataSource === "#AA_FC");
                    if (isShowFC) {
                        if (foreignCurrency > -1 && tableForeignCurrency > -1) {
                            tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace(
                                "]",
                                "+外币核算]"
                            );
                            tableContentList.value[tableAbstract].width += tableContentList.value[tableForeignCurrency].width;

                            if (tableAbstract > tableForeignCurrency) {
                                tableContentList.value[tableAbstract].footerStyle =
                                    tableContentList.value[tableForeignCurrency].footerStyle;
                                tableContentList.value[tableAbstract].footerHeight =
                                    tableContentList.value[tableForeignCurrency].footerHeight;
                                [tableContentList.value[tableAbstract], tableContentList.value[tableForeignCurrency]] = [
                                    tableContentList.value[tableForeignCurrency],
                                    tableContentList.value[tableAbstract],
                                ];
                                [tableAbstract, tableForeignCurrency] = [tableForeignCurrency, tableAbstract];
                            }
                            tableContentList.value.splice(tableForeignCurrency, 1);
                        }
                    }

                    tableAbstract = tableContentList.value.findIndex((i) => i.dataSource === "#DESCRIPTION");
                    let quantity = selectSeniorTableData.findIndex((i) => i.fieldName === "数量核算");
                    let tableQuantity = tableContentList.value.findIndex((i) => i.dataSource === "#AA_QUANTITY");
                    if (isShowQuantity) {
                        if (quantity > -1 && tableQuantity > -1) {
                            tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace(
                                "]",
                                "+数量核算]"
                            );
                            tableContentList.value[tableAbstract].width += tableContentList.value[tableQuantity].width;

                            if (tableAbstract > tableQuantity) {
                                tableContentList.value[tableAbstract].footerStyle = tableContentList.value[tableQuantity].footerStyle;
                                tableContentList.value[tableAbstract].footerHeight = tableContentList.value[tableQuantity].footerHeight;

                                [tableContentList.value[tableAbstract], tableContentList.value[tableQuantity]] = [
                                    tableContentList.value[tableQuantity],
                                    tableContentList.value[tableAbstract],
                                ];
                                [tableAbstract, tableQuantity] = [tableQuantity, tableAbstract];
                            }
                            tableContentList.value.splice(tableQuantity, 1);
                        }
                    }
                } else {
                    // 减少摘要列，如果合并了外币核算、数量核算，需要增加列
                    let foreignCurrency = selectSeniorTableData.findIndex((i) => i.fieldName === "外币核算");
                    let quantity = selectSeniorTableData.findIndex((i) => i.fieldName === "数量核算");
                    if (foreignCurrency > -1 && isShowFC) {
                        addTableContent(selectSeniorTableData[foreignCurrency], targetIndex, restoreEle);
                    }
                    if (quantity > -1 && isShowQuantity) {
                        addTableContent(selectSeniorTableData[quantity], targetIndex, restoreEle);
                    }
                }

                break;
            case "外币核算":
                if (isShowFC && abstract > -1) {
                    let tableAbstract = tableContentList.value.findIndex((i) => i.dataSource === "#DESCRIPTION");
                    let tableForeignCurrency = tableContentList.value.findIndex((i) => i.dataSource === "#AA_FC");
                    if (selectSeniorData?.checked) {
                        tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("]", "+外币核算]");
                        tableContentList.value[tableAbstract].width += tableContentList.value[tableForeignCurrency].width;
                        tableContentList.value.splice(tableForeignCurrency, 1);
                    } else {
                        tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("+外币核算", "");
                        tableContentList.value[tableAbstract].width -= MIN_CELL_WIDTH;
                        seniorTableData.value.width -= MIN_CELL_WIDTH;
                    }
                }
                break;
            case "数量核算":
                if (isShowQuantity && abstract > -1) {
                    let tableAbstract = tableContentList.value.findIndex((i) => i.dataSource === "#DESCRIPTION");
                    let tableQuantity = tableContentList.value.findIndex((i) => i.dataSource === "#AA_QUANTITY");
                    if (selectSeniorData?.checked) {
                        tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("]", "+数量核算]");
                        tableContentList.value[tableAbstract].width += tableContentList.value[tableQuantity].width;
                        tableContentList.value.splice(tableQuantity, 1);
                    } else {
                        tableContentList.value[tableAbstract].text = tableContentList.value[tableAbstract].text.replace("+数量核算", "");
                        tableContentList.value[tableAbstract].width -= MIN_CELL_WIDTH;
                        seniorTableData.value.width -= MIN_CELL_WIDTH;
                    }
                }
                break;
        }
    }
    // >>??合理性
    if (tableContentList.value.length === 1) {
        if (!tableContentList.value[0].footerHeight) {
            tableContentList.value[0].footerHeight = tableContentList.value[0].headerHeight;
        }
        if (JSON.stringify(tableContentList.value[0].footerStyle) === "{}") {
            tableContentList.value[0].footerStyle = getBaseStyle();
        }
        seniorTableData.value.width = tableContentList.value[0].width;
    }
    //按照顺序展示打印
    if (isShowFC && isShowQuantity) {
        let tableAbstract = tableContentList.value.findIndex((i) => i.text.includes("外币核算") && i.text.includes("数量核算"));
        if (tableAbstract > -1) {
            tableContentList.value[tableAbstract].text = "[=摘要+外币核算+数量核算]";
        }
    }
}

function updateBodySet(value?: any) {
    value && (bodySetCheck.value = value);
    // todo 更新设置完成后去调用上面的方法
    updateBodySetPlus();
    return;
}

// 没有列先不考虑吧
function addTableContent(field: any, abstract: number = -1, restoreEle: any = null) {
    console.log(restoreEle, tableContentList.value[0].headerHeight);
    // restoreEle?.width ||
    const data = {
        id: field.order,
        label: field.fieldName,
        text: `[=${field.fieldName}]`,
        width: MIN_CELL_WIDTH,
        headerHeight: tableContentList.value[0].headerHeight,
        bodyHeight: tableContentList.value[0].bodyHeight,
        footerHeight: tableContentList.value[0].footerHeight,
        dataSource: field.dataSource,
        inputable: false,
        headerStyle: restoreEle?.headerStyle || getBaseStyle(),
        bodyStyle: getBaseStyle(),
        footerStyle: getBaseStyle(),
    } as ITableList;

    if (abstract === -1) {
        tableContentList.value.push(data);
    } else {
        tableContentList.value.splice(abstract, 0, data);
    }
}

function bodySet(e: MouseEvent) {
    e.stopPropagation();
    bodySettingShow.value = true;
}

//工具map
//针对想要三分之一显示，设置默认宽度，20可提取为默认控件间间距
const defaultTextWidth = computed(() => {
    return (Number(getPageContentWH.value.width.replace("px", "")) - 20) / 3 - 1;
});

const typeSize = computed(() => {
    return new Map([
        ["text", { width: defaultTextWidth.value, height: 30, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
        ["textNoLabel", { width: defaultTextWidth.value, height: 30, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
        ["rectangle", { width: 100, height: 100, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
        ["img", { width: 150, height: 150, sticks: ["tl", "tm", "tr", "mr", "br", "bm", "bl", "ml"] }],
        ["line", { width: 200, height: 20, sticks: ["mr", "ml"] }],
    ]);
});
// 监听shift是否按下
const shiftKey = ref(false);
const copyData = ref();
const copyTime = ref(1);

// 监听shift ctrl是否按下,并监听删除按钮
function ctrlKeyChange() {
    document.onkeydown = function (e) {
        if (e.key === "Shift" && !e.altKey) {
            shiftKey.value = true;
        } else {
            shiftKey.value = false;
        }
        if (e.ctrlKey && e.key === "z") {
            preHistory();
            return false;
        }
        if (e.ctrlKey && e.key === "y") {
            nextHistory();
            return false;
        }
        if (e.ctrlKey && e.key === "c") {
            const activeElement = document.activeElement;
            if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
                return;
            }
            copyTime.value = 1;
            copyData.value = _.cloneDeep(Array.from(activeEle.value));
            return false;
        }
        if (e.ctrlKey && e.key === "v") {
            const activeElement = document.activeElement;
            if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
                return;
            }

            if (!copyData.value) return;
            copyData.value.forEach((item: any) => {
                if (item.id > 20 || [17, 18, 19].includes(item.id)) {
                    addDragBox(
                        item.type,
                        {
                            id: keyId.value++,
                            top: item.top + 10 * copyTime.value,
                            left: item.left + 10 * copyTime.value,
                            height: item.height,
                            width: item.width,
                            positionType: item.positionType,
                            label: item.label,
                            content: item.text,
                            dataSource: item.dataSource,
                            style: {...item.style},
                        },
                        false,
                        false,
                        true
                    );
                }
            });
            copyTime.value++;
            return false;
        }

        if (e.key === "Delete") {
            const activeEle = seniorModuleData.value.filter((i) => i.active);
            if (activeEle.length) {
                activeEle.forEach((item: any) => {
                    seniorModuleList.value.forEach((module) => {
                        module.seniorFieldList.forEach((field) => {
                            if (field.fieldName === item.text) {
                                field.checked = false;
                            }
                        });
                    });
                    if (item.type === "table") {
                        tableContentList.value = [tableContentList.value[0]];
                        seniorTableData.value.width = tableContentList.value[0].width;
                    }
                });
            }
            const lastBaxArr = seniorModuleData.value.filter((i) => !i.active || i.id === 20);
            syncSeniorMoudle();
            seniorModuleData.value = lastBaxArr;
            addHistory();
        }
        const target = e.target as HTMLElement;
        if (target && target.className !== "el-textarea__inner") {
            if (e.key === "ArrowUp" && Array.from(activeEle.value).length) {
                e.preventDefault();
                e.stopPropagation();
                // find min top
                let minTopItem = Array.from(activeEle.value).reduce(
                    (a, b) => (a === null || (a as IModuleData).top > (b as IModuleData).top ? b : a),
                    null
                ) as IModuleData;
                if (minTopItem.top === 0) {
                    return;
                }
                if (judgeOverLapTableAndNoRetore(minTopItem)) {
                    return;
                }

                activeEle.value.forEach((item: any) => {
                    if (item.top > 1) item.top = item.top - 1;
                    // judgeOverLapTable(item);
                });
                addHistory();
                // historySeniorModuleData.value = _.cloneDeep(seniorModuleData.value);
            }
            if (e.key === "ArrowDown" && Array.from(activeEle.value).length) {
                const activeElement = document.activeElement;
                // 检查是否是 <input> 元素，且类型为 text 或 password
                if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
                    return;
                }

                e.preventDefault();
                e.stopPropagation();
                // find max bottom
                let maxBottomItem = Array.from(activeEle.value).reduce(
                    (a, b) =>
                        a === null ||
                        (a as IModuleData).top + (a as IModuleData).height < (b as IModuleData).top + (b as IModuleData).height
                            ? b
                            : a,
                    null
                ) as IModuleData;
                if (maxBottomItem.top + maxBottomItem.height === getMainContentBoxDimension().height) {
                    return;
                }
                if (judgeOverLapTableAndNoRetore(maxBottomItem)) {
                    return;
                }

                activeEle.value.forEach((item: any) => {
                    if (item.top < getMainContentBoxDimension().height - 1) item.top = item.top + 1;
                    // judgeOverLapTable(item);
                });
                addHistory();
                // historySeniorModuleData.value = _.cloneDeep(seniorModuleData.value);
            }
            if (e.key === "ArrowLeft" && Array.from(activeEle.value).length) {
                const activeElement = document.activeElement;
                // 检查是否是 <input> 元素，且类型为 text 或 password
                if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
                    return;
                }

                e.preventDefault();
                e.stopPropagation();
                let minLeftItem = Array.from(activeEle.value).reduce(
                    (a, b) => (a === null || (a as IModuleData).left > (b as IModuleData).left ? b : a),
                    null
                ) as IModuleData;
                if (minLeftItem.left === 0) {
                    return;
                }
                if (judgeOverLapTableAndNoRetore(minLeftItem)) {
                    return;
                }

                activeEle.value.forEach((item: any) => {
                    if (item.left > 1) item.left = item.left - 1;
                    // judgeOverLapTable(item);
                });
                addHistory();
                // historySeniorModuleData.value = _.cloneDeep(seniorModuleData.value);
            }
            if (e.key === "ArrowRight" && Array.from(activeEle.value).length) {
                const activeElement = document.activeElement;
                // 检查是否是 <input> 元素，且类型为 text 或 password
                if (activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA")) {
                    return;
                }

                e.preventDefault();
                e.stopPropagation();
                let maxRightItem = Array.from(activeEle.value).reduce(
                    (a, b) =>
                        a === null ||
                        (a as IModuleData).left + (a as IModuleData).width < (b as IModuleData).left + (b as IModuleData).width
                            ? b
                            : a,
                    null
                ) as IModuleData;
                if (maxRightItem.left + maxRightItem.width === getMainContentBoxDimension().width) {
                    return;
                }
                if (judgeOverLapTableAndNoRetore(maxRightItem)) {
                    return;
                }

                activeEle.value.forEach((item: any) => {
                    if (item.left < getMainContentBoxDimension().width - 1) item.left = item.left + 1;
                    // judgeOverLapTable(item);
                });
                addHistory();
                // historySeniorModuleData.value = _.cloneDeep(seniorModuleData.value);
            }
        }
    };
    document.onkeyup = function (e) {
        shiftKey.value = false;
    };
}
// mm转换为整数px
function mmToPx(mm: number) {
    //1mm = 2.83pt 1pt = 1.33px 纸张给了pt只需要转化为pt
    return Math.round(mm * 2.83);
}
function mmToPxWithDigit(mm: number) {
    //1mm = 2.83pt 1pt = 1.33px 纸张给了pt只需要转化为pt
    return mm * 2.83;
}

//打印模版数据
const directionDisabled = computed({
    get() {
        return ![3, 4, 7, 8].includes(printInfo.value.printType);
    },
    set(value: boolean) {
        return value;
    },
});
function handleMargin(e: any, direction: string) {
    switch (direction) {
        case "top":
            handleMarginTop(e.target.value, e);
            break;
        case "left":
            handleMarginLeft(Number(e.target.value), e);
            break;
    }
}
function handleMarginTop(val: number, e?: any) {
    let marginTop = Number(val);
    if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0 || Number(val) < 0) {
        val = 25;
        ElNotify({ type: "warning", message: "请输入正整数边距~" });
    }
    if (marginTop > maxTBMargin.value - minBottomMargin.value) {
        ElNotify({ type: "warning", message: "最高可设置" + maxTBMargin.value + "边距" });
        marginTop = getMaxMargin(printInfo.value.printType, "top") - minBottomMargin.value;
    }
    e && (e.target.value = Number(marginTop));
    printInfo.value.marginTop = marginTop;
    printInfo.value.marginBottom = maxTBMargin.value - printInfo.value.marginTop;
}

function handleMarginBottom(val: number) {
    const marginBottom = val;
    printInfo.value.marginBottom = marginBottom;
    printInfo.value.marginTop = maxTBMargin.value - marginBottom;
}

function limitMarginBottom() {
    if (printInfo.value.isShowPrintDate || printInfo.value.isShowPageNumber) {
        if (printInfo.value.marginBottom < minBottomMargin.value) {
            handleMarginBottom(minBottomMargin.value);
        }
    }
}

function handleMarginLeft(val: number, e?: any) {
    if (Number(val + "") + "" === "NaN" || Number(val + "") % 1 !== 0 || Number(val) < 0) {
        val = 25;
        ElNotify({ type: "warning", message: "请输入正整数边距~" });
    }
    if (val > 35) {
        ElNotify({ type: "warning", message: "最高可设置35边距" });
        //为什么是25
        val = 25;
    }
    e && (e.target.value = Number(val));
    printInfo.value.marginLeft = val;
    printInfo.value.marginRight = 35 - printInfo.value.marginLeft;
}

const maxTBMargin = computed({
    get() {
        return getMaxMargin(printInfo.value.printType);
    },
    set(value: number) {
        return value;
    },
});
const minBottomMargin = computed(() => {
    if (printInfo.value.isShowPrintDate || printInfo.value.isShowPageNumber) {
        return printInfo.value.printType === 2 || printInfo.value.printType === 3 ? 8 : 12;
    }
    return 0;
});

const collapseIndex = ref(3);
// const seniorModuleList = ref(moduleList);
const seniorModuleList = ref(_.cloneDeep(moduleList));
//btnItem, moudleItem.moudleType, moudleItem.TableId
function changeSeniorMoudle(item: any, order: number) {
    item.checked = !item.checked;
    if (item.checked) {
        const { mainWidth, mainHeight } = getPagerWhNumber();
        let headResult: IModuleData | null = null as IModuleData | null;
        let targetTop = 0;
        let targetLeft = 0;
        let maxLeft = -Infinity;
        let maxTop = -Infinity;
        let targetList;
        let addBody: any;
        let firstCol = tableContentList.value[0];
        switch (order) {
            case 2:
            case 4:
                //找最后一个head
                // headResult = seniorModuleData.value.reduce(
                //     (acc: IResult, current, index) => {
                //         if (current.positionType === order) {
                //             return { module: current, index: index };
                //         }
                //         return acc;
                //     },
                //     { module: null, index: -1 }
                // );
                // 初始化最大的left和top值
                // 找到最右边和最底部的对象
                if (order === 2) {
                    targetList = seniorModuleData.value.filter(
                        (i) => i.positionType !== 3 && i.top + i.height <= seniorTableData.value.top
                    );
                } else {
                    targetList = seniorModuleData.value.filter(
                        (i) => i.positionType !== 3 && i.top > seniorTableData.value.top + seniorTableData.value.height
                    );
                }
                targetList.forEach((obj: any) => {
                    if (obj.top + obj.height > maxTop) {
                        maxTop = obj.top + obj.height;
                        maxLeft = obj.left + obj.width;
                        headResult = obj;
                    } else if (obj.top + obj.height === maxTop && obj.left + obj.width > maxLeft) {
                        maxLeft = obj.left + obj.width;
                        headResult = obj;
                    }
                });
                if (headResult !== null) {
                    if (headResult.left + SPACE_BETWEEN + headResult.width + typeSize.value.get("text")!.width < mainWidth) {
                        targetTop = headResult.top;
                        targetLeft = headResult.left + SPACE_BETWEEN + headResult.width;
                    } else {
                        if (headResult.top + SPACE_BETWEEN + headResult.height + typeSize.value.get("text")!.height < mainHeight) {
                            targetTop = headResult.top + SPACE_BETWEEN + headResult.height;
                            targetLeft = 0;
                        } else {
                            targetTop = mainHeight - typeSize.value.get("text")!.height;
                            targetLeft = mainWidth - typeSize.value.get("text")!.width;
                        }
                        //如果表头放不下，表格下都往下挪
                        if (order === 2 && targetTop + typeSize.value.get("text")!.height > seniorTableData.value.top) {
                            seniorModuleData.value
                                .filter((i) => i.top > seniorTableData.value.top + seniorTableData.value.height)
                                .forEach((item) => {
                                    if (headResult) {
                                        item.top = item.top + SPACE_BETWEEN + headResult.height;
                                    }
                                });
                            seniorTableData.value.top = seniorTableData.value.top + SPACE_BETWEEN + headResult.height;
                        }
                    }
                } else {
                    targetTop = 0;
                    targetLeft = 0;
                }
                addDragBox(
                    item.order === 0 ? "textNoLabel" : "text",
                    {
                        id: item.order,
                        top: targetTop,
                        left: targetLeft,
                        height: headResult && order === 2 ? headResult.height : 0,
                        positionType: order,
                        label: item.fieldLabel + "：",
                        dataSource: item.dataSource,
                        content: item.fieldName,
                    },
                    true,
                    true
                );
                break;
            case 3:
                //找最后一个body
                addBody = {
                    id: item.order,
                    label: item.fieldLabel,
                    dataSource: item.dataSource,
                    headerHeight: firstCol ? firstCol.headerHeight : 35,
                    bodyHeight: firstCol ? firstCol.bodyHeight : 35,
                    footerHeight: item.order === 12 || item.order === 13 || item.order === 5 ? (firstCol ? firstCol.footerHeight : 35) : 0,
                    text: `[=${item.fieldLabel}]`,
                    width: MIN_CELL_WIDTH,
                    height: 100,
                    index: 1,
                    inputable: false,
                    headerStyle: getBaseStyle(),
                    bodyStyle: getBaseStyle(),
                    footerStyle: item.order === 12 || item.order === 13 || item.order === 5 ? getBaseStyle() : {},
                };
                if (seniorTableData.value.width + seniorTableData.value.left + MIN_CELL_WIDTH > mainWidth) {
                    seniorTableData.value.width = mainWidth - seniorTableData.value.left;
                    const average = Math.ceil(MIN_CELL_WIDTH / tableContentList.value.length);
                    let total = 0;
                    tableContentList.value.forEach((item, index) => {
                        if (index === tableContentList.value.length - 1) {
                            item.width = seniorTableData.value.width - total - MIN_CELL_WIDTH;
                        } else if (item.width - average >= MIN_CELL_SIZE) {
                            item.width -= average;
                            total += item.width;
                        } else {
                            item.width = MIN_CELL_SIZE;
                            total += MIN_CELL_SIZE;
                        }
                    });
                }
                // //序号第一位
                if (item.order === 5) {
                    const totalStyleIndex = tableContentList.value.findIndex((i) => i.footerStyle && ![12, 13].includes(i.id));
                    if (totalStyleIndex !== -1) {
                        tableContentList.value[totalStyleIndex].footerStyle = {};
                        tableContentList.value[totalStyleIndex].footerHeight = 0;
                    }
                    tableContentList.value.splice(0, 0, addBody);
                } else {
                    tableContentList.value.push(addBody);
                }
                updateBodySetPlus(item.fieldLabel);
                seniorTableData.value.width = tableContentList.value.reduce((acc, cur) => acc + cur.width, 0);
                break;
        }
    } else {
        let targetIndex: number;
        let restoreTotal;
        switch (order) {
            case 2:
            case 4:
                targetIndex = seniorModuleData.value.findIndex((i) => i.positionType === order && i.id === item.order);
                if (targetIndex === -1) {
                    return;
                }
                seniorModuleData.value.splice(targetIndex, 1);
                break;
            case 3:
                if (tableContentList.value.length === 1) {
                    ElNotify({ type: "warning", message: "亲，至少保留一个标题字段哦~" });
                    item.checked = true;
                    return;
                }
                targetIndex = tableContentList.value.findIndex((i) => i.id === item.order);
                restoreTotal = tableContentList.value[targetIndex];
                if (targetIndex !== -1) {
                    tableContentList.value.splice(targetIndex, 1);
                }

                if (targetIndex === 0 && restoreTotal && tableContentList.value.length > 0) {
                    tableContentList.value[0].footerStyle = restoreTotal.footerStyle;
                    tableContentList.value[0].footerHeight = restoreTotal.footerHeight;
                }

                updateBodySetPlus(item.fieldLabel, targetIndex, restoreTotal);
                seniorTableData.value.width = tableContentList.value.reduce((acc, cur) => acc + cur.width, 0);
                break;
        }
    }
    addHistory();
}
function syncSeniorMoudle() {
    seniorModuleList.value.forEach((module) => {
        module.seniorFieldList.forEach((field) => {
            field.checked = false;
        });
    });
    seniorModuleData.value.forEach((item: any) => {
        //可整合
        if (item.positionType === 2 || item.positionType === 4) {
            const checkItem = seniorModuleList.value
                .find((i) => i.order === item.positionType)!
                .seniorFieldList.find((j) => j.dataSource === item.dataSource);
            if (checkItem) {
                checkItem.checked = true;
            }
        }
    });

    const seniorFieldList = seniorModuleList.value.find((i) => i.order === 3)?.seniorFieldList || [];
    tableContentList.value.forEach((item) => {
        seniorFieldList.forEach((field) => {
            if (item.text.includes(field.fieldName)) {
                field.checked = true;
            }
        });
    });
}

const leftNavShow = ref(true);
const rightNavShow = ref(true);
// 字体类型
const selectFontType = ref("SimSun");
// "Source Han Sans SC"
const fontTypeList = ref([
    { label: "宋体", value: "SimSun", style: "SimSun" },
    { label: "楷体", value: "KaiTi", style: "KaiTi" },
    { label: "黑体", value: "SimHei", style: "SimHei" },
]);

const selectStyleList = ref<IStyle[]>([]);

// 字体大小列表
const selectFontSize = ref(10);

const barCanEdit = computed(() => {
    return selectStyleList.value.length > 0;
});
const isBold = ref(false);
const isUnderline = ref(false);
const isItalic = ref(false);
const selectTextAlign = ref("");
const selectWhiteSpace = ref("");
const selectBoxAlign = ref(new Set());
// 选择需要修改的样式组件
function selectStyle(data: IStyle) {
    // 当按住shift时为多选
    if (shiftKey.value) {
        data.isSelected = true;
        selectStyleList.value.push(data);
    } else {
        selectStyleList.value.forEach((i) => (i.isSelected = false));
        data.isSelected = true;
        selectStyleList.value = [data];
    }
    selectFontType.value = data.fontFamily;
    selectFontSize.value = data.fontSize;
    isBold.value = data.fontWeight === "bold";
    isUnderline.value = data.textDecoration === "underline";
    isItalic.value = data.fontStyle === "italic";
    selectTextAlign.value = data.textAlign;
    if (data.shadowWhiteSpace) {
        selectWhiteSpace.value = data.shadowWhiteSpace;
    } else {
        selectWhiteSpace.value = data.whiteSpace;
    }
    rightNavShow.value = true;
}

// 改变字体样式
function changeBaseStyle(type: string, val: boolean | string | number) {
    if (val === "Reduce") {
        selectStyleList.value.forEach((i) => {
            const header = tableContentList.value.find((j) => j.headerStyle === i);
            const body = tableContentList.value.find((j) => j.bodyStyle === i);
            const footer = tableContentList.value.find((j) => j.footerStyle === i);
            if (header) {
                header.headerStyle.shadowFontSize = header.headerStyle.fontSize;
                header.headerStyle.shadowWhiteSpace = "Reduce";
                if (!canTextFitInDiv(header.label, header.headerStyle.fontSize * zoom.value, header.width, header.headerHeight)) {
                    header.headerStyle.fontSize = 7;
                    header.headerStyle.whiteSpace = "Wrap";
                }
            } else if (body) {
                body.bodyStyle.shadowFontSize = body.bodyStyle.fontSize;
                body.bodyStyle.shadowWhiteSpace = "Reduce";
                if (!canTextFitInDiv(body.text, body.bodyStyle.fontSize * zoom.value, body.width, body.bodyHeight)) {
                    body.bodyStyle.fontSize = 7;
                    body.bodyStyle.whiteSpace = "Wrap";
                }
            } else if (footer) {
                footer.footerStyle.shadowFontSize = footer.footerStyle.fontSize;
                footer.footerStyle.shadowWhiteSpace = "Reduce";
                let text = "合计：";
                if (footer.id === 12) {
                    text = "[=借方金额合计]";
                } else if (footer.id === 13) {
                    text = "[=贷方金额合计]";
                }
                if (!canTextFitInDiv(text, footer.footerStyle.fontSize * zoom.value, footer.width, footer.footerHeight)) {
                    footer.footerStyle.fontSize = 7;
                    footer.footerStyle.whiteSpace = "Wrap";
                }
            }
        });
        const activeEle = seniorModuleData.value.filter((i) => i.active);
        if (activeEle.length) {
            activeEle.forEach((item: IModuleData) => {
                item.style.shadowFontSize = item.style.fontSize;
                item.style.shadowWhiteSpace = "Reduce";
                if (!canTextFitInDiv(item.label, item.style.fontSize * zoom.value, item.width, item.height)) {
                    item.style.fontSize = 7;
                    item.style.whiteSpace = "Wrap";
                }
            });
        }
    } else {
        selectStyleList.value.forEach((i) => (i[type] = val));
    }
    addHistory();
}
// 改变字体粗细
function changeBold(type: boolean) {
    if (!barCanEdit.value) return;
    isBold.value = type;
    changeBaseStyle("fontWeight", type ? "bold" : "normal");
}
// 改变字体下划线
function changeUnderline(type: boolean) {
    if (!barCanEdit.value) return;
    isUnderline.value = type;
    changeBaseStyle("textDecoration", type ? "underline" : "none");
}
// 改变字体斜体
function changeItalic(type: boolean) {
    if (!barCanEdit.value) return;
    isItalic.value = type;
    changeBaseStyle("fontStyle", type ? "italic" : "normal");
}
// 改变文本对齐方式
function changeTextAlign(type: string) {
    if (!barCanEdit.value) return;
    selectTextAlign.value = type;
    changeBaseStyle("textAlign", type);
}
// 改变文本超长处理方式
function changeWhiteSpace(type: string) {
    if (!barCanEdit.value) return;
    selectWhiteSpace.value = type;
    changeBaseStyle("whiteSpace", type);
}

// 改变控件对齐方式
function changeBoxAlign(type: string) {
    selectBoxAlign.value.add(type);
    const { width: mainWidth, height: mainHeight } = getMainContentBoxDimension();
    let maxLeft: number;
    let maxRight: number;
    let maxTop: number;
    let maxBottom: number;
    let isActiveBoxArr = [
        ...seniorModuleData.value.filter((i) => {
            if (i.active) {
                maxLeft = maxLeft === undefined || maxLeft >= i.left ? i.left : maxLeft;
                maxRight = maxRight === undefined || maxRight <= i.left + i.width ? i.left + i.width : maxRight;
                maxTop = maxTop === undefined || maxTop >= i.top ? i.top : maxTop;
                maxBottom = maxBottom === undefined || maxBottom <= i.top + i.height ? i.top + i.height : maxBottom;
            }
            return i.active;
        }),
    ];
    if (isActiveBoxArr.length === 0) return;
    const isOne = isActiveBoxArr.length === 1;
    switch (type) {
        case "left":
            isActiveBoxArr.forEach((box, index) => {
                box.left = isOne ? 0 : maxLeft;
            });
            break;
        case "right":
            isActiveBoxArr.forEach((box, index) => {
                box.left = isOne ? mainWidth - box.width : maxRight - box.width;
            });
            break;
        case "xCenter":
            isActiveBoxArr.forEach((item) => {
                let width = item.width;
                let left = maxLeft + Math.round((maxRight - maxLeft - width) / 2);
                item.left = isOne ? Math.round((mainWidth - width) / 2) : left;
            });
            break;
        case "top":
            isActiveBoxArr.forEach((box, index) => {
                box.top = isOne ? 0 : maxTop;
            });
            break;
        case "bottom":
            isActiveBoxArr.forEach((box, index) => {
                box.top = isOne ? mainHeight - box.height : maxBottom - box.height;
            });
            break;
        case "yCenter":
            isActiveBoxArr.forEach((item) => {
                let height = item.height;
                let top = maxTop + Math.round((maxBottom - maxTop - height) / 2);
                item.top = isOne ? Math.round((mainHeight - height) / 2) : top;
            });
            break;
        default:
            break;
    }
    addHistory();
}

//历史记录
const historyArr = ref<IHistory[]>([]);
const historyIndex = ref(-1);
const ptLayout = ref();
interface IHistory {
    timestamp: number;
    moudle: IModuleData[];
    table: ITableList[];
    printInfo: ISeniorPrintInfo;
    zoom: number;
}
function addHistory() {
    const cloneSeniorModuleData = _.cloneDeep(seniorModuleData.value);
    const cloneTableContentList = _.cloneDeep(tableContentList.value);
    const history = {
        timestamp: new Date().getTime(),
        moudle: cloneSeniorModuleData,
        table: cloneTableContentList,
        printInfo: _.cloneDeep(printInfo.value),
        zoom: zoom.value,
    };

    historyArr.value.splice(historyIndex.value + 1, historyArr.value.length - historyIndex.value - 1, history);
    historyIndex.value++;
    historySeniorModuleData.value = _.cloneDeep(historyArr.value[historyIndex.value].moudle);
}
function preHistory() {
    if (historyIndex.value < 0) return;
    historyIndex.value--;
    const clonePtLayout = _.cloneDeep(ptLayout.value);
    const historyJosn = historyIndex.value == -1 ? clonePtLayout : historyArr.value[historyIndex.value];
    const cloneSeniorModuleData = _.cloneDeep(historyJosn.moudle);
    const cloneTableContentList = _.cloneDeep(historyJosn.table);
    if (historyJosn.zoom !== zoom.value) {
        zoomChange(historyJosn.zoom, zoom.value, cloneSeniorModuleData, cloneTableContentList);
    }
    seniorModuleData.value = cloneSeniorModuleData;
    tableContentList.value = cloneTableContentList;
    printInfo.value = _.cloneDeep(historyJosn.printInfo);
    syncSeniorMoudle();
}
function nextHistory() {
    if (historyArr.value.length === 0 || historyIndex.value === historyArr.value.length - 1) return;
    historyIndex.value++;
    const historyJosn = historyArr.value[historyIndex.value];
    const cloneSeniorModuleData = _.cloneDeep(historyJosn.moudle);
    const cloneTableContentList = _.cloneDeep(historyJosn.table);
    if (historyJosn.zoom !== zoom.value) {
        zoomChange(historyJosn.zoom, zoom.value, cloneSeniorModuleData, cloneTableContentList);
    }
    seniorModuleData.value = cloneSeniorModuleData;
    tableContentList.value = cloneTableContentList;
    printInfo.value = _.cloneDeep(historyJosn.printInfo);
    syncSeniorMoudle();
}

function changePagerSize(val: number) {
    let direction = "";
    let marginTop = 0;
    let marginBottom = 0;
    if ([4, 3, 7, 8].findIndex((item) => item === val) > -1) {
        direction = "H";
        if (val === 4) {
            marginTop = 9;
            marginBottom = 12;
        } else if (val === 3) {
            marginTop = 9;
            marginBottom = 9;
        } else {
            marginTop = 14;
            marginBottom = 15;
        }
    } else if ([1, 2, 6].findIndex((item) => item === val) > -1) {
        direction = "Z";
        if (val === 2) {
            marginTop = 5;
            marginBottom = 8;
        } else if (val === 1) {
            marginTop = 14;
            marginBottom = 15;
        } else if (val === 6) {
            marginTop = 14;
            marginBottom = 15;
        }
    }
    printInfo.value.direction = direction;
    printInfo.value.marginTop = marginTop;
    printInfo.value.marginBottom = marginBottom;
    setDragSize();

    window.dispatchEvent(new CustomEvent("refreshPageSize", { detail: { printType: val, direction: direction } }));
    if (!checkNoticeSettings(1) || checkNoticeSettings(1) === "false") {
        noticeChangeType.value = 1;
        noticeChange.value = true;
    }
}
function changePagerDirection() {
    if (!checkNoticeSettings(2)) {
        noticeChangeType.value = 2;
        noticeChange.value = true;
    }
    setDragSize();
}
const moudleInputRef = ref<any>([]);
const seniorModuleData = ref<IModuleData[]>([]);

const seniorTableData = computed({
    get() {
        return seniorModuleData.value.find((i) => i.type === "table")!;
    },
    set(value: IModuleData) {
        return value;
    },
});

const tableContentList = ref<ITableList[]>(transformTableData(seniorModuleData.value));
const lastTotalIndex = ref(0);
const calcTotalWidth = computed({
    get() {
        let totalWidth = 0;
        tableContentList.value.some((curr, index) => {
            if (curr.id === 12 || curr.id === 13) {
                return true;
            }
            lastTotalIndex.value = index;
            totalWidth += curr.width;
            return false;
        });
        return totalWidth;
    },
    set(value: number) {
        return value;
    },
});

const historySeniorModuleData = ref<IModuleData[]>([]);

//生成样式对象
function getStyle(item: any, type: IStyle, height = 30, isTable = false) {
    let align = "center";
    //先整体靠上除了A4三版单位
    if (item.type !== "table" && item.top < seniorTableData.value.top) {
        align = item.id === 1 && printInfo.value.printType === 2 ? "flex-end": "flex-start";
    } else if (item.type !== "table" && item.top >= seniorTableData.value.top) {
        align = "flex-start";
    }
    const style: { [key: string]: string } = {
        height: `${height}px`,
        display: "flex",
        alignItems: align,
        fontFamily: fontTypeList.value.find((i) => i.value === type.fontFamily)!.style + " !important",
        fontSize: `${type.fontSize * zoom.value}px`,
        fontWeight: type.fontWeight,
        fontStyle: type.fontStyle,
        textDecoration: type.textDecoration,
        whiteSpace: type.whiteSpace == "Wrap" ? "normal" : "nowrap",
        wordBreak: type.whiteSpace == "Wrap" ? "break-all" : "normal",
        //是否有折行显示需要
        overflow: type.whiteSpace == "Wrap" ? "hidden" : "hidden",
        textAlign: type.textAlign,
        textAlignLast: type.textAlign,
        // justifyContent: type.textAlign == "center" ? "center" : type.textAlign == "right" ? "flex-end" : "flex-start",
        justifyContent: type.textAlign == "center" ? "center" : "flex-start",
    };
    if (type.isSelected) {
        const color = isErp.value ? "rgba(61, 127, 255, 0.1)" : "rgba(148, 196, 148, 0.1)";
        if (isTable && seniorTableData.value.inputable) {
            style.border = isErp.value ? "1px solid #3d7fff" : "1px solid rgb(68, 180, 73)";
            style.backgroundColor = color;
        } else {
            style.backgroundColor = color;
        }
    }
    if (type.fontStyle === "italic" && !item.inputable) {
        //会导致颜色不一致
        // style["-webkit-transform"] = "skew(5deg)";
    }
    return style;
}

function bodyDown(event: any, data: any) {
    if (!shiftKey.value) {
        deselect(event);
        if (!data.active) {
            seniorModuleData.value.forEach((data: any) => {
                data.active = false;
            });
            data.active = true;
        }
    } else {
        if (data.active) {
            activeEle.value.delete(data);
        } else {
            activeEle.value.add(data);
        }
        data.active = !data.active;
    }
}
const isDrag = ref(true);
function allowDrop(event: DragEvent) {
    event.preventDefault();
}
const overLapId = ref<number[]>([]);
function judgeOverLap(target: any, item: any, offset: number = 0) {
    //根据width,height.left,top判断元素是否有重叠
    if (
        target.left < item.left + item.width + offset &&
        target.left + target.width + offset > item.left &&
        target.top < item.top + item.height + offset &&
        target.top + target.height + offset > item.top
    ) {
        return true;
    }
    return false;
}
function judgeOverLapTableAndNoRetore(item: any) {
    let hasOverlap = false;
    for (let data of historySeniorModuleData.value) {
        if (item.id !== data.id && [item.type, data.type].includes("table") && judgeOverLap(item, data, 1)) {
            hasOverlap = true;
            break; // 提前退出循环
        }
    }
    return hasOverlap;
}
function judgeOverLapTable(item: any) {
    let hasOverlap = false;
    historySeniorModuleData.value.forEach((data: IModuleData, index: number) => {
        if (item.id !== data.id && [item.type, data.type].includes("table") && judgeOverLap(item, data)) {
            overLapId.value.push(data.id);
            hasOverlap = true;
        }
    });
    if (!hasOverlap) {
        addHistory();
        return true;
    } else {
        let clearOverLap = setTimeout(() => {
            overLapId.value = [];
            clearTimeout(clearOverLap);
        }, 1000);
        const targetItem = historySeniorModuleData.value.find((i) => i.id === item.id);
        if (targetItem) {
            Object.defineProperties(item, Object.getOwnPropertyDescriptors(targetItem));
            // const { left, top, width, height } = targetItem;
            // item = { ...item, left, top, width, height };
            // Object.assign(item, { left, top, width, height });
            return item;
        }
        return false;
    }
}
let restoreDrag = { x: 0, y: 0, restoreId: -1 };
let activeDragSite = { x: 0, y: 0 };
let dragStartTime = 0;
let dragEndTime = 0;
const isResizing = ref(false);
function mouduleDrag(data: any, type: string, item?: any) {
    // && restoreDrag.restoreId === -1
    if (type === "drag-start") {
        dragStartTime = new Date().getTime();
        restoreDrag.x = data.x;
        restoreDrag.y = data.y;
        restoreDrag.restoreId = item.id;
    }
    if (type === "dragging") {
        const { x, y } = data;
        //拖动防抖
        if (restoreDrag.restoreId !== item.id) {
            return false;
        }
        if (activeEle.value.size) {
            // const asideEle = Array.from(activeEle.value)
            // .filter((i: any) => i.id !== item.id).find((i: any) =>  {
            //     const oldData = historySeniorModuleData.value.find((data) => i.id === data.id)!;
            //     return oldData.left + x - restoreDrag.x <= 0 || oldData.top + y - restoreDrag.y <= 0;
            // });
            const { mainWidth, mainHeight } = getPagerWhNumber();
            Array.from(activeEle.value)
                .filter((i: any) => i.id !== item.id)
                .forEach((data: any) => {
                    const oldData = historySeniorModuleData.value.find((i) => i.id === data.id)!;
                    let clientX = oldData.left + x - restoreDrag.x;
                    let clientY = oldData.top + y - restoreDrag.y;
                    clientX = clientX < 0 ? 0 : clientX;
                    clientY = clientY < 0 ? 0 : clientY;
                    clientX = clientX > mainWidth ? mainWidth : clientX;
                    clientY = clientY > mainHeight ? mainHeight : clientY;
                    data.left = clientX;
                    data.top = clientY;
                });
            // if(asideEle){
            //     // console.log("bbbbbbbbbb", asideEle);
            //     // item.left = activeDragSite.x;
            //     // item.top = activeDragSite.y;
            //     disabledX.value = true;
            //     // item.active = false;
            //     // item.inputable = true;
            //     // isDrag.value = false;
            //     // item.left = 0;
            //     // item.top = 0;
            //     return false;
            // }else{
            //     activeDragSite.x = x;
            //     activeDragSite.y = y;
            //     Array.from(activeEle.value)
            //     .filter((i: any) => i.id !== item.id)
            //     .forEach((data: any) => {
            //         const oldData = historySeniorModuleData.value.find((i) => i.id === data.id)!;
            //         data.left = oldData.left + x - restoreDrag.x;
            //         data.top = oldData.top + y - restoreDrag.y;
            //     });
            // }
        }
        if (item && item.left < 0) {
            isDrag.value = false;
            item.left = 0;
            return false;
        }
    } else if (type === "resizing") {
        isResizing.value = true;
    } else if (type == "drag-end" || type == "resize-end") {
        if (type === "drag-end") {
            //防抖
            dragEndTime = new Date().getTime();
            if (dragEndTime - dragStartTime < 200) {
                // const targetItem = historySeniorModuleData.value.find((i) => i.id === item.id);
                // if (targetItem) {
                //     const { left, top } = targetItem;
                //     Object.assign(item, { left, top });
                // }
                return false;
            } else {
                //拖动结束判断是否有重叠，因间距不变，所以不包含表格加判断
                if (!Array.from(activeEle.value).some((obj: any) => obj.id === 20)) {
                    judgeOverLapTable(item);
                } else {
                    addHistory();
                }
                restoreDrag.restoreId = -1;
                restoreDrag.x = 0;
                restoreDrag.y = 0;
            }
        } else {
            isResizing.value = false;
            addHistory();
            // if (!Array.from(activeEle.value).some((obj: any) => obj.id === 20)) {
            //     judgeOverLapTable(item);
            // } else {
            //     addHistory();
            // }
        }
    }
}

let restoreTableDrag = { x: 0, y: 0, restoreId: -1 };
let dragTableStartTime = 0;
let dragTableEndTime = 0;
let tableOldWidth = 0;
let tableOldHeight = 0;
function tableDrag(event: any, type: string, item?: any) {
    //获取height
    if (type === "drag-start") {
        dragTableStartTime = new Date().getTime();
        restoreTableDrag.x = event.x;
        restoreTableDrag.y = event.y;
        restoreTableDrag.restoreId = item.id;
    }
    if (type == "dragging") {
        const { x, y } = event;
        //拖动防抖
        // if (restoreTableDrag.restoreId !== item.id) {
        //     return false;
        // }
        if (activeEle.value.size) {
            const { mainWidth, mainHeight } = getPagerWhNumber();
            Array.from(activeEle.value)
                .filter((i: any) => i.id !== item.id)
                .forEach((data: any) => {
                    const oldData = historySeniorModuleData.value.find((i) => i.id === data.id)!;
                    let clientX = oldData.left + x - restoreTableDrag.x;
                    let clientY = oldData.top + y - restoreTableDrag.y;
                    clientX = clientX < 0 ? 0 : clientX;
                    clientY = clientY < 0 ? 0 : clientY;
                    clientX = clientX > mainWidth ? mainWidth : clientX;
                    clientY = clientY > mainHeight ? mainHeight : clientY;
                    data.left = clientX;
                    data.top = clientY;
                });
        }
        if (seniorTableData.value.left < 0) {
            isDrag.value = false;
            seniorTableData.value.left = 0;
            return false;
        }
    } else if (type == "resizing") {
        const distanceX = event.w - tableOldWidth;
        let totalWidth = 0;
        const initialTotalWidth = tableContentList.value.reduce((acc, item) => acc + item.width, 0);
        tableContentList.value.forEach((item, index) => {
            const widthChangeRatio = item.width / initialTotalWidth;
            if (index === tableContentList.value.length - 1) {
                //最后一个反计算
                item.width = Math.abs(event.w - totalWidth) - 1;
            } else {
                if (item.width + distanceX < MIN_CELL_SIZE) {
                    item.width = MIN_CELL_SIZE;
                } else {
                    item.width += distanceX * widthChangeRatio;
                }
                totalWidth += item.width;
            }
        });
        const distanceY = (event.h - tableOldHeight) / 3;
        tableContentList.value.forEach((item) => {
            if (item.headerHeight + distanceY < MIN_CELL_SIZE) {
                item.headerHeight = MIN_CELL_SIZE;
            } else {
                item.headerHeight += distanceY;
            }
            if (item.bodyHeight + distanceY < MIN_CELL_SIZE) {
                item.bodyHeight = MIN_CELL_SIZE;
            } else {
                item.bodyHeight += distanceY;
            }
            item.footerHeight = event.h - item.headerHeight - item.bodyHeight;
        });
        tableOldWidth = event.w;
        tableOldHeight = event.h;
    } else if (type == "resize-start") {
        tableOldWidth = event.w;
        tableOldHeight = event.h;
    } else if (type == "drag-end" || type == "resize-end") {
        if (type === "drag-end") {
            if (seniorTableData.value.inputable) {
                const tableHeader = document.querySelector(".table-lable") as HTMLElement;
                const tableHeaderHeight = tableHeader.offsetHeight;
                tableContentList.value.forEach((item) => {
                    item.headerHeight = tableHeaderHeight;
                });
                const tableContent = document.querySelector(".table-content") as HTMLElement;
                const tableContentHeight = tableContent.offsetHeight;
                tableContentList.value.forEach((item) => {
                    item.bodyHeight = tableContentHeight;
                });
                const tableFooter = document.querySelector(".table-total") as HTMLElement;
                const tableFooterHeight = tableFooter.offsetHeight;
                tableContentList.value.forEach((item) => {
                    item.footerHeight = tableFooterHeight;
                });
            }
            //防抖
            dragTableEndTime = new Date().getTime();
            if (dragTableEndTime - dragTableStartTime < 200) {
                // const targetItem = historySeniorModuleData.value.find((i) => i.id === item.id);
                // if (targetItem) {
                //     const { left, top } = targetItem;
                //     Object.assign(item, { left, top });
                // }
                return false;
            } else {
                //拖动结束判断是否有重叠，因间距不变，所以不包含表格加判断
                // if (!Array.from(activeEle.value).some((obj: any) => obj.id === 20)) {
                //     judgeOverLapTable(item);
                // } else {
                //     historySeniorModuleData.value = _.cloneDeep(seniorModuleData.value);
                // }
                //这里计算重叠怎么判断
                // if (judgeOverLapTable(item)) {
                //     // addHistory();
                // }else{
                //     nextTick(() => {
                //         // addHistory();
                //     });
                // }

                //????
                setTimeout(() => {
                    addHistory();
                });
                restoreTableDrag.restoreId = -1;
                restoreTableDrag.x = 0;
                restoreTableDrag.y = 0;
            }
        } else {
            addHistory();
        }
    }
}
function tableMouseDown(event: any, item: any) {
    if (!seniorTableData.value.inputable) {
        event.stopPropagation();
        bodyDown(event, item);
        selectStyle(item.style);
    }
}

const activeEle = ref(new Set());
function activeDrag(event: any, item: any, type: string) {
    if (!shiftKey.value) {
        activeEle.value.clear();
        if (type === "unTable") {
            seniorTableData.value.active = false;
            seniorTableData.value.inputable = false;
        }
    }
    if (type === "unTable") {
        setTimeout(() => {
            seniorModuleData.value.forEach((data: any) => {
                if (data.active) {
                    activeEle.value.add(data);
                }
            });
        });
    } else {
        setTimeout(() => {
            if (seniorTableData.value.active) activeEle.value.add(seniorTableData.value);
        });
    }
}
function deactivated(event: any, item: any) {
    item.inputable = false;
    if (shiftKey.value && !notDragHotZone.value) {
        // event.stopPropagation();
        // event.preventDefault();
        setTimeout(() => {
            item.style.isSelected = false;
            item.active = false;
        });
        activeEle.value.forEach((data: any) => {
            if (data.id !== item.id) data.active = true;
        });
    } else {
        item.active = false;
        item.style.isSelected = false;
        activeEle.value.delete(item);
    }
}

function moduleInputChange(item: any) {
    addHistory();
}

function inputKeyDown(event: any, item: any) {
    if (event.keyCode === 13 && item.dataSource) {
        event.preventDefault();
        return;
    }
}
function blurInput(event: any, item: any) {
    if (item.style.shadowWhiteSpace === "Reduce") {
        if (canTextFitInDiv(item.label, item.style.shadowFontSize* zoom.value, item.width, item.height)) {
            item.style.fontSize = item.style.shadowFontSize;
        } else {
            item.style.fontSize = 7;
        }
    }
}

function caseOnDrop(item: any) {
    if (item.type !== "textNoLabel" && item.type !== "img" && item.type !== "line" && item.type !== "rectangle") {
        item.inputable = true;
        nextTick(() => {
            if (item.type === "text") {
                moudleInputRef.value[item.id].focus();
            }
        });
    }
}

function tableListDrag(dragResult: any) {
    const { removedIndex, addedIndex, payload } = dragResult;
    if (removedIndex === null && addedIndex === null) return tableContentList.value;
    const result = [...tableContentList.value];
    let itemToAdd = payload;
    if (removedIndex !== null) {
        itemToAdd = result.splice(removedIndex, 1)[0];
    }

    if (addedIndex !== null) {
        result.splice(addedIndex, 0, itemToAdd);
    }
    if (removedIndex === 0 && addedIndex !== null && addedIndex !== 0) {
        result[0].footerHeight = itemToAdd.footerHeight;
        result[0].footerStyle = JSON.stringify(result[0].footerStyle) === "{}" ? itemToAdd.footerStyle : result[0].footerStyle;
        if (![12, 13].includes(result[addedIndex].id)) {
            result[addedIndex].footerHeight = 0;
            result[addedIndex].footerStyle = {};
        }
    }
    if (addedIndex === 0 && removedIndex !== null && removedIndex !== 0) {
        result[0].footerHeight = tableContentList.value[0].footerHeight;
        result[0].footerStyle = tableContentList.value[0].footerStyle;
        if (![12, 13].includes(result[removedIndex].id)) {
            result[removedIndex].footerHeight = 0;
            result[removedIndex].footerStyle = {};
        }
    }
    tableContentList.value = result;
    addHistory();
    tableListDragging.value = false;
}
const tableListDragging = ref(false);
function dragStart() {
    tableListDragging.value = true;
}

//表格
const tableLableInputRefs = ref<any>([]);
const activeTableInput = ref<any>([]);
function clickTableLable(item: any) {
    item.inputable = true;
    nextTick(() => {
        tableLableInputRefs.value[item.id].focus();
        activeTableInput.value.push(item.id);
    });
}
const nonHotZone = ref(false);
function blurTableLable(event: any, item: any) {
    if (nonHotZone.value) {
        event.preventDefault();
        event.stopPropagation();
        if (activeTableInput.value.length) {
            // tableData.value.inputable = true;
            activeTableInput.value.forEach((id: any) => {
                // tableList.value.find((i) => i.id === id)!.inputable = true;
                nextTick(() => {
                    tableLableInputRefs.value[id].focus();
                });
            });
        }
    } else {
        item.inputable = false;
        // activeTableInput.value = activeTableInput.value.filter((i) => i !== item.id);
    }
}
function tableLableChange() {
    addHistory();
}

//改变表格行宽
function resizeWidth(e: any, item: any) {
    e.preventDefault();
    e.stopPropagation();
    //去掉选中样式
    selectStyleList.value.forEach((i) => (i.isSelected = false));
    // 初始宽度
    const oldWidth = item.width;
    // 点击鼠标初始X值
    const startX = e.clientX;
    document.onmousemove = (e) => {
        // 当前鼠标X值
        const endX = e.clientX;
        // 当前鼠标在X轴移动的距离
        const distance = endX - startX;
        let newWidth = oldWidth + distance;
        const minWidth = tableContentList.value.length * MIN_CELL_SIZE;
        if (newWidth < MIN_CELL_SIZE) {
            newWidth = MIN_CELL_SIZE;
        }
        let calcWidth = tableContentList.value.reduce((prev, curr) => prev + curr.width, 0) - item.width + newWidth;
        if (calcWidth < minWidth) {
            calcWidth = minWidth;
        } else if (calcWidth + seniorTableData.value.left > getPagerWhNumber().mainWidth) {
            calcWidth = getPagerWhNumber().mainWidth - seniorTableData.value.left;
        } else {
            item.width = newWidth;
        }

        seniorTableData.value.width = calcWidth;
        //默认拖拽的列优先于借方金额或者贷方金额，则合计行的列宽也要跟着变化
        const amountCol = tableContentList.value.findIndex((i) => i.id === 12 || i.id === 13);
        if (amountCol !== -1 && item.index <= amountCol) {
            calcTotalWidth.value = calcTotalWidth.value + newWidth - oldWidth;
        }
    };
    document.onmouseup = (e) => {
        e.preventDefault();
        e.stopPropagation();
        document.onmousemove = null;
        document.onmouseup = null;
        if (e.clientX === startX) return;
        let dragTableElements = document.querySelectorAll(".table-drag-content");
        Array.from(dragTableElements).forEach((table, tableIndex) => {
            let dragRowElements = table.querySelectorAll(".table-body");
            Array.from(dragRowElements).forEach((element, elIndex) => {
                let elementWidth = Math.floor(element.getBoundingClientRect().width);
                tableContentList.value[elIndex].width = elementWidth < 20 ? 20 : elementWidth;
            });
        });
        seniorTableData.value.width = tableContentList.value.reduce((prev, curr) => prev + curr.width, 0) + 1;
        addHistory();
    };
    return false;
}
//改变表格行高
type HeightType = "footerHeight" | "bodyHeight" | "headerHeight";
function resizeHeight(e: any, type: HeightType) {
    e.preventDefault();
    // 初始高度
    const oldHeight = tableContentList.value[0][type];
    // 点击鼠标初始X值
    const startY = e.clientY;
    document.onmousemove = (e) => {
        // 当前鼠标X值
        const endY = e.clientY;
        // 当前鼠标在X轴移动的距离
        const distance = endY - startY;
        // left-top当前高度

        let newHeight = oldHeight + distance - 2;
        if (newHeight < MIN_CELL_SIZE) {
            newHeight = MIN_CELL_SIZE;
        }
        const lastHLine = document.querySelector("#lastHLine") as HTMLElement;
        //4为边框宽度
        if (getPagerWhNumber().mainHeight <= lastHLine.offsetTop + seniorTableData.value.top + 4 && distance > 0) {
            //取最大
            console.log("超出范围（计算有点问题）");
            return;
        }

        tableContentList.value.forEach((item) => {
            item[type] = newHeight;
        });
        const { headerHeight, bodyHeight, footerHeight } = tableContentList.value[0];
        seniorTableData.value.height = headerHeight + bodyHeight + footerHeight;
        let hasOverlap = false;
        historySeniorModuleData.value
            .filter((item) => item.positionType !== 3)
            .forEach((data: IModuleData, index: number) => {
                if (judgeOverLap(seniorTableData.value, data)) {
                    overLapId.value.push(data.id);
                    hasOverlap = true;
                }
            });
        if (hasOverlap) {
            let clearOverLap = setTimeout(() => {
                overLapId.value = [];
                clearTimeout(clearOverLap);
            }, 1000);
            tableContentList.value.forEach((item) => {
                item[type] = oldHeight;
            });
            const { headerHeight, bodyHeight, footerHeight } = tableContentList.value[0];
            seniorTableData.value.height = headerHeight + bodyHeight + footerHeight;
        }
    };
    document.onmouseup = () => {
        document.onmousemove = null;
        document.onmouseup = null;
        addHistory();
    };
    return false;
}

function getMainBoxDimension() {
    const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    return {
        height: mainDragBox.offsetHeight,
        width: mainDragBox.offsetWidth,
    };
}
function getMainContentBoxDimension() {
    const mainDragBox = document.querySelector(".main-drag-content") as HTMLElement;
    return {
        height: mainDragBox.offsetHeight,
        width: mainDragBox.offsetWidth,
    };
}

// 返回一个默认样式
function getBaseStyle(): IStyle {
    return {
        fontFamily: "SimSun",
        fontSize: 10,
        fontWeight: "normal",
        fontStyle: "normal",
        textDecoration: "none",
        whiteSpace: "Wrap",
        wordBreak: "normal",
        overflow: "hidden",
        textAlign: "left",
        justifyContent: "flex-start",
        isSelected: false,
    };
}
const keyId = ref(16);
// 添加一个可拖拽盒子
interface DragBoxOptions {
    top: number;
    left: number;
    id?: number;
    positionType?: number;
    height?: number;
    width?: number;
    label?: string;
    content?: ArrayBuffer;
    placeholder?: string;
    style?: IStyle;
    columnName?: string;
    dataSource?: string | ArrayBuffer;
}
function addDragBox(
    type: ModuleType,
    { height, width, id, positionType, top, left, label, content, placeholder, style, columnName, dataSource }: DragBoxOptions,
    needAddhistory = false,
    isAddByMoudle = false,
    isKeep = false
) {
    const { mainWidth, mainHeight } = getPagerWhNumber();
    const { width: contentBoxWidth, height: contentBoxHeight } = getMainContentBoxDimension();
    width = width || typeSize.value.get(type)!.width;
    height = height || typeSize.value.get(type)!.height;
    const marginLeft = mmToPx(printInfo.value.marginLeft);
    const marginTop = mmToPx(printInfo.value.marginTop);
    const marginRight = mmToPx(printInfo.value.marginRight);
    const marginBottom = mmToPx(printInfo.value.marginBottom);
    if (isAddByMoudle) {
        // if (left > contentBoxWidth - width) {
        //     left = contentBoxWidth - width;
        // }
        // if (top > contentBoxHeight - height) {
        //     top = contentBoxHeight - height;
        // }
    } else if (!left && !top && seniorModuleData.value.length > 0) {
        let maxLeft = -Infinity;
        let maxTop = -Infinity;
        let headResult: IModuleData | null = null as IModuleData | null;
        seniorModuleData.value
            .filter((i) => !["tableHeader", "tableBody", "tableFooter"].includes(i.type))
            .forEach((obj: any) => {
                if (obj.top + obj.height > maxTop) {
                    maxLeft = obj.left + obj.width;
                    maxTop = obj.top + obj.height;
                    headResult = obj;
                } else if (obj.top + obj.height === maxTop && obj.left + obj.width > maxLeft) {
                    maxTop = obj.top + obj.height;
                    headResult = obj;
                }
            });
        // this.zoomChange(1, this.zoom, [lastBox], []);
        if (headResult !== null) {
            left = headResult.left + headResult.width + SPACE_BETWEEN;
            if (left > mainWidth - width) {
                left = 0;
                top = headResult.top + headResult.height + SPACE_BETWEEN;
            } else {
                top = headResult.top;
            }
            left = Math.min(left, mainWidth - width);
            top = Math.min(top, mainHeight - height);
        } else {
            left = 0;
            top = 0;
        }
    } else if (isKeep) {
        left = Math.min(left, contentBoxWidth - width);
        top = Math.min(top, contentBoxHeight - height);
    } else {
        left = left - marginLeft * zoom.value;
        top = top - marginTop * zoom.value;
        left = Math.min(left, contentBoxWidth - width);
        top = Math.min(top, contentBoxHeight - height);
    }
    const sticks = typeSize.value.get(type)!.sticks;
    //判断表格重叠
    if (
        left < seniorTableData.value.left + seniorTableData.value.width &&
        top < seniorTableData.value.top + seniorTableData.value.height &&
        left + width > seniorTableData.value.left &&
        top + height > seniorTableData.value.top
    ) {
        ElNotify({
            type: "warning",
            message: "请勿与表格重叠",
        });
        return false;
    }
    let dargBox = {
        active: true,
        height,
        text: content ?? "",
        label: label ?? "",
        dataSource: dataSource || "",
        inputable: type === "text" && dataSource === undefined && !isKeep,
        id: id ?? keyId.value++,
        positionType: positionType ?? 1,
        left: left,
        style: style || getBaseStyle(),
        top: top,
        type: type,
        sticks,
        width,
        index: 1,
    };

    activeEle.value.add(dargBox);
    seniorModuleData.value.push(dargBox as IModuleData);
    nextTick(() => {
        if (type === "text" && typeof dargBox.dataSource === "string" && dargBox.dataSource === "" && !isKeep) {
            moudleInputRef.value[dargBox.id].focus();
        }
    });
    addHistory();
}
function dblAddModule(type: ModuleType, e: any) {
    if (e.target.className === "main-drag-content") {
        navSetting(type, e);
    }
}
function navSetting(type: ModuleType, e?: any, dataSource?: string | ArrayBuffer) {
    if (!e) {
        addDragBox(type, { top: 0, left: 0, dataSource }, true);
        return;
    }
    if (!limitLocation(e)) return;
    let mainDragBox = document.querySelector(".main-drag-box") as Element;
    const rect = mainDragBox.getBoundingClientRect();
    // 计算鼠标相对于元素的位置
    const x = Math.ceil(e.clientX - rect.left);
    const y = Math.ceil(e.clientY - rect.top);
    addDragBox(type, { top: y, left: x, dataSource }, true);
}

function limitLocation(e: any) {
    let mainDragBox = document.querySelector(".main-drag-box") as Element;
    const rect = mainDragBox.getBoundingClientRect();
    // 计算鼠标相对于元素的位置
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    const { width: mainWidth, height: mainHeight } = getMainBoxDimension();
    if (
        x > mainWidth - mmToPx(printInfo.value.marginLeft) ||
        x < mmToPx(printInfo.value.marginLeft) ||
        y > mainHeight - mmToPx(printInfo.value.marginTop) ||
        y < mmToPx(printInfo.value.marginTop)
    ) {
        return false;
    }
    if (
        x < seniorTableData.value.left + seniorTableData.value.width &&
        y < seniorTableData.value.top + seniorTableData.value.height &&
        x > seniorTableData.value.left &&
        y > seniorTableData.value.top
    ) {
        ElNotify({
            type: "warning",
            message: "请勿与表格重叠",
        });
        return false;
    }

    return true;
}
const addImgTarget = ref(null);
function addImg(e?: any) {
    addImgTarget.value = e || null;
    if (e && !limitLocation(e)) return;
    const input = document.getElementById("selfSealInput");
    input!.click();
}
function handleFileSelect(event: any) {
    var file = event.target.files[0]; // 获取选择的文件
    if (!file) return;
    const acceptedImageTypes = ["image/jpeg", "image/png", "image/webp", "image/svg+xml", "image/bmp"];
    if (!acceptedImageTypes.includes(file.type)) {
        ElNotify({
            type: "warning",
            message: "请选择为图像类型的文件",
        });
        return;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        ElNotify({
            type: "warning",
            message: "上传图片大小不能超过 2MB!",
        });
        return;
    }
    if (file) {
        var reader = new FileReader(); // 创建FileReader对象
        reader.onload = function (e) {
            //ArrayBuffer Blob
            navSetting("img", addImgTarget.value, e.target!.result as ArrayBuffer);
            event.target.value = "";
        };
        reader.readAsDataURL(file); // 读取文件内容并触发onload事件
    }
}

// 框选功能
function boxSelectCallback({ width, height, left, top }: { width: number; height: number; left: number; top: number }) {
    const marginLeft = mmToPx(printInfo.value.marginLeft) * zoom.value;
    const marginTop = mmToPx(printInfo.value.marginTop) * zoom.value;
    left = left - marginLeft - 24 * zoom.value;
    top = top - marginTop - 24 * zoom.value;
    // 将dragBoxArr中控件位置在盒子内的控件选中
    seniorModuleData.value.forEach((item: any) => {
        if (item.left >= left && item.left <= left + width && item.top >= top && item.top <= top + height) {
            item.active = true;
            item.style.isSelected = true;
            selectStyleList.value.push(item.style);
        } else if (item.left >= left && item.left <= left + width && item.top <= top && top <= item.top + item.height) {
            item.active = true;
            item.style.isSelected = true;
            selectStyleList.value.push(item.style);
        } else {
            item.active = false;
            item.style.isSelected = false;
        }
    });
    // 将tableList中控件位置在盒子内的控件选中, 以及控件中的单元格选中
    let tableHeight = seniorTableData.value.height;
    if (
        seniorTableData.value.left >= left &&
        seniorTableData.value.left <= left + width &&
        seniorTableData.value.top >= top &&
        seniorTableData.value.top <= top + height
    ) {
        seniorTableData.value.active = true;
        tableContentList.value.forEach((cell) => {
            cell.bodyStyle.isSelected = true;
            cell.headerStyle.isSelected = true;
            if (cell.footerStyle) {
                cell.footerStyle.isSelected = true;
                selectStyleList.value.push(cell.footerStyle);
            }
            selectStyleList.value.push(cell.bodyStyle);
            selectStyleList.value.push(cell.headerStyle);
        });
    } else {
        seniorTableData.value.active = false;
        tableContentList.value.forEach((cell) => {
            cell.bodyStyle.isSelected = false;
            cell.headerStyle.isSelected = false;
            if (cell.footerStyle) {
                cell.footerStyle.isSelected = false;
            }
        });
    }
    if (selectStyleList.value.length > 0) {
        const {
            fontFamily: newFontFamily,
            fontSize: newFontSize,
            fontWeight: newIsBold,
            textDecoration: newIsUnderline,
            fontStyle: newIsItalic,
            textAlign: newTextAlign,
            whiteSpace: newWhiteSpace,
        } = selectStyleList.value[0];

        selectFontType.value = newFontFamily;
        selectFontSize.value = newFontSize;
        isBold.value = newIsBold === "bold";
        isUnderline.value = newIsUnderline === "underline";
        isItalic.value = newIsItalic === "italic";
        selectTextAlign.value = newTextAlign;
        selectWhiteSpace.value = newWhiteSpace;
    }
}

function addEvents(events: Map<string, EventListenerOrEventListenerObject>, element: Element) {
    events.forEach((listener, eventName) => {
        element.addEventListener(eventName, listener);
    });
}
function deselect(e: any) {
    if ((e && e.target.className.indexOf("main-drag-box") > -1) || e.target.className.indexOf("main-drag-content") > -1) {
        activeEle.value.clear();
        seniorModuleData.value.forEach((data: any) => {
            data.active = false;
        });
        seniorTableData.value.active = false;
        selectStyleList.value.forEach((i: any) => {
            i.isSelected = false;
        });
        selectStyleList.value = [];
        return;
    } else if (e && e.target.className.indexOf("drag-box-inner") > -1) {
        // console.log(e.target.className);
    }
}

function getSetting(useDefault = false) {
    let requestUrl = window.printHost + "/api/VoucherPrintSettings/ByType?settingType=0&printType=" + printInfo.value.printType;
    if (useDefault) {
        requestUrl = window.printHost + "/api/VoucherPrintSettings/DefaultByType?settingType=0&printType=" + printInfo.value.printType;
    }
    loading.value = true;
    request({
        url: requestUrl,
        method: useDefault ? "GET" : "POST",
        data: useDefault ? {} : getVoucherSeniorPrintInfo(),
    }).then((res: any) => {
        if (res.state === 1000) {
            printInfo.value.direction = res.data.direction;
            printInfo.value.printType = res.data.pageType;
            handleMarginLeft(res.data.marginLeft);
            handleMarginTop(res.data.marginTop);
            let maxId = -1;
            seniorModuleData.value = [];

            nextTick(() => {
                seniorModuleData.value = res.data.printData.map((item: any) => {
                    if (item.id > maxId) {
                        maxId = item.id;
                    }
                    const { dataType, fontFamily, fontSize, isBold, isItalic, textAlign, justifyContent, overflow, isUnderLine, ...data } =
                        item;
                    data.style.isSelected = false;
                    return {
                        ...data,
                        type: getKeyByValue(dataType),
                        active: false,
                        inputable: false,
                    };
                });
                tableContentList.value = transformTableData(seniorModuleData.value);
                syncSeniorMoudle();
                zoomChange(1, zoom.value, seniorModuleData.value, tableContentList.value);
                ptLayout.value = {
                    moudle: _.cloneDeep(seniorModuleData.value),
                    table: _.cloneDeep(tableContentList.value),
                    printInfo: _.cloneDeep(printInfo.value),
                    zoom: zoom.value,
                };
                historySeniorModuleData.value = _.cloneDeep(ptLayout.value.moudle);
                //系统设置的最大id 20
                if (maxId > 20) {
                    keyId.value = maxId + 1;
                } else {
                    keyId.value = 21;
                }
            });
            const { voucherLine, ...bodySet } = res.data.tableStyle;
            bodySetCheck.value = bodySet;

            loading.value = false;
            if (firstLoading.value) {
                firstLoading.value = false;
                nextTick(() => {
                    initEvents();
                });
            }
        }
    });
}

getSetting();

function resetSetting() {
    if(document.querySelector('#voucherPrintResetSetting')) return
    ElAlert({
        message: "重置将清除所有的修改内容并恢复为系统默认，您确定要继续吗？",
        options: { confirmButtonText: "继续重置", cancelButtonText: "取消" },
        id: "voucherPrintResetSetting",
    }).then((r: boolean) => {
        if (r) {
            historyIndex.value = -1;
            historyArr.value = [];
            seniorModuleList.value = _.cloneDeep(moduleList);
            getSetting(true);
        }
    });
}

function preview() {
    saveSetting(2, 2, () => {
        fullScreen.value = false;
        const printUrl = encodeURIComponent(
            window.printHost +
                "/api/Voucher/PreviewSeniorPrint?appasid=" +
                getGlobalToken() +
                "&" +
                getUrlSearchParams({ ...printParams(printInfo.value), isPrintTitlePage: false }) +
                "&noCheckHttp&r=" +
                Math.random()
        );
        globalWindowOpenPage("/PreviewPrint?appasid=" + getGlobalToken() + "&iframeSrc=" + printUrl, "凭证打印样式预览");
    });
}

function saveSetting(settingType: number, settingId: number, callBack?: Function) {
    //去掉选中样式
    selectStyleList.value.forEach((i) => (i.isSelected = false));
    useLoading().enterLoading("努力保存中，请稍候...");
    tableContentList.value.forEach((item, index) => {
        item.index = index + 1;
    });
    const cloneSeniorModuleData = _.cloneDeep(seniorModuleData.value);
    const cloneTableContentList = _.cloneDeep(tableContentList.value);
    zoomChange(zoom.value, 1, cloneSeniorModuleData, cloneTableContentList);
    const splitTable = cloneTableContentList.reduce((prev: ISavePrintData[], item: ITableList, index: number) => {
        prev.push(...splitTableObject(item, index));
        return prev;
    }, []);
    const printData: ISavePrintData[] = cloneSeniorModuleData
        .filter((item) => {
            // 排除表格
            const tableArray = ["tableHeader", "tableBody", "tableTotal"];
            return !tableArray.includes(item.type);
        })
        .map((item) => {
            const { type, active, sticks, inputable, ...data } = item;
            return {
                ...data,
                dataType: VoucherPrintDataType[type],
                fontFamily: item.style.fontFamily,
                fontSize: item.style.whiteSpace === "Reduce" ? item.style.shadowFontSize : item.style.fontSize,
                isBold: item.style.fontWeight === "bold",
                isItalic: item.style.fontStyle === "italic",
                textAlign: TextAlignmentType[item.style.textAlign],
                justifyContent: 0,
                overflow: judgeOverFlow(item.style.whiteSpace, item.style.shadowWhiteSpace),
                isUnderLine: item.style.textDecoration === "underline",
            };
        });
    printData.push(...splitTable);
    const {
        isPrintFrontCover: isPrintTitlePage,
        isPrintFileNum,
        printFile: isIncludeFile,
        simultaneouslyPrintFileList: includeManifest,
        continuousFiles: isContinuePrint,
        isShowPageNumber: isShowPageNumber,
        isShowSplitLine,
        isSplitPageByVNum,
        isShowPrintDate,
        printDateText,
        printType: pageType,
        direction,
        marginTop,
        marginLeft,
        isHideZero,
        isLineHeightAdaptive,
        isHideEmpty,
        isShowSummaryPrint,
        summaryAsubLevel,
        isSummaryByDirection,
        isShowAssitItem,
    } = printInfo.value;
    const data = {
        settingsId: settingId,
        settingType: settingType,
        isPrintTitlePage,
        isPrintFileNum,
        isIncludeFile,
        includeManifest,
        isContinuePrint,
        isShowPageNumber,
        isShowSplitLine,
        isSplitPageByVNum,
        isShowPrintDate,
        printDateText,
        pageType,
        direction,
        marginTop,
        marginLeft,
        tableStyle: {
            ...bodySetCheck.value,
            isHideZero,
            isLineHeightAdaptive,
            voucherLine: 5,
            isHideEmpty,
        },
        printData,
        isShowSummaryPrint,
        summaryAsubLevel,
        isSummaryByDirection,
        isHideZero,
        isShowSummaryAssist: isShowAssitItem,
    };
    request({
        url: window.printHost + "/api/VoucherPrintSettings",
        method: "post",
        data,
    })
        .then((res: any) => {
            if (res.state === 1000) {
                if (callBack) {
                    useLoading().quitLoading();
                    callBack();
                } else {
                    setVoucherSeniorPrintInfo(printInfo.value);
                    window.dispatchEvent(
                        new CustomEvent("refreshVoucherSettingDialog", {
                            detail: {
                                printInfo: {
                                    ...printInfo.value,
                                    summaryAsubLevel:
                                        printInfo.value.isShowSummaryPrint && printInfo.value.summaryAsubLevel === 0
                                            ? 1
                                            : printInfo.value.summaryAsubLevel,
                                },
                            },
                        })
                    );
                    historyIndex.value = -1;
                    historyArr.value = [];
                    ptLayout.value = {
                        moudle: _.cloneDeep(seniorModuleData.value),
                        table: _.cloneDeep(tableContentList.value),
                        printInfo: _.cloneDeep(printInfo.value),
                        zoom: zoom.value,
                    };
                    historySeniorModuleData.value = _.cloneDeep(ptLayout.value.moudle);
                    ElNotify({
                        type: "success",
                        message: "保存成功",
                    });
                }
            } else {
                if (res.subState === 6) {
                    const errorObj = JSON.parse(res.msg);
                    let repeatStr = "";
                    let overFlowStr = "";
                    let msg = "";
                    if (errorObj.TableOverlap.length) {
                        const matchedObjects = seniorModuleData.value.filter((item) => errorObj.TableOverlap.includes(item.id));
                        matchedObjects.forEach((obj) => {
                            repeatStr += `${obj.label ? obj.label.slice(0, -1) : typeTransform(obj.type)} `;
                        });
                        msg = `${repeatStr.slice(0, -1)}与表格重叠，`;
                    }
                    if (errorObj.PageOverflow.length) {
                        const matchedObjects = seniorModuleData.value.filter((item) => errorObj.PageOverflow.includes(item.id));
                        matchedObjects.forEach((obj) => {
                            overFlowStr += `${obj.label ? obj.label.slice(0, -1) : typeTransform(obj.type)} `;
                        });
                        msg += `${overFlowStr.slice(0, -1)}超出纸张范围，`;
                    }
                    ElNotify({
                        type: "warning",
                        message: `${msg}请重新设置`,
                    });
                } else {
                    ElNotify({
                        type: "error",
                        message: "保存失败，请稍后重试或联系侧边栏客服哦~",
                    });
                }
            }
        })
        .finally(() => {
            if(!callBack) useVoucherSettingsStore().cacheCheckSeniorSettings = false
            useLoading().quitLoading();
        });
}

const firstLoading = ref(true);
const dblDragContent = ref(0);
const dblCoord = { x: 0, y: 0, timestamp: 0 };
function resetDblValues(e?: any) {
    if (e) {
        dblCoord.x = e.clientX;
        dblCoord.y = e.clientY;
        dblCoord.timestamp = new Date().getTime();
        dblDragContent.value = 1;
        return;
    }
    dblDragContent.value = 0;
    dblCoord.x = 0;
    dblCoord.y = 0;
    dblCoord.timestamp = 0;
}
const notDragHotZone = ref(false);
function docMouseDown(e: any) {
    const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    const printRight = document.querySelector(".print-right") as HTMLElement;
    notDragHotZone.value = false;
    if (e.target.className === "main-drag-content") {
        dblDragContent.value++;
        if (dblDragContent.value === 1) {
            dblCoord.x = e.clientX;
            dblCoord.y = e.clientY;
            dblCoord.timestamp = new Date().getTime();
        }
        if (
            dblDragContent.value === 2 &&
            e.clientX === dblCoord.x &&
            e.clientY === dblCoord.y &&
            new Date().getTime() - dblCoord.timestamp <= 300
        ) {
            dblAddModule("text", e);
            resetDblValues();
        }
        if (
            dblDragContent.value === 2 &&
            (new Date().getTime() - dblCoord.timestamp >= 300 || e.clientX !== dblCoord.x || e.clientY !== dblCoord.y)
        ) {
            resetDblValues(e);
        }
    } else {
        resetDblValues();
    }
    if (e && mainDragBox.contains(e.target)) {
        if (e.target.className !== "el-input__inner") {
            nonHotZone.value = false;
            activeTableInput.value.forEach((id: any) => {
                tableContentList.value.find((i) => i.id === id)!.inputable = false;
                activeTableInput.value = [];
            });
        }
        if (e.target.className === "main-drag-content" || e.target.className === "drag-box") {
            // if(e.target.className === "main-drag-content"){
            //     e.stopPropagation();
            // }
            seniorTableData.value.inputable = false;
            seniorTableData.value.active = false;
        }
    } else {
        if (e && printRight.contains(e.target)) {
            nonHotZone.value = true;
            if (activeTableInput.value.length) {
                seniorTableData.value.inputable = true;
                activeTableInput.value.forEach((id: any) => {
                    tableContentList.value.find((i) => i.id === id)!.inputable = true;
                    nextTick(() => {
                        tableLableInputRefs.value[id].focus();
                    });
                });
            } else {
                activeEle.value.forEach((item: any) => {
                    item.active = false;
                });
            }
        } else {
            notDragHotZone.value = true;
            activeEle.value.clear();
        }
    }
}
function initEvents() {
    ctrlKeyChange();
    let domEvents = new Map<string, EventListenerOrEventListenerObject>([["mousedown", deselect]]);
    //加了加载，没有加载元素
    const mainDragBox = document.querySelector(".main-drag-box") as HTMLElement;
    const printCenter = document.querySelector(".print-center") as HTMLElement;
    const printRight = document.querySelector(".print-right") as HTMLElement;
    addEvents(domEvents, mainDragBox);
    const mainContainer = document.querySelector(".main-container") as HTMLElement;
    printRight.addEventListener("mousedown", (e: any) => {
        // if (!seniorTableData.value.inputable) {
        e.stopPropagation();
        // }
    });
    document.addEventListener("mousedown", docMouseDown);
    useBoxSelection(
        printCenter,
        mainDragBox,
        [
            "print-center",
            "main-drag-box",
            "main-drag-content",
            "top-left-subscript",
            "top-right-subscript",
            "bottom-left-subscript",
            "bottom-right-subscript",
        ],
        boxSelectCallback
    ).addEventListener();
}
onMounted(() => {
    window.addEventListener("refreshVoucherSettingPage", (e: any) => {
        if (e.detail?.printInfo.printType !== printInfo.value.printType) {
            printInfo.value = _.cloneDeep(e.detail?.printInfo);
            getSetting();
        } else {
            printInfo.value = _.cloneDeep(e.detail?.printInfo);
        }
    });
});
onBeforeUnmount(() => {
    const bigBox = document.querySelector(".main-drag-box") as HTMLElement;
    const printCenter = document.querySelector(".print-center") as HTMLElement;
    useBoxSelection(printCenter, bigBox, [
        "print-center",
        "main-drag-box",
        "main-drag-content",
        "top-left-subscript",
        "top-right-subscript",
        "bottom-left-subscript",
        "bottom-right-subscript",
    ]).cleanUp();
});

const route = useRoute();

let originClose = false;
const validator: (path:string,operate: string) => boolean = (path,operate) => {
    let leaveText = operate === "remove" ? "离开" : "刷新";
    let cancelText = operate === "remove" ? "留下" : "取消";
    if (historyArr.value.length > 0 || printInfo.value.printType !== getVoucherSeniorPrintInfo().printType) {
        ElAlert({
            message: `当前打印模板还没有保存，确定要${leaveText}吗`,
            options: { confirmButtonText: cancelText, cancelButtonText: leaveText },
            close: () => {
                originClose = true;
                return true;
            },
        }).then((r) => {
            if (!r && !originClose) {
                if (operate === "remove") {
                    //因为是实时的，所以route有问题
                    routerArrayStore.forceRemoveRouter("/Voucher/SeniorVoucherPrint");
                } else if (operate === "refresh") {
                    seniorModuleList.value = _.cloneDeep(moduleList);
                    getSetting();
                }
            }
            originClose = false;
        });
        return false;
    } else {
        return true;
    }
};

const leaveValidator = routerArrayStore.registerOperateLeaveValidator(route.path, validator);

onUnmounted(() => {
    leaveValidator.dispose();
});
onActivated(() => {
    if (route.query.collapse) collapseIndex.value = Number(route.query.collapse);
    if (route.query.isReset) resetSetting();
    tryClearCustomUrlParams(route);
    ctrlKeyChange();
    // initEvents();
    document.addEventListener("mousedown", docMouseDown);
});
onDeactivated(() => {
    document.removeEventListener("mousedown", docMouseDown);
});

interface IAccountSet {
    asId: number;
    asName: string;
    checked: boolean;
}

const searchText = ref("");
const accountSetList = ref<IAccountSet[]>([]);
const storeAccountSetList = ref<IAccountSet[]>([]);

// 获取账套数据
function getAccountData() {
    let url = `/api/AccountSet/FullListByFunctionCode?searchText=${searchText.value}&functionCode=voucher-canprint`;
    if (window.isProSystem) {
        url = window.eHost + "/wb/valveacc_vip_web" + url + (window.isAccountingAgent ? "" : "&serviceID=" + getServiceId());
    }else if(window.isErp){
        url =`/api/AccountSet/FullListByFunctionCodeForERP?searchText=${searchText.value}&functionCode=voucher-canprint&serviceId=`+getServiceId();
    }
    request({
        url: url,
    }).then((res: any) => {
        if (res.state === 1000) {
            accountSetList.value = res.data.filter((i: any) => i.asId !== accountsetStore.accountSet?.asId);
            accountSetList.value.forEach((i) => {
                i.checked = false;
            });
            if (searchText.value.length === 0) {
                storeAccountSetList.value = _.cloneDeep(accountSetList.value);
            }
        } else {
            accountSetList.value = [];
        }
    });
}
getAccountData();

// 同步设置
function syncSettings(data: any) {
    loading.value = true;
    request({
        url: window.printHost + "/api/VoucherPrintSettings/Copy?settingType=0&printType=" + printInfo.value.printType,
        method: "post",
        data: data,
    })
        .then((res: any) => {
            if (res.state === 1000) {
                ElNotify({
                    type: "success",
                    message: "凭证高级打印同步成功",
                });
                syncDialogShow.value = false;
            }
        })
        .finally(() => {
            loading.value = false;
        });
}
</script>

<style lang="less" scoped>
@import "@/style/Voucher/SeniorPrint.less";
</style>
