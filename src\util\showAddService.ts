import { createApp } from "vue";
import AddServiceDialog from "@/components/AddServiceDialog/index.vue";

// 专业版添加客服弹窗
export const showAddServiceDialog = (): Promise<boolean> => {
    return new Promise<boolean>(() => {
        const capp = createApp(AddServiceDialog);
        const container = document.createElement("div");
        container.className = "add-service-dialog";
        capp.mount(container);
        document.body.insertBefore(container, document.body.firstChild); //插入到body最前面，层级更高
    });
};
