<template>
  <div class="deduction-table">
    <LMTable
      :data="props.data"
      :rowKey="props.rowKey"
      :columns="inputStatisticsColumns"
      empty-text="暂无数据">
      <template #type>
        <el-table-column label="进项抵扣类型">
          <template #default="{ row }: { row: IDeductionItem }">
            <div class="type-cell">
              <span>{{ row.deductText }}</span>
              <el-popover
                v-if="row.deductText === '认证相符的增值税专用发票'"
                placement="right"
                trigger="click"
                width="400">
                <template #reference>
                  <el-icon
                    size="14"
                    class="question-icon">
                    <QuestionFilled />
                  </el-icon>
                </template>
                <div class="popover-content">
                  <p>取税款所属期已认证对应发票类型合计数，包括发票类型有：</p>
                  <ul class="invoice-types-list">
                    <li>数电票（增值税专用发票）</li>
                    <li>增值税专用发票</li>
                    <li>数电票（机动车销售统一发票）</li>
                    <li>机动车销售统一发票</li>
                    <li>道路通行费电子普通发票</li>
                    <li>数电票（通行费发票）</li>
                    <li>数电票（航空运输电子客票行程单）</li>
                    <li>数电票（铁路电子客票）</li>
                    <li>税控机动车专用发票</li>
                  </ul>
                </div>
              </el-popover>
              <el-popover
                v-if="row.deductText === '其他'"
                placement="right"
                trigger="click"
                width="400">
                <template #reference>
                  <el-icon
                    size="14"
                    class="question-icon">
                    <QuestionFilled />
                  </el-icon>
                </template>
                <div class="popover-content">
                  <p>取税款所属期已认证对应发票类型合计数，包括发票类型有：</p>
                  <ul class="invoice-types-list">
                    <li>数电票（普通发票）</li>
                    <li>增值税电子普通发票</li>
                    <li>增值税普通发票</li>
                  </ul>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>
      </template>
    </LMTable>
  </div>
</template>

<script setup lang="ts">
  import { formatCount, formatValue } from "../utils"
  import { IDeductionItem } from "../types"

  interface DeductionTableProps {
    data?: IDeductionItem[]
    rowKey?: string
    loading?: boolean
  }

  // 进项统计表列配置
  const inputStatisticsColumns = [
    {
      slot: "type",
    },
    {
      label: "份数",
      prop: "count",
      minWidth: 150,
      formatter: (row: IDeductionItem) => {
        return formatCount(row.count)
      },
    },
    {
      label: "金额",
      prop: "amount",
      minWidth: 150,
      formatter: (row: IDeductionItem) => {
        // 海关进口增值税缴款书特殊处理
        if (row.deductText === "海关进口增值税缴款书") {
          return "--" // 金额显示--
        }
        return formatValue(row.amount)
      },
    },
    {
      label: "税额",
      prop: "tax",
      minWidth: 150,
      formatter: (row: IDeductionItem) => {
        return formatValue(row.tax)
      },
    },
  ]

  const props = defineProps<DeductionTableProps>()
</script>

<style scoped lang="scss">
  .deduction-table {
    .type-cell {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .popover-content {
        p {
          margin-top: 0;
          margin-bottom: 8px;
          font-weight: bold;
        }

        .invoice-types-list {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
            font-size: var(--h1);
          }
        }
      }
    }

    .invoice-types {
      margin-top: 4px;

      .invoice-type-item {
        margin-bottom: 2px;
        color: #606266;
        font-size: 13px;
      }
    }
  }
</style>
