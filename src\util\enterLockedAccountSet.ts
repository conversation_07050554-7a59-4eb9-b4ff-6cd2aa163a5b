import { createApp } from "vue";
import  EnterLockedAccountSet  from "@/components/EnterLockedAccountSet/index.vue";
import ElementPlus from "element-plus";

let capp: any = null; // 在函数外部定义capp

export const EnterLockedAccountSetDialog = (asId:number,asName:string|undefined):Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if(!capp){
            const props = {
                asId,
                asName,
                close: () => {
                    if (close) close();
                    resolve(false);
                    unmountAndRemove()
                },
                confirm: () => {
                    resolve(true);
                    unmountAndRemove()
                },
                cancel: () => {
                    resolve(false);
                    unmountAndRemove()
                },
            };
             capp = createApp( EnterLockedAccountSet, props);
             capp.use(ElementPlus);
        }
        const container = document.createElement("div");
        capp.mount(container);
        document.body.insertBefore(container, document.body.firstChild); //插入到body最前面，层级更高

        const unmountAndRemove= ()=>{
            capp.unmount();
            document.body.removeChild(container);
            capp = null   //重置capp，以便于下次再次创建vue应用
        }
    });
}