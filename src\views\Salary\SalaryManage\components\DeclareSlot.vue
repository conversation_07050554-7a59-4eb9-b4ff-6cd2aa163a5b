<template>
        <div class="declare-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <el-select id="declareYear" v-model="declareSearchInfo.year" style="width: 88px">
                        <el-option value="2021">2021年</el-option>
                        <el-option value="2022">2022年</el-option>
                        <el-option value="2023">2023年</el-option>
                        <el-option value="2024">2024年</el-option>
                        <el-option value="2025">2025年</el-option>
                        <el-option value="2026">2026年</el-option>
                        <el-option value="2027">2027年</el-option>
                        <el-option value="2028">2028年</el-option>
                        <el-option value="2029">2029年</el-option>
                        <el-option value="2030">2030年</el-option>
                    </el-select>
                    <el-radio-group v-model="declareSearchInfo.status" class="ml-10">
                        <el-radio label="1" size="large">申报成功</el-radio>
                        <el-radio label="0" size="large">申报失败</el-radio>
                    </el-radio-group>
                </div>
                <div class="main-tool-right">
                    <a class="button solid-button" id="btnTaxDiffAdj" v-show="declareSearchInfo.status == '1'" @click="showTaxDiffAdj"
                        >调整差异</a
                    >
                    <a class="button ml-10" @click="CloseTaxDeclareRecord">返回</a>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :data="tableData"
                    :columns="columns"
                    :pageIsShow="true"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :current-page="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    empty-text="暂无数据"
                    :scrollbarShow="true"
                    :tableName="setModule"
                >
                    <template #status_name>
                        <el-table-column 
                            label="状态" 
                            align="left" 
                            header-align="left" 
                            prop="status_name"
                            :width="getColumnWidth(setModule, 'status_name')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.status_name === '申报中'">
                                    <img
                                        src="@/assets/Salary/tax-declare-waiting.png"
                                        style="animation: rotating 2s linear infinite; vertical-align: text-bottom; margin-right: 5px"
                                    /><span>申报中</span>
                                </template>
                                <template v-else>
                                    <template v-if="scope.row.status === 4">{{ scope.row.status_name }}</template>
                                    <template v-else>
                                        <template v-if="scope.row.pay_status_name === '未缴税'">
                                            {{ scope.row.status_name }}
                                            <a
                                                href="javascript:void(0)"
                                                attr_pay="1"
                                                id='btn" + row.ID + "'
                                                @click="submitTaxPay(scope.row.salary_period, scope.row.id)"
                                                style="text-decoration: underline; color: #3385ff"
                                                >立即缴税</a
                                            >
                                        </template>
                                        <template v-else-if="scope.row.pay_status_name === '缴税失败'">
                                            {{ scope.row.status_name }}
                                            <a
                                                href="javascript:void(0)"
                                                attr_pay="1"
                                                id='btn" + row.ID + "'
                                                @click="submitTaxPay(scope.row.salary_period, scope.row.id)"
                                                style="text-decoration: underline; color: #3385ff"
                                                >立即缴税</a
                                            >
                                            <img
                                                src="@/assets/Icons/warn.png"
                                                style="width: 12px; margin-left: 2px; cursor: pointer; position: absolute; margin-top: 2px"
                                                @mouseover="showPayResult(scope.row.id)"
                                                @mouseout="hidePayResult(scope.row.id)"
                                            />
                                            <div
                                                v-show="scope.row.payMenoShow"
                                                style="
                                                    position: absolute;
                                                    display: none;
                                                    margin-top: -60px;
                                                    margin-left: 115px;
                                                    background: rgba(0, 0, 0, 0.75);
                                                    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
                                                    font-size: 12px;
                                                    color: #ffffff;
                                                    padding: 8px 8px 8px 8px;
                                                    border-radius: 3px;
                                                "
                                            >
                                                {{ scope.row.pay_memo }}
                                            </div>
                                            <div
                                               v-show="scope.row.payMenoShow"
                                                style="
                                                    position: absolute;
                                                    display: none;
                                                    border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;
                                                    border-width: 5px;
                                                    border-style: solid;
                                                    margin-left: 122px;
                                                    margin-top: -28px;
                                                "
                                            ></div>
                                        </template>
                                        <template v-else>
                                            <template v-if="scope.row.pay_status_name === '缴税中'">
                                                {{ scope.row.status_name }}
                                                <img
                                                    src="@/assets/Salary/tax-declare-waiting.png"
                                                    style="
                                                        animation: rotating 2s linear infinite;
                                                        vertical-align: text-bottom;
                                                        margin-right: 5px;
                                                    "
                                                /><span>缴税中</span>
                                            </template>
                                            <template v-else> {{ scope.row.status_name }} {{ scope.row.pay_status_name }} </template>
                                        </template>
                                    </template>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #diff>
                        <el-table-column
                            label="本次个税差异"
                            prop="diff"
                            header-align="left"
                            min-width="120"
                            align="right"
                            show-overflow-tooltip
                            :width="getColumnWidth(setModule, 'diff')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.diff !== null">
                                    <a
                                        href="javascript:void(0)"
                                        @click="showTaxDiff(scope.row.declare_year_month.replace('年', '').replace('月', ''))"
                                        style="text-decoration: underline"
                                    >
                                        {{ scope.row.diff.toFixed(2) }}</a
                                    >
                                </template>
                                <template v-else-if="scope.row.status_name === '申报成功'">
                                    <img
                                        src="@/assets/Salary/tax-declare-waiting.png"
                                        style="animation: rotating 2s linear infinite; vertical-align: text-bottom; margin-right: 5px"
                                    /><span>计算中...</span>
                                </template>
                                <template v-else> </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #year_diff>
                        <el-table-column
                            label="累计个税差异"
                            prop="year_diff"
                            header-align="left"
                            min-width="120"
                            align="right"
                            show-overflow-tooltip
                            :resizable="false"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.year_diff !== null">
                                    <a
                                        href="javascript:void(0)"
                                        @click="showTaxDiff(scope.row.declare_year_month.replace('年', '').replace('月', ''))"
                                        style="text-decoration: underline"
                                    >
                                        {{ scope.row.year_diff.toFixed(2) }}</a
                                    >
                                </template>
                                <template v-else-if="scope.row.status_name === '申报成功'">
                                    <img
                                        src="@/assets/Salary/tax-declare-waiting.png"
                                        style="animation: rotating 2s linear infinite; vertical-align: text-bottom; margin-right: 5px"
                                    /><span>计算中...</span>
                                </template>
                                <template v-else> </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    <el-dialog v-model="divTaxDiffAdj" title="调整差异" center width="600px" class="dialogDrag">
        <div class="tax-diff-adj-dialog" v-dialogDrag>
            <div class="tax-diff-adj-content">
                <div class="form-line mt-20">
                    <div class="form-title">个税申报月份：</div>
                    <div class="form-field">
                        <el-select v-model="taxDiffAdjDeclarePeriod" placeholder="请选择">
                            <el-option
                                v-for="item in taxDiffAdjDeclarePeriodList"
                                :key="item"
                                :label="getTaxDiffAdjSalaryPeriodLabel(item)"
                                :value="item"
                            ></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-line mt-20">
                    <div class="form-title">工资表月份：</div>
                    <div class="form-field">
                        <span>{{ taxDiffAdjSalaryPeriod }}</span>
                    </div>
                </div>
                <div class="form-line mt-20">
                    <div class="form-title">调整差异：</div>
                    <div class="form-field">
                        <el-radio-group v-model="rdTaxDiffAdjType">
                            <el-radio label="1">本次个税差异</el-radio>
                            <el-radio label="2">累计个税差异</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="form-line mt-20">
                    <div class="form-title">更新工资表数据：</div>
                    <div class="form-field">
                        <el-select v-model="taxDiffAdjUpdateSalary">
                            <el-option
                                v-for="item in taxDiffAdjUpdateSalaryList"
                                :key="item"
                                :label="getTaxDiffAdjSalaryPeriodLabel(item)"
                                :value="item"
                            ></el-option>
                        </el-select>
                    </div>
                </div>
                <div class="form-line mt-20 mb-20">
                    <div class="form-title">&nbsp;</div>
                    <div class="form-field">
                        <span class="highlight-red">*个税差异将在该月份更新调整数据</span>
                    </div>
                </div>
            </div>
            <div class="dialog-buttons">
                <a class="solid-button" @click="submitTaxDiffAdj">确定</a>
                <a class="button ml-10" @click="cancelTaxDiffAdj">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import { usePagination } from "@/hooks/usePagination";
import { ref, watch,onMounted,onUnmounted } from "vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "SalaryDeclare";
interface ITaxDeclarationWaitParams {
    waitingFrom: string;
    employeeCount: number;
    spanEmpCount: string;
    waitingText: string;
}

const props = defineProps<{
    declareData: any;
    declareTotal: number;
    declareStatus: any;
    showTaxDeclarationWait: (params: ITaxDeclarationWaitParams) => void;
    declareRecordHandle: (type: 0 | 1) => void;
    queryTaxDeclareRecord: () => void;
}>();
const emits = defineEmits(["CloseTaxDeclareRecord", "enterTaxDiff"]);

const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();

const declareSearchInfo = ref({
    year: "",
    status: "0",
});
const taxDiffAdjDeclarePeriod = ref("");
const taxDiffAdjDeclarePeriodList = ref<Array<string>>([]);
const divTaxDiffAdj = ref(false);
const taxDiffAdjSalaryPeriod = ref("");
const rdTaxDiffAdjType = ref<"1" | "2">("1");
const taxDiffAdjUpdateSalary = ref("");
const taxDiffAdjUpdateSalaryList = ref<Array<string>>([]);

interface IQueryTaxDiffAdjInfoBack {
    salaryPeriod: number;
    existsSalaryPeriods: Array<number>;
}

watch(taxDiffAdjDeclarePeriod, () => {
    queryTaxDiffAdjInfo();
});
function queryTaxDiffAdjInfo() {
    const declarePeriod = taxDiffAdjDeclarePeriod.value;
    if (declarePeriod == null || declarePeriod == "") {
        ElNotify({
            message: "亲，没有可供调整的数据。",
            type: "warning",
        });
        return false;
    }

    //调整个税差异参数
    request({ url: "/api/SalaryTaxDifference/GetAdjustInfo?period=" + declarePeriod, method: "post" }).then(
        (result: IResponseModel<IQueryTaxDiffAdjInfoBack>) => {
            if (result.state !== 1000) {
                ElNotify({
                    message: "亲，操作失败，请稍后重试。~",
                    type: "warning",
                });
                return;
            }
            //申报时使用的工资数据月份
            const salaryPeriod = result.data.salaryPeriod;
            taxDiffAdjSalaryPeriod.value = getTaxDiffAdjSalaryPeriodLabel(salaryPeriod);
            rdTaxDiffAdjType.value = "1";
            taxDiffAdjUpdateSalaryList.value = result.data.existsSalaryPeriods.map((item) => {
                return item + "";
            });
            taxDiffAdjUpdateSalary.value = result.data.existsSalaryPeriods[0] + "" || "";
        }
    );
}
let canSave = true;
function submitTaxDiffAdj() {
    if (!canSave) {
        return false;
    }
    const declarePeriod = taxDiffAdjDeclarePeriod.value;
    const adjType = rdTaxDiffAdjType.value;
    const adjSalaryPeriod = taxDiffAdjUpdateSalary.value;

    if (declarePeriod == null || declarePeriod == "") {
        ElNotify({
            message: "亲，没有可调整的数据。",
            type: "warning",
        });
        return false;
    }
    const params = {
        declarPeriod: declarePeriod,
        adjType: adjType,
        adjSalaryPeriod: adjSalaryPeriod,
    };
    canSave = false;
    request({
        url: "/api/SalaryTaxDifference/Adjust?" + getUrlSearchParams(params),
        method: "post",
    })
        .then((result: IResponseModel<boolean>) => {
            canSave = true;
            if (result.state !== 1000 || !result.data) {
                ElNotify({
                    message: result.msg || "亲，操作失败，请稍后重试。~",
                    type: "warning",
                });
                return;
            }
            divTaxDiffAdj.value = false;
            props.queryTaxDeclareRecord();
        })
        .catch(() => {
            canSave = true;
            ElNotify({
                message: "亲，操作失败，请稍后重试。~",
                type: "warning",
            });
        });
}
function cancelTaxDiffAdj() {
    divTaxDiffAdj.value = false;
}

defineExpose({ declareSearchInfo });

function CloseTaxDeclareRecord() {
    emits("CloseTaxDeclareRecord");
}

watch(
    () => props.declareStatus,
    () => {
        declareSearchInfo.value.status = props.declareStatus.declareStatus;
    }
);

watch(
    () => declareSearchInfo.value.status,
    () => {
        setColumns();
    }
);
const columns = ref<Array<IColumnProps>>();

function setColumns() {
    if (declareSearchInfo.value.status == "0") {
        columns.value = [
            { label: "个税申报年度", prop: "declare_year", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'declare_year') },
            { label: "个税申报月份", prop: "declare_year_month", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'declare_year_month') },
            { label: "工资表月份", prop: "salary_year_month", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'salary_year_month') },
            { label: "申报人数", prop: "salary_count", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'salary_count') },
            { label: "操作人", prop: "created_by_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'created_by_name') },
            { label: "操作时间", prop: "created_date_str", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'created_date_str') },
            { label: "状态", prop: "status_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'status_name') },
            { label: "备注", prop: "memo", align: "left", headerAlign: "left", resizable: false },
        ];
    } else {
        columns.value = [
            { label: "个税申报年度", prop: "declare_year", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'declare_year') },
            { label: "个税申报月份", prop: "declare_year_month", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'declare_year_month') },
            { label: "工资表月份", prop: "salary_year_month", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'salary_year_month') },
            { label: "申报人数", prop: "salary_count", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'salary_count') },
            { label: "操作人", prop: "created_by_name", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'created_by_name') },
            { label: "操作时间", prop: "created_date_str", align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'created_date_str') },
            { slot: "status_name" },
            {
                label: "申报金额",
                prop: "amount",
                align: "left",
                headerAlign: "left",
                width: getColumnWidth(setModule, 'amount'),
                formatter: function (row) {
                    return row.amount != null ? row.amount.toFixed(2) : "0.00";
                },
            },
            { slot: "diff" },
            { slot: "year_diff" },
        ];
    }
}

function getTaxDiffAdjSalaryPeriodLabel(value: string | number): string {
    return value.toString().substring(0, 4) + "年" + value.toString().substring(4, 6) + "月";
}

function showTaxDiffAdj() {
    //查询申报成功的年月列表
    request({
        url: `/api/SalaryTaxDeclare/GetTaxDeclarePeriodList`,
        method: "post",
    }).then((res: IResponseModel<Array<number>>) => {
        if (res.state === 1000) {
            if (res.data.length == 0) {
                ElNotify({
                    message: "亲，您暂无申报数据可以调整哦~",
                    type: "warning",
                });
            } else {
                taxDiffAdjDeclarePeriodList.value = res.data.map((item) => {
                    return item + "";
                });
                taxDiffAdjDeclarePeriod.value = res.data[0] + "" || "";
                divTaxDiffAdj.value = true;
            }
        } else {
            ElNotify({
                message: "亲，操作失败，请稍后重试。~",
                type: "warning",
            });
        }
    });
}

//获取申报记录列表
const tableData = ref<any[]>([]);
const getTableData = () => {
    const params = {
        year: declareSearchInfo.value.year,
        status: declareSearchInfo.value.status,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    request({
        url: `/api/SalaryTaxDeclare/PagingList?` + getUrlSearchParams(params),
    })
        .then((res: any) => {
            if (res.state === 1000) {
                // setColumns();
                tableData.value = res.data.data;
                tableData.value.forEach((item: any) => {
                    item.payMenoShow = false;
                });
                paginationData.total = res.data.count;
            }
        })
        .catch((error) => {
            console.log(error);
        });
};

watch(
    [() => props.declareData, () => props.declareTotal],
    () => {
        tableData.value = props.declareData;
        tableData.value.forEach((item: any) => {
            item.payMenoShow = false;
        });
        setColumns();
        paginationData.total = props.declareTotal;
    },
    { immediate: true }
);
interface ISalaryTaxDeclareBack {
    applyId: string;
    employeeCount: number;
}
//个税缴费
function submitTaxPay(period: number, applyId: number) {
    // if ($("#btn" + applyId).attr("attr_pay") == "0") {
    //     Notify("提示", "缴税中，请稍后。");
    //     return false;
    // }
    // $("#btn" + applyId).html("缴税中...");
    // $("#btn" + applyId).attr("attr_pay", "0");
    request({ url: "/api/SalaryTaxDeclare/TaxPay?mid=" + period + "&applyId=" + applyId, method: "post" })
        .then((res: IResponseModel<ISalaryTaxDeclareBack>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: "操作失败，请稍后重试。" });
                return;
            }
            const params = {
                waitingFrom: "2",
                waitingText: "正在缴税中",
                employeeCount: res.data.employeeCount,
                spanEmpCount: res.data.employeeCount + "个员工，缴税完成后可以查看缴税状态",
            };
            props.showTaxDeclarationWait(params);
            startQueryTaxPayResult(res.data.applyId);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "操作失败，请稍后重试。" });
        });
}

//查询缴税结果
let timer: any = null;
function startQueryTaxPayResult(applyId: string) {
    timer = setInterval(function () {
        queryTaxPayResult(timer, applyId);
    }, 20000);
}
function queryTaxPayResult(timer: any, applyId: string) {
    request({ url: "/api/SalaryTaxDeclare/GetTaxPayStatusById?applyId=" + applyId, method: "post" }).then((res: IResponseModel<number>) => {
        if (res.state !== 1000) {
            clearInterval(timer);
            return;
        }
        const status = res.data;
        if (status === 11) {
            //成功
            clearInterval(timer);
            console.log("success and stop query pay result");
            props.declareRecordHandle(1);
            CloseTaxDeclareRecord()
        } else if (status === 12) {
            //失败
            clearInterval(timer);
            console.log("fail and stop query pay result");
            props.declareRecordHandle(1);
            CloseTaxDeclareRecord()
        } else {
            //缴税中
        }
    });
}
//缴税失败原因
function showPayResult(id: number) {
    tableData.value.find((item: any) => {
        if (item.id === id) {
            item.payMenoShow = true;
        }
    });
}
function hidePayResult(id: number) {
    tableData.value.find((item: any) => {
        if (item.id === id) {
            item.payMenoShow = false;
        }
    });
}
//个税差异
function showTaxDiff(period: string) {
    //查询申报成功的年月列表
    request({
        url: `/api/SalaryTaxDeclare/GetTaxDeclarePeriodList`,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            let optionList = [];
            for (let i = 0; i < res.data.length; i++) {
                let value = res.data[i].toString();
                let text = value.toString().substring(0, 4) + "年" + value.toString().substring(4, 6) + "月";
                optionList.push({ value: value, label: text });
            }
            emits("enterTaxDiff", optionList, period);
        } else {
            ElNotify({
                message: "亲，操作失败，请稍后重试。~",
                type: "warning",
            });
        }
    });
}

//监听年份选择框和成功和失败记录单选框
watch(
    declareSearchInfo,
    () => {
        getTableData();
    },
    { deep: true }
);

//轮询结果，但本身立即缴税后是有轮询的，后续是否可结合，待商榷
let dataPollingTimer:number = 0;
onMounted(() => {
    dataPollingTimer = setInterval(function () {
        getTableData();
    }, 1000 * 60 * 5);
});
onUnmounted(() => {
    clearInterval(dataPollingTimer);
});
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";
.declare-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}
.main-center {
    background-color: var(--white);
    :deep(.el-table__empty-text){
        line-height: 400px;
    }
}
.tax-diff-adj-dialog {
    font-size: 12px;
    .tax-diff-adj-content {
        display: flex;
        flex-direction: column;
        .form-line {
            display: flex;
            align-items: center;
            .form-title {
                width: 188px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                text-align: right;
                margin-right: 40px;
            }
            .form-field {
                :deep(.el-select) {
                    width: 260px;
                }
            }
        }
    }
    .dialog-buttons {
        padding-top: 10px;
        padding-bottom: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
