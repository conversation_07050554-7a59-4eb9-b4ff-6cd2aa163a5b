<template>
    <div class="title">{{ titleName }}</div>
    <CategoryForm
        @changeAAType="changeAAType"
        @formChanged="handleFormChanged"
        @formCancel="handleCancel"
        ref="categoryFormRef"
        :aaLength="aaLength"
        v-show="aaType === 10000"
    />
    <CustomerOrVendorForm
        :aaType="10001"
        name="客户"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        @formCancel="handleCancel"
        ref="customerformRef"
        v-show="aaType === 10001"
    />
    <CustomerOrVendorForm
        :aaType="10002"
        name="供应商"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        @formCancel="handleCancel"
        ref="vendorformRef"
        v-show="aaType === 10002"
    />
    <EmployeeForm
        @formCancel="handleCancel"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        ref="employeeFormRef"
        v-show="aaType === 10003"
    />
    <DepartmentForm
        @formCancel="handleCancel"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        ref="departmentFormRef"
        v-show="aaType === 10004"
    />
    <ProjectForm
        @formCancel="handleCancel"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        ref="projectFormRef"
        v-show="aaType === 10005"
    />
    <StockForm
        @formCancel="handleCancel"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        ref="stockFormRef"
        v-show="aaType === 10006"
    />
    <OthersForm
        :aaType="aaType"
        @formCancel="handleCancel"
        @formCancelEdit="cancelEdit"
        @formChanged="handleFormChanged"
        ref="othersFormRef"
        :rows="rows"
        v-show="aaType > 10007"
    />
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from "vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import CategoryForm from "./CategoryForm.vue";
import CustomerOrVendorForm from "./CustomerOrVendorForm.vue";
import EmployeeForm from "./EmployeeForm.vue";
import DepartmentForm from "./DepartmentForm.vue";
import ProjectForm from "./ProjectForm.vue";
import StockForm from "./StockForm.vue";
import OthersForm from "./OthersForm.vue";

const categoryFormRef = ref<InstanceType<typeof CategoryForm>>();
const customerformRef = ref<InstanceType<typeof CustomerOrVendorForm>>();
const vendorformRef = ref<InstanceType<typeof CustomerOrVendorForm>>();
const employeeFormRef = ref<InstanceType<typeof EmployeeForm>>();
const departmentFormRef = ref<InstanceType<typeof DepartmentForm>>();
const projectFormRef = ref<InstanceType<typeof ProjectForm>>();
const stockFormRef = ref<InstanceType<typeof StockForm>>();
const othersFormRef = ref<InstanceType<typeof OthersForm>>();

const props = defineProps<{ rows: any; aaLength: number }>();

const aaType = ref(10000);
const formAaType = ref(10001);
const titleName = ref("核算类别设置");
const rows = computed(() => props.rows);

const emit = defineEmits(["changeAAType", "handleCancel"]);
const handleCancel = () => {
    resetInit()
    emit("handleCancel", formAaType.value)
};
const changeAAType = () => {
    resetInit()
    emit("changeAAType")
};

const handleNew = (type: number, aaNum: string) => {
    aaType.value = type;
    switch (type) {
        case 10000:
            categoryFormRef.value?.changeType("New");
            break;
        case 10001:
            customerformRef.value?.changeType("New");
            customerformRef.value?.editForm({ aaNum });
            break;
        case 10002:
            vendorformRef.value?.changeType("New");
            vendorformRef.value?.editForm({ aaNum });
            break;
        case 10003:
            employeeFormRef.value?.changeType("New");
            employeeFormRef.value?.editForm({ aaNum });
            break;
        case 10004:
            departmentFormRef.value?.changeType("New");
            departmentFormRef.value?.editForm({ aaNum });
            break;
        case 10005:
            projectFormRef.value?.changeType("New");
            projectFormRef.value?.editForm({ aaNum });
            break;
        case 10006:
            stockFormRef.value?.changeType("New");
            stockFormRef.value?.editForm({ aaNum });
            break;
        default:
            othersFormRef.value?.changeType("New");
            othersFormRef.value?.editForm({ aaNum });
            break;
    }
    nextTick(() => {
        init =true
    });
};
const handleEdit = (type: number, params: any) => {
    aaType.value = type;
    formAaType.value = params.AATypeId;
    switch (type) {
        case 10000:
            categoryFormRef.value?.changeType("Edit");
            categoryFormRef.value?.editForm(params);
            break;
        case 10001:
            customerformRef.value?.changeType("Edit");
            customerformRef.value?.editForm(params);
            break;
        case 10002:
            vendorformRef.value?.changeType("Edit");
            vendorformRef.value?.editForm(params);
            break;
        case 10003:
            employeeFormRef.value?.changeType("Edit");
            employeeFormRef.value?.editForm(params);
            break;
        case 10004:
            departmentFormRef.value?.changeType("Edit");
            departmentFormRef.value?.editForm(params);
            break;
        case 10005:
            projectFormRef.value?.changeType("Edit");
            projectFormRef.value?.editForm(params);
            break;
        case 10006:
            stockFormRef.value?.changeType("Edit");
            stockFormRef.value?.editForm(params);
            break;
        default:
            othersFormRef.value?.changeType("Edit");
            othersFormRef.value?.editForm(params);
            break;
    }
    nextTick(() => {
        init =true
    });
};
const handleReset = (aaType: number) => {
    switch (aaType) {
        case 10000:
            categoryFormRef.value?.resetForm();
            break;
        case 10001:
            customerformRef.value?.resetForm();
            break;
        case 10002:
            vendorformRef.value?.resetForm();
            break;
        case 10003:
            employeeFormRef.value?.resetForm();
            break;
        case 10004:
            departmentFormRef.value?.resetForm();
            break;
        case 10005:
            projectFormRef.value?.resetForm();
            break;
        case 10006:
            stockFormRef.value?.resetForm();
            break;
        default:
            othersFormRef.value?.resetForm();
            break;
    }
};
let init = false;
const isEditting = ref(false);
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
const resetInit=()=>{
    init=false;
    isEditting.value = false;
}
const handleFormChanged = () => {
    if (!init) return;
    isEditting.value = true;
};
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
const cancelEdit=()=>{
    nextTick(() => {
        isEditting.value = false;
    });
}
defineExpose({ handleNew, handleEdit, handleReset });

watch(aaType, (val) => {
    let name = "";
    switch (val) {
        case 10001:
            name = " - 客户";
            break;
        case 10002:
            name = " - 供应商";
            break;
        case 10003:
            name = " - 职员";
            break;
        case 10004:
            name = " - 部门";
            break;
        case 10005:
            name = " - 项目";
            break;
        case 10006:
            name = " - 存货";
            break;
        case 10007:
            name = " - 现金流";
            break;
        default:
            name = "";
            break;
    }
    titleName.value = "辅助核算设置" + name;
    if (val === 10000) {
        titleName.value = "核算类别设置";
    }
});
</script>

<style lang="less" scoped></style>
