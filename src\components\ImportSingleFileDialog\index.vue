<template>
    <el-dialog
        v-model="importShow"
        :title="importTitle"
        destroy-on-close
        :lock-scroll="false"
        center
        :width="dialogWidth"
        class="custom-confirm dialogDrag"
        :modal-class="modalClass"
    >
        <div v-show="!importErrorShow && importShow" v-dialogDrag class="import-dialog">
            <slot name="top-tips"><div style="margin-top: 20px"></div></slot>
            <div class="download-content">
                <slot name="download">
                    1、下载导入模板，按照模板格式进行数据整理再导入<a class="link ml-20" @click="downloadTemplate">下载模板</a>
                </slot>
            </div>
            <div class="import-content">
                <slot name="import-content">
                    <span>2、选择文件导入</span>
                </slot>
                <label class="file-button" :style="{ marginLeft: fileMarginBotton }">
                    <input @change="onFileSelected" ref="fileInputRef" type="file" :accept="allowFileType" />
                    <a class="link">选择文件</a>
                </label>
                <Tooltip :content="fileName" :maxWidth="160" placement="right">
                    <span class="file-name">{{ fileName }}</span>
                </Tooltip>
            </div>
            <slot name="bottom-tips">
                <div style="margin-bottom: 20px"></div>
            </slot>
            <div class="buttons">
                <a class="button solid-button" @click="uploadFile">导入</a>
                <a class="button" :class="{ 'ml-10': !isErp }" @click="importShow = false">取消</a>
            </div>
        </div>
        <div v-show="importErrorShow && importShow" v-dialogDrag class="import-error-dialog">
            <div class="import-error-msg" v-html="importErrorMessage"></div>
            <div class="buttons">
                <a class="button solid-button" @click="cancelImportError">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch, toRef } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { useLoading } from "@/hooks/useLoading";
import { useRouterArrayStoreHook, type IRouterModel } from "@/store/modules/routerArray";
import Tooltip from "@/components/Tooltip/index.vue";

const fileName = ref("");
const selectedFile = ref();
const fileInputRef = ref();

const props = defineProps({
    dialogWidth: {
        type: String,
        default: "550px",
    },
    importShow: {
        type: Boolean,
        default: false,
    },
    needLoading: {
        type: Boolean,
        default: false,
    },
    importTitle: {
        type: String,
        default: "导入票据信息",
    },
    importUrl: {
        type: String,
        default: "",
    },
    uploadCheck: {
        type: Function,
        default: () => {
            return true;
        },
    },
    uploadSuccess: {
        type: Function,
        default: () => {},
    },
    uploadError: {
        type: Function,
        default: () => {
            ElNotify({
                type: "warning",
                message: "亲，出错了，请刷新页面重试或联系客服",
            });
        },
    },
    uploadFinish: {
        type: Function,
        default: () => {},
    },
    downloadTemplate: {
        type: Function,
        default: () => {},
    },
    importErrorShow: {
        type: Boolean,
        default: false,
    },
    importErrorMessage: {
        type: String,
        default: "",
    },
    allowFileType: {
        type: String,
        default: ".xls,.xlsx",
    },
    fileMarginBotton: {
        type: String,
        default: "20px",
    },
    isClearFile: { 
        type: Boolean,
        default: true,
    },
    modalClass: {
        type: String,
        default: "modal-class",
    },
    preCheck: {
        type: Boolean,
        default: true,
    }
});

const emit = defineEmits(["update:importShow", "update:importErrorShow", "update:importErrorMessage"]);
const importShow = computed({
    get() {
        return props.importShow;
    },
    set(value) {
        emit("update:importShow", value);
    },
});

const importErrorShow = computed({
    get() {
        return props.importErrorShow;
    },
    set(value) {
        emit("update:importErrorShow", value);
    },
});

const importErrorMessage = computed({
    get() {
        return props.importErrorMessage;
    },
    set(value) {
        emit("update:importErrorMessage", value);
    },
});
const isErp = ref(window.isErp);
const importTitle = computed(() => {
    return props.importTitle;
});

const downloadTemplate = () => {
    props.downloadTemplate();
};

let canImport = true;
const uploadFile = async () => {
    if (!canImport) return;
    canImport = false;
    if (props.preCheck && !selectedFile.value) {
        ElNotify({
            type: "warning",
            message: "请选择文件",
        });
        canImport = true;
        return;
    }
    if (!(await props.uploadCheck(selectedFile.value))) {
        canImport = true;
        return;
    }

    const fileExtension = selectedFile.value.name.split(".").pop().toLowerCase();
    if (
        props.allowFileType !== "" && !props.allowFileType
            .split(",")
            .map((ext) => ext.trim().replace(".", ""))
            .includes(fileExtension)
    ) {
        ElNotify({
            type: "warning",
            message: `仅支持上传${props.allowFileType}文件`,
        });
        canImport = true;
        return;
    }
    if (props.needLoading) useLoading().enterLoading("努力导入中，请稍候...");
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    request({
        url: props.importUrl,
        data: formData,
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
        },
    })
        .then((res) => {
            if (props.importTitle === '导入凭证') {
                props.uploadSuccess(res, selectedFile.value);
            } else {
                props.uploadSuccess(res);
            }
        })
        .catch((error) => {
            props.uploadError(error);
        })
        .finally(() => {
            props.uploadFinish();
            canImport = true;
            if (props.needLoading) useLoading().quitLoading();
        });
};

const onFileSelected = (event: Event) => {
    const input = event.target as HTMLInputElement;
    const file: File = (input.files as FileList)[0];
    if (!file) {
        fileName.value = "";
        selectedFile.value = null;
        return;
    }
    fileName.value = file.name;
    selectedFile.value = file;
};
const routerArrayStore = useRouterArrayStoreHook();
const routerArray = toRef(routerArrayStore, "routerArray");
const currentPage = computed(() => {
    return routerArray.value.find((item) => item.alive);
});
function cancelImportError() {
    importShow.value = false;
    routerArrayStore.refreshRouter(currentPage.value!.path)
    importErrorShow.value = false;
    importErrorMessage.value = "";
}

watch(
    () => props.importShow,
    (newVal: boolean) => {
        if (newVal && props.isClearFile) {
            // 隐藏上传对话框时，清空文件选择
            fileName.value = "";
            selectedFile.value = null;
            if (fileInputRef.value) fileInputRef.value.value = null;
        }
    }
);
</script>

<style lang="less" scoped>
.import-dialog {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    .download-content {
        margin-top: 10px;
        margin-left: 40px;
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
    }

    .import-content {
        margin-top: 20px;
        margin-left: 40px;
        font-size: var(--font-size);
        color: var(--font-color);
        line-height: 20px;
        display: flex;
        align-items: center;
        flex-direction: row;
        .file-name {
            display: inline-block;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 20px;
            word-break: break-all;
            white-space: nowrap;
            cursor: pointer;
        }
        :deep(.el-tooltip__trigger) {
            height: 20px;
        }
        .file-button {
            // margin-top: 10px;
            margin-left: 20px;
        }
    }

    .buttons {
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        padding: 10px;
    }
}

.import-error-dialog {
    & .import-error-msg {
        padding: 20px 40px;
        color: var(--font-color);
        font-size: var(--font-size);
        line-height: var(--line-height);
        min-height: 120px;
    }
    & .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }
}
</style>
