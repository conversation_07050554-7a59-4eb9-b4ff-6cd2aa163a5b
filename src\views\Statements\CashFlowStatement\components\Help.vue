<template>
    <div class="help-tip">
        <div class="help-icon" style="margin-right: 2px"></div>
        <a class="link">帮助</a>
        <div class="help-tip-expend">
            <div class="help-tip-content" id="alertcontent">
                <div>公式法</div>
                <div>在凭证上录入数据，系统会自动核算现金流量表，如需修改，点击报表底稿即可调整。</div>
                <div>&nbsp;</div>
                <div>辅助核算法</div>
                <div>
                    设置“{{accountStandard!==3?'库存':''}}现金”、“银行存款”、“其他货币资金”等货币类科目为"辅助核算-现金流"，在凭证上录入辅助核算的现金流数据，系统会自动核算现金流量表。
                </div>
                <div>&nbsp;</div>
                <div>如果报表不平，请检查报表各个项目是否与“{{accountStandard!==3?'库存':''}}现金”、“银行存款”、 “其他货币资金”等货币类科目的数据存在差异</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    const props=defineProps({
        accountStandard:{
            type:Number,
        required:true
        }

    })
</script>

<style lang="less" scoped>
.help-tip {
    position: relative;
    width: 50px;
    &:hover {
        .help-tip-expend {
            display: block;
        }
    }
    .help-tip-expend {
        display: none;
        position: absolute;
        left: -88.5px;
        top: 20px;
        padding-top: 6px;
        width: 227px;
        text-align: center;
        transition: var(--transition-time);
        z-index: 10;
        cursor: pointer;
        .help-tip-content {
            text-align: left;
            background-color: rgba(0, 0, 0, 0.8);
            padding: 10px;
            box-shadow: 0px 0px 20px 0px rgba(179, 179, 179, 0.3);
            color: var(--white);
            font-size: var(--h5);
            line-height: 17px;
        }
    }
    .help-icon {
        display: inline-block;
        height: 16px;
        width: 16px;
        background: url(@/assets/Icons/help.png) no-repeat;
        vertical-align: top;
        margin-top: 2px;
        margin-right: 2px;
    }
}
</style>
