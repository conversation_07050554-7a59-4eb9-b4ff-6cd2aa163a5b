<template>
    <div class="content">
        <div class="slot-title">{{ formType === "add" ? "新增票据" : "编辑票据" }}</div>
        <div class="slot-content">
            <div class="title">{{ formType === "add" ? "新增票据" : "编辑票据" }}</div>
            <div class="main-center">
                <div class="edit-draft-form">
                    <el-form
                        ref="ruleFormRef"
                        inline
                        label-width="132px"
                        label-suffix=":"
                        class="draft-form"
                        :model="draftForm"
                        :show-message="false"
                        :status-icon="false"
                        :validate-on-rule-change="false"
                    >
                        <div class="top-form">
                            <div class="first-col">
                                <el-form-item label="上传票面" class="line-item">
                                    <el-upload
                                        ref="uploadRef"
                                        class="avatar-uploader"
                                        action=""
                                        :auto-upload="false"
                                        :show-file-list="false"
                                        :on-change="avatarUploadChange"
                                    >
                                        <div v-if="imageUrl" class="has-image">
                                            <img :src="imageUrl" class="avatar" />
                                            <el-icon
                                                class="avatar-delete-icon"
                                                @click.stop="
                                                    imageUrl = '';
                                                    draftForm.draftPic = '';
                                                "
                                            >
                                                <Delete />
                                            </el-icon>
                                        </div>
                                        <div v-else class="avatar-uploader-tips">
                                            <el-icon class="avatar-uploader-icon">
                                                <Plus />
                                            </el-icon>
                                            <span class="avatar-uploader-text">上传票面</span>
                                        </div>
                                    </el-upload>
                                </el-form-item>
                                <el-form-item label="票据号码" required class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.draftNo"
                                        ref="draftNoRef"
                                        @keydown.enter="draftAmountRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item label="出票人全称" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.drawerName"
                                        ref="drawerNameRef"
                                        @keydown.enter="drawerAccountRef.focus()"
                                    />
                                </el-form-item>

                                <el-form-item label="收票人全称" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.receiverName"
                                        ref="receiverNameRef"
                                        @keydown.enter="receiverAccountRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item v-show="draftForm.draftType !== 2" label="承兑人全称" required class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.acceptorName"
                                        ref="acceptorNameRef"
                                        @keydown.enter="acceptorAccountRef.focus()"
                                    />
                                </el-form-item>
                            </div>
                            <div class="second-col">
                                <el-form-item label="出票日" required class="line-item">
                                    <el-date-picker
                                        class="line-item-field line-item-date"
                                        v-model="draftForm.draftCreateDate"
                                        value-format="YYYY-MM-DD"
                                        type="date"
                                        clearable
                                        :editable="false"
                                        placeholder=""
                                        @change="DraftEndDateTip"
                                        ref="draftCreateDateRef"
                                        @keydown.enter="draftTypeRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item label="汇款到期日" :status-icon="false" class="line-item">
                                    <el-date-picker
                                        class="line-item-field line-item-date"
                                        v-model="draftForm.draftEndDate"
                                        value-format="YYYY-MM-DD"
                                        type="date"
                                        clearable
                                        :editable="false"
                                        placeholder=""
                                        @change="DraftEndDateTip"
                                        ref="draftEndDateRef"
                                        @keydown.enter="draftStatusRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item label="票据金额" required class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.draftAmount"
                                        @input="handleAmountInput($event)"
                                        ref="draftAmountRef"
                                        @keydown.enter="drawerNameRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item label="出票人账号" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.drawerAccount"
                                        ref="drawerAccountRef"
                                        @keydown.enter="drawerBankRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item label="收票人账号" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.receiverAccount"
                                        ref="receiverAccountRef"
                                        @keydown.enter="receiverBankRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item v-show="draftForm.draftType !== 2" label="承兑人账号/行号" required class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.acceptorAccount"
                                        ref="acceptorAccountRef"
                                        @keydown.enter="acceptorBankRef.focus()"
                                    />
                                </el-form-item>
                            </div>
                            <div class="third-col">
                                <el-form-item label="票据种类" required class="line-item">
                                    <el-select
                                        class="line-item-field line-item-select"
                                        :suffix-icon="CaretBottom"
                                        v-model="draftForm.draftType"
                                        placeholder=""
                                        ref="draftTypeRef"
                                        @keydown.enter="draftEndDateRef.focus()"
                                        :filterable="true"
                                        :filter-method="draftTypeFilterMethod"
                                    >
                                        <el-option
                                            v-for="item in showDraftTypeOptions"
                                            :key="item.value"
                                            :label="item.name"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="票据状态" required class="line-item">
                                    <el-select
                                        class="line-item-field line-item-select"
                                        :suffix-icon="CaretBottom"
                                        v-model="draftForm.draftStatus"
                                        placeholder=" "
                                        @change="draftStatusChange"
                                        ref="draftStatusRef"
                                        @keydown.enter="draftNoRef.focus()"
                                        :filterable="true"
                                        :filter-method="draftStatusFilterMethod"
                                    >
                                        <el-option
                                            v-for="item in showDraftStatusOptions"
                                            :key="item.value"
                                            :label="item.name"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="能否转让" required class="line-item">
                                    <el-radio-group class="line-item-field" v-model="draftForm.canTransfer">
                                        <el-radio :label="1" :value="1"> 能 </el-radio>
                                        <el-radio :label="0" :value="0">否</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item label="出票人开户银行" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.drawerBank"
                                        ref="drawerBankRef"
                                        @keydown.enter="receiverNameRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item label="收票人开户银行" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.receiverBank"
                                        ref="receiverBankRef"
                                        @keydown.enter="draftForm.draftType !== 2 ? acceptorNameRef.focus() : memoRef.focus()"
                                    />
                                </el-form-item>
                                <el-form-item v-show="draftForm.draftType !== 2" label="承兑人开户银行" class="line-item">
                                    <el-input
                                        class="line-item-field"
                                        v-model="draftForm.acceptorBank"
                                        ref="acceptorBankRef"
                                        @keydown.enter="memoRef.focus()"
                                    />
                                </el-form-item>
                            </div>
                        </div>
                        <div class="memo-row">
                            <el-form-item label="备注" class="line-item line-item-memo">
                                <el-input
                                    class="line-item-field line-item-field-memo"
                                    :rows="4"
                                    v-model="draftForm.memo"
                                    type="textarea"
                                    ref="memoRef"
                                />
                            </el-form-item>
                        </div>
                        <div class="attach-file-center">
                            <div class="upload-title">附件</div>
                            <div v-if="draftForm.attachsCount === 0" class="upload-item" @click.stop="getAttachFileList">
                                <div class="upload-item-title"><img src="@/assets/Invoice/upload.png" /></div>
                                <div class="upload-item-tip">单个文件不超过 100M</div>
                            </div>
                            <template v-else>
                                <a class="link" @click.stop="getAttachFileList">查看附件({{ draftForm.attachsCount }})</a>
                            </template>
                        </div>
                        <div class="bottom-row">
                            <el-form-item class="form-button">
                                <a class="button solid-button mr-10" @click="saveSubmit"> 保存 </a>
                                <a class="button" @click="resetForm">取消</a>
                            </el-form-item>
                        </div>
                    </el-form>
                </div>
            </div>
        </div>
        <UploadFileDialog :readonly="readonly" ref="uploadFileDialogRef" @save="saveAttachFile" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, watchEffect } from "vue";
import { CaretBottom, Plus, Delete } from "@element-plus/icons-vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { useLoading } from "@/hooks/useLoading";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import dayjs from "dayjs";

import type { ITableItem, IDraftStatusOptions, IEditDraftItem, IORCRes } from "../types";
import type { IFileInfo, IGetAttachFileListBack } from "@/components/UploadFileDialog/types";

import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

let emit = defineEmits(["addDraftSuccess"]);
const props = defineProps({
    editData: {
        type: Object,
        defined: () => {},
    },
    formType: {
        type: String,
        required: true,
    },
    editDraftPic: {
        type: String,
        default: "",
    },
    draftStatusOptions: {
        type: Array<IDraftStatusOptions>,
        default: () => [],
    },
    draftTypeOptions: {
        type: Array<IDraftStatusOptions>,
        default: () => [],
    },
});
const imageUrl = ref("");

const draftForm = reactive<IEditDraftItem>({
    id: "",
    draftCreateDate: "",
    draftEndDate: "",
    draftStatus: 1,
    draftType: 1,
    draftNo: "",
    draftAmount: "",
    canTransfer: 1,
    drawerName: "",
    drawerAccount: "",
    drawerBank: "",
    receiverName: "",
    receiverAccount: "",
    receiverBank: "",
    acceptorName: "",
    acceptorAccount: "",
    acceptorBank: "",
    memo: "",
    draftPic: "",
    attachsCount: 0,
    attachFiles: "",
});
let attachFiles = ref<Array<IFileInfo>>([]);
const draftCreateDateRef = ref();
const draftEndDateRef = ref();
const draftStatusRef = ref();
const draftTypeRef = ref();
const draftNoRef = ref();
const draftAmountRef = ref();
const drawerNameRef = ref();
const drawerAccountRef = ref();
const drawerBankRef = ref();
const receiverNameRef = ref();
const receiverAccountRef = ref();
const receiverBankRef = ref();
const acceptorNameRef = ref();
const acceptorAccountRef = ref();
const acceptorBankRef = ref();
const memoRef = ref();
let ruleFormRef = ref();
const resetForm = () => {
    init = false;
    addDraftIsEditting.value = false;
    draftForm.id = "";
    draftForm.draftCreateDate = "";
    draftForm.draftEndDate = "";
    draftForm.draftStatus = 1;
    draftForm.draftType = 1;
    draftForm.draftNo = "";
    draftForm.draftAmount = "";
    draftForm.canTransfer = 1;
    draftForm.drawerName = "";
    draftForm.drawerAccount = "";
    draftForm.drawerBank = "";
    draftForm.receiverName = "";
    draftForm.receiverAccount = "";
    draftForm.receiverBank = "";
    draftForm.acceptorName = "";
    draftForm.acceptorAccount = "";
    draftForm.acceptorBank = "";
    draftForm.memo = "";
    draftForm.draftPic = "";
    imageUrl.value = "";
    draftForm.attachsCount = 0;
    attachParentId = 0;
    attachFiles.value.length = 0;
    emit("addDraftSuccess");
};
function draftStatusChange(val: number) {
    if (val !== 1) {
        ElConfirm("请检查票据状态是否正确。", true);
    }
}
function editInit(rowData: ITableItem, editDraftPic: string) {
    draftForm.id = rowData.id;
    draftForm.draftCreateDate = rowData.draftCreateDate;
    draftForm.draftEndDate = rowData.draftEndDate;
    draftForm.draftStatus = rowData.draftStatus;
    draftForm.draftType = rowData.draftType;
    draftForm.draftNo = rowData.draftNo;
    draftForm.draftAmount = rowData.draftAmount;
    draftForm.canTransfer = rowData.canTransfer;
    draftForm.drawerName = rowData.drawerName;
    draftForm.drawerAccount = rowData.drawerAccount;
    draftForm.drawerBank = rowData.drawerBank;
    draftForm.receiverName = rowData.receiverName;
    draftForm.receiverAccount = rowData.receiverAccount;
    draftForm.receiverBank = rowData.receiverBank;
    draftForm.acceptorName = rowData.acceptorName;
    draftForm.memo = rowData.memo;
    draftForm.acceptorAccount = rowData.acceptorAccount;
    draftForm.acceptorBank = rowData.acceptorBank;
    draftForm.draftPic = editDraftPic;
    draftForm.attachsCount = rowData.attachsCount;
    draftForm.attachFiles = rowData.attachFiles;
    attachParentId = 0;
    attachFiles.value.length = 0;
    imageUrl.value = editDraftPic;
    let dom = document.querySelector(".line-item-field-memo .el-textarea__inner") as HTMLTextAreaElement;
    if (dom?.style.height) {
        dom.style.height = "auto";
    }
    nextTick(() => {
        init = true;
    });
}
function handleAmountInput(e: any): void {
    draftForm.draftAmount =
        e
            .replace(/[^-\d^.]+/g, "") // 第二步：把不是数字、不是小数点和不是负号的字符过滤掉
            .replace(/^-?(0+)(\d)/, "-$2") // 第三步：第一位可以是负号，0开头，0后面为数字，则过滤掉0，并保留负号
            .replace(/^\./, "0.") // 第四步：如果输入的第一位为小数点，则替换成 0. 实现自动补全
            .match(/^-?\d*(\.?\d{0,2})/g)[0] || ""; // 第五步：最终匹配得到结果，以数字或负号开头，只有一个小数点，而且小数点后面只能有0到2位小数
}
//票据截止日期提示
function DraftEndDateTip() {
    var start = draftForm.draftCreateDate;
    var end = draftForm.draftEndDate;
    if (start != "" && end != "") {
        var startDate = new Date(start.replace(new RegExp(/-/gm), "/"));
        var endDate = new Date(end.replace(new RegExp(/-/gm), "/"));
        var days = (Number(endDate) - Number(startDate)) / (1 * 24 * 60 * 60 * 1000);
        if (days > 183) {
            ElConfirm("汇票最长不超过6个月，请确定到期日是否录入正确。", true);
        }
    }
}
const avatarUploadChange = (rawFile: any) => {
    if (!/(\.jpg|\.jpeg|\.bmp|\.png|\.gif)$/i.test(rawFile.name)) {
        ElNotify({
            message: "请上传.png/jpg/jpeg格式的文件!",
            type: "warning",
        });
        return false;
    }
    let oFileReader = new FileReader();
    oFileReader.onloadend = function (e: any) {
        // 结果
        imageUrl.value = e.target.result;
        draftForm.draftPic = imageUrl.value;
        let params = {
            imgType: "1",
        };
        useLoading().enterLoading("正在识别中");

        request({
            url: "/api/Draft/OCR",
            method: "post",
            params,
            data: {
                base64Str: imageUrl.value.split(",")[1],
            },
            headers: {
                "Content-Type": "multipart/form-data",
            },
        }).then((res: IResponseModel<IORCRes>) => {
            useLoading().quitLoading();

            if (res.state === 1000 && res.data.issueDate) {
                const updateDraftForm = () => {
                    draftForm.draftCreateDate = dayjs(res.data.issueDate).format("YYYY-MM-DD");
                    draftForm.draftNo = res.data.billNo;
                    draftForm.draftAmount = (res.data.billAmt / 100).toFixed(2);
                    draftForm.drawerName = res.data.drawerName;
                    draftForm.drawerAccount = res.data.drawerAccount;
                    draftForm.drawerBank = res.data.drawerBankName;
                    draftForm.receiverName = res.data.payeeName;
                    draftForm.receiverAccount = res.data.payeeAccount;
                    draftForm.receiverBank = res.data.payeeBankName;
                    draftForm.acceptorName = res.data.accepterName;
                    draftForm.acceptorAccount = res.data.accepterBank;
                    draftForm.acceptorBank = res.data.accepterBankName;
                    draftForm.draftEndDate = dayjs(res.data.maturityDate).format("YYYY-MM-DD");
                };
                if (draftForm.draftCreateDate) {
                    ElConfirm("是否使用上传票面的OCR识别信息覆盖已录入的票据资料？").then((r: boolean) => {
                        if (r) {
                            updateDraftForm();
                        }
                    });
                } else {
                    updateDraftForm();
                }
            }
        });
    };
    oFileReader.readAsDataURL(rawFile.raw);
};
const saveSubmit = () => {
    if (!draftForm.draftCreateDate) {
        ElNotify({
            message: "出票日不能为空",
            type: "warning",
        });
        return;
    }
    if (!draftForm.draftNo) {
        ElNotify({
            message: "票据号码不能为空",
            type: "warning",
        });
        return;
    }
    if (!draftForm.draftAmount) {
        ElNotify({
            message: "票据金额不能为空",
            type: "warning",
        });
        return;
    }
    if (!draftForm.acceptorName && draftForm.draftType !== 2) {
        ElNotify({
            message: "承兑人全称不能为空",
            type: "warning",
        });
        return;
    }
    if (!draftForm.acceptorAccount && draftForm.draftType !== 2) {
        ElNotify({
            message: "承兑人账号/行号不能为空",
            type: "warning",
        });
        return;
    }
    let requestType = props.formType === "add" ? "post" : "put";
    request({
        url: "/api/Draft",
        method: requestType,
        data: JSON.stringify(draftForm),
        headers: {
            "Content-Type": "application/json",
        },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000 && res.data) {
                resetForm();
                ElNotify({
                    message: `保存成功`,
                    type: "success",
                });
            } else {
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        })
        .catch((e) => {
            throw e;
        });
};

// 附件
const readonly = ref(false);
let attachParentId = 0;
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
function getAttachFileList() {
    if (attachParentId === 0) {
        const params = { id: draftForm.id };
        request({ url: "/api/Draft/GetAttachFileList", method: "post", params }).then((res: IResponseModel<IGetAttachFileListBack>) => {
            if (res.state === 1000 && res.data.result) {
                const list = res.data.data.map((item: any) => {
                    item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                    return item;
                });
                attachFiles.value = list;
                attachParentId = res.data.parentId;
                openFileDialog(attachFiles.value);
            } else {
                ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
            }
        });
    } else {
        openFileDialog(attachFiles.value);
    }
}
const openFileDialog = (list: IFileInfo[]) => {
    const dateMonth = draftForm.draftCreateDate.split("-").slice(0, 2);
    const startDate = dateMonth.join("-") + "-01";
    const day = getDaysInMonth(Number(dateMonth[0]), Number(dateMonth[1]));
    const endDate = dateMonth.join("-") + "-" + day;
    const _params = {
        eRecordSearchDate: { startDate, endDate },
        id: draftForm.id,
    };
    uploadFileDialogRef.value?.open(_params, list, attachParentId);
};
async function saveAttachFile(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
    attachFiles.value = fileList.map((f) => {
        return {
            fileId: f.fileId,
            fileName: f.fileName,
            fileSize: f.fileSize,
            fileType: f.fileType,
            relativePath: f.relativePath,
        };
    });
    draftForm.attachsCount = fileList.length;
    draftForm.attachFiles = fileList.map((item: any) => item.fileId).join(",");
}
//编辑状态
let init = false;
const addDraftIsEditting = ref(false);
const getEditStatus = () => {
    return addDraftIsEditting.value;
};
const changeAddInit = (val: boolean) => {
    init = val;
};
watch(
    draftForm,
    () => {
        if (!init) return;
        addDraftIsEditting.value = true;
    },
    { deep: true }
);
defineExpose({
    editInit,
    getEditStatus,
    changeAddInit,
});

const draftTypeOptionsAll = ref<Array<IDraftStatusOptions>>([]);
const showDraftTypeOptions = ref<Array<IDraftStatusOptions>>([]);
const draftStatusOptionsAll = ref<Array<IDraftStatusOptions>>([]);
const showDraftStatusOptions = ref<Array<IDraftStatusOptions>>([]);
watchEffect(() => {
    draftTypeOptionsAll.value = props.draftTypeOptions.slice(1);
    showDraftTypeOptions.value = JSON.parse(JSON.stringify(draftTypeOptionsAll.value));

    draftStatusOptionsAll.value = props.draftStatusOptions.slice(1);
    showDraftStatusOptions.value = JSON.parse(JSON.stringify(draftStatusOptionsAll.value));
});
function draftTypeFilterMethod(value: string) {
    showDraftTypeOptions.value = commonFilterMethod(value, draftTypeOptionsAll.value, 'name');
}
function draftStatusFilterMethod(value: string) {
    showDraftStatusOptions.value = commonFilterMethod(value, draftStatusOptionsAll.value, 'name');
}
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";
.content {
    .slot-content {
        width: 1000px !important;
        margin: 32px auto 0;
        .main-center {
            padding: 0;
        }
    }
}
.main-center {
    background-color: #fff;
    overflow: hidden;

    .edit-draft-form {
        margin: 40px auto 0;
        min-width: 986px;
        max-width: 1200px;

        .draft-form {
            width: 90%;
            margin: 0 30px 0 20px;

            .top-form {
                width: 100%;
                display: flex;
                margin: auto;

                .first-col {
                    width: 25%;
                    display: flex;
                    flex-direction: column;
                    align-items: self-start;
                }

                .second-col {
                    flex: 1;
                    width: 30%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                }

                .third-col {
                    display: flex;
                    flex-direction: column;

                    .el-form-item {
                        margin-right: 0;
                    }
                }
            }

            .memo-row {
                width: 100%;
                margin: auto;

                .line-item {
                    width: 100%;
                }
            }

            :deep(.el-form-item__content) {
                min-width: 160px;

                .line-item-field {
                    width: 160px;

                    &.line-item-select {
                        .el-input {
                            width: 100%;
                        }
                    }
                }
            }

            .line-item {
                width: 45%;

                :deep(.line-item-date) {
                    width: 160px !important;

                    .el-input__wrapper {
                        .el-input__prefix {
                            position: absolute;
                            top: 0;
                            right: 0;
                        }
                    }

                    .el-input__suffix {
                        transform: translateX(-15px);
                    }
                }
            }
        }
    }

    .line-item {
        width: 30%;
    }

    .el-form-item__label {
        width: auto;
        text-align: right;
    }

    .line-item-field {
        width: 160px !important;

        &.line-item-select {
            :deep(.el-select .el-input) {
                width: 100% !important;
            }
        }
    }

    .line-item-memo {
        width: 100%;

        .line-item-field-memo {
            width: 100% !important;
        }
    }

    .form-button {
        margin: 30px auto 40px;
    }
}
.avatar-uploader {
    width: 158px;
    height: 84px;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #dddddd;
    position: relative;

    .avatar-delete-icon {
        position: absolute;
        right: 5px;
        top: 5px;
        font-size: 16px;
        color: #eaaf0b;
    }

    .avatar {
        width: 158px;
        height: 84px;
        border-radius: 3px;
    }

    .avatar-uploader-tips {
        text-align: center;

        .avatar-uploader-icon {
            margin: 18px auto 4px;
            height: 25px;
            font-size: 25px;
            font-weight: bold;
            line-height: 20px;
            color: #000000;
            opacity: 0.45;
            display: block;
        }

        .avatar-uploader-text {
            font-size: 12px;
            color: #000000;
            line-height: 22px;
            display: block;
        }
    }

    // }
}
.attach-file-center {
    width: 100%;
    display: flex;
    .upload-title {
        width: 120px;
        padding-right: 12px;
        font-weight: 700;
        text-align: right;
        font-size: var(--font-size);
    }
    .upload-item {
        height: 70px;
        width: 240px;
        border: 1px solid var(--border-color);
        cursor: pointer;
        text-align: left;
        .upload-item-title {
            margin: 10px 0 0 70px;
            img {
                height: 14px;
                width: 14px;
            }
        }
        .upload-item-tip {
            margin: 10px 0 0 60px;
            font-size: 12px;
            color: var(--menu-font-color);
        }
    }
}
</style>
