<template>
    <div class="slot-mini-content">
        <div class="title">自定义工资项目</div>
        <div class="wang-wrap">
            <div class="wang-content" v-if="pageIsShow">
                <div class="sub-title">工资总额</div>
                <div class="item-container">
                    <div class="item-title">项目：</div>
                    <div class="items" id="userDefinedContent">
                        <div class="item-box" v-for="item in salaryTotal" :key="item.item_id">
                            <input
                                type="text"
                                v-model="(item as ICustomWage).item_name"
                                :readonly="(item as ICustomWage).item_id === 10001"
                                :id="item.item_id + ''"
                                :class="checkPermission(['salarymanage-project-canedit']) ? 'canedit' : ''"
                                :disabled="!checkPermission(['salarymanage-project-canedit'])"
                            />
                            <div
                                v-permission="['salarymanage-project-candelete']"
                                class="delete-btn deleteUserDefined"
                                v-if="(item as ICustomWage).item_id !== 10001"
                                @click="deleteHandle($event)"
                            ></div>
                        </div>
                        <div
                            class="button solid-button"
                            id="addUserDefined"
                            v-if="addUserDefinedShow"
                            @click="addUserDefined('userDefinedContent', 'addUserDefined')"
                            v-permission="['salarymanage-project-canedit']"
                            style="margin-top: 20px; width: 130px; height: 32px; line-height: 32px"
                        >
                            添加列
                        </div>
                    </div>
                </div>
                <div class="line"></div>
                <div class="sub-title">应扣工资</div>
                <div class="item-container">
                    <div class="item-title">项目：</div>
                    <div class="items" id="userDefinedContent2">
                        <div class="item-box" v-for="item in salaryDeduct" :key="item.item_id">
                            <input
                                type="text"
                                v-model="(item as ICustomWage).item_name"
                                :id="item.item_id + ''"
                                :class="checkPermission(['salarymanage-project-canedit']) ? 'canedit' : ''"
                                :disabled="!checkPermission(['salarymanage-project-canedit'])"
                            />
                            <div
                                v-permission="['salarymanage-project-candelete']"
                                class="delete-btn deleteUserDefined"
                                @click="deleteHandle($event)"
                            ></div>
                        </div>
                        <div
                            class="button solid-button"
                            id="addUserDefined2"
                            v-if="addUserDefined2Show"
                            @click="addUserDefined('userDefinedContent2', 'addUserDefined2')"
                            v-permission="['salarymanage-project-canedit']"
                            style="margin-top: 20px; width: 130px; height: 32px; line-height: 32px"
                        >
                            添加列
                        </div>
                    </div>
                </div>
                <div class="line"></div>
                <div class="sub-title">实发工资调整</div>
                <div class="item-container">
                    <div class="item-title">项目：</div>
                    <div class="items" id="userDefinedContent3">
                        <div class="item-box" v-for="item in salaryAdjust" :key="item.item_id">
                            <input
                                type="text"
                                v-model="(item as ICustomWage).item_name"
                                :id="item.item_id + ''"
                                :class="checkPermission(['salarymanage-project-canedit']) ? 'canedit' : ''"
                                :disabled="!checkPermission(['salarymanage-project-canedit'])"
                            />
                            <div
                                v-permission="['salarymanage-project-candelete']"
                                class="delete-btn deleteUserDefined"
                                @click="deleteHandle($event)"
                            ></div>
                        </div>
                        <div
                            class="button solid-button"
                            id="addUserDefined3"
                            v-if="addUserDefined3Show"
                            @click="addUserDefined('userDefinedContent3', 'addUserDefined3')"
                            v-permission="['salarymanage-project-canedit']"
                            style="margin-top: 20px; width: 130px; height: 32px; line-height: 32px"
                        >
                            添加列
                        </div>
                    </div>
                </div>
                <div class="line"></div>
                <div class="buttons">
                    <a v-permission="['salarymanage-project-canedit']" class="button solid-button" @click="SubmitSalaryItem">保存</a
                    ><a class="button ml-10" @click="Cancel">取消</a>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ICustomWage } from "../types";
import { useLoading } from "@/hooks/useLoading";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { ref, nextTick, watch, onMounted, onUnmounted } from "vue";
import { checkPermission } from "@/util/permission";

const props = defineProps({
    month: {
        type: String,
        default: "",
    },
    data: {
        type: Array,
        default: () => [],
    },
});

const emits = defineEmits(["cancleCustomWage"]);
const customWageData = ref<ICustomWage[]>([]);
const salaryTotal = ref<ICustomWage[]>([]);
const salaryDeduct = ref<ICustomWage[]>([]);
const salaryAdjust = ref<ICustomWage[]>([]);
const addUserDefinedShow = ref<Boolean>(false);
const addUserDefined2Show = ref<Boolean>(false);
const addUserDefined3Show = ref<Boolean>(false);
const pageIsShow = ref<Boolean>(true);
function Cancel() {
    pageIsShow.value = false;
    setTimeout(() => {
        let userDefineds = document.querySelectorAll("#userDefinedContent input[name=userDefinedTxt]");
        let userDefineds2 = document.querySelectorAll("#userDefinedContent2 input[name=userDefinedTxt2]");
        let userDefineds3 = document.querySelectorAll("#userDefinedContent3 input[name=userDefinedTxt3]");
        userDefineds && userDefineds.forEach((item: Element) => item.parentNode!.parentNode!.removeChild(item.parentNode!));
        userDefineds2 && userDefineds2.forEach((item: Element) => item.parentNode!.parentNode!.removeChild(item.parentNode!));
        userDefineds3 && userDefineds3.forEach((item: Element) => item.parentNode!.parentNode!.removeChild(item.parentNode!));
        emits("cancleCustomWage");
    });
}

// 遍历新增的自定义项,修改placeholder
const updatePlaceholder = (boxId: string) => {
    const itemBox = document.getElementById(boxId);
    const ipts = itemBox?.querySelectorAll(".new-item>input");
    if (!ipts) return;
    for (let i = 0; i < ipts.length; i++) {
        if (!(ipts[i] as HTMLInputElement).value) {
            (ipts[i] as HTMLInputElement).placeholder = `自定义列${+i + 1}`;
        }
    }
};

let index1 = 1,
    index2 = 1,
    index3 = 1;

function addUserDefined(boxId: string, buttonId: string) {
    let str = "";
    switch (boxId) {
        case "userDefinedContent":
            str = `<input type="text"  placeholder='自定义列${index1}' name="userDefinedTxt" class="canedit" />`;
            index1++;
            break;
        case "userDefinedContent2":
            str = `<input type="text"  placeholder='自定义列${index2}' name="userDefinedTxt2" class="canedit" />`;
            index2++;
            break;
        case "userDefinedContent3":
            str = `<input type="text"  placeholder='自定义列${index3}' name="userDefinedTxt3" class="canedit" />`;
            index3++;
            break;
    }
    const itemBox = document.getElementById(boxId);
    str += `<div v-permission="['salarymanage-project-candelete']" class="delete-btn deleteUserDefined" ></div>`;
    let node = document.createElement("div");
    node.innerHTML = str;
    node.className = "item-box new-item";
    itemBox?.insertBefore(node, document.getElementById(buttonId));
    const inputElement = node.querySelector("input");
    if (inputElement) {
        inputElement.addEventListener("focus", () => {
            inputElement.classList.add("focused");
        });
        inputElement.addEventListener("blur", () => {
            inputElement.classList.remove("focused");
        });
        inputElement.focus();
    }

    // 重排placeholder顺序
    updatePlaceholder(boxId);

    nextTick().then(() => {
        checkUserDefinedNum();
        checkUserDefinedNum3();
        checkUserDefinedNum2();
    });
}
onMounted(() => {
    let el = document.querySelector(".wang-wrap");
    if (el) {
        el.addEventListener("click", deleteHandle);
    }
});
onUnmounted(() => {
    let el = document.querySelector(".wang-wrap");
    if (el) {
        el.removeEventListener("click", deleteHandle);
    }
});
function checkUserDefinedNum() {
    if (document.querySelectorAll("#userDefinedContent input").length > 9) {
        addUserDefinedShow.value = false;
    } else {
        addUserDefinedShow.value = true;
    }
}
function checkUserDefinedNum2() {
    if (document.querySelectorAll("#userDefinedContent2 input").length > 9) {
        addUserDefined2Show.value = false;
    } else {
        addUserDefined2Show.value = true;
    }
}
function checkUserDefinedNum3() {
    if (document.querySelectorAll("#userDefinedContent3 input").length > 9) {
        addUserDefined3Show.value = false;
    } else {
        addUserDefined3Show.value = true;
    }
}
const delFlag = ref(false);
function deleteHandle(evt: any) {
    if (evt.target.className == "delete-btn deleteUserDefined") {
        // evt.target.parentNode.remove();
        let item_id = evt.target.parentNode.firstElementChild.getAttribute("id");
        if (item_id == null) {
            evt.target.parentNode.remove();
            // let userDefineds = document.querySelectorAll("#userDefinedContent input[name=userDefinedTxt]");
            // let userDefineds2 = document.querySelectorAll("#userDefinedContent2 input[name=userDefinedTxt]");
            // let userDefineds3 = document.querySelectorAll("#userDefinedContent3 input[name=userDefinedTxt]");
            switch (evt.target.parentNode.firstElementChild.getAttribute("name")) {
                case "userDefinedTxt":
                    // for (let i = 0; i < userDefineds.length; i++) {
                    //     userDefineds[i].setAttribute("placeholder", "自定义列" + (i + 1));
                    // }
                    // index1--;
                    updatePlaceholder("userDefinedContent");
                    checkUserDefinedNum();
                    break;
                case "userDefinedTxt2":
                    checkUserDefinedNum2();
                    // for (let i = 0; i < userDefineds2.length; i++) {
                    //     userDefineds2[i].setAttribute("placeholder", "自定义列" + (i + 1));
                    // }
                    // index2--;
                    updatePlaceholder("userDefinedContent2");
                    break;
                case "userDefinedTxt3":
                    checkUserDefinedNum3();
                    // for (let i = 0; i < userDefineds3.length; i++) {
                    //     userDefineds3[i].setAttribute("placeholder", "自定义列" + (i + 1));
                    // }
                    // index3--;
                    updatePlaceholder("userDefinedContent3");
                    break;
            }
        } else {
            if (delFlag.value) return;
            delFlag.value = true;
            request({
                url: `/api/SalarySettings/ItemExistsData?itemId=${item_id}&mid=${props.month}`,
                method: "post",
            }).then((res: any) => {
                delFlag.value = false;
                if (res.data) {
                    ElConfirm("当前项目本月已有数据，确定删除当前项目吗？").then((r: boolean) => {
                        if (r) {
                            let deleteIndex = customWageData.value.findIndex((item: any) => item.item_id === Number(item_id));
                            customWageData.value.splice(deleteIndex, 1);
                            evt.target.parentNode.remove();
                            deletearr.push(Number(item_id));
                        }
                    });
                } else {
                    let deleteIndex = customWageData.value.findIndex((item: any) => item.item_id === Number(item_id));
                    customWageData.value.splice(deleteIndex, 1);
                    evt.target.parentNode.remove();
                    deletearr.push(Number(item_id));
                    nextTick().then(() => {
                        checkUserDefinedNum();
                        checkUserDefinedNum3();
                        checkUserDefinedNum2();
                    });
                }
            });
        }
    }
}

async function IsHasCheckOut() {
    let status = false;
    await request({
        url: `/api/Salary/IsCheckOut?mid=` + props.month,
        method: "post",
    }).then((res: any) => {
        if (res.state == 1000 && res.data === true) {
            status = true;
        }
    });
    return status;
}

let updatearr = new Array();
let addarr = new Array();
let deletearr = new Array();
const needWaiting = ref(true); //防止点击速度过快导致的重复提交
async function SubmitSalaryItem() {
    // customWageData.value.splice(
    //     customWageData.value.findIndex((item: any) => item.item_id === 10001),
    //     1
    // );
    if (!needWaiting.value) return;
    needWaiting.value = false;
    for (let i = 0; i < customWageData.value.length; i++) {
        var salaryitem_name = customWageData.value[i].item_name;
        var count_id = Number(customWageData.value[i].item_id);

        if (salaryitem_name != undefined && salaryitem_name != "") {
            if (salaryitem_name.length > 6) {
                updatearr.splice(0, updatearr.length);
                ElNotify({
                    type: "warning",
                    message: "自定义工资项目名称长度不允许超过6个汉字",
                });
                needWaiting.value = true;
                return;
            }
            var um = { type: count_id, value: salaryitem_name.trim() };
            updatearr.push(um);
        }
    }
    let list = document.querySelectorAll("#userDefinedContent input[name=userDefinedTxt]");

    for (let i = 0; i < list.length; i++) {
        // var item_name = list[i].getAttribute("value") == "" ? list[i].getAttribute("placeholder") : list[i].getAttribute("value");

        let item_name = (!(list[i] as any).value ? list[i].getAttribute("placeholder") : (list[i] as any).value) as string;
        if (item_name!.length > 6) {
            updatearr.splice(0, updatearr.length);
            addarr.splice(0, addarr.length);
            ElNotify({
                type: "warning",
                message: "自定义工资项目名称长度不允许超过6个汉字",
            });
            needWaiting.value = true;
            return;
        }
        var vm = { type: 1010, value: item_name!.trim() };
        addarr.push(vm);
    }

    var list2 = document.querySelectorAll("#userDefinedContent2 input[name=userDefinedTxt2]");
    for (let i = 0; i < list2.length; i++) {
        // let item_name2 = list2[i].getAttribute("value") == "" ? list2[i].getAttribute("placeholder") : list2[i].getAttribute("value");
        let item_name2 = (!(list2[i] as any).value ? list2[i].getAttribute("placeholder") : (list2[i] as any).value) as string;
        if (item_name2!.length > 6) {
            updatearr.splice(0, updatearr.length);
            addarr.splice(0, addarr.length);
            ElNotify({
                type: "warning",
                message: "自定义工资项目名称长度不允许超过6个汉字",
            });
            needWaiting.value = true;
            return;
        }
        var vm2 = { type: 1020, value: item_name2!.trim() };
        addarr.push(vm2);
    }
    var list3 = document.querySelectorAll("#userDefinedContent3 input[name=userDefinedTxt3]");
    for (var i = 0; i < list3.length; i++) {
        // var item_name3 = list3[i].getAttribute("value") == "" ? list3[i].getAttribute("placeholder") : list3[i].getAttribute("value");
        var item_name3 = (!(list3[i] as any).value ? list3[i].getAttribute("placeholder") : (list3[i] as any).value) as string;
        if (item_name3!.length > 6) {
            updatearr.splice(0, updatearr.length);
            addarr.splice(0, addarr.length);
            ElNotify({
                type: "warning",
                message: "自定义工资项目名称长度不允许超过6个汉字",
            });
            needWaiting.value = true;
            return;
        }
        var vm3 = { type: 1030, value: item_name3!.trim() };
        addarr.push(vm3);
    }

    if (await IsHasCheckOut()) {
        ElConfirm("当前月份工资数据已结账，无法修改工资项目！",true);
        needWaiting.value = true;
    } else {
        const data = {
            addlist: addarr,
            updatelist: updatearr,
            deletelist: deletearr,
        };
        useLoading().enterLoading("努力加载中，请稍候...");
        request({
            url: `/api/SalarySettings?mid=${props.month}`,
            method: "put",
            data,
        })
            .then((res: any) => {
                useLoading().quitLoading();
                if (res.state === 1000) {
                    if (res.data) {
                        ElNotify({
                            type: "success",
                            message: "工资项目修改成功，本期间生效！",
                        });
                        setTimeout(() => {
                            let userDefineds = document.querySelectorAll("#userDefinedContent input[name=userDefinedTxt]");
                            let userDefineds2 = document.querySelectorAll("#userDefinedContent2 input[name=userDefinedTxt2]");
                            let userDefineds3 = document.querySelectorAll("#userDefinedContent3 input[name=userDefinedTxt3]");
                            userDefineds &&
                                userDefineds.forEach((item: Element) => item.parentNode!.parentNode!.removeChild(item.parentNode!));
                            userDefineds2 &&
                                userDefineds2.forEach((item: Element) => item.parentNode!.parentNode!.removeChild(item.parentNode!));
                            userDefineds3 &&
                                userDefineds3.forEach((item: Element) => item.parentNode!.parentNode!.removeChild(item.parentNode!));
                            emits("cancleCustomWage");
                        }, 1000);
                    } else {
                        ElNotify({
                            type: "error",
                            message: res.message,
                        });
                    }
                    deletearr.splice(0, deletearr.length);
                    addarr.splice(0, addarr.length);
                    updatearr.splice(0, updatearr.length);
                }
            })
            .finally(() => {
                needWaiting.value = true;
            });
    }
}

watch(
    () => props.data,
    (newVal) => {
        pageIsShow.value = true;
        customWageData.value = (newVal as any).reverse();
        salaryTotal.value = customWageData.value.reduce((prev: any, item: any) => {
            if (item.item_type === 1010) prev.push(item);
            return prev;
        }, []);
        salaryDeduct.value = customWageData.value.reduce((prev: any, item: any) => {
            if (item.item_type === 1020) prev.push(item);
            return prev;
        }, []);
        salaryAdjust.value = customWageData.value.reduce((prev: any, item: any) => {
            if (item.item_type === 1030) prev.push(item);
            return prev;
        }, []);
        nextTick().then(() => {
            checkUserDefinedNum();
            checkUserDefinedNum3();
            checkUserDefinedNum2();
        });
    },
    { immediate: true }
);
// watchEffect(() => {
//     getCustomWage();
// });
</script>

<style scoped lang="less">
@import "@/style/Salary/CustomWageSlot.less";
.slot-mini-content {
    .wang-wrap {
        width: 1200px;
        margin: 32px auto 10px;
        border: 1px solid var(--slot-title-color);
    }
    .wang-content {
        width: 1000px;
        margin: 0 auto;
    }
}
body[erp] {
    .items {
        :deep(.item-box) {
            &:hover {
                & input.canedit {
                    border: 1px solid #3d7fff;
                    & + div {
                        display: inline-block;
                    }
                }
            }
            & input {
                & .focused {
                    border: 1px solid #3d7fff;
                }
            }
            & .delete-btn {
                background: url(@/assets/Salary/2-erp.png) no-repeat center;
                background-size: 100%;
                display: none;
            }
        }
    }
    .sub-title {
        font-weight: 600;
    }
    .content .slot-mini-content {
        height: auto;
    }
}
</style>
