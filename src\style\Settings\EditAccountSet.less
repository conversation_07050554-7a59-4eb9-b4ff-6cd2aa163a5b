.edit-accountSet {
    position: relative;
    & .edit-tips {
        position: absolute;
        font-size: 13px;
        line-height: 19px;
        font-weight: 400;
        top: 18px;
        right: 30px;
    }
    & .accountset-edit-content {
        padding-top: 50px;
        text-align: center;
        height: auto;
        & .line-item1 {
            height: 32px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            margin-top: 24px;
            & .line-item-single {
                height: 32px;
                float: left;
                width: 70%;

                & .line-item-field {
                    width: 296px;
                    float: right;
                    text-align: left;
                    padding-left: 10px;
                    position: relative;
                }
                & .line-item-title {
                    float: right;
                    text-align: right;
                }
            }

            &.line-item1-nofloat {
                float: none;
                height: auto;
                & .line-item-single {
                    float: none;
                    height: auto;
                    width: auto;
                    text-align: left;
                    margin-left: 301px;
                    & .line-item-title {
                        float: none !important;
                        display: inline-block;
                        text-align: right;
                    }
                    & .line-item-field {
                        float: none;
                        display: inline-block;
                        width: auto;
                    }
                }
            }
        }
        & .line-item2 {
            margin-top: 24px;
            height: 20px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 20px;
            & .line-item-single {
                height: 20px;
                float: left;
                width: 70%;
                & .line-item-title {
                    float: right;
                }
                & .line-item-field {
                    height: 20px;

                    width: 296px;
                    float: right;
                    text-align: left;
                    padding-left: 10px;
                    position: relative;
                    & :deep(.el-radio.el-radio--large) {
                        height: 20px;
                    }
                }
            }
        }
        & .content-block {
            & .content-left {
                display: inline-block;
                text-align: right;
                & .line-item {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 32px;
                }
            }
            & .content-right {
                display: inline-block;
                padding-left: 10px;
                text-align: left;
                margin-right: 12px;
            }
        }
    }
    & .buttons {
        margin-top: 0px;
        padding-top: 16px;
        padding-bottom: 16px;
        border-top: 1px solid var(--border-color);
    }
}

.accountset-upgrade-farmerCooperative {
    font-size: var(--font-size);

    & .content-block {
        display: flex;
        padding: 32px 60px 0px;
        margin-bottom: 16px;

        &.space-between {
            justify-content: space-between;
        }

        & .standard-farmerCooperative {
            height: 28px;
            width: 240px;
            box-sizing: border-box;
            border-radius: 2px;
            border: 1px solid rgb(217, 217, 217);
            padding-left: 10px;
            margin: -4px 8px;
            font-weight: 400;
            line-height: 22px;
            display: flex;
            align-items: center;
        }

        & + .content-block {
            padding-top: 0px;
        }
    }
    & .des-card {
        width: 540px;
        height: 148px;
        box-sizing: border-box;
        border: 1px solid rgb(223, 228, 235);
        margin: 0px auto 12px;
        cursor: pointer;
        position: relative;

        & .card-title {
            padding: 17px 0px 10px 46px;
            font-size: 15px;
            font-weight: 500;
            color: var(--font-size);
            line-height: 22px;
        }
        & .card-tip {
            padding-left: 46px;
            font-weight: 400;
            color: rgb(102, 102, 102);
            line-height: 30px;
            font-size: 13px;
            display: flex;
            align-items: center;

        }

        &::before {
            content: " ";
            position: absolute;
            top: 20px;
            left: 19px;
            width: 15px;
            height: 15px;
            background-repeat: no-repeat;
            background-size: 100%;
            background-image: url("@/assets/Settings/accountset-upgrad-unselected.png");
        }
        &.selected::before {
            background-image: url("@/assets/Settings/accountset-upgrad-selected.png");
        }
    }
}

.bottom-line {
    border-top: 1px solid var(--border-color);
}
