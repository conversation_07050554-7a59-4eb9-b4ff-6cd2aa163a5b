<template>
    <div class="slot-content align-center panel-body" v-show="showMain">
        <div class="slot-content-mini">
            <div class="autoBanktitle" v-show="props.helpShow && !isHideBarcode">
                <a class="link" @click="globalWindowOpen(bankHelpLink)"> 点此查看操作手册 </a>
            </div>
            <div :class="{ top: true, more: props.bankType === BankType.CMB }">
                <div class="autoBankSelect">
                    <el-form v-if="props.bankType === BankType.CMB" :show-message="false" label-width="140px">
                        <el-form-item class="auto-bank-form-item necessary" label="公司名称：">
                            <el-autocomplete
                                ref="oppositePartyRef"
                                v-model="accname"
                                :fetch-suggestions="querySearch"
                                :prop="[{ required: true, trigger: ['change'] }]"
                                :trigger-on-focus="false"
                                placeholder=" "
                                class="oppositePartyInput"
                                :debounce="300"
                                :teleported="true"
                                :fit-input-width="true"
                                popper-class="opposite-autocomplete-popper"
                                @select="handleSelect"
                            >
                                <template #default="{ item }">
                                    <div class="value">{{ item.value }}</div>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary" label="统一社会信用代码：">
                            <el-input v-model="uscc" />
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary" label="企业编号：">
                            <el-input v-model="enterprise_number" />
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary" label="用户编号：">
                            <el-input v-model="uid" />
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary bank-account" label="银行账户：">
                            <Select
                                v-model="acid"
                                :teleported="false"
                                :bottom-html="newBankAccountHtml"
                                :placeholder="'请选择'"
                                @bottom-click="newBankAccount"
                                :filterable="true"
                                :filter-method="bankAccountFilterMethod"
                            >
                                <Option
                                    v-for="item in showBankAccountList"
                                    :key="item.ac_id"
                                    :label="item.label"
                                    :value="item.ac_id"
                                />
                            </Select>
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary" label="银行账号：" prop="mainaccno">
                            <el-input v-model="mainaccno" />
                        </el-form-item>
                    </el-form>
                    <el-form v-else :show-message="false" label-width="140px">
                        <el-form-item class="auto-bank-form-item necessary" label="公司名称：">
                            <el-autocomplete
                                ref="oppositePartyRef"
                                v-model="accname"
                                :fetch-suggestions="querySearch"
                                :prop="[{ required: true, trigger: ['change'] }]"
                                :trigger-on-focus="false"
                                placeholder=" "
                                class="oppositePartyInput"
                                :debounce="300"
                                :teleported="true"
                                :fit-input-width="true"
                                popper-class="opposite-autocomplete-popper"
                                @select="(val:any)=>{accname = val.value, uscc = val.creditCode}"
                            >
                                <template #default="{ item }">
                                    <div class="value">{{ item.value }}</div>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary" label="统一社会信用代码：">
                            <el-input v-model="uscc" />
                        </el-form-item>
                        <el-form-item
                            class="auto-bank-form-item necessary bank-account"
                            label="银行账户："
                        >
                            <Select
                                v-model="acid"
                                :teleported="false"
                                :bottom-html="newBankAccountHtml"
                                :placeholder="'请选择'"
                                @bottom-click="newBankAccount"
                                :filterable="true"
                                :filter-method="bankAccountFilterMethod"
                            >
                                <Option
                                    v-for="item in showBankAccountList"
                                    :key="item.ac_id"
                                    :label="item.label"
                                    :value="item.ac_id"
                                />
                            </Select>
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item necessary" label="银行账号：">
                            <el-input v-model="mainaccno" />
                        </el-form-item>
                        <el-form-item class="auto-bank-form-item boc" label="企业客户号：" v-if="props.bankType === BankType.BOC">
                            <el-input v-model="custno" :placeholder="'若集团账号签约，请输入企业客户号'" />
                            <el-popover trigger="hover" :width="600" placement="right" :effect="'light'">
                                <template #reference>
                                    <div class="help-tip"></div>
                                </template>
                                <div class="boc-bank-popover">
                                    <div>1、企业客户号如何查询？</div>
                                    <div>登录集团网银后，点击【管理员-综合信息查询-企业基本信息】，查询企业客户号</div>
                                    <div><img src="@/assets/Cashier/boc_cust_1.jpg" /></div>
                                    <div>2、查询不到企业客户号？</div>
                                    <div>尝试切换角色或检查登录网银是否正确后，再次按照上述步骤查询</div>
                                    <div><img src="@/assets/Cashier/boc_cust_2.jpg" /></div>
                                </div>
                            </el-popover>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="autoBankDescription">
                    <p class="textRed">提示：</p>
                    <p class="textNormal">
                        <template v-for="(item, index) in tipArray" :key="index">
                            <div class="tip-item">
                                <span class="line-sn">{{ item[0] }}</span>
                                <span>{{ item[1] }}</span>
                            </div>
                        </template>
                    </p>
                </div>
            </div>
            <div class="bottom">
                <div class="authButton">
                    <a class="solid-button" style="width: 102px" @click="confirmBank">{{ impowerValue }}</a>
                </div>
                <div class="autoBankToOpen" style="height: 16px">
                    <a class="link" v-show="props.orderAccountShow && !isHideBarcode" @click="toBankAccPreOpen"
                        >没有{{ props.bankName === "工商银行" ? "工行" : props.bankName }}账户？点此预约开户</a
                    >
                </div>
            </div>
            <CommonBottom></CommonBottom>
        </div>
    </div>
    <AddAccountDialog
        ref="addAccountFormRef"
        v-model="addAccountDialogShow"
        :j_type="1020"
        :currencyList="currencyList"
        @save-success="saveSuccessHandle"
    />
</template>

<script setup lang="ts">
import { ref, computed, watch, watchEffect } from "vue";
import { request } from "@/util/service";
import { globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import type { ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import { ElNotify } from "@/util/notify";
import { BankType } from "@/constants/bankKey";
import type { IBankAccount } from "../types";
import type { ICurrencyList } from "@/views/Cashier/components/types";
import { checkPermission } from "@/util/permission";
import AddAccountDialog from "@/views/Cashier/components/AddAccountDialog.vue";
import CommonBottom from "./CommonBottom.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { commonFilterMethod } from "@/components/Select/utils";

const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const handleSelect = (val: any) => {
    accname.value = val.value;
    uscc.value = val.creditCode;
};

const props = withDefaults(
    defineProps<{
        showMain: boolean;
        impowerValue: string;
        currencyList: ICurrencyList[];
        bankType: BankType;
        tipArray: string[];
        bankAccountList: IBankAccount[];

        accname: string;
        uscc: string;
        acid: string;
        mainaccno: string;
        custno?: string;
        enterprise_number?: string;
        uid?: string;

        helpShow?: boolean;
        bankHelpLink?: string;
        orderAccountShow?: boolean;
        bankHref?: string;
        bankName?: string;
        acname: string;
        usccNumber: string;
    }>(),
    {
        helpShow: true,
        bankHelpLink: "",
        orderAccountShow: false,
        bankHref: "",
        bankName: "",

        custno: "",
        enterprise_number: "",
        uid: "",
    }
);

const tipArray = computed(() => {
    const tips: string[][] = [];
    props.tipArray.forEach((item) => {
        const index = item.indexOf("、");
        const line_sn = item.slice(0, index);
        const line = item.slice(index + 1);
        tips.push([line_sn + "、", line]);
    });
    return tips;
});

const emit = defineEmits([
    "save-success",
    "confirm-bank",
    "update:showMain",
    "update:impowerValue",
    "update:accname",
    "update:uscc",
    "update:acid",
    "update:mainaccno",
    "update:custno",
    "update:uid",
    "update:enterprise_number",
]);

const accname = computed({
    get() {
        return props.accname;
    },
    set(value: string) {
        emit("update:accname", value);
    },
});
const uscc = computed({
    get() {
        return props.uscc;
    },
    set(value: string) {
        emit("update:uscc", value);
    },
});
const acid = computed({
    get() {
        return props.acid;
    },
    set(value: string) {
        emit("update:acid", value);
    },
});
const mainaccno = computed({
    get() {
        return props.mainaccno;
    },
    set(value: string) {
        emit("update:mainaccno", value);
    },
});
const custno = computed({
    get() {
        return props.custno;
    },
    set(value: string) {
        emit("update:custno", value);
    },
});
const uid = computed({
    get() {
        return props.uid;
    },
    set(value: string) {
        emit("update:uid", value);
    },
});
const enterprise_number = computed({
    get() {
        return props.enterprise_number;
    },
    set(value: string) {
        emit("update:enterprise_number", value);
    },
});

const bankAccountList = computed(() => props.bankAccountList);
const currencyList = computed(() => props.currencyList);
const addAccountFormRef = ref<InstanceType<typeof AddAccountDialog>>();
const addAccountDialogShow = ref(false);

const showMain = computed({
    get() {
        return props.showMain;
    },
    set(value: boolean) {
        emit("update:showMain", value);
    },
});
const impowerValue = computed({
    get() {
        return props.impowerValue;
    },
    set(value: string) {
        emit("update:impowerValue", value);
    },
});

const newBankAccountHtml = window.isErp
    ? ""
    : `<li class="new-cd-account">
    <a class="link">
        +点击添加
    </a>
</li>`;

const newBankAccount = () => {
    let ac_name = autoAddName.value;
    request({ url: "/api/CDAccount/List?acType=1020" }).then((res: any) => {
        if (res.state == 1000) {
            const list = res.data;
            const total = list.length;
            let ac_no = total + 1;
            let ac_no_str = ac_no.toString().padStart(3, "0");
            const arr = list.map(function (o: any) {
                return o.ac_no;
            });
            while (arr.indexOf(ac_no_str) > -1) {
                ac_no = ac_no + 1;
                ac_no_str = ac_no.toString().padStart(3, "0");
            }
            addAccountFormRef.value?.resetForm();
            addAccountFormRef.value?.editSearchInfo({ AC_NO: ac_no_str }, ac_name);
            addAccountDialogShow.value = true;
        }
    });
};
const toBankAccPreOpen = () => {
    if (checkPermission(["bankpreopen-bankpreopen"]) || window.isErp) {
        globalWindowOpenPage("/Cashier/BankAccPreOpen", "预约开户");
    } else {
        ElNotify({ type: "warning", message: "您无预约开户的权限，请联系管理员添加" });
    }
};
const saveSuccessHandle = (ac_no: string) => {
    addAccountDialogShow.value = false;
    emit("save-success", ac_no);
};
const confirmBank = () => {
    emit("confirm-bank");
};

const isPlaceholderText = ref(true);
watch(acid, (val) => {
    const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
    mainaccno.value = item?.bank_account || "";
    if (val === "") {
        isPlaceholderText.value = true;
    } else {
        isPlaceholderText.value = false;
    }
});
const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
const querySearch = (queryString: string, cb: any) => {
    if (!queryString) {
        return;
    }
    getCompanyList(1060, queryString, cb, queryParams);
};

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//模糊搜索
interface IBankAccountAll extends IBankAccount {
    label: string;
}
const showBankAccountList = ref<Array<IBankAccountAll>>([]);
const showBankAccountListAll = ref<Array<IBankAccountAll>>([]);
watchEffect(() => { 
    showBankAccountListAll.value = bankAccountList.value.map((item : IBankAccount) => {
        return {
            ...item,
            label: item.ac_no + '-' + item.ac_name,
        }
    })
    showBankAccountList.value = JSON.parse(JSON.stringify(showBankAccountListAll.value));
});
function bankAccountFilterMethod(value: string) {
    showBankAccountList.value = commonFilterMethod(value, showBankAccountListAll.value, 'label');
    autoAddName.value = showBankAccountList.value.length === 0 ? value.trim() : ""; 
}

</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
.tip-item {
    display: flex;
    padding-right: 40px;
    box-sizing: border-box;
}
</style>
<style lang="less">
.boc-bank-popover {
    img {
        width: 100%;
    }
}
</style>