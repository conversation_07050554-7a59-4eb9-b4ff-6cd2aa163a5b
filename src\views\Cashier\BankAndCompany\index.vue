<template>
    <div class="content">
        <div class="title">银企互联</div>
        <div class="slot-content bankandcompany">
            <div class="slot-title">银企互联</div>
            <el-tabs v-model="autoBankName">
                <el-tab-pane label="工商银行" name="ICBC">
                    <ICBCBankCompany
                        bank-name="工商银行"
                        ref="bankCompanyIcbcRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.ICBC"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
                <el-tab-pane label="中国银行" name="BOC">
                    <BOCBankCompany
                        ref="bankCompanyBOCRef"
                        :updateBankAccountList="updateBankAccountList"
                        :bank-account-list="bankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.BOC"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    ></BOCBankCompany>
                </el-tab-pane>
                <el-tab-pane label="招商银行" name="CMB">
                    <CMBBankCompany
                        ref="bankCompanyCMBRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.CMB"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
                <el-tab-pane label="邮储银行" name="PSBC">
                    <PSBCBankCompany
                        bank-name="邮储银行"
                        ref="bankCompanyPSBCRef"
                        :updateBankAccountList="updateBankAccountList"
                        :bank-account-list="bankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.PSBC"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    ></PSBCBankCompany>
                </el-tab-pane>
                <el-tab-pane label="交通银行" name="BCM">
                    <BCMBankCompany
                        ref="bankCompanyBCMRef"
                        :updateBankAccountList="updateBankAccountList"
                        :bank-account-list="bankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.BCM"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    ></BCMBankCompany>
                </el-tab-pane>
                <el-tab-pane label="浦发银行" name="SPDB">
                    <SPDBBankCompany
                        bank-name="浦发银行"
                        ref="bankCompanySPDBRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.SPDB"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
                <el-tab-pane label="平安银行" name="PA">
                    <PABankCompany
                        bank-name="平安银行"
                        ref="bankCompanyPaRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.PABANK"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
                <el-tab-pane label="光大银行" name="CEB">
                    <CEBBankCompany
                        ref="bankCompanyCEBRef"
                        :updateBankAccountList="updateBankAccountList"
                        :bank-account-list="bankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystem"
                        :currencyList="currencyList"
                        :bank-type="BankType.CEB"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    ></CEBBankCompany>
                </el-tab-pane>
                <el-tab-pane label="民生银行" name="CMBC">
                    <CMBCBankCompany
                        ref="bankCompanyCMBCRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.CMBC"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
                <el-tab-pane label="广发银行" name="CGB">
                    <CGBBankCompany
                        ref="bankCompanyCGBRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.CGB"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
                <el-tab-pane label="微众银行" name="WEBANK">
                    <WEBANKBankCompany
                        ref="bankCompanyWebankRef"
                        :bank-account-list="bankAccountList"
                        :updateBankAccountList="updateBankAccountList"
                        :checkAuthorization="CheckBankAccountInOtherSystemNew"
                        :currencyList="currencyList"
                        :bank-type="BankType.WEBANK"
                        :acname="acname"
                        :usccNumber="usccNumber"
                    />
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
    <sms-confirm-dialog
        :phoneList="smsPhoneList"
        :message="smsMessage"
        :accountInfo="smsAccountInfo"
        @confirm-success="confirmSuccess"
        v-model="smsConfirmDialogShow"
        :success-handle="smsHandledSuccess"
        @close="CloseConfirmDialog"
    ></sms-confirm-dialog>
    <el-dialog v-model="repetition" title="提示" width="540" class="dialogDrag">
        <div class="common-dialog-main" v-dialogDrag>
            <div class="common-dialog-main-title">
                {{ repetitionText }}
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="() => (repetition = false)">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "BankAndCompany",
};
</script>
<script setup lang="ts">
import { ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getAccountSetInfoWithOnAsidApi } from "@/api/accountSet";

import { BankType } from "@/constants/bankKey";
import type { IBankAccount } from "./types";
import type { ICurrencyList } from "@/views/Cashier/components/types";

import SmsConfirmDialog from "./components/SmsConfirmDialog.vue";
import ICBCBankCompany from "./components/ICBCBankCompany.vue";
import PABankCompany from "./components/PABankCompany.vue";
import CMBCBankCompany from "./components/CMBCBankCompany.vue";
import CMBBankCompany from "./components/CMBBankCompany.vue";
import SPDBBankCompany from "./components/SPDBBankCompany.vue";
import CGBBankCompany from "./components/CGBBankCompany.vue";
import CEBBankCompany from "./components/CEBBankCompany.vue";
import BCMBankCompany from "./components/BCMBankCompany.vue";
import PSBCBankCompany from "./components/PSBCBankCompany.vue";
import BOCBankCompany from "./components/BOCBankCompany.vue";
import WEBANKBankCompany from "./components/WEBANKBankCompany.vue";
import { useCurrencyStore } from "@/store/modules/currencyList";

const bankCompanyIcbcRef = ref<InstanceType<typeof ICBCBankCompany>>();
const bankCompanyPaRef = ref<InstanceType<typeof PABankCompany>>();
const bankCompanyCMBCRef = ref<InstanceType<typeof CMBCBankCompany>>();
const bankCompanyCMBRef = ref<InstanceType<typeof CMBBankCompany>>();
const bankCompanyCGBRef = ref<InstanceType<typeof CGBBankCompany>>();
const bankCompanySPDBRef = ref<InstanceType<typeof SPDBBankCompany>>();
const bankCompanyCEBRef = ref<InstanceType<typeof CEBBankCompany>>();
const bankCompanyBCMRef = ref<InstanceType<typeof BCMBankCompany>>();
const bankCompanyPSBCRef = ref<InstanceType<typeof PSBCBankCompany>>();
const bankCompanyBOCRef = ref<InstanceType<typeof BOCBankCompany>>();
const bankCompanyWebankRef = ref<InstanceType<typeof WEBANKBankCompany>>();
let initBankCompanyState: Function | undefined;

const autoBankName = ref("ICBC");
const acname = ref("");
const usccNumber = ref("");
const repetition = ref(false);
const repetitionText = ref("");

const smsConfirmDialogShow = ref(false);
const smsPhoneList = ref<Array<string>>([""]);
const smsMessage = ref(
    "检查到该卡号已在其他账套（免费版-测试交行银企互联）的深圳易财信息技术有限公司账户下签约，如需重复签约，则需要管理员验证"
);
const smsAccountInfo = ref("交通银行443066261013006486065");
let smsHandledSuccess: Function | undefined;

const bankAccountList = ref<IBankAccount[]>([]);
const getBankAccountList = () => {
    request({ url: "/api/CDAccount/List?acType=1020" }).then((res: any) => {
        if (res.state == 1000) bankAccountList.value = res.data;
    });
};

const currencyList = ref<ICurrencyList[]>([]);
const getCurrencyList = async () => {
    await useCurrencyStore().getCurrencyList();
    currencyList.value = [...useCurrencyStore().fcList];
};

const updateBankAccountList = (val: IBankAccount[]) => {
    bankAccountList.value = val;
};

const handleSetInitBankCompanyState = (bankType: BankType) => {
    initBankCompanyState = undefined;
    switch (bankType) {
        case BankType.ICBC:
            initBankCompanyState = () => bankCompanyIcbcRef.value?.handleConfirmUnLock();
            break;
        case BankType.PABANK:
            initBankCompanyState = () => bankCompanyPaRef.value?.handleConfirmUnLock();
            break;
        case BankType.CMBC:
            initBankCompanyState = () => bankCompanyCMBCRef.value?.handleConfirmUnLock();
            break;
        case BankType.CMB:
            initBankCompanyState = () => bankCompanyCMBRef.value?.handleConfirmUnLock();
            break;
        case BankType.SPDB:
            initBankCompanyState = () => bankCompanySPDBRef.value?.handleConfirmUnLock();
            break;
        case BankType.CEB:
            initBankCompanyState = () => bankCompanyCEBRef.value?.handleConfirmUnLock();
            break;
        case BankType.CGB:
            initBankCompanyState = () => bankCompanyCGBRef.value?.handleConfirmUnLock();
            break;
        case BankType.BCM:
            initBankCompanyState = () => bankCompanyBCMRef.value?.handleConfirmUnLock();
            break;
        case BankType.PSBC:
            initBankCompanyState = () => bankCompanyPSBCRef.value?.handleConfirmUnLock();
            break;
        case BankType.BOC:
            initBankCompanyState = () => bankCompanyBOCRef.value?.handleConfirmUnLock();
            break;
        case BankType.WEBANK:
            initBankCompanyState = () => bankCompanyWebankRef.value?.handleConfirmUnLock();
            break;
        default:
            initBankCompanyState = undefined;
    }
};

const CheckBankAccountInOtherSystem = (bankType: BankType, accountNo: string, success?: Function) => {
    const url = `/api/BankSign/CheckBeforeBankAuthorized?bank=${bankType}&bankCode=${accountNo}`;
    handResultCheckBankAccount(url, bankType, accountNo, success);
};
//目前招商和民生银行签约（后续其他银行都将调用它）
const CheckBankAccountInOtherSystemNew = (bankType: BankType, accountNo: string, success?: Function) => {
    const url = `/api/BankSign/CheckBeforeBankAuthorizedNew?bank=${bankType}&bankCode=${accountNo}`;
    handResultCheckBankAccount(url, bankType, accountNo, success);
};
const handResultCheckBankAccount = (url: string, bankType: BankType, accountNo: string, success?: Function) => {
    handleSetInitBankCompanyState(bankType);
    request({
        url: url,
        method: "post",
    }).then((res) => {
        const result = res as IResponseModel<any>;
        if (result.state === 1000) {
            if (result.data.code === 0) {
                if (result.data.data === null) {
                    if (success) {
                        success();
                    }
                } else {
                    smsPhoneList.value = result.data.data.adminMobiles;
                    smsMessage.value = result.data.data.message;
                    smsAccountInfo.value = result.data.data.bankNameAndNo;
                    smsHandledSuccess = success;
                    smsConfirmDialogShow.value = true;
                }
            } else if (result.data.code === 1) {
                if (initBankCompanyState) {
                    initBankCompanyState();
                }
                repetitionText.value = result.data.message;
                repetition.value = true;
            } else {
                if (success) {
                    success();
                }
            }
        } else {
            if (initBankCompanyState) {
                initBankCompanyState();
            }
            ElNotify({ type: "warning", message: "检查签约失败" });
        }
    });
};

const CloseConfirmDialog = () => {
    smsConfirmDialogShow.value = false;
    if (initBankCompanyState) {
        initBankCompanyState();
    }
};

const confirmSuccess = () => {
    smsConfirmDialogShow.value = false;
    // ElNotify({ type: "success", message: "签约成功" });
};

const handleInit = () => {
    getBankAccountList();
    getCurrencyList();
    getAccountSetInfoWithOnAsidApi().then((res) => {
        if (res.state === 1000) {
            acname.value = res.data.taxpayerName ?? "";
            usccNumber.value = res.data.unifiedNumber ?? "";
        } else {
            ElNotify({ type: "warning", message: "获取账套信息失败" });
        }
    });
};

handleInit();
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
.common-dialog-main {
    .common-dialog-main-title {
        padding: 40px 80px;
        min-height: 42px;
        color: var(--font-color);
        font-weight: normal;
        font-size: 16px;
        line-height: 24px;
        text-align: left;
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.bankandcompany {
    :deep(.el-tabs) {
        .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item {
            padding: 24px 20px !important;
        }
    }
}
</style>
