<template>
    <div class="open-account detail-content">
        <div class="detail-back-button" @click="back">
            <img src="@/assets/Icons/back.png" alt="" />
            <span>返回</span>
        </div>
        <div class="slot-title">预约开户详情</div>
        <div class="open-main-content">
            <div class="step-edit">
                <div class="block-title">开户银行</div>
                <div class="block-main">
                    <el-row class="isRow"><div class="openbank-line">开户银行：浦发银行</div> </el-row>
                </div>
                <div class="line"></div>
                <div class="block-title">银行网点信息</div>
                <div class="block-main">
                    <el-form :model="SPDBDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="网点机构号：">
                                <div class="w-240">{{ SPDBDeltailInfo.branchNo }}</div>
                            </el-form-item>
                            <el-form-item label="网点名称：">
                                <div class="w-240">{{ SPDBDeltailInfo.branchName }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="网点地址：">
                                <div class="w-240">{{ SPDBDeltailInfo.branchAddress }}</div>
                            </el-form-item>
                            <el-form-item label="网点电话：">
                                <div class="w-240">{{ SPDBDeltailInfo.branchTel }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">业务信息</div>
                <div class="block-main">
                    <el-form :model="SPDBDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="账户类型：">
                                <div class="w-240">{{ SPDBDeltailInfo.accountType === 0 ? "没有基本户" : "已有基本户" }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow" v-if="SPDBDeltailInfo.accountType === 1">
                            <el-form-item label="基本户开户银行：">
                                <div class="w-240">{{ SPDBDeltailInfo.openBankName }}</div>
                            </el-form-item>
                            <el-form-item label="基本户账号：">
                                <div class="w-240">{{ SPDBDeltailInfo.basicBankAcctNo }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow" v-if="SPDBDeltailInfo.accountType === 1">
                            <el-form-item label="基本户开户许可证核准号：">
                                <div class="w-240">{{ SPDBDeltailInfo.openAcctLcnsId }}</div>
                            </el-form-item>
                            <el-form-item label="一般存款账户类型：">
                                <div class="w-240">{{ getAcctType(SPDBDeltailInfo.depositAcctType) }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">基本信息</div>
                <div class="block-main">
                    <el-form :model="SPDBDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="公司名称：">
                                <div class="w-240">{{ SPDBDeltailInfo.companyName }}</div>
                            </el-form-item>
                            <el-form-item label="统一社会信用代码：">
                                <div class="w-240">{{ SPDBDeltailInfo.unifiedNumber }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="注册资金：">
                                <div class="w-240">{{ SPDBDeltailInfo.regCapital }}</div>
                            </el-form-item>
                            <el-form-item label="统一社会信用代码到期日：">
                                <div class="w-240">{{ SPDBDeltailInfo.unifiedNumberExpData }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="办公地址：">
                                <div class="w-240">{{ SPDBDeltailInfo.companyAddr }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人姓名：">
                                <div class="w-240">{{ SPDBDeltailInfo.legalName }}</div>
                            </el-form-item>
                            <el-form-item label="法人手机号码：">
                                <div class="w-240">{{ SPDBDeltailInfo.legalMobile }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人证件类型：">
                                <div class="w-240">{{ getLegalRprsntIDType(SPDBDeltailInfo.legalRprsntIDType) }}</div>
                            </el-form-item>
                            <el-form-item label="法人证件号码：">
                                <div class="w-240">{{ SPDBDeltailInfo.legalRprsntIDNo }}</div>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item label="法人证件到期日：">
                                <div class="w-240">{{ SPDBDeltailInfo.legalRprsntIDExpData }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">更多信息</div>
                <div class="block-main">
                    <el-form :model="SPDBDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="客户名称：">
                                <div class="w-240">{{ SPDBDeltailInfo.clientName }}</div>
                            </el-form-item>
                            <el-form-item label="预留手机号：">
                                <div class="w-240">{{ SPDBDeltailInfo.defaultMobile }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">预约时间</div>
                <div class="block-main">
                    <el-form :model="SPDBDeltailInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="预约时间：">
                                <div class="w-240">{{ SPDBDeltailInfo.appointmentTime }}</div>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch, type PropType } from "vue";
import { type IBankDetail, type ISPDBDetail, SPDBlegalRprsntIDTypeList, SPDBacctTypeList } from "../types";
const props = defineProps({
    data: {
        type: Object as PropType<IBankDetail<ISPDBDetail>>,
        required: true,
        default: () => ({
            bankType: 0,
            openState: 0,
            id: "",
            content: {
                accountType: 0,
                acctType: "",
                appointmentTime: "",
                basicBankAcctNo: "",
                branchAddress: "",
                branchProvince: "",
                branchCity: "",
                branchName: "",
                branchNo: "",
                branchTel: "",
                clientName: "",
                companyAddr: "",
                companyName: "",
                defaultMobile: "",
                depositAcctType: "",
                legalMobile: "",
                legalName: "",
                legalRprsntIDExpData: "",
                legalRprsntIDNo: "",
                legalRprsntIDType: "",
                openAcctLcnsId: "",
                openBankName: "",
                regCapital: "",
                unifiedNumber: "",
                unifiedNumberExpData: "",
            },
        }),
    },
});
const emit = defineEmits<{
    (e: "back"): void;
}>();

const SPDBDeltailInfo = computed(() => props.data.content);

const getLegalRprsntIDType = (value: string) => {
    const targetItem = SPDBlegalRprsntIDTypeList.find((item) => item.value === value);
    return targetItem?.label ?? "";
};

const getAcctType = (value: string) => {
    const acctType = SPDBacctTypeList.find((item) => item.value === value);
    return acctType?.label ?? "";
};
const back = () => {
    emit("back");
};
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
