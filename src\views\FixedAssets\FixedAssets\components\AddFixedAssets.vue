<template>
  <div class="add-content">
    <div class="title">新增资产</div>
    <div class="divEdit">
      <div class="block-title">
        基本信息
        <span id="createtime">录入日期：{{ getFormatterDate() }}</span>
      </div>
      <div class="block-main">
        <el-form
          label-position="left"
          :show-message="false"
          :model="addForm"
          ref="formRefInfo">
          <el-row class="isRow">
            <el-form-item
              label="资产类别："
              prop="faType"
              :required="true">
              <Select
                class="FaNumRef4"
                ref="FaNumRef4"
                v-model="addForm.faType"
                :teleported="false"
                :fitInputWidth="true"
                :filterable="true"
                @change="AssetClassChange"
                :input-text="faTypeName"
                @keyup.enter="nextFocus(5)"
                :filter-method="faTypeFilterMethod">
                <ElOption
                  v-for="item in showfaTypeList"
                  :value="item.value"
                  :label="item.label"
                  :key="item.value"></ElOption>
              </Select>
            </el-form-item>
            <el-form-item
              label="资产编号："
              prop="faNum"
              :required="true">
              <Tooltip
                :content="addForm.faNum"
                :isInput="true">
                <el-input
                  class="FaNumRef"
                  ref="FaNumRef"
                  v-model="addForm.faNum"
                  autofocus
                  @keyup.enter="nextFocus(2)"></el-input>
              </Tooltip>
            </el-form-item>
            <el-form-item
              label="资产名称："
              prop="faName"
              class="fa-name">
              <div v-if="faNameTextareaShow">
                <el-input
                  class="FaNumRef3"
                  ref="FaNumRef3"
                  v-model="addForm.faName"
                  @keyup.enter="nextFocus(4)"
                  @blur="inputTypeBlur('FaNumRef3')"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.faName, '资产名称')"
                  @focus="inputTypeFocus()"
                  resize="none"></el-input>
              </div>
              <Tooltip
                :content="addForm.faName"
                :isInput="true"
                v-else>
                <el-input
                  @focus="inputTypeFocus(3)"
                  class="FaNumRef3"
                  ref="FaNumRef3"
                  v-model="addForm.faName"
                  @keyup.enter="nextFocus(4)"></el-input>
              </Tooltip>
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              label="规格型号："
              prop="faModel"
              class="non-required">
              <div v-if="faModelTextareaShow">
                <el-input
                  class="FaNumRef5"
                  ref="FaNumRef5"
                  v-model="addForm.faModel"
                  @blur="inputTypeBlur('FaNumRef5')"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.faModel, '规格型号')"
                  @focus="inputTypeFocus()"
                  resize="none"
                  style="z-index: 1001"></el-input>
              </div>
              <Tooltip
                :content="addForm.faModel"
                :isInput="true"
                v-else>
                <el-input
                  @focus="inputTypeFocus(5)"
                  class="FaNumRef5"
                  ref="FaNumRef5"
                  v-model="addForm.faModel"></el-input>
              </Tooltip>
            </el-form-item>
            <el-form-item class="non-required">
              <template #label>
                <component :is="renderDateContent('录入期间：', () => emits('goDepreciationTab'))" />
              </template>
              <el-input
                v-model="dateInfo"
                :disabled="true"></el-input>
            </el-form-item>
            <el-form-item
              prop="startedDate"
              :required="true">
              <template #label>
                <component :is="renderDateContent('开始使用日期：', () => emits('goDepreciationTab'))" />
              </template>
              <el-date-picker
                class="FaNumRef6"
                ref="FaNumRef6"
                style="width: 160px; height: 28px"
                v-model="addForm.startedDate"
                type="date"
                placeholder=" "
                value-format="YYYY-MM-DD"
                size="small"
                clearable
                @change="handleDateChange"
                :disabled-date="disabledDateEnd"
                :cell-class-name="dateCellClassName"
                :default-value="new Date(fixedLastDay.year, fixedLastDay.month - 1, 1)"
                @keyup.enter="nextFocus(7)" />
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              label="使用部门："
              :required="true">
              <VirtualSelectCheckbox
                ref="departmentListRef"
                :options="departmentList"
                :maxWidth="150"
                v-model:selectedList="addForm.department"
                :bottomText="hasPermissionDepartment() ? '新增部门' : ''"
                @handleClick="clickAddDepartment"
                @change="changeDepartmentOpt"></VirtualSelectCheckbox>
            </el-form-item>
            <el-form-item
              label="增加方式："
              v-show="addForm.faProperty !== 2"
              prop="createdWay"
              class="non-required">
              <el-select
                v-model="addForm.createdWay"
                :teleported="false"
                :filterable="true"
                :filter-method="createWayFilterMethod">
                <el-option
                  v-for="item in showCreateWayList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="供应商："
              prop="vendor"
              class="non-required">
              <Select
                v-model="addForm.vendor"
                :teleported="false"
                :fitInputWidth="true"
                :filterable="true"
                :IconClearRight="'24px'"
                :clearable="true"
                :allow-create="!isErp"
                :input-text="vendorName"
                :bottom-html="hasPermissionVendor() ? newVendorHtml : ''"
                @bottom-click="clickAddVendor"
                :filter-method="vendorFilterMethod">
                <ElOption
                  v-for="item in showVendorList"
                  :key="item.value"
                  :value="isErp ? item.value : item.label"
                  :label="item.label"></ElOption>
              </Select>
            </el-form-item>
            <el-form-item
              v-if="addForm.faProperty === 2"
              label="使用人："
              prop="usePeople"
              class="non-required">
              <div v-if="usePeopleTextareaShow">
                <el-input
                  ref="FaNumRef10"
                  v-model="addForm.usePeople"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.usePeople, '使用人')"
                  @focus="inputTypeFocus()"
                  @blur="inputTypeBlur('FaNumRef10')"
                  resize="none"
                  style="z-index: 1001"></el-input>
              </div>
              <Tooltip
                :content="addForm.usePeople"
                :isInput="true"
                v-else>
                <el-input
                  v-model="addForm.usePeople"
                  maxlength="256"
                  @focus="inputTypeFocus(10)"
                  @input="limitInputLength(addForm.usePeople, '使用人')"></el-input>
              </Tooltip>
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              v-if="addForm.faProperty !== 2"
              label="使用人："
              prop="usePeople"
              class="non-required">
              <div v-if="usePeopleTextareaShow">
                <el-input
                  ref="FaNumRef10"
                  v-model="addForm.usePeople"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.usePeople, '使用人')"
                  @focus="inputTypeFocus()"
                  @blur="inputTypeBlur('FaNumRef10')"
                  resize="none"
                  style="z-index: 1001"></el-input>
              </div>
              <Tooltip
                :content="addForm.usePeople"
                :isInput="true"
                v-else>
                <el-input
                  v-model="addForm.usePeople"
                  maxlength="256"
                  @focus="inputTypeFocus(10)"
                  @input="limitInputLength(addForm.usePeople, '使用人')"></el-input>
              </Tooltip>
            </el-form-item>
            <el-form-item
              label="存放位置："
              prop="location"
              class="non-required">
              <div v-if="locationTextareaShow">
                <el-input
                  ref="FaNumRef11"
                  v-model="addForm.location"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.location, '存放位置')"
                  @focus="inputTypeFocus(11)"
                  @blur="inputTypeBlur('FaNumRef11')"
                  resize="none"
                  style="z-index: 1001"></el-input>
              </div>
              <Tooltip
                :content="addForm.location"
                :isInput="true"
                v-else>
                <el-input
                  ref="FaNumRef11"
                  @focus="inputTypeFocus(11)"
                  v-model="addForm.location"></el-input>
              </Tooltip>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div class="line"></div>
      <div class="block-title">折旧/摊销方式</div>
      <div class="block-main">
        <el-form
          :model="addForm"
          ref="formRefDepreciation">
          <el-row class="isRow">
            <el-form-item
              :label="[-1, 0].includes(addForm.faProperty) ? '折旧方法：' : '摊销方法：'"
              :required="true"
              prop="depreciationType">
              <el-select
                ref="testRef"
                v-model="addForm.depreciationType"
                :teleported="false"
                :filterable="true"
                :filter-method="depreciationTypeFilterMethod">
                <el-option
                  v-for="item in showDepreciationTypeList"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :label="[-1, 0].includes(addForm.faProperty) ? '录入当期是否折旧：' : '录入当期是否摊销：'"
              :required="true"
              label-width="172px"
              prop="depreciationNow">
              <el-radio-group
                v-model="addForm.depreciationNow"
                :disabled="isdepreciatedRadioFlag">
                <el-radio
                  :label="1"
                  size="large">
                  是
                </el-radio>
                <el-radio
                  :label="0"
                  size="large">
                  否
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <template v-if="addForm.faProperty === 2 || (addForm.faProperty === 1 && [3, 4, 5].includes(accountingStandard))">
            <el-row class="isRow">
              <el-form-item
                label="资产科目："
                :required="true"
                prop="faAsub">
                <Select
                  ref="faAsubRef"
                  v-model="addForm.faAsub"
                  :teleported="false"
                  :input-text="faAsubName"
                  @visible-change="clickAsub('faAsub')"
                  :filterable="true"
                  :filter-method="faAsubFilterMethod">
                  <ElOption
                    :label="item.label"
                    :value="item.value"
                    v-for="item in showfaAsubList"
                    :key="item.value"
                    @click="clickAsubOption('资产科目', item.label, item.type, item.value)"></ElOption>
                </Select>
              </el-form-item>
              <el-form-item
                label="资产处置科目："
                prop="disposalAsub"
                :required="true"
                v-if="addForm.faProperty === 1">
                <Select
                  ref="disposalAsubRef"
                  v-model="addForm.disposalAsub"
                  :teleported="false"
                  style="width: 160px"
                  :input-text="disposalAsubName"
                  @visible-change="clickAsub('disposalAsub')"
                  :filterable="true"
                  :filter-method="disAsubFilterMethod">
                  <ElOption
                    :label="item.label"
                    :value="item.value"
                    v-for="item in showdisAsubList"
                    :key="item.value"
                    @click="clickAsubOption('资产处置科目', item.label, item.type, item.value)"></ElOption>
                </Select>
              </el-form-item>
              <el-form-item
                label="摊销费用科目："
                prop="costAsub"
                :required="true"
                class="jtzj">
                <Select
                  ref="costAsubRef"
                  v-model="addForm.costAsub"
                  :disabled="addForm.department.length > 1"
                  :filterable="true"
                  :fitInputWidth="true"
                  :placeholder="' '"
                  @change="changeCostAsub"
                  :filter-method="costAsubFilterMethod">
                  <ElOption
                    v-for="item in showcostAsubList"
                    :key="item.asubId"
                    :value="item.asubId"
                    :label="item.asubCode + ' ' + item.asubAAName"
                    @click="clickAsubOption('摊销费用科目', item.asubName, item.aatypes, item.asubId, 0, item.aatypesAllowNull)">
                    <span>{{ item.asubCode + " " + item.asubAAName }}</span>
                  </ElOption>
                </Select>
              </el-form-item>
              <el-form-item class="non-required apportion">
                <el-tooltip
                  v-model:visible="amortizationTips"
                  effect="light"
                  placement="top">
                  <template #content>
                    <span>多部门才可进行费用分摊哦~</span>
                  </template>
                  <el-checkbox
                    v-model="isAmortization"
                    :disabled="addForm.department.length < 2"
                    @mouseenter="enterAmortization"
                    @mouseleave="leaveAmortization">
                    分摊
                  </el-checkbox>
                </el-tooltip>
              </el-form-item>
            </el-row>
          </template>
          <template v-else>
            <el-row class="isRow">
              <el-form-item
                label="资产科目："
                prop="faAsub"
                :required="true">
                <Select
                  ref="faAsubRef"
                  v-model="addForm.faAsub"
                  :teleported="false"
                  :input-text="faAsubName"
                  @visible-change="clickAsub('faAsub')"
                  :filterable="true"
                  :filter-method="faAsubFilterMethod">
                  <ElOption
                    :label="item.label"
                    :value="item.value"
                    v-for="item in showfaAsubList"
                    :key="item.value"
                    @click="clickAsubOption('资产科目', item.label, item.type, item.value)"></ElOption>
                </Select>
              </el-form-item>
              <el-form-item
                :label="[-1, 0].includes(addForm.faProperty) ? '累计折旧科目：' : '累计摊销科目:'"
                :required="true"
                prop="depreciationAsub">
                <Select
                  ref="deprecationAsubRef"
                  v-model="addForm.depreciationAsub"
                  :teleported="false"
                  :input-text="depreciationAsubName"
                  @visible-change="clickAsub('deprecationAsub')"
                  :filterable="true"
                  :filter-method="deAsubFilterMethod">
                  <ElOption
                    :label="item.label"
                    :value="item.value"
                    v-for="item in showdeAsubList"
                    :key="item.value"
                    @click="
                      clickAsubOption(addForm.faProperty === 0 ? '累计折旧科目' : '累计摊销科目', item.label, item.type, item.value)
                    "></ElOption>
                </Select>
              </el-form-item>
              <el-form-item
                label="资产处置科目："
                prop="disposalAsub"
                :required="true">
                <Select
                  ref="disposalAsubRef"
                  v-model="addForm.disposalAsub"
                  :teleported="false"
                  style="width: 160px"
                  :input-text="disposalAsubName"
                  @visible-change="clickAsub('disposalAsub')"
                  :filterable="true"
                  :filter-method="disAsubFilterMethod">
                  <ElOption
                    :label="item.label"
                    :value="item.value"
                    v-for="item in showdisAsubList"
                    :key="item.value"
                    @click="clickAsubOption('资产处置科目', item.label, item.type, item.value)"></ElOption>
                </Select>
              </el-form-item>
            </el-row>
            <el-row class="isRow">
              <el-form-item
                prop="impairmentProvisionAsub"
                label="资产减值准备科目："
                v-if="[2, 3].includes(accountingStandard)"
                :required="true"
                class="lineHless">
                <Select
                  ref="impairmentProvisionAsubRef"
                  v-model="addForm.impairmentProvisionAsub"
                  :teleported="false"
                  :input-text="impairmentProvisionName"
                  @visible-change="clickAsub('impairmentProvisionAsub')"
                  :filterable="true"
                  :filter-method="impAsubFilterMethod">
                  <ElOption
                    :label="item.label"
                    :value="item.value"
                    v-for="item in showimpAsubList"
                    :key="item.value"
                    @click="clickAsubOption('资产减值准备科目', item.label, item.type, item.value)"></ElOption>
                </Select>
              </el-form-item>
              <el-form-item
                prop="costAsub"
                :label="[-1, 0].includes(addForm.faProperty) ? '折旧费用科目：' : '摊销费用科目：'"
                :required="true">
                <Select
                  ref="costAsubRef"
                  v-model="addForm.costAsub"
                  :disabled="addForm.department.length > 1"
                  :filterable="true"
                  :fitInputWidth="true"
                  :placeholder="' '"
                  @change="changeCostAsub"
                  :filter-method="costAsubFilterMethod">
                  <ElOption
                    v-for="item in showcostAsubList"
                    :key="item.asubId"
                    :value="item.asubId"
                    :label="item.asubCode + ' ' + item.asubAAName"
                    @click="
                      clickAsubOption(
                        [-1, 0].includes(addForm.faProperty) ? '折旧费用科目' : '摊销费用科目',
                        item.asubName,
                        item.aatypes,
                        item.asubId,
                        0,
                        item.aatypesAllowNull
                      )
                    "></ElOption>
                </Select>
              </el-form-item>
              <el-form-item class="non-required apportion">
                <el-tooltip
                  :visible="amortizationTips"
                  effect="light"
                  placement="top">
                  <template #content>
                    <span>多部门才可进行费用分摊哦~</span>
                  </template>
                  <el-checkbox
                    v-model="isAmortization"
                    :disabled="addForm.department.length < 2"
                    @mouseenter="enterAmortization"
                    @mouseleave="leaveAmortization">
                    分摊
                  </el-checkbox>
                </el-tooltip>
              </el-form-item>
            </el-row>
          </template>
        </el-form>
        <div
          class="apportionment-table"
          v-if="isAmortization">
          <div class="apportionment-title">{{ [-1, 0].includes(addForm.faProperty) ? "折旧费用分摊表" : "摊销费用分摊表" }}</div>
          <Table
            :data="addForm.amortizations"
            :columns="amortizationColums"
            :hasAddSub="true"
            :customSubtract="true"
            :show-overflow-tooltip="true"
            :tooltip-options="{ effect: 'light', placement: 'top' }"
            :addRowData="{ department_id: '', ratio: '', asub_id: '' }"
            @handleSubtract="subtractAmortization"
            @table-add-or-subtract="handleTableAddSub"
            :tableName="setModule">
            <template #department_id>
              <el-table-column
                label="使用部门"
                min-width="212"
                align="center"
                header-align="center"
                prop="description"
                :width="getColumnWidth(setModule, 'description')">
                <template #default="scope">
                  <Select
                    :style="{ width: '100%' }"
                    v-model="scope.row.department_id"
                    :filterable="true"
                    :fitInputWidth="true"
                    @change="changeDepartment"
                    :filter-method="departFilterMethod">
                    <ElOption
                      v-for="item in showdepartmentList"
                      :key="item.id"
                      :value="item.id"
                      :label="item.name"></ElOption>
                  </Select>
                </template>
              </el-table-column>
            </template>
            <template #ratio>
              <el-table-column
                label="分摊比例"
                min-width="200"
                align="center"
                header-align="center"
                prop="ratio"
                :show-overflow-tooltip="false"
                :width="getColumnWidth(setModule, 'ratio')">
                <template #default="scope">
                  <div class="cell-flex">
                    <el-input
                      style="width: 89%"
                      v-model="scope.row.ratio"
                      @input="(val) => ratioInput(val, scope.$index)" />
                    <span style="padding-left: 2px">%</span>
                  </div>
                </template>
              </el-table-column>
            </template>
            <template #asub>
              <el-table-column
                :label="[-1, 0].includes(addForm.faProperty) ? '折旧费用科目' : '摊销费用科目'"
                min-width="210"
                align="center"
                header-align="center"
                prop="description"
                :resizable="false">
                <template #default="scope">
                  <SelectV2
                    :class="'visibleSelect' + scope.$index"
                    :options="showcostAsubAmList"
                    :filterable="true"
                    :placeholder="' '"
                    v-model="scope.row.asub_id"
                    @change="(value) => clickCostAsub(value, scope.$index)"
                    :toolTipOptions="{ dynamicWidth: true }"
                    @visible-change="costAsubVisibleChange"
                    :remote="true"></SelectV2>
                </template>
              </el-table-column>
            </template>
          </Table>
        </div>
      </div>
      <div class="line"></div>
      <div class="block-title">资产数据</div>
      <div class="block-main">
        <el-form
          :model="addForm"
          ref="formRefAsset">
          <el-row class="isRow">
            <el-form-item
              label="资产原值："
              prop="value"
              :required="true">
              <el-input
                class="dadaNumRef1"
                ref="dadaNumRef1"
                v-model="addForm.value"
                @keydown.enter="nextFocusData(2)"
                maxlength="13"
                @input="(val) => handleInput(val, 'value')"></el-input>
            </el-form-item>
            <el-form-item
              label="残值率："
              prop="netSalvageRate"
              :required="true">
              <el-input
                class="dadaNumRef2"
                ref="dadaNumRef2"
                v-model="addForm.netSalvageRate"
                @keydown.enter="nextFocusData(3)"
                @input="(val) => handleInput(val, 'netSalvageRate')">
                <template #suffix>
                  <span class="percentsign">%</span>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item
              label="预计残值："
              prop="estimateResidualsRate"
              class="non-required">
              <el-input
                v-model="addForm.estimateResidualsRate"
                :disabled="true"></el-input>
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              label="预计使用月份："
              :required="true"
              prop="useFullLife">
              <el-input
                class="dadaNumRef3"
                ref="dadaNumRef3"
                v-model="addForm.useFullLife"
                @keydown.enter="nextFocusData(4)"></el-input>
            </el-form-item>
            <el-form-item
              :label="[-1, 0].includes(addForm.faProperty) ? '已折旧月份：' : '已摊销月份：'"
              :required="true"
              prop="usedLife">
              <el-input
                class="dadaNumRef4"
                ref="dadaNumRef4"
                v-model="addForm.usedLife"
                @keydown.enter="nextFocusData(5)"></el-input>
            </el-form-item>
            <el-form-item
              label="剩余使用月份："
              class="non-required"
              prop="remainingMonth">
              <el-input
                v-model="addForm.remainingMonth"
                :disabled="true"></el-input>
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              :label="[-1, 0].includes(addForm.faProperty) ? '累计折旧：' : '累计摊销：'"
              :required="true"
              prop="accumlatedDepreciation">
              <el-input
                class="dadaNumRef5"
                ref="dadaNumRef5"
                v-model="addForm.accumlatedDepreciation"
                @keydown.enter="nextFocusData(6)"
                @input="(val) => handleInput(val, 'accumlatedDepreciation')"></el-input>
            </el-form-item>
            <el-form-item
              class="non-required"
              :label="[-1, 0].includes(addForm.faProperty) ? '本年累计折旧：' : '本年累计摊销：'"
              prop="accumlatedDepreciationTY">
              <el-input
                class="dadaNumRef6"
                ref="dadaNumRef6"
                v-model="addForm.accumlatedDepreciationTY"
                @keydown.enter="nextFocusData(7)"
                @input="(val) => handleInput(val, 'accumlatedDepreciationTY')"></el-input>
            </el-form-item>
            <el-form-item
              class="non-required"
              :label="[-1, 0].includes(addForm.faProperty) ? '以前年度累计折旧：' : '以前年度累计摊销：'"
              prop="prevAccumulatedDepreciation">
              <el-input
                v-model="addForm.prevAccumulatedDepreciation"
                :disabled="true"></el-input>
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              label="减值准备："
              v-if="
                (accountingStandard === 3 && addForm.faProperty === 0) || (accountingStandard === 2 && [0, 1].includes(addForm.faProperty))
              "
              prop="impairmentProvision"
              class="non-required">
              <template v-slot:label>
                <span class="label_before">减值准备：</span>
              </template>
              <el-input
                class="dadaNumRef7"
                ref="dadaNumRef7"
                v-model="addForm.impairmentProvision"
                @keydown.enter="nextFocusData(8)"></el-input>
            </el-form-item>
            <el-form-item
              :label="addForm.faProperty === 0 ? '月折旧额：' : '月摊销额：'"
              :required="true"
              prop="monthDepreciationValue">
              <el-input
                v-model="addForm.monthDepreciationValue"
                :disabled="true"></el-input>
            </el-form-item>
            <el-form-item
              label="进项税额抵扣："
              class="non-required"
              v-if="taxType === 2 && [1, 2, 3].includes(accountingStandard)"
              prop="inputTax">
              <el-input v-model="addForm.inputTax"></el-input>
            </el-form-item>
            <el-form-item
              v-if="noteShow() < 2"
              label="净值："
              class="non-required"
              prop="netValue">
              <el-input
                v-model="addForm.netValue"
                :disabled="true"></el-input>
            </el-form-item>
            <el-form-item
              v-if="noteShow() < 1"
              label="备注："
              class="non-required"
              prop="note">
              <div v-if="noteTextareaShow">
                <el-input
                  class="dadaNumRef8"
                  ref="dadaNumRef8"
                  v-model="addForm.note"
                  @keydown.enter="nextFocusData(1)"
                  @blur="inputTypeBlur('dadaNumRef8')"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.note, '备注')"
                  @focus="inputTypeFocus(18)"
                  resize="none"></el-input>
              </div>
              <Tooltip
                :content="addForm.note"
                :isInput="true"
                v-else>
                <el-input
                  @focus="inputTypeFocus(18)"
                  class="dadaNumRef8"
                  ref="dadaNumRef8"
                  v-model="addForm.note"
                  @keydown.enter="nextFocusData(1)"></el-input>
              </Tooltip>
            </el-form-item>
          </el-row>
          <el-row class="isRow">
            <el-form-item
              v-if="noteShow() >= 2"
              label="净值："
              class="non-required"
              prop="netValue">
              <template v-slot:label>
                <span class="label_before">净值：</span>
              </template>
              <el-input
                v-model="addForm.netValue"
                :disabled="true"></el-input>
            </el-form-item>
            <el-form-item
              v-if="noteShow() >= 1"
              label="备注："
              prop="note">
              <template v-slot:label>
                <span class="label_before">备注：</span>
              </template>
              <div v-if="noteTextareaShow">
                <el-input
                  class="dadaNumRef8"
                  ref="dadaNumRef8"
                  v-model="addForm.note"
                  @keydown.enter="nextFocusData(1)"
                  @blur="inputTypeBlur('dadaNumRef8')"
                  :autosize="{ minRows: 1, maxRows: 3.5 }"
                  type="textarea"
                  maxlength="256"
                  @input="limitInputLength(addForm.note, '备注')"
                  @focus="inputTypeFocus(18)"
                  resize="none"></el-input>
              </div>
              <Tooltip
                :content="addForm.note"
                :isInput="true"
                v-else>
                <el-input
                  @focus="inputTypeFocus(18)"
                  class="dadaNumRef8"
                  ref="dadaNumRef8"
                  v-model="addForm.note"
                  @keydown.enter="nextFocusData(1)"></el-input>
              </Tooltip>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div class="line"></div>
      <div class="block-title">附件</div>
      <div class="attach-file-center">
        <div
          v-if="addForm.attachsCount === 0"
          class="upload-item"
          @click.stop="getAttachFileList">
          <div class="upload-item-title"><img src="@/assets/Invoice/upload.png" /></div>
          <div class="upload-item-tip">单个文件不超过 100M</div>
        </div>
        <template v-else>
          <a
            class="link"
            @click.stop="getAttachFileList">
            查看附件({{ addForm.attachsCount }})
          </a>
        </template>
      </div>
      <div
        class="buttons"
        :style="isErp ? 'margin-top:0' : ''">
        <a
          class="button solid-button mr-10"
          @click="savedataClick">
          保存
        </a>
        <a
          class="button"
          @click="divCancel">
          取消
        </a>
      </div>
    </div>
  </div>
  <AddDepartment
    ref="departmentAddRef"
    :isShow="departmentAdd"
    :departmentCode="departmentCode"
    @cancel="() => (departmentAdd = false)"
    @close="() => (departmentAdd = false)"
    @save="saveDepartment" />
  <AddVendor
    ref="vendorAddRef"
    :isShow="vendorAdd"
    :vendorCode="vendorCode"
    :codeDisabled="codeDisabled"
    :vendorNameAuto="vendorNameAuto"
    @cancel="() => (vendorAdd = false)"
    @close="() => (vendorAdd = false)"
    @save="saveVendor"></AddVendor>
  <AddAssistingAccountingEntryDialog
    ref="addAssistingAccountingEntryDialogRef"
    :title="title"
    @save-success="erpAAESaveSuccess"></AddAssistingAccountingEntryDialog>
  <AuxiliaryDialog
    v-model="auxiliaryDialogShow"
    :title="auxiliaryTitle"
    :asubName="auxiloaryAsubName"
    :aacode="auxiloaryCode"
    :aaAllowNull="auxiliaryAllowNull"
    :aaId="auxiliaryId"
    :departmentId="addForm.department"
    @setAsubWithAAE="setAsubWithAAE"></AuxiliaryDialog>
  <UploadFileDialog
    ref="uploadFileDialogRef"
    @save="saveAttachFile" />
</template>

<script setup lang="ts">
  import Select from "@/components/Select/index.vue";
  import Tooltip from "@/components/Tooltip/index.vue";
  import ElOption from "@/components/Option/index.vue";
  import SelectV2 from "@/components/AsubSelect/index.vue";
  import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
  import AuxiliaryDialog from "./AuxiliaryDialog.vue";
  import Table from "@/components/Table/index.vue";
  import { useLoading } from "@/hooks/useLoading";
  import { ref, reactive, computed, watchEffect, watch, onMounted, nextTick, onUnmounted, toRef } from "vue";
  import { request, type IResponseModel } from "@/util/service";
  import { ElNotify } from "@/util/notify";
  import { ElConfirm, ElAlert } from "@/util/confirm";
  import { getFormatterDate } from "@/util/date";
  import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
  import AddDepartment from "./AddDepartment.vue";
  import AddVendor from "./AddVendor.vue";
  import type { IDepartmentAddForm } from "./AddDepartment.vue";
  import type { IVendorAddForm } from "./AddVendor.vue";
  import type { Option } from "@/components/SelectCheckbox/types";
  import type { IColumnProps } from "@/components/Table/IColumnProps";
  import type { IFAType, IDepartment, ISelectStrItem, IFAAsubDto, IAsubSelect, IAddAsset, IAccountSubjectList } from "../types";
  import { checkAssetsInput, isDecimalPointLaterTwo, isDecimal, isInteger } from "../validator";
  import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
  import { provide } from "vue";
  import { checkPermission } from "@/util/permission";
  import { textareaBottom, getCostAsubList, useTooltip } from "../utils";
  import { getGlobalLodash } from "@/util/lodash";
  import type { ElInput } from "element-plus";
  import type { IAssistingAccount } from "@/api/assistingAccounting";
  import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
  import type { IFileInfo, IGetAttachFileListBack } from "@/components/UploadFileDialog/types";
  import { getColumnWidth } from "@/components/ColumnSet/utils";
  import { commonFilterMethod } from "@/components/Select/utils";
  import { createWayList, depreciationTypeList } from "@/views/FixedAssets/utils";
  import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
  import { handleExpiredCheckData, ExpiredCheckModuleEnum } from "@/util/proUtils";

  const setModule = "AEFixedAssets";
  const props = defineProps<{
    faid: number;
    pid: number;
    dateInfo: string;
    activeName: string;
    initType: string;
    iaPid: number;
  }>();
  const _ = getGlobalLodash();
  const emits = defineEmits(["divCancel","goDepreciationTab"]);

  const { renderDateContent } = useTooltip()
  const accountSetStore = useAccountSetStore();
  const accountingStandard = accountSetStore.accountSet!.accountingStandard as number;
  const taxType = useAccountSetStore().accountSet?.taxType;
  const dateInfo = ref(props.dateInfo);
  const isErp = ref(window.isErp);

  function noteShow() {
    let prevContent = 0;
    if ((accountingStandard === 3 && addForm.faProperty === 0) || (accountingStandard === 2 && [0, 1].includes(addForm.faProperty))) {
      prevContent++;
    }
    if (taxType === 2 && [1, 2, 3].includes(accountingStandard)) {
      prevContent++;
    }
    return prevContent;
  }
  const faNameTextareaShow = ref(false);
  const faModelTextareaShow = ref(false);
  const usePeopleTextareaShow = ref(false);
  const locationTextareaShow = ref(false);
  const vendorTextareaShow = ref(false);
  const noteTextareaShow = ref(false);
  const inputTypeBlur = (value: string) => {
    switch (value) {
      case "FaNumRef3":
        faNameTextareaShow.value = false;
        break;
      case "FaNumRef5":
        faModelTextareaShow.value = false;
        break;
      case "FaNumRef8":
        vendorTextareaShow.value = false;
        break;
      case "dadaNumRef8":
        noteTextareaShow.value = false;
        break;
      case "FaNumRef10":
        usePeopleTextareaShow.value = false;
        break;
      case "FaNumRef11":
        locationTextareaShow.value = false;
        break;
    }
  };
  const inputTypeFocus = (num?: number) => {
    textareaBottom(formRefInfo);
    switch (num) {
      case 3:
        faNameTextareaShow.value = true;
        break;
      case 5:
        faModelTextareaShow.value = true;
        break;
      case 8:
        vendorTextareaShow.value = true;
        break;
      case 11:
        usePeopleTextareaShow.value = false;
        locationTextareaShow.value = true;
        break;
      case 10:
        locationTextareaShow.value = false;
        usePeopleTextareaShow.value = true;
        break;
      case 18:
        textareaBottom(formRefAsset);
        noteTextareaShow.value = true;
        break;
    }
    nextTick(() => {
      if (num) {
        getTextareaFocus(num);
      }
    });
  };
  const getTextareaFocus = (num: number) => {
    switch (num) {
      case 3:
        FaNumRef3.value.focus();
        break;
      case 5:
        FaNumRef5.value.focus();
        break;
      case 8:
        FaNumRef8.value.focus();
        break;
      case 10:
        FaNumRef10.value.focus();
        break;
      case 11:
        FaNumRef11.value.focus();
        break;
      case 18:
        dadaNumRef8.value.focus();
        break;
    }
  };

  const disabledDateEnd = (time: Date) => {
    return time >= getNextMonthFirstDay(props.dateInfo + "1日");
  };
  const dateCellClassName = (date: Date) => {
    if (
      !addForm.startedDate &&
      date.getFullYear() === fixedLastDay.value.year &&
      date.getMonth() + 1 === fixedLastDay.value.month &&
      date.getDate() === fixedLastDay.value.lastDay
    ) {
      return "current";
    } else {
      return "";
    }
  };

  const getNextMonthFirstDay = (dateStr: string) => {
    // 将日期字符串转换为 JavaScript 可以解析的格式
    let formattedStr = dateStr.replace("年", "-").replace("月", "-").replace("日", "");
    // 创建 Date 对象
    let date = new Date(formattedStr);
    // 获取当前月份，并加 1 来获取下个月的月份
    let nextMonth = date.getMonth() + 1;
    // 设置 Date 对象的月份和日期
    date.setMonth(nextMonth);
    date.setDate(1);
    // 设置 Date 对象的小时、分钟、秒和毫秒为 0
    date.setHours(0, 0, 0, 0);
    // 返回下个月的第一天
    return date;
  };

  // 使用部门下拉框
  const departmentAdd = ref(false);
  const departmentCode = ref("");
  //供应商下拉框
  const vendorAdd = ref(false);
  const vendorCode = ref("");
  const vendorNameAuto = ref("");
  const codeDisabled = ref(false);
  const addForm = reactive({
    faNum: "",
    createdPeriod: props.pid,
    createdWay: 1,
    faName: "",
    faType: "",
    faModel: "",
    startedDate: "",
    department: [0],
    vendor: "",
    usePeople: "",
    location: "",
    subjectCode: "",
    depreciationNow: 0,
    faAsub: "",
    depreciationType: "1",
    depreciationAsub: "",
    disposalAsub: "",
    costAsub: "",
    fa_prepareAcc: "",
    depreciationExpenseAcc: "",
    value: "",
    netValue: "",
    netSalvageRate: "",
    estimateResidualsRate: "",
    useFullLife: "",
    usedLife: "0",
    remainingMonth: "",
    accumlatedDepreciation: "0",
    accumlatedDepreciationTY: "0",
    prevAccumulatedDepreciation: "0",
    diminutionProvision: "0",
    monthDepreciationValue: 0,
    note: "",
    impairmentProvisionAsub: "",
    faProperty: -1,
    impairmentProvision: 0,
    inputTax: 0,
    amortizations: [
      {
        department_id: 0,
        ratio: "",
        asub_id: "",
        asub_aae: "",
      },
    ],
    asubAAERelation: {
      fa_aae: "",
      depreciation_aae: "",
      disposal_aae: "",
      impairment_provision_aae: "",
    },
    attachsCount: 0,
    attachFiles: "",
  });
  const isAmortization = ref(false);
  watch(
    () => addForm.department,
    (val) => {
      if (val.length > 1) {
        isAmortization.value = true;
        addForm.costAsub = "";
        addForm.amortizations = addForm.department.map((item) => {
          return {
            department_id: item,
            ratio: "",
            asub_id: "",
            asub_aae: "",
          };
        });
      } else {
        addForm.amortizations = [
          {
            department_id: addForm.department[0],
            ratio: "100",
            asub_id: addForm.costAsub + "",
            asub_aae: "",
          },
        ];
      }
    }
  );

  let attachParentId = 0;
  const attachFiles = ref<Array<IFileInfo>>([]);
  const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
  function getAttachFileList() {
    const params = { faId: 0 };
    if (attachParentId === 0) {
      request({
        url: "/api/FixedAssets/GetAttachFileList",
        method: "post",
        params,
      }).then((res: IResponseModel<IGetAttachFileListBack>) => {
        if (res.state === 1000 && res.data.result) {
          const list = res.data.data.map((item: any) => {
            item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
            return item;
          });
          attachFiles.value = list;
          attachParentId = res.data.parentId;
          uploadFileDialogRef.value?.open(params, attachFiles.value, attachParentId);
        } else {
          ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
        }
      });
    } else {
      uploadFileDialogRef.value?.open(params, attachFiles.value, attachParentId);
    }
  }
  function saveAttachFile(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
    attachFiles.value = fileList.slice();
    addForm.attachsCount = fileList.length;
    addForm.attachFiles = fileList.map((item: any) => item.fileId).join(",");
  }

  const amortizationColums = ref<Array<IColumnProps>>([{ slot: "department_id" }, { slot: "ratio" }, { slot: "asub" }]);
  function changeDepartment(val: number) {
    if (val) {
      addForm.department = addForm.amortizations.map((item) => item.department_id);
    }
  }
  function handleTableAddSub(data: any) {
    addForm.amortizations = data;
  }
  function subtractAmortization(index: number) {
    if (addForm.amortizations[index].department_id) {
      addForm.department = addForm.department.filter((item) => item != addForm.amortizations[index].department_id);
    }
    addForm.amortizations.splice(index, 1);
    if (addForm.amortizations.length < 2) isAmortization.value = false;
  }
  const amortizationTips = ref(false);
  function enterAmortization() {
    if (addForm.department.length < 2) {
      amortizationTips.value = true;
    }
  }
  function leaveAmortization() {
    amortizationTips.value = false;
  }
  watch(
    () => isAmortization.value,
    (val) => {
      if (!val) {
        addForm.department = addForm.amortizations[0].department_id ? [addForm.amortizations[0].department_id] : [];
        addForm.amortizations = [addForm.amortizations[0]];
        getDefaultCostAsub();
      }
    }
  );
  //部门供应商新增权限
  const hasPermissionDepartment = () => {
    if (isErp.value) {
      return checkPermission(["Department-编辑"]);
    } else {
      return checkPermission(["assistingaccount-canedit"]);
    }
  };
  const hasPermissionVendor = () => {
    if (isErp.value) {
      return checkPermission(["Vendors-编辑"]);
    } else {
      return checkPermission(["assistingaccount-canedit"]);
    }
  };

  const faAsubName = computed(() => {
    return faAsubList.value.find((item) => item.value == addForm.faAsub)?.label;
  });
  const depreciationAsubName = computed(() => {
    return deprecationAsubList.value.find((item) => item.value == addForm.depreciationAsub)?.label;
  });
  const disposalAsubName = computed(() => {
    return disposalAsubList.value.find((item) => item.value == addForm.disposalAsub)?.label;
  });
  const impairmentProvisionName = computed(() => {
    return impairmentProvisionAsubList.value.find((item) => item.value == addForm.impairmentProvisionAsub)?.label;
  });
  const faTypeName = computed(() => {
    return faTypeList.value.find((item) => item.value == addForm.faType)?.label;
  });
  // const departmentName = computed(() => {
  //     return departmentList.value.find((item) => item.value == addForm.department)?.label;
  // });
  const vendorName = computed(() => {
    return vendorList.value.find((item) => item.value == addForm.vendor)?.label;
  });
  const FaNumRef = ref<InstanceType<typeof ElInput>>();
  const FaNumRef2 = ref();
  const FaNumRef3 = ref();
  const FaNumRef4 = ref();
  const FaNumRef5 = ref();
  const FaNumRef6 = ref();
  const FaNumRef7 = ref();
  const FaNumRef8 = ref();
  const FaNumRef10 = ref();
  const FaNumRef11 = ref();

  const dadaNumRef1 = ref();
  const dadaNumRef2 = ref();
  const dadaNumRef3 = ref();
  const dadaNumRef4 = ref();
  const dadaNumRef5 = ref();
  const dadaNumRef6 = ref();
  const dadaNumRef7 = ref();
  const dadaNumRef8 = ref();

  function limitInputLength(val: string, name: string, limitLength: number = 256) {
    if (val.length === limitLength) {
      ElNotify({ type: "warning", message: "亲，" + name + `不能超过${limitLength}个字哦~` });
    }
  }
  const nextFocus = (val: number) => {
    if (val === 2) {
      FaNumRef2.value.focus();
      FaNumRef2.value.dropMenuVisible = true;
    }
    if (val === 3) FaNumRef3.value.focus();
    if (val === 4) FaNumRef4.value.focus();
    if (val === 5) FaNumRef5.value.focus();
    if (val === 6) FaNumRef6.value.focus();
    if (val === 7) FaNumRef7.value.focus();
    if (val === 8) {
      FaNumRef8.value.focus();
    }
    const inputE = document.querySelector(`.FaNumRef${val} .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
      if (e.keyCode === 13) {
        if (val != 2) {
          if (val != 8) {
            nextFocus(val + 1);
          } else {
            FaNumRef8.value.blur();
          }
        }
      }
    });
  };
  const nextFocusData = (val: number) => {
    if (val === 1) dadaNumRef1.value.focus();
    if (val === 2) dadaNumRef2.value.focus();
    if (val === 3) dadaNumRef3.value.focus();
    if (val === 4) dadaNumRef4.value.focus();
    if (val === 5) dadaNumRef5.value.focus();
    if (val === 6) dadaNumRef6.value.focus();
    if (val === 7) dadaNumRef7.value?.focus(); //减值准备可能没有
    if (val === 8) {
      dadaNumRef8.value.focus();
      return;
    }
    const inputE = document.querySelector(`.dadaNumRef${val} .el-input__inner`);
    inputE?.addEventListener("keydown", (e: any) => {
      if (e.keyCode === 13) {
        if (val === 6 && !dadaNumRef7.value) {
          nextFocusData(val + 2);
        } else {
          nextFocusData(val + 1);
        }
      }
    });
  };
  function handleInput(value: string, type: string) {
    const regex = /^\d+(?:\.\d{0,2})?/;
    const match = value.match(regex);
    switch (type) {
      case "value":
        addForm.value = match ? match[0] : "";
        break;
      case "accumlatedDepreciation":
        addForm.accumlatedDepreciation = match ? match[0] : "";
        break;
      case "accumlatedDepreciationTY":
        addForm.accumlatedDepreciationTY = match ? match[0] : "";
        break;
      case "netSalvageRate":
        addForm.netSalvageRate = match ? match[0] : "";
        break;
    }
  }

  const newVendorHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">
            新增供应商
        </a>
    </div>`;

  const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
  const erpAAESaveSuccess = (data: any) => {
    if (title.value === "新增部门") {
      useAssistingAccountingStore()
        .getDepartment()
        .then(() => {
          getDepartmentApi("1", data);
        });
    } else if (title.value === "新增供应商") {
      useAssistingAccountingStore()
        .getAssistingAccounting()
        .then(() => {
          getVendorList("1", data);
        });
    } else {
      useAssistingAccountingStore().getAssistingAccounting();
    }
  };
  const title = ref("添加辅助核算项目");
  function clickAddDepartment() {
    if (isErp.value) {
      addAssistingAccountingEntryDialogRef.value?.showAADialog(10004);
      title.value = "新增部门";
    } else {
      // 获取最新的辅助核算编码
      request({
        url: `/api/AssistingAccounting/GetNewAANum?aaType=10004&categoryId=0`,
        method: "post",
      }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
          departmentCode.value = res.data;
          departmentAdd.value = true;
        }
      });
    }
  }

  function changeDepartmentOpt() {
    if (addForm.department.length < 2) {
      isAmortization.value = false;
    }
  }
  function clickAddVendor() {
    if (isErp.value) {
      addAssistingAccountingEntryDialogRef.value?.showAADialog(10002, autoAddName.value);
      title.value = "新增供应商";
      return;
    }
    request({
      url: `/api/ErpAssistingAccounting/GetIsEntityNumberChangeable?aaType=10002`,
      method: "post",
    }).then((res: IResponseModel<boolean>) => {
      if (res.state === 1000) {
        codeDisabled.value = !res.data;
      }
    });
    vendorNameAuto.value = autoAddName.value;
    // 获取最新的辅助核算编码
    let numUrl = isErp.value
      ? `/api/ErpAssistingAccounting/GetNewVendCode?categoryId=10002`
      : `/api/AssistingAccounting/GetNewAANum?aaType=10002&categoryId=0`;
    request({
      url: numUrl,
      method: "post",
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000) {
        vendorCode.value = res.data;
        vendorAdd.value = true;
        if (res.msg) {
          ElNotify({ type: "warning", message: res.msg });
        }
      }
    });
  }

  const departmentAddRef = ref<InstanceType<typeof AddDepartment>>();
  function saveAs(formData: IDepartmentAddForm) {
    const entity = {
      manager: formData.departmentManager,
      mobilePhone: formData.departmentManagerPhone,
      startDate: formData.departmentEstablishDate,
      endDate: formData.departmentCancelDate,
      note: formData.departmentRemark,
    };
    const departmentData = {
      aaNum: formData.departmentCode,
      aaName: formData.departmentName,
      entity,
      hasEntity: Object.values(entity).some((value) => {
        return value !== null && value !== undefined && value !== "";
      }),
    };
    // 添加部门辅助核算
    request({
      url: `/api/AssistingAccounting/Department`,
      method: "post",
      data: departmentData,
    }).then(async (res: IResponseModel<string>) => {
      if (res.state === 1000 && res.data) {
        useAssistingAccountingStore().getAssistingAccounting();
        useAssistingAccountingStore()
          .getDepartment()
          .then(() => {
            getDepartmentApi("1", departmentData);
            departmentAdd.value = false;
            departmentAddRef.value?.resetFields();
          });
      }
    });
  }

  function saveDepartment(formData: IDepartmentAddForm) {
    const data = {
      aaNum: formData.departmentCode,
      aaName: formData.departmentName,
    };
    // 添加校验
    request({
      url: `/api/AssistingAccounting/Check?aaType=10004`,
      method: "post",
      data,
    }).then((res: IResponseModel<string>) => {
      if (res.data === "N") {
        saveAs(formData);
      } else if (res.data == "Name") {
        ElConfirm("亲，此名称已存在，是否继续保存？").then((r: boolean) => {
          if (r) saveAs(formData);
        });
      } else {
        ElNotify({
          type: "warning",
          message: "部门编号重复，请重新编号！",
        });
      }
    });
  }
  const vendorAddRef = ref<InstanceType<typeof AddVendor>>();
  function saveVendor(formData: IVendorAddForm) {
    if (isErp.value) {
      const data = {
        vendCode: formData.vendorCode,
        vendName: formData.vendorName,
        vendType: formData.vendorType,
        vendConcat: formData.vendorManager,
        vendMobile: formData.vendorManagerPhone,
        VendWechartOrQQ: formData.concatWays,
      };
      request({
        url: `/api/ErpAssistingAccounting/AddVendor`,
        method: "post",
        data,
      }).then((res: IResponseModel<string>) => {
        if (res.state === 1000 && res.data) {
          ElNotify({ message: "亲，保存成功啦", type: "success" });
          useAssistingAccountingStore()
            .getAssistingAccounting()
            .then(() => {
              getVendorList("1", data);
              vendorAdd.value = false;
              vendorCode.value = "";
              vendorAddRef.value?.resetFields();
            });
        } else {
          ElNotify({ message: res.msg || "保存失败", type: "warning" });
        }
      });
    } else {
      const data = {
        aaNum: formData.vendorCode,
        aaName: formData.vendorName,
        note: formData.note,
      };
      request({
        url: `/api/AssistingAccounting/Check?aaType=10002`,
        method: "post",
        data,
      }).then((res: IResponseModel<string>) => {
        if (res.data === "N") {
          saveVendorInner(data);
        } else if (res.data == "Name") {
          ElConfirm("亲，此名称已存在，是否继续保存？").then((r: boolean) => {
            if (r) saveVendorInner(data);
          });
        } else {
          ElNotify({
            type: "warning",
            message: "供应商编号重复，请重新编号！",
          });
        }
      });
    }
  }

  function saveVendorInner(formData: any) {
    const entityParams = {
      type: "",
      adLevel0: "",
      adLevel1: "",
      adLevel2: "",
      adLevel3: "",
      address: "",
      contact: "",
      mobilePhone: "",
      taxNumber: "",
      note: formData.note,
    };
    const params = {
      aaNum: formData.aaNum,
      aaName: formData.aaName,
      uscc: "",
      status: 0,
      hasEntity: true,
      ifvoucher: true,
      entity: entityParams,
    };
    // 添加供应商辅助核算
    request({
      url: `/api/AssistingAccounting/Vendor`,
      method: "post",
      data: params,
    }).then(async (res: IResponseModel<string>) => {
      if (res.state === 1000 && res.data) {
        ElNotify({ message: "亲，保存成功啦", type: "success" });
        useAssistingAccountingStore()
          .getAssistingAccounting()
          .then(() => {
            getVendorList("1", formData);
            vendorAdd.value = false;
            vendorCode.value = "";
            vendorAddRef.value?.resetFields();
          });
      } else {
        ElNotify({ message: res.msg || "保存失败", type: "warning" });
      }
    });
  }

  let saveFlag = false;
  let timer = ref(0);
  function savedataClick() {
    if (timer.value) {
      return;
    }

    if (props.activeName === "fixedassetsList" && props.pid < props.iaPid && addForm.faProperty !== 0) {
      ElNotify({
        type: "warning",
        message: "亲，无形资产卡片录入期间小于无形资产启用期间，资产卡片保存失败",
      });
      return;
    }
    if (isAmortization.value) {
      let sumRadio = 0;
      let hasNegativeRatio = false;
      let hasEmptyDepartment = false;
      let hasEmptyAsub = false;
      let hasEmptyRatio = false;
      addForm.amortizations.forEach((item) => {
        if (Number(item.ratio) < 0) hasNegativeRatio = true;
        if (!item.department_id) hasEmptyDepartment = true;
        if (!item.asub_id) hasEmptyAsub = true;
        if (item.department_id && item.asub_id) {
          if (!item.ratio) hasEmptyRatio = true;
          sumRadio += Number(item.ratio);
        }
      });
      if (hasEmptyDepartment) {
        saveFlag = false;
        ElNotify({
          type: "warning",
          message: "部门不能为空，请重新分配哦~",
        });
        return;
      }
      if (hasEmptyAsub) {
        saveFlag = false;
        ElNotify({
          type: "warning",
          message: "费用科目不能为空，请重新分配哦~",
        });
        return;
      }
      if (hasNegativeRatio) {
        saveFlag = false;
        ElNotify({
          type: "warning",
          message: "费用分摊比例不能为负数，请重新分配哦~",
        });
        return;
      }
      if (hasEmptyRatio) {
        saveFlag = false;
        ElNotify({
          type: "warning",
          message: "费用分摊比例不能为空，请重新分配哦~",
        });
        return;
      }
      if (sumRadio !== 100) {
        saveFlag = false;
        ElNotify({
          type: "warning",
          message: "费用分摊比例之和需为100%，请重新分配哦~",
        });
        return;
      }
    }
    savedata();
    timer.value = setTimeout(() => {
      timer.value = 0;
    }, 1000);
  }
  function savedata() {
    if (saveFlag) return;
    saveFlag = true;
    if (!checkAssetsInput(addForm, accountingStandard, isAmortization.value)) {
      saveFlag = false;
      return;
    }
    if (addForm.faProperty === -1) {
      ElNotify({
        type: "warning",
        message: "亲，资产类别不能为空，请重新选择哦~",
      });
      saveFlag = false;
      return;
    }
    useLoading().enterLoading("努力加载中，请稍候...");
    request({
      url: `/api/FixedAssets/CheckNum?fanum=${encodeURIComponent(addForm.faNum)}`,
      method: "post",
    })
      .then((res: IResponseModel<string>) => {
        useLoading().quitLoading();
        if (res.data === "N") {
          SaveAssetData();
        } else {
          if (res.data === "Y1" && props.activeName === "initFixedassets") {
            ElAlert({
              message: "亲，资产编号重复了，编号" + addForm.faNum + "已被资产列表使用,是否继续？",
              modalClass: " ",
            }).then((r) => {
              if (r) {
                SaveAssetData();
              }
            });
          } else {
            ElAlert({
              message: "亲，资产编号重复了，是否继续？",
              modalClass: " ",
            }).then((r) => {
              if (r) {
                SaveAssetData();
              }
            });
          }
        }
      })
      .finally(() => {
        saveFlag = false;
      });
  }

  function SaveAssetData() {
    addForm.createdPeriod = props.pid;
    const savedata: IAddAsset = _.cloneDeep(addForm);
    if (Object.prototype.hasOwnProperty.call(savedata, "department")) {
      delete savedata.department;
    }
    savedata.amortizations = savedata.amortizations.map((item: any) =>
      Object.assign({}, item, { ratio: item.ratio ? item.ratio / 100 : item.ratio })
    );
    let pid = props.initType === "intangibles" ? props.iaPid : props.pid;
    (savedata as any).IsNeedSaveToVoucherForAdd = true;
    (savedata as any).IsNeedSaveToVoucherForDelete = false;
    // 新增资产
    request({
      url:
        props.activeName === "fixedassetsList"
          ? `/api/FixedAssets?pid=${props.pid === -1 ? "" : props.pid}`
          : `/api/FixedAssets/Initial?pid=${pid === -1 ? "" : pid}`,
      method: "post",
      data: savedata,
    }).then((res: IResponseModel<boolean>) => {
      if (res.state == 1000 && res.data) {
        ElNotify({
          type: "success",
          message: "保存成功",
        });
        resetFormdata();
        const container = document.querySelector(".router-container");
        container!.scrollTop = 0;
        emits("divCancel", true, props.initType === "intangibles", pid);
        handleExpiredCheckData(ExpiredCheckModuleEnum.Assets);
      } else {
        ElNotify({
          type: "warning",
          message: res.msg || "保存失败",
        });
      }
    });
  }
  const departmentListRef = ref();
  //重置表格数据
  function resetFormdata() {
    addSlotInit = false;
    addSlotIsEditting.value = false;
    formRefInfo.value.resetFields();
    addForm.department = [departmentList.value[0]?.id];
    isAmortization.value = false;
    // addForm.vendor = vendorList.value[0]?.value;
    formRefDepreciation.value.resetFields();
    formRefAsset.value.resetFields();
    attachFiles.value.length = 0;
    attachParentId = 0;
    addForm.attachsCount = 0;
    addForm.attachFiles = "";
    nextTick(() => {
      departmentListRef.value?.restAll();
    });
  }

  function divCancel() {
    resetFormdata();
    nextTick(() => {
      if (props.activeName === "fixedassetsList") {
        addForm.depreciationNow = 0;
      } else {
        addForm.depreciationNow = 1;
      }
      addForm.remainingMonth = "";
      addForm.estimateResidualsRate = "";
    });
    const container = document.querySelector(".router-container");
    container!.scrollTop = 0;
    emits("divCancel", false, props.initType === "intangibles");
  }

  //资产类别数据
  const faTypeList = ref<ISelectStrItem[]>([]);
  const assetDataList = ref<IFAType[]>([]);
  function getFaTypeApi() {
    // 获取类别列表
    request({
      url: `/api/FixedAssetsType/List`,
      method: "get",
    }).then((res: IResponseModel<IFAType[]>) => {
      if (res.state == 1000) {
        assetDataList.value = res.data;
        faTypeList.value = res.data.reduce((prev: ISelectStrItem[], item: IFAType) => {
          switch (props.initType) {
            case "fixAsset":
              if (item.fa_property === 0) {
                prev.push({
                  value: item.typeId + "",
                  label: item.typeNum + "-" + item.typeName,
                });
              }
              break;
            case "intangibles":
              if ([1, 2].includes(item.fa_property)) {
                prev.push({
                  value: item.typeId + "",
                  label: item.typeNum + "-" + item.typeName,
                });
              }
              break;
            default:
              prev.push({
                value: item.typeId + "",
                label: item.typeNum + "-" + item.typeName,
              });
              break;
          }
          return prev;
        }, []);
      }
    });
  }
  //资产类别不同选择触发的资产数据
  function AssetClassChange(val: string) {
    assetDataList.value.forEach((item: IFAType) => {
      if (val == String(item.typeId)) {
        addForm.netSalvageRate = item.netsalvageRate.toFixed(2);
        addForm.useFullLife = String(item.serviceMonths);
        addForm.depreciationType = String(item.depreciationType);
        if (addForm.faProperty !== item.fa_property) {
          init();
          addForm.faProperty = item.fa_property;
          getFixedAssetsAsubList();
          getDeprecationAsubList();
          getDisposalAsubList();
          getDefaultCostAsub();
          [AccountStandard.CompanyStandard, AccountStandard.FolkComapnyStandard].includes(accountingStandard) &&
            getImpairmentProvisionAsubList();
          if (item.fa_property === 0 && props.activeName === "fixedassetsList") {
            addForm.depreciationNow = 0;
          } else {
            addForm.depreciationNow = 1;
          }
        }
        addForm.createdWay = addForm.faProperty === 2 ? 3 : 1;
      }
    });
  }

  watch(
    () => props.activeName,
    (val) => {
      if (val === "fixedassetsList") {
        addForm.depreciationNow = 0;
      } else {
        addForm.depreciationNow = 1;
      }
    },
    { immediate: true }
  );

  function GetMonthReduce() {
    if (Number(addForm.useFullLife) - Number(addForm.remainingMonth) === 0) {
      addForm.monthDepreciationValue = 0;
    } else {
      if (
        isDecimal(addForm.value) &&
        isDecimalPointLaterTwo(addForm.netSalvageRate) &&
        isInteger(addForm.useFullLife) &&
        isInteger(addForm.usedLife) &&
        isDecimal(addForm.accumlatedDepreciation) &&
        isDecimal(addForm.impairmentProvision + "") &&
        Number(addForm.useFullLife) - Number(addForm.usedLife) != 0
      ) {
        const myzj =
          (Number(addForm.value) * (1 - Number(addForm.netSalvageRate) / 100) -
            Number(addForm.accumlatedDepreciation) -
            Number(addForm.impairmentProvision)) /
          (Number(addForm.useFullLife) - Number(addForm.usedLife));
        addForm.monthDepreciationValue = _.round(myzj, 2); //生成每月折旧额
      } else {
        addForm.monthDepreciationValue = 0;
      }
    }
  }

  //预计残值
  watch([() => addForm.value, () => addForm.netSalvageRate], () => {
    if (isDecimalPointLaterTwo(addForm.netSalvageRate) && isDecimal(addForm.value)) {
      addForm.estimateResidualsRate = _.round((Number(addForm.value) * Number(addForm.netSalvageRate)) / 100, 2) + ""; //生成预计残值
    } else {
      addForm.estimateResidualsRate = "0"; //生成预计残值
    }
    GetMonthReduce();
  });

  //剩余使用月份
  watch([() => addForm.useFullLife, () => addForm.usedLife], () => {
    if (Object.is(Number(addForm.useFullLife) - Number(addForm.usedLife), NaN)) {
      addForm.remainingMonth = "0";
    } else if (addForm.useFullLife === "") {
      addForm.remainingMonth = "0";
    } else {
      addForm.remainingMonth = String(Number(addForm.useFullLife) - Number(addForm.usedLife));
    }
  });
  //以前年度累计折旧
  //  addForm.AccumlatedDepreciation累计折旧 addForm.AccumlatedDepreciationTY本年折旧
  watch(
    () => addForm.accumlatedDepreciation,
    () => {
      if (Object.is(Number(addForm.accumlatedDepreciation) - Number(addForm.accumlatedDepreciationTY), NaN)) {
        addForm.prevAccumulatedDepreciation = "0";
      } else {
        addForm.prevAccumulatedDepreciation = String(Number(addForm.accumlatedDepreciation) - Number(addForm.accumlatedDepreciationTY));
      }
    }
  );
  watch(
    () => addForm.accumlatedDepreciationTY,
    () => {
      if (Object.is(Number(addForm.accumlatedDepreciation) - Number(addForm.accumlatedDepreciationTY), NaN)) {
        addForm.prevAccumulatedDepreciation = "0";
      } else {
        addForm.prevAccumulatedDepreciation = (Number(addForm.accumlatedDepreciation) - Number(addForm.accumlatedDepreciationTY)).toFixed(
          2
        );
      }
    }
  );

  //月折旧额
  watchEffect(() => {
    if (Number(addForm.useFullLife) - Number(addForm.usedLife) == 0) {
      addForm.monthDepreciationValue = 0;
    } else {
      if (
        Object.is(
          (Number(addForm.value) * (1 - Number(addForm.netSalvageRate) / 100) -
            Number(addForm.accumlatedDepreciation) -
            Number(addForm.impairmentProvision)) /
            (Number(addForm.useFullLife) - Number(addForm.usedLife)),
          NaN
        )
      ) {
        addForm.monthDepreciationValue = 0;
      } else {
        addForm.monthDepreciationValue = _.round(
          (Number(addForm.value) * (1 - Number(addForm.netSalvageRate) / 100) -
            Number(addForm.accumlatedDepreciation) -
            Number(addForm.impairmentProvision)) /
            (Number(addForm.useFullLife) - Number(addForm.usedLife)),
          2
        );
      }
    }
  });

  //净值
  watchEffect(() => {
    addForm.netValue = _.round(Number(addForm.value) - Number(addForm.accumlatedDepreciation), 2) + "";
  });

  //部门
  const deleteDepartmentFlag = ref(false);
  const departmentListStore = toRef(useAssistingAccountingStore(), "departmentList");
  const departmentList = ref<Option[]>([]);
  watchEffect(() => {
    departmentList.value = departmentListStore.value.reduce((prev: Option[], item: IDepartment) => {
      if (item.aaeid > 0 && item.aaname !== "未录入辅助核算") {
        prev.push({
          id: item.aaeid,
          name: item.aaname,
        });
      }
      return prev;
    }, []);
    if (deleteDepartmentFlag.value) {
      addForm.department = [departmentList.value[0].id];
      deleteDepartmentFlag.value = false;
    }
  });

  function getDepartmentApi(flag?: string, addDepartmentData?: any) {
    const list = useAssistingAccountingStore().departmentList;
    departmentList.value = list.reduce((prev: Option[], item: IDepartment) => {
      if (item.aaeid > 0 && item.aaname !== "未录入辅助核算") {
        prev.push({
          id: item.aaeid,
          name: item.aaname,
        });
      }
      return prev;
    }, []);
    if (flag && addDepartmentData) {
      const selectDepartment = list.find((item: IDepartment) => {
        return isErp.value ? item.aanum == addDepartmentData.departmentCode : item.aanum == addDepartmentData.aaNum;
      });
      if (isErp.value && addForm.department.length === 1) {
        const rootDepartment = departmentList.value.find((item) => item.id == addForm.department[0]);
        !rootDepartment && (addForm.department = []);
      }
      addForm.department = selectDepartment
        ? [...addForm.department, selectDepartment.aaeid]
        : [...addForm.department, departmentList.value[0].id];
    } else {
      addForm.department = [departmentList.value[0].id];
    }
  }

  //业财环境的供应商
  const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingList");
  const vendorListAll = computed<Array<IAssistingAccount>>(() => {
    return assistingAccountingList.value.reduce((prev: IAssistingAccount[], item: IDepartment) => {
      if (item.aaeid > 0 && item.aaname !== "未录入辅助核算" && item.aatype === 10002) {
        prev.push({ ...item });
      }
      return prev;
    }, []);
  });
  const vendorList = computed(() => {
    return vendorListAll.value
      .filter((item) => item.status === 0)
      .map((item) => {
        return {
          value: isErp.value ? item.aaeid.toString() : item.aaname,
          label: item.aaname,
        };
      });
  });
  function getVendorList(flag?: string, addDepartmentData?: any) {
    const list = assistingAccountingList.value.filter((item) => item.aatype === 10002);
    if (flag && addDepartmentData) {
      const selectVendor = list.find((item: IDepartment) => {
        return item.aanum == addDepartmentData.aaNum;
      });
      let AddVendorVal = vendorList.value[0].value;
      vendorList.value.forEach((item) => {
        if (Number(item.value) > Number(AddVendorVal)) {
          AddVendorVal = item.value;
        }
      });
      if (isErp.value) {
        addForm.vendor = selectVendor ? selectVendor.aaeid.toString() : AddVendorVal;
      } else {
        addForm.vendor = selectVendor ? selectVendor.aaname.toString() : vendorList.value[0].label;
      }
    } else {
      // addForm.vendor = vendorList.value[0]?.value;
    }
  }

  function ratioInput(value: string, index: number) {
    let ratio = value.replace(".", "");
    const regex = /^(100|[1-9][0-9]?)$/;
    addForm.amortizations[index].ratio = ratio.match(regex) ? ratio.match(regex)![0] : "";
  }

  //点击资产科目、累计折旧科目、资产处置科目、资产减值准备科目时，则没有科目可选择
  const faAsubRef = ref<InstanceType<typeof Select>>();
  const deprecationAsubRef = ref<InstanceType<typeof Select>>();
  const disposalAsubRef = ref<InstanceType<typeof Select>>();
  const impairmentProvisionAsubRef = ref<InstanceType<typeof Select>>();
  const costAsubRef = ref<InstanceType<typeof Select>>();
  let isClickAsub = false;
  function clickAsub(asubType: string) {
    if (isClickAsub) return;
    isClickAsub = true;
    if (addForm.faType === "") {
      let timer = setTimeout(() => {
        ElNotify({
          type: "warning",
          message: "请先选择资产类别哦~",
        });
        isClickAsub = false;
        clearTimeout(timer);
      }, 0);
      switch (asubType) {
        case "faAsub":
          faAsubRef.value?.blur();
          break;
        case "deprecationAsub":
          deprecationAsubRef.value?.blur();
          break;
        case "disposalAsub":
          disposalAsubRef.value?.blur();
          break;
        case "impairmentProvisionAsub":
          impairmentProvisionAsubRef.value?.blur();
          break;
      }
    } else {
      isClickAsub = false;
    }
  }

  //科目启用辅助核算
  const auxiliaryDialogShow = ref(false);
  const auxiliaryTitle = ref("");
  const auxiloaryAsubName = ref("");
  const auxiloaryCode = ref<string[]>([]);
  const auxiliaryAllowNull = ref<string[]>([]);
  const clickAsubId = ref();
  const costAsubIndex = ref(0);
  const auxiliaryId = ref();
  function setAsubWithAAE(params: any) {
    switch (auxiliaryTitle.value) {
      case "资产科目":
        addForm.asubAAERelation.fa_aae = params.map((item: any) => item.id).join(",");
        addForm.faAsub = clickAsubId.value;
        break;
      case "累计折旧科目":
      case "累计摊销科目":
        addForm.asubAAERelation.depreciation_aae = params.map((item: any) => item.id).join(",");
        addForm.depreciationAsub = clickAsubId.value;
        break;
      case "资产处置科目":
        addForm.asubAAERelation.disposal_aae = params.map((item: any) => item.id).join(",");
        addForm.disposalAsub = clickAsubId.value;
        break;
      case "资产减值准备科目":
        addForm.asubAAERelation.impairment_provision_aae = params.map((item: any) => item.id).join(",");
        addForm.impairmentProvisionAsub = clickAsubId.value;
        break;
      case "折旧费用科目":
      case "摊销费用科目":
        !isAmortization.value && (addForm.costAsub = clickAsubId.value);
        addForm.amortizations[costAsubIndex.value].asub_aae = params.map((item: any) => item.id).join(",");
        addForm.amortizations[costAsubIndex.value].asub_id = clickAsubId.value;
        break;
    }
  }
  function clickCostAsub(costValue: string, index: number) {
    const asubInfo = costAsubList.value!.find((item) => item.asubId == costValue) as IAccountSubjectList;
    clickAsubOption(
      [-1, 0].includes(addForm.faProperty) ? "折旧费用科目" : "摊销费用科目",
      asubInfo.asubName,
      asubInfo.aatypes,
      asubInfo.asubId,
      index,
      asubInfo.aatypesAllowNull
    );
  }

  function clickAsubOption(title: string, name: string, aaTypes: string, value: any, costIndex?: number, aatypesAllowNull?: string) {
    costAsubIndex.value = costIndex ? costIndex : 0;
    if (!aaTypes) return;
    clickAsubId.value = value;
    auxiliaryTitle.value = title;
    auxiloaryAsubName.value = name;
    auxiloaryCode.value = aaTypes.includes(",")
      ? aaTypes.split(",").map((item) => {
          return item.split("_")[0];
        })
      : [aaTypes.split("_")[0]];
    if (["折旧费用科目", "摊销费用科目"].includes(title)) {
      auxiliaryAllowNull.value = auxiloaryCode.value.map((item) => {
        return aatypesAllowNull?.split(",").includes(item) ? "1" : "0";
      });
    } else {
      auxiliaryAllowNull.value = aaTypes.includes(",")
        ? aaTypes.split(",").map((item) => {
            return item.split("_")[1];
          })
        : [aaTypes.split("_")[1]];
    }
    switch (auxiliaryTitle.value) {
      case "资产科目":
        faAsubRef.value?.blur();
        auxiliaryId.value = addForm.asubAAERelation.fa_aae.split(",");
        break;
      case "累计折旧科目":
      case "累计摊销科目":
        deprecationAsubRef.value?.blur();
        auxiliaryId.value = addForm.asubAAERelation.depreciation_aae.split(",");
        break;
      case "资产处置科目":
        disposalAsubRef.value?.blur();
        auxiliaryId.value = addForm.asubAAERelation.disposal_aae.split(",");
        break;
      case "资产减值准备科目":
        impairmentProvisionAsubRef.value?.blur();
        auxiliaryId.value = addForm.asubAAERelation.impairment_provision_aae.split(",");
        break;
      case "折旧费用科目":
      case "摊销费用科目":
        costAsubRef.value?.blur();
        auxiliaryId.value = addForm.amortizations[costAsubIndex.value].asub_aae
          ? addForm.amortizations[costAsubIndex.value].asub_aae.split(",")
          : [];
        break;
    }
    auxiliaryDialogShow.value = true;
  }
  provide("clickAsubWithtype", clickAsubOption);
  //资产科目
  const faAsubList = ref<IAsubSelect[]>([]);
  function getFixedAssetsAsubList() {
    let asubUrl = "";
    switch (addForm.faProperty) {
      case 0:
        asubUrl = `/api/FixedAssets/GetFixedAssetsAsubList`;
        break;
      case 1:
        asubUrl = `/api/FixedAssets/GetAllLeafIntangibleAsubList`;
        break;
      case 2:
        asubUrl = `/api/FixedAssets/GetAllLeafLongTermDeferredExpensesAsubList`;
        break;
      case undefined:
        return;
    }
    request({
      url: asubUrl,
      method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
      faAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
        prev.push({
          value: item.asubId + "",
          label: item.asubName,
          type: item.aatypes,
        });
        return prev;
      }, []);
      if (res.data[0].aatypes) {
        addForm.faAsub = "";
      } else {
        addForm.faAsub = faAsubList.value[0].value + "";
      }
    });
  }

  //累计折旧科目
  const deprecationAsubList = ref<IAsubSelect[]>([]);
  function getDeprecationAsubList() {
    let depreciationUrl = "";
    switch (addForm.faProperty) {
      case 0:
        depreciationUrl = `/api/FixedAssets/GetDeprecationAsubList`;
        break;
      case 1:
        depreciationUrl = `/api/FixedAssets/GetAllLeafAccumulatedAmortizationAsub`;
        break;
    }
    request({
      url: depreciationUrl,
      method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
      deprecationAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
        prev.push({
          value: String(item.asubId),
          label: item.asubName,
          type: item.aatypes,
        });
        return prev;
      }, []);
      if (res.data[0].aatypes) {
        addForm.depreciationAsub = "";
      } else {
        addForm.depreciationAsub = deprecationAsubList.value[0].value + "";
      }
    });
  }

  //资产处置科目
  const disposalAsubList = ref<IAsubSelect[]>([]);
  function getDisposalAsubList() {
    let disposalUrl = "";
    switch (addForm.faProperty) {
      case 0:
        disposalUrl = `/api/FixedAssets/GetDisposalAsubList`;
        break;
      case 1:
        disposalUrl = `/api/FixedAssets/GetAllLeafIntangibleDisposalAsub`;
        break;
    }
    request({
      url: disposalUrl,
      method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
      disposalAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
        prev.push({
          value: String(item.asubId),
          label: item.asubName,
          type: item.aatypes,
        });
        return prev;
      }, []);
      if (res.data[0].aatypes) {
        addForm.disposalAsub = "";
      } else {
        addForm.disposalAsub = disposalAsubList.value[0].value + "";
      }
    });
  }

  // 获取减值准备科目
  const impairmentProvisionAsubList = ref<IAsubSelect[]>([]);
  function getImpairmentProvisionAsubList() {
    let impairmentProvisionUrl = "";
    switch (addForm.faProperty) {
      case 0:
        impairmentProvisionUrl = `/api/FixedAssets/GetImpairmentProvisionAsubList`;
        break;
      case 1:
        impairmentProvisionUrl = `/api/FixedAssets/GetAllLeafIntangibleImpairmentAsub`;
        break;
    }
    request({
      url: impairmentProvisionUrl,
      method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
      if (res.state !== 1000) {
        return;
      }
      if (res.state === 1000) {
        impairmentProvisionAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
          prev.push({
            value: item.asubId + "",
            label: item.asubName,
            type: item.aatypes,
          });
          return prev;
        }, []);
        if (res.data[0].aatypes) {
          addForm.impairmentProvisionAsub = "";
        } else {
          addForm.impairmentProvisionAsub = impairmentProvisionAsubList.value[0].value + "";
        }
      }
    });
  }

  function changeCostAsub(value: string) {
    addForm.amortizations[0].asub_id = value;
  }

  //折旧费用科目
  const costAsubList = ref<IAccountSubjectList[]>();
  const costAsubAmList = ref<any[]>([]);

  watchEffect(() => {
    const res = getCostAsubList(accountingStandard, addForm.faProperty);
    costAsubList.value = res.costAsubList as IAccountSubjectList[];
    costAsubAmList.value = res.costAsubAmList;
  });

  //默认折旧费用科目
  function getDefaultCostAsub() {
    request({
      url: `/api/FixedAssets/GetDefaultCostAsub?faProperty=${addForm.faProperty === -1 ? 0 : addForm.faProperty}`,
      method: "post",
    }).then((res: IResponseModel<any>) => {
      if (res.state === 1000) {
        if (!isAmortization.value) {
          addForm.costAsub = res.data.assit ? "" : String(res.data.asubId);
          addForm.amortizations[0].asub_id = addForm.costAsub;
        }
      }
    });
  }

  function init() {
    // addForm.faProperty = -1;
    addForm.faAsub = "";
    addForm.depreciationAsub = "";
    addForm.disposalAsub = "";
    addForm.costAsub = "";
    addForm.impairmentProvisionAsub = "";
  }

  //打开开始使用日期默认显示
  function getYearMonthLastDay(dateStr: string) {
    let formattedStr = dateStr.replace("年", "-").replace("月", "");
    const [year, month] = formattedStr.split("-");
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;
    //会计期间大于本月默认定位今天 小于就定位到会计期间月份的最后一天
    if (Number(year) > currentYear || (Number(year) === currentYear && Number(month) >= currentMonth)) {
      return {
        year: currentYear,
        month: currentMonth,
        lastDay: new Date().getDate(),
      };
    } else {
      const nextMonth = new Date(Number(year), Number(month), 1);
      const lastDay = new Date(nextMonth.getTime() - 1);
      return {
        year: lastDay.getFullYear(),
        month: lastDay.getMonth() + 1,
        lastDay: lastDay.getDate(),
      };
    }
  }
  const fixedLastDay: any = ref({}); //资产启用日期当月最后一天的日期
  watch(
    () => props.dateInfo,
    () => {
      if (props.dateInfo) {
        fixedLastDay.value = getYearMonthLastDay(props.dateInfo);
      }
    },
    { immediate: true }
  );

  watch(
    () => props.initType,
    () => {
      getFaTypeApi();
    },
    { immediate: true }
  );
  //折旧方法变成不折旧的时候 录入当期是否折旧单选框为不折旧 切不可点击
  const isdepreciatedRadioFlag = ref<boolean>(false);
  watchEffect(() => {
    isdepreciatedRadioFlag.value = addForm.depreciationType === "0";
  });
  //当折旧方法变成不折旧的时候 录入当期是否折旧的值为否
  watchEffect(() => {
    if (addForm.depreciationType == "0") {
      addForm.depreciationNow = 0;
    }
  });

  //取消或者保存新增资产数据 重置表格数据
  const formRefInfo = ref();
  const formRefDepreciation = ref();
  const formRefAsset = ref();

  //选择开始使用日期计算已折旧月份
  const handleDateChange = (val: string) => {
    if (!val) return;
    let year = ~~props.dateInfo.match(/\d{4}(?=年)/g)![0];
    let month = ~~props.dateInfo.match(/\d{1,2}(?=月)/g)![0];
    let strval = val.split("-");
    if ((year - Number(strval[0])) * 12 + month - Number(strval[1]) - 1 >= 0) {
      addForm.usedLife = String((year - Number(strval[0])) * 12 + month - Number(strval[1]) - 1);
    }
  };

  const myFocus = () => {
    (FaNumRef.value as any).focus();
    request({
      url: "/api/FixedAssets/GetNextFaNum",
      method: "post",
    }).then((res: IResponseModel<string>) => {
      addForm.faNum = res.data;
    });
    addForm.faProperty = -1;
  };
  // 启用日期
  watchEffect(() => {
    dateInfo.value = props.dateInfo;
  });

  function deleteFAType(e: any) {
    if (addForm.faType === e.detail) {
      addForm.faType = "";
    }
  }
  function deleteDepartment(e: any) {
    if (addForm.department === e.detail.toString()) {
      deleteDepartmentFlag.value = true;
    }
  }
  //编辑状态处理
  let addSlotInit = false;
  const addSlotIsEditting = ref(false);
  const getAddSlotIsEditting = () => {
    return addSlotIsEditting.value;
  };
  const changeInit = (val: boolean) => {
    nextTick(() => {
      addSlotInit = val;
    });
  };
  watch(
    addForm,
    () => {
      if (!addSlotInit) return;
      addSlotIsEditting.value = true;
    },
    { deep: true }
  );
  onMounted(() => {
    window.addEventListener("AddTypeFlag", getFaTypeApi);
    window.addEventListener("deleteFAType", (event: any) => deleteFAType(event));
    window.addEventListener("deleteDepartment", (event: any) => deleteDepartment(event));
    getDepartmentApi();
    getVendorList();
    // 进入聚焦资产编号
    myFocus();
  });

  onUnmounted(() => {
    window.removeEventListener("deleteDepartment", deleteDepartment);
    window.removeEventListener("deleteFAType", deleteFAType);
    window.removeEventListener("AddTypeFlag", getFaTypeApi);
  });
  defineExpose({ myFocus, changeInit, getAddSlotIsEditting });

  //搜索无数据时，传入新增弹窗内的字段
  const autoAddName = ref("");
  //模糊搜索
  watch(
    () => addForm.faProperty,
    () => {
      createWayList[1].label = addForm.faProperty === 1 ? "内部研发" : "在建工程转入";
      depreciationTypeList[1].label = [-1, 0].includes(addForm.faProperty) ? "不折旧" : "不摊销";
    },
    {
      immediate: true,
    }
  );
  const showfaTypeList = ref<Array<ISelectStrItem>>([]);
  const showCreateWayList = ref<Array<any>>([]);
  const showVendorList = ref<Array<any>>([]);
  const showDepreciationTypeList = ref<Array<any>>([]);
  const showfaAsubList = ref<Array<IAsubSelect>>([]);
  const showdeAsubList = ref<Array<IAsubSelect>>([]);
  const showdisAsubList = ref<Array<IAsubSelect>>([]);
  const showimpAsubList = ref<Array<IAsubSelect>>([]);
  const costAsubListAll = ref<Array<any>>([]);
  const showcostAsubList = ref<Array<any>>([]);
  const showdepartmentList = ref<Array<Option>>([]);
  const showcostAsubAmList = ref<Array<any>>([]);
  watchEffect(() => {
    showCreateWayList.value = JSON.parse(JSON.stringify(createWayList));
    showDepreciationTypeList.value = JSON.parse(JSON.stringify(depreciationTypeList));
    showfaTypeList.value = JSON.parse(JSON.stringify(faTypeList.value));
    showVendorList.value = JSON.parse(JSON.stringify(vendorList.value));
    showfaAsubList.value = JSON.parse(JSON.stringify(faAsubList.value));
    showdeAsubList.value = JSON.parse(JSON.stringify(deprecationAsubList.value));
    showdisAsubList.value = JSON.parse(JSON.stringify(disposalAsubList.value));
    showimpAsubList.value = JSON.parse(JSON.stringify(impairmentProvisionAsubList.value));
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
    showcostAsubAmList.value = JSON.parse(JSON.stringify(costAsubAmList.value));
  });
  watchEffect(() => {
    costAsubListAll.value = costAsubList.value!.map((item) => {
      return {
        ...item,
        label: item.asubCode + " " + item.asubAAName,
      };
    });
    showcostAsubList.value = JSON.parse(JSON.stringify(costAsubListAll.value));
  });
  function faTypeFilterMethod(value: string) {
    showfaTypeList.value = commonFilterMethod(value, faTypeList.value, "label");
  }
  function createWayFilterMethod(value: string) {
    showCreateWayList.value = commonFilterMethod(value, createWayList, "label");
  }
  function vendorFilterMethod(value: string) {
    showVendorList.value = commonFilterMethod(value, vendorList.value, "label");
    autoAddName.value = showVendorList.value.length === 0 ? value.trim() : "";
  }
  function depreciationTypeFilterMethod(value: string) {
    showDepreciationTypeList.value = commonFilterMethod(value, depreciationTypeList, "label");
  }
  function faAsubFilterMethod(value: string) {
    showfaAsubList.value = commonFilterMethod(value, faAsubList.value, "label");
  }
  function deAsubFilterMethod(value: string) {
    showdeAsubList.value = commonFilterMethod(value, deprecationAsubList.value, "label");
  }
  function disAsubFilterMethod(value: string) {
    showdisAsubList.value = commonFilterMethod(value, disposalAsubList.value, "label");
  }
  function costAsubFilterMethod(value: string) {
    showcostAsubList.value = commonFilterMethod(value, costAsubListAll.value, "label");
  }
  function impAsubFilterMethod(value: string) {
    showimpAsubList.value = commonFilterMethod(value, impairmentProvisionAsubList.value, "label");
  }
  function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value, "name");
  }

  const costAsubVisibleChange = (visible: boolean) => {
    if (visible) {
      showcostAsubAmList.value = JSON.parse(JSON.stringify(costAsubAmList.value));
    }
  };
</script>

<style scoped lang="less">
  :deep(.isRow) {
    & .el-form-item__content {
      padding-top: 0px !important;
      line-height: 31px;
    }
    & .el-date-editor {
      & .el-input__prefix {
        position: absolute;
        right: 10;
      }
    }
  }
  :deep(.el-textarea__inner) {
    position: absolute;
    top: -32px;
    width: 160px;
    z-index: 1000;
  }
  :deep(.el-form-item.is-error .el-input__wrapper) {
    box-shadow: 0 0 0 1px #c0c4cc;
    &.is-focus {
      box-shadow: 0 0 0 1px var(--main-color) inset !important;
    }
  }

  .add-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .divEdit {
      & .block-title {
        padding: 20px 20px 15px 20px;
        color: var(--font-color);
        font-size: var(--h3);
        line-height: 22px;
        font-weight: bold;
        text-align: left;
        & #createtime {
          color: var(--weaker-font-color);
          font-size: var(--font-size);
          line-height: var(--line-height);
          font-weight: normal;
          float: right;
        }
      }
      & .block-main {
        & .isRow {
          padding-left: 30px;
          & :deep(.el-form-item) {
            text-align: left;

            & .el-form-item__label {
              width: 138px;
              padding: 0px;
              justify-content: flex-start;
            }
            &.non-required .el-form-item__label {
              padding-left: 10px;
            }
            & .el-form-item__content {
              width: 160px;
              padding: 0px 25px 5px 0;
            }
            &.apportion .el-form-item__content {
              padding-left: 10px;
            }
          }
          & .FaNumRef7 {
            display: flex;
            flex-wrap: nowrap;
          }
          & :deep(.el-date-editor) {
            & .el-input__prefix {
              position: absolute;
              right: 0;
            }
            & .el-input__suffix {
              position: absolute;
              right: 20px;
            }
          }
        }
      }
      & .buttons {
        margin-top: 20px;
        margin-bottom: 40px;
        text-align: center;
        border: 0;
      }
    }
    .percentsign {
      color: var(--font-color);
    }

    .el-radio.el-radio--large {
      height: 32px;
    }
  }

  .apportionment-table {
    width: 624px;
    text-align: left;
    padding-left: 30px;
    padding-bottom: 30px;
    .apportionment-title {
      padding-bottom: 10px;
    }
    :deep(.custom-table tbody tr td .cell input[type="text"]) {
      border: none;
    }
    :deep(.el-select-v2__placeholder) {
      text-align: left;
      width: calc(100% - 17px);
    }
  }
  :deep(.el-form-item__error) {
    display: none;
  }
  :deep(.el-form-item.fa-name) {
    .el-form-item__label::before {
      content: "*";
      color: var(--el-color-danger);
      margin-right: 4px;
    }
  }

  :deep(.el-select-dropdown__list) {
    max-height: 200px;
  }

  :deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
  }

  :deep(.el-input--small),
  :deep(.el-input) {
    .el-input__wrapper {
      padding: 1px 10px;
    }
    .el-input__inner {
      font-size: var(--el-form-label-font-size);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .label_before::before {
    content: "";
    display: inline-block;
    width: 6.38px;
    margin-right: 4px;
  }

  :deep(.el-scrollbar__bar) {
    &.is-vertical {
      display: block !important;
    }
  }
  // :deep(.el-form-item__label) {
  //     pointer-events: none;
  // }
  .attach-file-center {
    padding-left: 80px;
    width: 100%;
    display: flex;
    box-sizing: border-box;
    .upload-item {
      height: 70px;
      width: 240px;
      border: 1px solid var(--border-color);
      cursor: pointer;
      text-align: left;
      .upload-item-title {
        margin: 10px 0 0 70px;
        img {
          height: 14px;
          width: 14px;
        }
      }
      .upload-item-tip {
        margin: 10px 0 0 60px;
        font-size: 12px;
        color: var(--menu-font-color);
      }
    }
  }
  .line {
    height: 0.5px;
  }
  .cell-flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
