<template>
  <div class="content">
    <ContentSlider
      :slots="slots"
      :currentSlot="currentSlot">
      <template #main>
        <div class="main-content">
          <div class="tabs-header">
            <div
              class="header-right"
              style="display: flex">
              <span>发票税款所属期：{{ formatYearMonthToChinese(invoiceTaxPeriod) }}</span>
              <component
                :is="renderTooltip(invoiceTaxPeriodTip)"
                :key="invoiceTaxPeriodTip" />
            </div>
            <el-tabs
              v-model="currentTabName"
              @tab-change="handleTabChange"
              lazy>
              <el-tab-pane
                v-for="tab in tabs"
                :key="tab.name"
                :label="tab.label"
                :name="tab.name">
                <component
                  v-if="['抵扣勾选', '不抵扣勾选'].includes(tab.label) && currentTabName === tab.name"
                  :is="tab.component"
                  :selType="props.type"
                  :invoiceTaxPeriod="invoiceTaxPeriod"
                  :isActive="currentTabName === tab.name"
                  @check="checkDetails"></component>
                <component
                  v-else-if="currentTabName === tab.name"
                  :is="tab.component"
                  :invoiceTaxPeriod="invoiceTaxPeriod"
                  :isActive="currentTabName === tab.name"></component>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </template>
      <template #invoiceDetails>
        <invoiceDetails @back="currentSlot = 'main'" />
      </template>
    </ContentSlider>
  </div>
</template>

<script setup lang="ts">
  import { useTooltip } from "../../../hooks/useTooltip"
  import { useTaxBureau, TaxBureauLoginType, TaskType } from "@/hooks/useTaxBureau"
  import { DEDUCTION_TABS, NONDEDUCTION_TABS } from "@/views/InvoiceSelection/constants"
  import { request, IResponseModel } from "@/utils/service"
  import { formatYearMonthToChinese } from "@/utils/format"
  import { getAddTaxMonthDeclarationDeadline } from "@/api/taxDeclaration"

  const Selection = defineAsyncComponent(() => import("../components/Selection.vue"))
  const StatConfirm = defineAsyncComponent(() => import("@/views/InvoiceSelection/DeductionSelection/components/StatConfirm.vue"))
  const invoiceDetails = defineAsyncComponent(() => import("../components/InvoiceDetails.vue"))

  const props = defineProps({
    type: {
      type: String as PropType<"deducSelect" | "nonDeducSelect">,
    },
  })

  const componentMap: Record<string, any> = {
    Selection,
    StatConfirm,
  }

  const { renderTooltip } = useTooltip()
  const slots = ["main", "invoiceDetails"]
  const currentSlot = ref("main")
  const route = useRoute()

  const currentTabName = ref<string>((route.query.tabName as string) ?? props.type)

  const specialTabs = ["unprocAgriProd"]

  const handleTabChange = (tab: any) => {
    if (specialTabs.includes(tab)) {
      useTaxBureau().checkLoginState(TaskType.TaxBureau, TaxBureauLoginType.Jump, "dedeuction-type-checked-business")
      nextTick(() => {
        currentTabName.value = "deducSelect"
      })
    }
  }

  const tabs = computed(() =>
    (props.type === "deducSelect" ? DEDUCTION_TABS : NONDEDUCTION_TABS).map((tab) => ({
      ...tab,
      component: tab.component && componentMap[tab.component],
    })),
  )

  const checkDetails = (row: any) => {
    currentSlot.value = "invoiceDetails"
  }
  const invoiceTaxPeriod = ref("")
  // 勾选截止日期
  const invoiceSelectDeadline = ref("")
  function getInvoiceTaxPeriod() {
    return request({
      url: "api/invoice-selection/period",
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000) {
        invoiceTaxPeriod.value = res.data
      }
      return res
    })
  }
  function getDeadline() {
    return getAddTaxMonthDeclarationDeadline().then((res) => {
      if (res.state === 1000) {
        invoiceSelectDeadline.value = res.data.endDate
      }
      return res
    })
  }

  Promise.all([getInvoiceTaxPeriod(), getDeadline()]).catch((error) => {
    console.error("加载数据失败:", error)
  })

  const invoiceTaxPeriodTip = computed(() => {
    if (!invoiceTaxPeriod.value || !invoiceSelectDeadline.value) return ""
    const year = parseInt(invoiceTaxPeriod.value.substring(0, 4))
    const month = invoiceTaxPeriod.value.substring(4, 6)
    return `<span>当前可勾选发票的开票日期范围：${year}-${month}-01至${year}-${month}-${new Date(year, parseInt(month), 0).getDate()}</span></br>
      <span>勾选操作的截止日期：${invoiceSelectDeadline.value.slice(0, 11)}</span>`
  })
</script>
<style scoped lang="scss">
  .tabs-header {
    position: relative;
    height: 100%;

    .header-right {
      position: absolute;
      right: 10px;
      top: 12px;
      z-index: 2;
    }
  }
</style>
