<script setup lang="ts">
  import type { DialogOptions } from "./types"

  const props = withDefaults(
    defineProps<{
      options?: DialogOptions
      onConfirm?: () => void
      onCancel?: () => void
      onClose?: () => void
    }>(),
    {
      options: () => ({ message: "", showCancel: true }),
    },
  )

  const visible = defineModel<boolean>("visible")

  const handleClose = () => {
    visible.value = false
    props.onClose?.()
  }

  const handleConfirm = async () => {
    try {
      await props.onConfirm?.()
    } catch (error) {
      console.error("确认操作失败:", error)
    } finally {
      visible.value = false
    }
  }

  const handleCancel = () => {
    props.onCancel?.()
    visible.value = false
  }
</script>

<template>
  <el-dialog
    class="custom-confirm dialogDrag"
    v-model="visible"
    :title="options.title ?? '提示'"
    :modal-class="options.modal?.class ?? 'modal-class'"
    destroy-on-close
    :width="options.modal?.width ?? '440px'"
    align-center
    :z-index="options.position?.zIndex"
    :close-on-click-modal="options.modal?.closeOnClick"
    :show-close="options.modal?.showClose ?? true"
    :append-to-body="options.mount?.global"
    @close="handleClose">
    <div
      class="dialog-content"
      v-dialogDrag>
      <div class="dialog-content-body">
        <div
          v-if="options.message && typeof options.message === 'string'"
          class="dialog-content-message"
          :class="{ 'text-left': options.position?.align === 'left' }"
          v-html="options.message"></div>
        <component
          v-else-if="options.message && typeof options.message === 'object'"
          :is="options.message"
          class="dialog-content-message"
          :class="{ 'text-left': options.position?.align === 'left' }"></component>
        <slot />
      </div>
    </div>
    <template #footer>
      <el-button
        v-if="options.showCancel"
        @click="handleCancel">
        {{ options.buttons?.cancel ?? "取消" }}
      </el-button>
      <el-button
        type="primary"
        @click="handleConfirm">
        {{ options.buttons?.confirm ?? "确定" }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
  .dialog-content {
    .dialog-content-message {
      margin-bottom: 20px;

      &.text-left {
        text-align: left;
      }
    }
  }
</style>
