<script lang="ts" setup>
import { ref } from "vue";
import type { IConfirmOptions } from "@/util/confirm";

const props = withDefaults(
    defineProps<{
        title?: string;
        hideCancel?: boolean;
        message?: string;
        options?: IConfirmOptions;
        confirm: Function;
        cancel: Function;
        close: Function;
        defaultOpen?: boolean;
        closeAfterConfirm?: boolean;
        leftJustifying?: boolean;
        zIndex?: number;
        closeOnClickModal?: boolean;
        showClose?: boolean;
        isGlobal?: boolean;
        dialogWidth?: string;
        modalClass?: string;
        buttonClass?: string;
        id?: string;
    }>(),
    {
        defaultOpen: true,
        closeAfterConfirm: true,
        leftJustifying: false,
        closeOnClickModal: false,
        showClose: true,
        isGlobal: false,
        dialogWidth: "440",
        modalClass: "modal-class",
        buttonClass: '',
        id:''
    }
);

const visible = ref(props.defaultOpen);
const isErp = ref(window.isErp);

const onConfirmClick = () => {
    props.confirm();
    if (props.closeAfterConfirm) visible.value = false;
};

const onCancelClick = () => {
    props.cancel();
    visible.value = false;
};
const showConfirm = () => {
    visible.value = true;
};
const closeConfirm = () => (visible.value = false);
defineExpose({ showConfirm, closeConfirm });
const handleClose = () => {
    props.close();
    visible.value = false;
};
</script>

<template>
    <el-dialog
        :title="title ?? '提示'"
        :id="id"
        :modal-class="modalClass"
        :draggable="false"
        v-model="visible"
        destroy-on-close
        :width="dialogWidth"
        align-center
        center
        :z-index="zIndex"
        :close-on-click-modal="closeOnClickModal"
        @close="handleClose"
        :show-close="showClose"
        :append-to-body="isGlobal"
        class="dialogDrag"
    >
        <div class="dialog-content" v-dialogDrag>
            <div class="dialog-content-body">
                <div class="dialog-content-message" :style="leftJustifying ? ' text-align: left;' : ''" v-html="message"></div>
                <slot> </slot>
            </div>
            <div class="buttons" :class="{'erp-buttons':isErp,[buttonClass]:true}" >
                <a class="button solid-button mr-20" @click="onConfirmClick">{{ options?.confirmButtonText ?? "确定" }}</a>
                <a class="button" v-if="!hideCancel" @click="onCancelClick">{{ options?.cancelButtonText ?? "取消" }}</a>
            </div>
        </div>
    </el-dialog>
</template>

<style lang="less" scoped>
@import (reference) "@/style/Common.less";
.dialog-content {
    .buttons {
        text-align: center;
        padding: 10px 0;
        border-top: 1px solid var(--border-color);
    }
    .erp-buttons {
        padding: 16px 20px 16px 0;
        text-align: right;
        height: 30px;
        & a {
            float: right;
            margin: 0 10px;
            min-width: 64px !important;
            font-size: 12px;
        }
    }
}
</style>
