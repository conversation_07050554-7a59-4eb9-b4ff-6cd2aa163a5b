<template>
    <div class="slot-title">科目管理</div>
    <div id="divEdit" class="edit-content" style="display: block">
        <div class="error-msg" id="errorMsg">{{ errorMsg }}</div>
        <div class="line-item1">
            <div class="line-item-left">
                <div class="line-item-field">
                    <div id="asubCodeControl" class="disabled">
                        <span></span>
                        <Tooltip :content="state.asubCode" :isInput="true">
                            <el-input
                                ref="asubCodeRef"
                                class="subject-edit-input"
                                :maxlength="lengthLimit"
                                v-model="state.asubCode"
                                :disabled="!isEdit || (isInitialAsubEdit && state.editType === 2) || props.currentRow.operation === 0"
                                @change="asubCodeChange"
                                @input="handleAsubCodeInput"
                                @keydown="handleAsubCodeDowm"
                            ></el-input>
                        </Tooltip>
                    </div>
                </div>
                <div class="line-item-title"><span class="highlight-red">*</span>科目编码：</div>
            </div>
            <div class="line-item-right">
                <div class="line-item-title"><span class="highlight-red">*</span>科目名称：</div>
                <div class="line-item-field">
                    <Tooltip
                        :content="state.asubName"
                        :isInput="true"
                        v-if="checkSixMajor(currentRow.asubCode, accountStandard, firstCodeLength) && state.editType === 1"
                    >
                        <div class="autocomplete-input">
                            <el-autocomplete
                                ref="subjectNameInputRef"
                                v-model="state.asubName"
                                :prop="[{ required: true, trigger: ['change'] }]"
                                :fetch-suggestions="querySearch"
                                :trigger-on-focus="false"
                                placeholder=" "
                                class="subject-edit-input"
                                :fit-input-width="true"
                                :teleported="false"
                                :debounce="500"
                                popper-class="autocomplete-popper"
                                @select="handleSelect"
                            >
                                <template #default="{ item }">
                                    <Tooltip :content="item.value" :line-clamp="2" placement="right" :maxWidth="436" :teleported="true">
                                        <div class="value">{{ item.value }}</div>
                                    </Tooltip>
                                </template>
                            </el-autocomplete>
                        </div>
                    </Tooltip>
                    <div v-else>
                        <el-input
                            v-if="textareaShow"
                            ref="subjectNameInputRef"
                            :autosize="{ minRows: 1.2, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            resize="none"
                            @blur="inputTypeBlur"
                            :disabled="
                                (!isEdit && isInitialAsubEdit) ||
                                (isInitialAsubEdit && state.editType === 2) ||
                                props.currentRow.operation === 0
                            "
                            v-model="state.asubName"
                            class="subject-edit-input"
                            @input="inputLength"
                            @focus="inputTypeFocus"
                        ></el-input>
                        <Tooltip :content="state.asubName" :isInput="true" v-else>
                            <el-input
                                ref="subjectNameInputRef"
                                :disabled="
                                    (!isEdit && isInitialAsubEdit) ||
                                    (isInitialAsubEdit && state.editType === 2) ||
                                    props.currentRow.operation === 0
                                "
                                v-model="state.asubName"
                                @focus="inputTypeFocus"
                                class="subject-edit-input"
                            ></el-input>
                        </Tooltip>
                    </div>
                </div>
            </div>
        </div>
        <div class="line-item1">
            <div class="line-item-left">
                <div class="line-item-field">
                    <el-input v-model="state.parent_asub_name" class="subject-edit-input" disabled></el-input>
                </div>
                <div class="line-item-title"><span class="highlight-red">*</span>上级科目：</div>
            </div>
            <div class="line-item-right">
                <div class="line-item-title">科目类别：</div>
                <div class="line-item-field">
                    <div class="jqtransform jqtransformdone">
                        <el-select
                            v-model="asubTypeCode[state.asubType - 1]"
                            class="subject-edit-input"
                            :teleported="false"
                            :fit-input-width="true"
                            disabled
                        >
                        </el-select>
                    </div>
                </div>
            </div>
        </div>
        <div class="line-item2">
            <div class="line-item-left">
                <div class="line-item-field">
                    <el-radio-group
                        v-model="state.direction"
                        :disabled="
                            !isEdit ||
                            (asubIsUsed && state.editType === 2) ||
                            (isInitialAsubEdit && state.editType === 2) ||
                            props.currentRow.operation === 0
                        "
                    >
                        <el-radio :label="1" size="large">借</el-radio>
                        <el-radio :label="2" size="large">贷</el-radio>
                    </el-radio-group>
                </div>
                <div class="line-item-title">余额方向：</div>
            </div>
            <div class="line-item-right" id="asubStatusControl">
                <div class="line-item-title">科目状态：</div>
                <div class="line-item-field">
                    <el-radio-group
                        v-model="state.status"
                        :disabled="(asubIsUsed && state.editType === 1) || props.currentRow.operation === 0"
                        @change="changeSubStatus"
                    >
                        <el-radio :label="0" size="large">启用</el-radio>
                        <el-radio :label="1" size="large">停用</el-radio>
                    </el-radio-group>
                </div>
            </div>
        </div>
        <div id="trLimit" class="line-item2" v-if="accountStandard === 3 && state.asubType === 8">
            <div class="line-item-left">
                <div class="line-item-field">
                    <el-radio-group v-model="state.restricted" :disabled="restrictedEditUnable || props.currentRow.operation === 0">
                        <el-radio :label="0" size="large">非限定性</el-radio>
                        <el-radio :label="1" size="large">限定性</el-radio>
                    </el-radio-group>
                </div>
                <div class="line-item-title">收入属性：</div>
            </div>
        </div>
        <div class="line-item3" id="trQuality">
            <div class="line-item-title">
                <el-checkbox
                    label="数量核算"
                    v-model="isCheckUnit"
                    :disabled="!isEdit || unitCheckUnable || props.currentRow.operation === 0"
                ></el-checkbox>
            </div>
            <div class="line-item-field" v-show="isCheckUnit">
                <div class="measure-unit-container float-l pl-20">
                    计量单位：
                    <el-select 
                        v-show="isErp" 
                        v-model="state.measureunitId" 
                        :disabled="props.currentRow.operation === 0"
                        :filterable="true"
                        :filter-method="unitFilterMethod"
                    >
                        <el-option 
                            v-for="item in showUnitList" 
                            :key="item.id" 
                            :value="item.id" 
                            :label="item.value"
                        ></el-option>
                    </el-select>
                    <el-input
                        v-show="!isErp"
                        type="text"
                        size="small"
                        v-model="state.measureunit"
                        class="measure-unit"
                        :disabled="props.currentRow.operation === 0"
                        style="width: 60px"
                        @input="checkMeasureUnitLength"
                    />
                </div>
            </div>
        </div>
        <div class="line-item3" style="height: auto">
            <div class="line-item-title" id="trAssist">
                <el-checkbox
                    label="辅助核算"
                    v-model="isCheckAA"
                    :disabled="!isEdit || aaCheckUnable || props.currentRow.operation === 0"
                ></el-checkbox>
            </div>
            <div class="line-item-field aa-types-check-list" v-show="isCheckAA">
                <el-checkbox-group
                    class="float-l pl-20"
                    v-model="aaTypesChecked"
                    :disabled="aaGroupUnable || props.currentRow.operation === 0"
                >
                    <el-checkbox v-for="item in aaTypeList" :key="item.code" :label="item.code">{{ item.name }}</el-checkbox>
                </el-checkbox-group>
            </div>
        </div>
        <div class="line-item3" v-show="isCheckAA && !parentNode.assistingAccounting && checkPermission(['assistingaccount-canview'])">
            <div class="line-item-title">&nbsp;</div>
            <div class="line-item-field float-l">
                <a class="link pl-20" @click="globalWindowOpenPage('/Settings/AssistingAccounting?aaType=10000', '辅助核算')"
                    >设置辅助核算</a
                >
            </div>
        </div>
        <div class="line-item3" v-show="isCheckAA" style="height: auto">
            <div class="line-item-title" id="trAssist" style="line-height: 32px">
                <span>设置非必录项</span>
                <el-tooltip effect="light" content="" placement="right-start" :teleported="false">
                    <template #content>
                        <div style="text-align: left;width:240px">
                            <div>1.设置为非必录后，凭证录入时可以不用录入对应的辅助核算数据即可保存</div>
                            <div>2.非必录可随时勾选或取消勾选</div>
                        </div>
                    </template>
                        <span class="question-icon"></span>
                </el-tooltip>
            </div>
            <div class="line-item-field aa-types-check-list">
                <el-checkbox-group class="float-l pl-20" v-model="aaTypesAllowNullChecked">
                    <el-checkbox
                        v-for="item in aaTypeAllowNullList"
                        :disabled="props.currentRow.operation === 0"
                        :key="item.code"
                        :label="item.code"
                        >{{ item.name }}</el-checkbox
                    >
                </el-checkbox-group>
            </div>
        </div>

        <div class="line-item3" id="fcControls">
            <div class="line-item-title">
                <el-checkbox
                    label="外币核算"
                    v-model="isCheckFC"
                    :disabled="!isEdit || fcCheckUnable || props.currentRow.operation === 0"
                    @change="changeFCStutas"
                ></el-checkbox>
            </div>
            <div class="line-item-field aa-types-check-list pl-20" v-show="isCheckFC">
                <el-checkbox
                    class="float-l pl-20"
                    v-if="state.asubType === 1 || state.asubType === 2"
                    :disabled="fcGroupUnable || props.currentRow.operation === 0"
                    v-model="state.fcadjust"
                    >期末调汇
                    <el-icon class="warning-icon" @click.stop.prevent="ElConfirm('启用期末调汇，期末结转时自动计算汇兑损益！', true)">
                        <WarningFilled /> </el-icon
                ></el-checkbox>
                <el-checkbox
                    v-for="item in fcList"
                    :key="item.id"
                    :label="item.id"
                    :disabled="
                        (fcGroupUnable && item.checked && fcAccountingCheckedOrigin.includes(item.id)) || props.currentRow.operation === 0
                    "
                    v-model="item.checked"
                    >{{ item.name }}</el-checkbox
                >
            </div>
        </div>
        <div class="line-item3" v-show="isCheckFC && !parentNode.foreigncurrency">
            <div class="line-item-title">&nbsp;</div>
            <div class="line-item-field">
                <a id="manageFc" class="link float-l pl-20" @click="globalWindowOpenPage('/Settings/Currency', '币别')">设置外币核算</a>
            </div>
        </div>
        <div class="buttons" id="newBtns" style="margin: 40px 0">
            <a class="button solid-button mr-10" @click="submitHandle">保存</a>
            <a class="button mr-10" v-show="state.editType !== 2" @click="submitHandleAndNew">保存并新增</a
            ><a class="button" @click="addCancel">取消</a>
        </div>
        <div v-show="(asubIsUsed && state.editType === 2) || !isEdit" id="stamp" class="stamp"></div>
    </div>
    <div v-if="selectAuxiliaryVisible">
        <el-dialog 
            :draggable="false" 
            v-model="selectAuxiliaryVisible" 
            title="选择辅助核算" 
            center 
            width="500" 
            class="custom-confirm dialogDrag"
        >
            <div class="account-entry-content" v-dialogDrag>
                <div class="add-account-warn">
                    <img
                        alt=""
                        style="width: 10px; margin-right: 5px; vertical-align: -1px"
                        src="@/assets/Icons/warn-red.png"
                    />注意：科目已被使用，数据将转移到辅助核算明细
                </div>
                <div class="add-account-center">
                    <div class="add-account-content">
                        <div class="add-account-line line1">
                            <div class="add-account-title">科目名称：</div>
                            <div class="add-account-input">
                                <span>{{ state.asubName }}</span>
                            </div>
                        </div>
                        <div class="add-account-line line2">选择数据迁移的辅助核算项目</div>
                        <div id="assistLines" class="add-account-list">
                            <div class="add-account-line" v-for="item in auxiliarySelectList" :key="item.name">
                                <div class="add-account-title">{{ item.name.slice(0, 8) }}：</div>
                                <div class="add-account-input jqtransform">
                                    <Select v-model="item.model">
                                        <ElOption
                                            v-for="option in item.selectList"
                                            :key="option.aaeid"
                                            :value="option.aaeid"
                                            :label="option.aaname === '无' ? '无' : option.aanum + '-' + option.aaname"
                                        ></ElOption>
                                    </Select>
                                </div>
                            </div>
                        </div>
                        <div class="add-account-line last-line">
                            <span class="bottom-tip-icon"> </span>
                            <span>
                                <span class="bottom-tip" v-if="noAuxiliary.aaName">{{
                                    isErp ? "如需添加辅助核算，请到基础资料中设置" : noAuxiliaryList.join("、") + "当前未设置辅助核算项目"
                                }}</span>
                                <span v-else id="assistInfo bottom-tip">{{
                                    isErp ? "如需添加辅助核算，请到基础资料中设置" : "如需设置辅助核算项目"
                                }}</span>
                                <span v-permission="['assistingaccount-canview']" class="" v-show="!isErp">，</span>
                                <a
                                    v-permission="['assistingaccount-canview']"
                                    v-show="!isErp"
                                    class="link"
                                    @click="globalWindowOpenPage(`/Settings/AssistingAccounting`, '辅助核算')"
                                    >请点击前往</a
                                >
                            </span>
                        </div>
                        <p class="tips">
                            <img alt="" style="width: 15px; margin-right: 5px; margin-top: 3px" src="@/assets/Icons/warn-red.png" />
                            {{ accountSetBackupTips() }}
                        </p>
                    </div>
                </div>
                <div v-if="!isErp" class="buttons add-account-btns">
                    <a class="button" @click="selectAuxiliaryVisible = false">取消</a>
                    <a v-show="!noAuxiliary.aaName" class="button solid-button ml-20" @click="handleSubmit">确定</a>
                </div>
                <div v-else class="buttons add-account-btns">
                    <a v-show="!noAuxiliary.aaName" class="button solid-button" @click="handleSubmit">确定</a>
                    <a class="button ml-20" @click="selectAuxiliaryVisible = false">取消</a>
                </div>
            </div>
        </el-dialog>
    </div>
    <div v-if="hasSubjectData">
        <el-dialog 
            :draggable="false" 
            v-model="hasSubjectData" 
            title="提示" 
            center 
            width="500" 
            class="custom-confirm dialogDrag"
        >
            <div class="has-data" v-dialogDrag>
                <div class="has-data-content">
                    <div class="has-data-txt">上级科目已有数据，保存后，将同时新增同名下级科目{{state.asubCode}}{{currentClickAsubAAName}}-{{state.parent_asub_name}}替代，您确定要继续吗？</div>
                    <div class="link mt-10 pl-10" v-if="!isExpandData" @click="isExpandData = true">查看示例</div>
                    <div class="has-data-expand" v-else>
                        <div class="has-data-tip">示例：“库存现金”已有数据时，新增二级科目“微信”，将同时新增“1001001库存现金-库存现金”二级科目，原“1001库存现金”的数据将迁移至“1001001库存现金-库存现金”二级科目。</div>
                        <div class="has-data-mod">
                            <div class="exchange"></div>
                            <div class="mod-table">
                                <div class="mod-tr head">
                                    <div class="mod-td">科目编码</div>
                                    <div class="mod-td">科目名称</div>
                                </div>
                                <div class="mod-tr">
                                    <div class="mod-td">1001</div>
                                    <div class="mod-td">库存现金</div>
                                </div>
                            </div>
                            <div class="mod-table ml-20">
                                <div class="mod-tr head">
                                    <div class="mod-td">科目编码</div>
                                    <div class="mod-td">科目名称</div>
                                </div>
                                <div class="mod-tr">
                                    <div class="mod-td">1001</div>
                                    <div class="mod-td">库存现金</div>
                                </div>
                                <div class="mod-tr">
                                    <div class="mod-td pl-15">1001001</div>
                                    <div class="mod-td pl-15">库存现金</div>
                                </div>
                                <div class="mod-tr">
                                    <div class="mod-td pl-15">1001002</div>
                                    <div class="mod-td pl-15">微信</div>
                                </div>
                            </div>
                        </div> 
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="confimSubject">确定</a>
                    <a class="button ml-20" @click="cancelSubject">取消</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, computed, watch, onMounted, watchEffect, onUnmounted } from "vue";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { WarningFilled } from "@element-plus/icons-vue";
import Select from "@/components/Select/index.vue";
import ElOption from "@/components/Option/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { getGlobalToken } from "@/util/baseInfo";
import type { ICheckedItem, IEditFormData, ITableDataItem, ICurrencyResItem, IAuxiliarySelectItem, IAaTypeListItem } from "../types";
import { useAccountSetStore } from "@/store/modules/accountset";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { globalWindowOpenPage } from "@/util/url";
import { checkSixMajor } from "@/util/subject";
import { nextTick } from "vue";
import Tooltip from "@/components/Tooltip/index.vue";
import type { IAssistingAccountType } from "@/api/assistingAccounting";
import { getCompanyListApi, getCompanyDetailApi, type ICompanyList, type ICompanyInfo } from "@/api/getCompanyList";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { getGlobalLodash } from "@/util/lodash";
import { getCompanyList } from "@/util/getCompanyList";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { checkPermission } from "@/util/permission";
import { textareaBottom } from "@/views/FixedAssets/FixedAssets/utils";
import type { IProcessBack } from "@/views/Settings/Backup/types";
import { accountSetBackupTips } from "@/util/showTips";
import { useThirdPartInfoStore } from "@/store/modules/thirdpart";
import { dangerousOperationNext } from "@/util/autoBackup";
import { commonFilterMethod } from "@/components/Select/utils";
import { getAsubCodeIsUsedBaseItem } from "../utils";
import { useCurrencyStore } from "@/store/modules/currencyList";

const _ = getGlobalLodash();
const accountStandard = useAccountSetStore().accountSet?.accountingStandard ?? 1;
const textareaShow = ref(false);
const isErp = ref(window.isErp);
const subjectNameInputRef = ref();
const props = defineProps({
    currentRow: {
        type: Object,
        default: () => {},
    },
    addTitle: {
        type: String,
        default: "",
    },
    subjectCode: {
        type: String,
        default: "",
    },
    firstCodeLength: {
        type: Number,
        default: 4,
    },
    curTableData: {
        type: Array<ITableDataItem>,
        require: true,
    },
    codeLengthRule: {
        type: Object,
        require: true,
    },
    currentSlot: {
        type: String,
        default: "",
    }
});
const asubTypeCode = ["资产类", "负债类", "共同类", "权益类", "成本类", "损益类", "净资产类", "收入类", "费用类", "支出类"];
const emits = defineEmits(["addCancel", "submitAdd", "updateAllData"]);
// 从'银行存款','库存现金'，'其他货币资金'，'现金'点进来的或他孩子点进来的才有现金流
const aaTypeList = computed(() => {
    let arr = useAssistingAccountingStore().assistingAccountingTypeList.map((item: IAssistingAccountType) => {
        return {
            name: item.aaTypeName,
            code: String(item.aaType),
        };
    });

    if (!(state.asubCode?.startsWith("1001") || state.asubCode.startsWith("1002") || state.asubCode.startsWith("1012"))) {
        return arr.filter((v) => v.name !== "现金流");
    }
    return arr;
});
let aaTypeAllowNullList = ref<Array<ICheckedItem>>([]);
let isCheckAA = ref(false);
let isCheckUnit = ref(false);
let isCheckFC = ref(false);
let fcAccountingChecked = ref<number[]>([]);
let aaTypesAllowNullChecked = ref<string[]>([]);
let aaTypesChecked = ref<string[]>([]);
let state = reactive<IEditFormData>({
    // as_id: accountSetId,
    asubName: "",
    asubCode: "",
    asubCodeBefore: "",
    asubType: 0,
    parentId: 0,
    direction: 1,
    assistingaccounting: 0,
    aaTypes: "",
    allowNullAaTypes: "",
    aaEntries: "",
    quantityaccounting: 0,
    measureunit: "",
    measureunitId: "",
    status: 0,
    fcadjust: false,
    foreigncurrency: 0,
    fcids: "",
    fceditType: 0,
    isUsedAsubAddFC: "",
    editType: 1, //D=(AddRoot,) 1=(Add Child) 2=(Edit) 3=(EditBaselnfo,) 4=(Delete,)= [o,'T,'2:'3,'47.
    isCancelAssit: "",
    restricted: 0,
    uscc: "",
    parent_asub_name: "",
});
const unitList = ref<Array<{ id: string; value: string }>>([]);
request({
    url: "/api/AccountSubject/GetUnitList",
    method: "post",
}).then((res: IResponseModel<Array<{ id: number; value: string }>>) => {
    if (res.state === 1000) {
        unitList.value = res.data.map((item) => {
            return {
                id: item.id.toString(),
                value: item.value,
            };
        });
    }
});
let fcList = ref<ICurrencyResItem[]>();
// 获取币别
// 获取人民币id，用于判断是否存在外币核算
let RMBId = ref(0);
const getCurrencyList = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcList];
    RMBId.value = (useCurrencyStore().fcList as any)?.find((item: ICurrencyResItem) => item.name === "人民币").id || 1;
};
getCurrencyList();
function changeFCStutas(v: any) {
    state.fcadjust = v;
    if (asubIsUsed.value) {
        (fcList.value as ICurrencyResItem[])[0].checked = true;
        fcAccountingChecked.value.push(RMBId.value);
    }
}

const inputTypeBlur = () => {
    textareaShow.value = false;
};
const inputTypeFocus = () => {
    textareaBottom(subjectNameInputRef);
    textareaShow.value = true;
    nextTick(() => {
        getTextareaFocus();
    });
};
const getTextareaFocus = () => {
    subjectNameInputRef.value.focus();
};
let isEdit = ref(true);
const codeLengthRule = computed(() => {
    return props.codeLengthRule;
});
const currentLevel = ref();
let lengthLimit = ref();
const asubLengthArr: any = {
    1: { asubName: "firstAsubLength", codeName: "firstCodeLength", capital: "一" },
    2: { asubName: "secondAsubLength", codeName: "secondCodeLength", capital: "二" },
    3: { asubName: "thirdAsubLength", codeName: "thirdCodeLength", capital: "三" },
    4: { asubName: "forthAsubLength", codeName: "forthCodeLength", capital: "四" },
    5: { capital: "五" },
    6: { capital: "六" },
    7: { capital: "七" },
    8: { capital: "八" },
    9: { capital: "九" },
    10: { capital: "十" },
};
let suggestCode = ref("");

let tableData = computed(() => _.cloneDeep(props.curTableData));
let isInitialAsubEdit = ref(false);
let currentClickAsubAAName = "";
let parentNode = ref<any>({});
let parentFCids = ref("");
let editAsubId = ref(0);
// 收入属性是否不可选
const restrictedEditUnable = ref<boolean>(false);
let ancestors = ref();
let fcAccountingCheckedOrigin = ref<number[]>([]);
const stateRow = ref({});
const stateEditType = ref(0);
const errorMsgElNotify = ref(false);
const initState = async (row: any, editType: number) => {
    stateRow.value = row;
    stateEditType.value = editType;
    resetEditForm();
    parentNode.value = row;
    editAsubId.value = row.asubId;
    currentClickAsubAAName = row.asubAAName;
    state.status = row.status || 0;
    state.foreigncurrency = row.foreigncurrency;
    // state.as_id = row.asubId;
    state.asubName = editType === 2 ? row.asubName : "";
    subjectNameInputRef?.value.suggestions?.splice(0, subjectNameInputRef.value.suggestions.length);
    state.editType = editType;
    state.parent_asub_name =
        editType === 0
            ? "没有上级科目"
            : editType === 1
            ? row.asubName
            : editType === 2 && row.parentId === 0
            ? "没有上级科目"
            : findParentAsub(row)?.asubName;

    if (editType === 2) {
        state.asubCode = row.asubCode;
    } else if (editType === 0) {
        await getMaxAsubCode(0, row.accountCategory);
    } else {
        state.asubCode = setChildAsubCode(row);
    }
    state.asubCodeBefore = row.asubCode;
    state.asubType = row.accountCategory;
    state.parentId = row.parentId2;
    state.direction = row.direction || 1;
    state.assistingaccounting = row.assistingAccounting;

    state.aaTypes = row.aatypes;
    state.allowNullAaTypes = row.aatypesAllowNull;
    state.quantityaccounting = row.quantityAccounting;
    state.measureunit = row.measureUnit;
    state.measureunitId = row.measureUnitId;
    state.fcadjust = Boolean(row.fcAdjust);
    state.fcids = row.fcIds;
    parentFCids.value = row.fcIds;
    fcAccountingChecked.value = row.fcIds ? row.fcIds.split(",").map((item: string) => +item) : [];
    fcAccountingCheckedOrigin.value = _.cloneDeep(fcAccountingChecked.value);
    fcList.value?.map((v: ICurrencyResItem) => {
        v.checked = fcAccountingChecked.value.includes(v.id);
        return v;
    });
    state.fceditType = 0;
    state.restricted = row.restricted || 0;
    state.uscc = row.uscc;
    // 编码长度问题
    // codeLengthRule.value = row.codeLengthRule;
    currentLevel.value = row.currentLevel;
    lengthLimit.value = currentLevelAsubLength(row.currentLevel);
    isCheckAA.value = Boolean(state.assistingaccounting);
    isCheckFC.value = Boolean(state.foreigncurrency);
    isCheckUnit.value = Boolean(state.quantityaccounting);
    aaTypesAllowNullChecked.value = editType === 2 ? (state.allowNullAaTypes ? state.allowNullAaTypes.split(",") : []) : [];
    aaTypesChecked.value = row.aatypes ? row.aatypes.split(",") : [];

    if (editType === 2) {
        // 编辑时判断科目是否有下级科目
        isEdit.value = !tableData.value?.find((item: ITableDataItem) => item.parentId == row.asubId);
        errorMsg.value = !isEdit.value ? "科目含有子级，个别信息不支持修改" : "";
    } else {
        isEdit.value = true;
        errorMsg.value = "";
    }

    if (editType !== 0) {
        checkAusbUsedInVcAndGl(row, editType);
    } else {
        asubIsUsed.value = false;
        errorMsg.value = "";
        isInitialAsubEdit.value = false;
    }
    if (editType !== 2) {
        let timer = setTimeout(() => {
            const focusedElement = document.activeElement as HTMLElement;
            focusedElement?.blur();
            subjectNameInputRef.value.focus();
            clearTimeout(timer);
        }, 1000);
    }
    suggestCode.value = state.asubCode;
    if (accountStandard === 3 && state.asubType === 8) {
        restrictedEditUnable.value =
            (editType === 2 && (!row.isEdit || checkAsubIsInitial(row.asubId) || findParent(row) < 80000 || asubIsUsed.value)) ||
            (editType === 1 && (row.asubLevel === 1 ? false : checkAsubIsInitial(findParent(row) || asubIsUsed.value)));
    }
    nextTick(() => {
        init = true;
    });
};
//编辑状态，浏览器刷新提示
let init = false;
const isEditting = ref(false);
const resetInit = () => {
    init = false;
    isEditting.value = false;
};
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
watch(
    [state, isCheckAA, aaTypesChecked, isCheckUnit, isCheckFC, fcList, aaTypesAllowNullChecked],
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    if (useThirdPartInfoStore().isThirdPart) return;
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});

function findParentAsub(row: ITableDataItem) {
    let parent = tableData.value?.find((v: ITableDataItem) => v.asubId === row.parentId);
    return parent;
}

function findDetailAsub() {
    let parentId = state.parentId;
    if (state.editType === 2) {
        let row = tableData.value?.find((v: ITableDataItem) => v.asubId === state.parentId);
        row && (parentId = row.parentId);
    }
    return {
        parentId,
        itemSame: checkAsubCodeIsUsedBase(state.asubName, "asubName", parentId),
    };
}

function getMaxAsubCode(asubId: number, asubType: number) {
    return request({
        url: "/api/AccountSubject/GetMaxAsubCode",
        method: "post",
        params: { parentId: asubId, asubType },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            state.asubCode = String(Number(res.data) + 1);
        }
    });
}
function inputLength() {
    if (state.asubName.length === 256) {
        ElNotify({ type: "warning", message: "亲，科目名称不能超过256个字哦~" });
    }
}
function setChildAsubCode(row: any): string {
    let childrenArr = state.editType === 0 ? tableData.value : tableData.value?.filter((item: any) => item.parentId == row.asubId) || [];
    let maxChildCode = 1;
    if (childrenArr?.length) {
        maxChildCode = childrenArr.reduce(function (acc: any, obj: any) {
            return obj.asubCode > acc ? obj.asubCode : acc;
        }, childrenArr[0].asubCode);
    }
    if (newlyIncreasedCode.length) {
        let newArr = newlyIncreasedCode.slice();
        newArr.push(String(maxChildCode));
        let numbersAsNumbers = newArr.map(Number);
        maxChildCode = Math.max.apply(null, numbersAsNumbers);
    }
    let countLength = codeLengthRule.value?.codeLength[state.editType === 0 ? 0 : row.asubLevel];
    let nextChildCode = state.editType === 0 ? maxChildCode + 1 : Number(String(maxChildCode).slice(row.asubCode.length)) + 1;
    if (nextChildCode === Math.pow(10, countLength)) {
        if (childrenArr?.length + newlyIncreasedCode.length >= Math.pow(10, countLength) - 1) {
            ElConfirm("科目编码已用尽，请考虑设置辅助核算").then((r) => {
                emits("addCancel", refresh.value);
            });
            return "";
        } else {
            return String(findFistNotUsedCode(row,countLength));
        }
    }

    let countStr = String(nextChildCode).padStart(countLength, "0");
    const code = state.editType === 0 ? countStr : row.asubCode + countStr;
    //产品要加，感觉没必要，不论代码角度还是业务角度
    if(code === undefined){
        ElConfirm("明细科目编码已用尽，请考虑设置辅助核算或修改科目编码规则").then((r) => {
            emits("addCancel", refresh.value);
        });
    }
    return code;
}
function findFistNotUsedCode(row: any,countLength:number) {
    var childsOrigin = tableData.value?.filter((item: any) => item.parentId == row.asubId).map((obj) => obj.asubCode) || [];
    let childs = childsOrigin;
    if (newlyIncreasedCode.length) {
        childs = childsOrigin.concat(newlyIncreasedCode).sort((a, b) => parseInt(a) - parseInt(b));
    }
    for (var i = 0; i < childs.length; i++) {
        //取第一位的后countLength位
        if(i == 0 && childs[i].slice(-countLength) !== String(i+1).padStart(countLength, "0")){
            return childs[i].slice(0,-countLength) + String(i+1).padStart(countLength, "0");
        }
        if (parseInt(childs[i]) - parseInt(childs[i - 1]) > 1) {
            if (newlyIncreasedCode.length && newlyIncreasedCode.includes((parseInt(childs[i]) - 1).toString())) {
                i++;
                continue;
            } else {
                return parseInt(childs[i]) - 1;
            }
        }
    }
}
function findParent(row: ITableDataItem) {
    let index = tableData.value?.findIndex((v: ITableDataItem) => v.asubId === row.asubId) as number;
    let secondParent = (tableData.value as any)
        .slice(0, index + 1)
        .reverse()
        .find((v: ITableDataItem) => v.asubLevel === 2);
    if (!secondParent) {
        return 80000;
    }
    return secondParent.asubId;
}
// 算当前科目级别编码长度
function currentLevelAsubLength(level: number) {
    let length = 0;
    for (let i = 0; i < level; i++) {
        length += codeLengthRule.value?.codeLength[i];
    }
    return length;
}
const queryParams = {
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
};
const querySearch = (queryString: string, cb: any) => {
    if (!state.asubName) {
        return;
    }
    if (state.asubName === queryParams.name) {
        queryParams.isFromDb = false;
        queryParams.name = "";
        queryParams.data = [] as ICompanyInfo[];
    }
    getCompanyList(1030, queryString, cb, queryParams);
};

function handleSelect(val: any) {
    state.asubName = val.value;
    getCompanyDetailApi(1030, val.value);
}
// 科目是否为系统初使科目
function checkAsubIsInitial(asubId: number) {
    return asubId < 80000;
}
// 科目是否被使用,使用后余额方向不可更改
let asubIsUsed = ref(false);
// 数量核算是否不可勾选
let unitCheckUnable = ref(false);
let unitInputUnable = ref(false);
// 辅助核算是否不可勾选
let aaCheckUnable = ref(false);
let aaGroupUnable = ref(false);
// 外币是否不可勾选
let fcCheckUnable = ref(false);
let fcGroupUnable = ref(false);
const editReminder = ref("");
// 检测科目是否被凭证和总账使用
function checkAusbUsedInVcAndGl(row: any, editType: number, isErrorMsgElNotify?: boolean) {
    // 系统初始科目一级不可修改,收入类一级和二级不可修改
    isInitialAsubEdit.value = checkAsubIsInitial(row.asubId) && row.asubLevel === 1;
    request({
        url: "/api/AccountSubject/CheckAusbUsedInVcAndGl",
        method: "post",
        params: { asubId: row.asubId },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000) {
                asubIsUsed.value = res.data;
                // 已被使用，编辑状态下，数量核算、辅助核算，外币已有情况下要禁止,计量单位在凭证优化2.0中允许编辑
                if (res.data) {
                    unitCheckUnable.value = Boolean(row.quantityAccounting) || editType === 1;
                    unitInputUnable.value = Boolean(row.measureUnit);
                    aaCheckUnable.value = Boolean(row.assistingAccounting) || editType === 1;
                    aaGroupUnable.value = Boolean(row.aatypes);
                    fcCheckUnable.value = Boolean(row.foreigncurrency) || editType === 1;
                    fcGroupUnable.value = Boolean(row.fcIds);
                    restrictedEditUnable.value = true;
                    if (isEdit.value && editType === 2) {
                        errorMsg.value =
                            checkAsubIsInitial(row.asubId) && row.asubLevel === 1
                                ? "系统初始科目已被使用，个别信息不支持修改"
                                : "科目已被使用，个别信息不支持修改";
                        editReminder.value= errorMsg.value
                        if(isErrorMsgElNotify&&route.path==='/Settings/AccountSubject'){
                            ElNotify({
                                type: "warning",
                                message: "保存失败！科目已被使用，个别信息不支持修改哦~",
                            });
                            errorMsgElNotify.value = false;
                        }
                    }
                } else {
                    if (editType === 1) {
                        unitCheckUnable.value = Boolean(row.quantityAccounting);
                        unitInputUnable.value = Boolean(row.measureUnit);
                        aaCheckUnable.value = Boolean(row.assistingAccounting);
                        aaGroupUnable.value = Boolean(row.aatypes);
                        fcCheckUnable.value = Boolean(row.foreigncurrency);
                        fcGroupUnable.value = Boolean(row.fcIds);
                        return;
                    }
                    unitCheckUnable.value = false;
                    aaCheckUnable.value = false;
                    fcCheckUnable.value = false;
                }
            }
        })
        .catch(() => {
            if(route.path==='/Settings/AccountSubject'){
                ElNotify({ type: "warning", message: "科目检查失败，请刷新后重试！" });
            }
        }).finally(()=>{
            if(errorMsgElNotify.value&&route.path==='/Settings/AccountSubject'){
                ElNotify({
                    type: "success",
                    message: `${state.editType === 2 ? "编辑" : "保存"}成功`,
                });
                errorMsgElNotify.value = false;
            }
        });
}
let errorMsg = ref("");

const addTitle = ref(props.addTitle);
const subjectCode = ref(props.subjectCode);
let newDataAddBySuperiors = false;
const hasSubjectData = ref(false);
const isExpandData = ref(false);
let saveHasNew = false;
function addSaveTips(hasNew: boolean) {
    hasSubjectData.value = true;
    saveHasNew = hasNew;
    // ElConfirm(
    //     `上级科目已有数据，保存后，将同时新增同名下级科目${state.asubCode}${currentClickAsubAAName}-${state.parent_asub_name}替代，您确定要继续吗？`
    // ).then((r: boolean) => {
    //     if (r) {
    //         newDataAddBySuperiors = true;
    //         handleEditApi(hasNew);
    //     }
    // });
}
function confimSubject() {
    newDataAddBySuperiors = true;
    hasSubjectData.value = false;
    isExpandData.value = false;
    handleEditApi(saveHasNew);
}
function cancelSubject() {
    hasSubjectData.value = false;
    isExpandData.value = false;
}

let selectAuxiliaryVisible = ref(false);
// 选择辅助核算弹框的下拉框列表
let auxiliarySelectList = ref<IAuxiliarySelectItem[]>([]);
let noAuxiliary = ref<any>({});
let noAuxiliaryList: string[] = [];
let submitHasNew = false;

function editSaveTips() {
    if (isCheckUnit.value && parentNode.value.quantityAccounting === 0 && asubIsUsed.value) {
        ElConfirm("本科目已被使用，开启数量核算后，全部凭证和期初数据将显示数量和单价，并默认为0，您确定要继续吗？").then((r) => {
            if (r) {
                checkAASubmit();
            }
            return false;
        });
    } else {
        checkAASubmit();
    }
}
async function checkAASubmit() {
    if (props.currentSlot !== "add") return;
    noAuxiliaryList = [];
    if (isCheckAA.value && parentNode.value.assistingAccounting === 0 && asubIsUsed.value) {
        //没有设置核算条目的情况下，查询是否为有数据科目，如果是，则弹出核算条目设置框
        auxiliarySelectList.value = [];
        noAuxiliary.value = {};
        selectAuxiliaryVisible.value = true;
        for (const item of aaTypesChecked.value.sort()) {
            let obj: any = {};
            obj.name = (aaTypeList.value as Array<any>).find((i: IAaTypeListItem) => i.code === item).name;
            let list = useAssistingAccountingStore().getAssistingAccountingByTypeModel(Number(item));
            if (item === "10007") {
                list = list.filter((v) => v.aaname !== "期初现金余额");
            }
            if (item === "10004") {
                list = await useAssistingAccountingStore().departmentList;
            }
            list = list.filter((v) => v.aaeid > 0);
            obj.selectList = list.length ? list : [{ aaeid: "无", aaname: "无", aanum: " " }];
            if (!list.length) {
                noAuxiliary.value.aaTypeId = item;
                noAuxiliary.value.aaName = obj.name;
                if (!noAuxiliaryList.includes(obj.name)) {
                    noAuxiliaryList.push(obj.name);
                }
            }
            obj.model = obj.selectList[0]?.aaeid || "无";
            obj.code = item;
            auxiliarySelectList.value.push(obj);
        }
        return false;
    }
    checkFcSubmit();
}
function checkFcSubmit() {
    if (isCheckFC.value && parentNode.value.foreigncurrency === 0 && asubIsUsed.value) {
        if (fcAccountingChecked.value.length > 0 && fcAccountingChecked.value.includes(1)) {
            state.isUsedAsubAddFC = "1";
            handleEditApi(submitHasNew);
        } else {
            ElConfirm("本科目已被使用，如需开启外币核算，请同时勾选人民币。").then((r: boolean) => {
                (fcList.value as ICurrencyResItem[])[0].checked = true;
                fcAccountingChecked.value.push(1);
            });
            canTransfer = true;
            return false;
        }
    } else if (parentNode.value.foreigncurrency === 1 && state.fcids !== parentNode.value.fcIds) {
        state.fceditType = 1;

        if (asubIsUsed.value) {
            ElConfirm("科目已发生业务，您确定需要核算新的币别吗？").then((r: boolean) => {
                if (r) {
                    handleEditApi(submitHasNew);
                } else {
                    canTransfer = true;
                    state.fceditType = 0;
                }
            });
            return false;
        } else {
            handleEditApi(submitHasNew);
        }
    } else {
        handleEditApi(submitHasNew);
    }
}
let canTransfer = true;
const handleSubmit = async () => {
    if (!canTransfer) return;
    canTransfer = false;
    const canNext = await dangerousOperationNext();
    if (!canNext) {
        canTransfer = true;
        return;
    }
    submitAssistEntry();
};

function submitAssistEntry() {
    let checkedAssistEntry: number[] = [];
    auxiliarySelectList.value.forEach((item: IAuxiliarySelectItem) => {
        checkedAssistEntry.push(Number(item.model));
    });

    let notEnteredName: string[] = [];
    auxiliarySelectList.value.forEach((v) => {
        if (Number(v.model) && Number(v.model) < 0 && !aaTypesAllowNullChecked.value.includes(v.code)) {
            notEnteredName.push(v.name);
        }
    });
    state.aaEntries = checkedAssistEntry.join(",");
    if (notEnteredName.length) {
        ElNotify({
            type: "warning",
            message: `${notEnteredName.join("、")}未设置非必录，请选择具体的辅助核算哦~`,
        });
        canTransfer = true;
        return;
    }
    selectAuxiliaryVisible.value = false;
    checkFcSubmit();
}

let newlyIncreasedName: string[] = [];
const debounce = (fn: Function, delay: number = 3000, hasNew: boolean = false, immediate = true) => {
    let timer: number | null = null;
    let delayTime = delay;
    if (timer) {
        clearTimeout(timer);
    }
    return function () {
        if (timer) {
            if (errorMsg.value) {
                ElNotify({
                    type: "warning",
                    message: "请先处理错误信息后保存",
                });
            }
            return;
        }
        if (immediate) {
            let bool = !timer;
            timer = setTimeout(() => (timer = null), delayTime);
            return bool && fn(hasNew);
        }
    };
};
const submitHandle = debounce(submitHandleFn, 3000);
const submitHandleAndNew = debounce(submitHandleFn, 3000, true);
const modifyaccountSubject = ref(false);
function submitHandleFn(hasNew: boolean) {
    submitHasNew = hasNew;
    state.assistingaccounting = isCheckAA.value ? 1 : 0;
    state.aaTypes = aaTypesChecked.value.sort().join(",");
    state.allowNullAaTypes = aaTypesAllowNullChecked.value.sort().join(",");
    state.quantityaccounting = isCheckUnit.value ? 1 : 0;
    state.foreigncurrency = isCheckFC.value ? 1 : 0;
    if (!state.asubCode) {
        errorMsg.value = "科目编码不能为空！";
        return false;
    } else if (!Number(state.asubCode) && Number(state.asubCode) !== 0) {
        errorMsg.value = "科目编码只能是数字！";
        return false;
    } else {
        asubCodeChange(state.asubCode);
        if (errorMsg.value) {
            return false;
        }
    }
    if (!state.asubName.trim()) {
        errorMsg.value = "科目名称不能为空！";
        return false;
    } else if (state.asubName.length > 256) {
        ElNotify({
            type: "warning",
            message: "亲，科目名称不能超过256个字哦~",
        });
        return false;
    } else {
        errorMsg.value = "";
    }

    const { itemSame } = findDetailAsub();
    if (
        (itemSame || newlyIncreasedName.includes(state.asubName)) &&
        state.editType !== 2
    ) {
        errorMsg.value = `科目名称在同级下已存在：${itemSame?.asubCode}-${itemSame?.asubName}`;
        return false;
    } else {
        errorMsg.value = "";
    }

    if (isCheckUnit.value && !state.measureunit) {
        errorMsg.value = "请设置数量核算单位名称！";
        return false;
    } else {
        errorMsg.value = "";
    }
    if (isCheckAA.value) {
        if (!state.aaTypes) {
            errorMsg.value = "请设置辅助核算的具体核算项！";
            return false;
        }
    } else {
        state.aaTypes = "";
        state.allowNullAaTypes = "";
        errorMsg.value = "";
    }
    if (isCheckFC.value) {
        fcAccountingChecked.value = fcList.value?.filter((v: ICurrencyResItem) => v.checked).map((v: ICurrencyResItem) => v.id) || [];
        if (!fcAccountingChecked.value.length) {
            errorMsg.value = "请设置外币核算的具体核算项！";
            return false;
        } else if (fcAccountingChecked.value.length === 1 && fcAccountingChecked.value[0] === RMBId.value) {
            errorMsg.value = "不涉及外币，无需设置外币核算";
            return false;
        } else {
            state.fcids = fcAccountingChecked.value.sort((a, b) => Number(a) - Number(b)).join(",");
            errorMsg.value = "";
        }
    } else {
        state.fcids = "";
        errorMsg.value = "";
    }

    if (state.editType === 1 && asubIsUsed.value) {
        addSaveTips(hasNew);
        return false;
    } else if (state.editType === 2 || asubIsUsed.value) {
        editSaveTips();
        return false;
    } else {
        handleEditApi(submitHasNew);
    }
}

function getCharSameSubject(hasNew: boolean, parentId:number) {
    const currentName = state.asubName;
    const currentNameE = currentName.replace("(","（").replace(")","）");
    const itemE = getAsubCodeIsUsedBaseItem(tableData.value || [], currentNameE, parentId);
    if (route.path==='/Settings/AccountSubject' && itemE) {
        let code = itemE ? itemE.asubCode : "";
        let name = itemE ? itemE.asubName : "";
        ElConfirm(`亲，已存在同级科目：${code}-${name}，是否继续保存?`).then((r) => {
            r && handleEditApiNow(hasNew);
        });
    } else {
        handleEditApiNow(hasNew);
    }
}

let newlyIncreasedCode: string[] = [];
let isSaving = ref(false);
let refresh = ref(false);
function handleEditApi(hasNew: boolean) {
    const {parentId, itemSame} = findDetailAsub();
    if (checkSixMajor(props.currentRow.asubCode, accountStandard, props.firstCodeLength) && !itemSame) {
        getCharSameSubject(hasNew, parentId);
    } else {
        handleEditApiNow(hasNew);
    }
}
function handleEditApiNow(hasNew: boolean) {
    let Type = state.editType === 2 ? "" : state.editType === 0 ? "/Root" : "/Child";
    const asubCodeBefore = state.asubCodeBefore ? state.asubCodeBefore : "1001";
    const fcadjust = state.fcadjust ? 1 : 0;
    const asubName = state.asubName.trim();
    const uscc = (state.uscc || "").trim();

    // 同时使用delete删除及obj.key = value赋值 会导致低版本浏览器（搜狗10.0.0.33101）不兼容，会将源对象所有的key全删掉，原因未知
    const template: any = {};
    for (let key in state) {
        key !== "parent_asub_name" && (template[key] = (state as any)[key]);
    }
    const data = { ...template, asubName, uscc, fcadjust, asubCodeBefore };

    if (!isSaving.value) {
        isSaving.value = true;
        request({
            url: `/api/AccountSubject${Type}`,
            method: state.editType === 2 ? "put" : "post",
            data,
            headers: {
                "Content-Type": "application/json",
            },
        })
            .then((res: IResponseModel<number>) => {
                canTransfer = true;
                if (res.state === 1000) {
                    if (editReminder.value === "") {
                        if (stateEditType.value !== 0) {
                            errorMsgElNotify.value = true;
                            checkAusbUsedInVcAndGl(stateRow.value, stateEditType.value, errorMsgElNotify.value);
                        }
                    }else{
                        if(route.path==='/Settings/AccountSubject'){
                            ElNotify({
                                type: "success",
                                message: `${state.editType === 2 ? "编辑" : "保存"}成功`,
                            })
                        }
                    }
                    editReminder.value = "";
                    emits("updateAllData");
                    window.dispatchEvent(new CustomEvent("modifyaccountSubject", { detail: modifyaccountSubject.value }));
                    useAccountSubjectStore().getAccountSubject();
                    if (submitHasNew) {
                        refresh.value = true;
                        newlyIncreasedName.push(state.asubName);
                        newlyIncreasedCode.push(state.asubCode);
                        if (newDataAddBySuperiors) {
                            newlyIncreasedCode.push(String(Number(state.asubCode) + 1));
                        }
                        // state.asubCode = String(Number(newlyIncreasedCode[newlyIncreasedCode.length - 1]) + 1);
                        // suggestCode.value = String(Number(newlyIncreasedCode[newlyIncreasedCode.length - 1]) + 1);
                        state.asubCode = setChildAsubCode(parentNode.value);
                        suggestCode.value = setChildAsubCode(parentNode.value);
                        state.asubName = "";
                        resetEditForm();
                    } else {
                        emits("submitAdd", { subjectCode: subjectCode.value });
                        newlyIncreasedName = [];
                        asubIsUsed.value = false;
                    }
                    newDataAddBySuperiors = false;
                    resetInit();
                } else {
                    const { itemSame } = findDetailAsub();
                    let msg = res.msg === '科目名称在同级下存在重复' ? `科目名称在同级下已存在：${itemSame?.asubCode}-${itemSame?.asubName}` : res.msg;
                    if(route.path==='/Settings/AccountSubject'){
                        ElNotify({ type: "warning", message: msg });
                    }
                    asubIsUsed.value = false;
                    newDataAddBySuperiors = false;
                }
                isSaving.value = false;
            })
            .catch((e) => {
                canTransfer = true;
                isSaving.value = false;
                console.log(e);
            });
    }
}
function addCancel() {
    emits("addCancel", refresh.value);
    newlyIncreasedName = [];
    refresh.value = false;
    let timer = setTimeout(() => {
        resetEditForm();
        clearTimeout(timer);
    }, 0);
}
function resetEditForm() {
    resetInit();
    state.direction = parentNode.value.direction || 1;
    state.status = 0;
    isCheckUnit.value = false;
    isCheckAA.value = false;
    aaTypesChecked.value = [];
    state.aaTypes = "";
    aaTypesAllowNullChecked.value = [];
    state.allowNullAaTypes = "";
    isCheckFC.value = false;
    fcAccountingChecked.value = [];
    state.foreigncurrency = 0;
    state.fcids = "";
    state.fcadjust = 0;
    state.fceditType = 0;
    state.aaEntries = "";
    state.assistingaccounting = 0;
    state.aaTypes = "";
    state.allowNullAaTypes = "";
    state.quantityaccounting = 0;
    state.measureunit = "";
    state.measureunitId = "";
    state.fcadjust = false;
    state.foreigncurrency = 0;
    state.fcids = "";
    state.isUsedAsubAddFC = "";
    state.isCancelAssit = "";
    state.restricted = 0;
    state.uscc = "";
    asubIsUsed.value = false;
    unitCheckUnable.value = false;
    unitInputUnable.value = false;
    aaCheckUnable.value = false;
    aaGroupUnable.value = false;
    fcCheckUnable.value = false;
    fcGroupUnable.value = false;
    fcList.value?.forEach((v) => (v.checked = false));
    newlyIncreasedCode = [];
    newlyIncreasedName = [];
}
watch(aaTypesChecked, (newVal: string[]) => {
    if (newVal.length === 6) {
        newVal.pop();
        ElNotify({
            type: "warning",
            message: "您最多只能设置5个辅助核算类别",
        });
    }
    aaTypeAllowNullList.value = aaTypeList.value.filter((item: ICheckedItem) => newVal.includes(item.code));
});
watch(isCheckUnit, (v) => {
    if (!v) {
        state.measureunit = "";
    }
});
watch(
    () => state.measureunitId,
    () => {
        if (state.measureunitId) {
            state.measureunit = unitList.value.find((item) => item.id === state.measureunitId)?.value || "";
        }
    }
);

defineExpose({
    initState,
    fcList,
});
const asubCodeRef = ref();
const handleAsubCodeDowm = (event: any) => {
    let inputElement = asubCodeRef.value.$refs.input;
    // 获取光标聚焦的位置
    let selectionStart = inputElement.selectionStart;
    let selectionEnd = inputElement.selectionEnd;
    //上级编码长度
    let parentCodeLength = lengthLimit.value - codeLengthRule.value?.codeLength[currentLevel.value - 1];
    //选中文本的长度
    let selectedLength = window.getSelection().toString().length;
    //是否有选中文本
    let isSelectInput = false;
    if (selectedLength > 0) {
        isSelectInput = true;
    }
    if (isSelectInput) {
        if (selectionEnd <= parentCodeLength || selectionStart < parentCodeLength) {
            event.preventDefault();
        }
    } else {
        if (event.keyCode === 8) {
            //退格键
            if (selectionEnd <= parentCodeLength) {
                event.preventDefault();
            }
        }
        if (event.keyCode === 46) {
            //delete键
            if (selectionEnd < parentCodeLength) {
                event.preventDefault();
            }
        }
    }
};
let historyCode: string[] = [];
const handleAsubCodeInput = (entry: string) => {
    historyCode.push(entry);
    if (historyCode[historyCode.length - 1].length < lengthLimit.value - codeLengthRule.value?.codeLength[currentLevel.value - 1]) {
        historyCode.pop();
        state.asubCode = historyCode[historyCode.length - 1];
    }
};
// 检查科目编码是被已经被占用
const checkAsubCodeIsUsedBase = (value: string, field: string, parentId?: number): ITableDataItem | null => {
    // 遍历props.tableData,查找是否 已被占用
    let tableDataList: ITableDataItem[] = tableData.value || [];
    if (field === "asubName") {
        tableDataList = tableDataList.filter((v: ITableDataItem) => v.parentId === parentId);
    }
    let index = tableDataList.findIndex((item: ITableDataItem) => (item as any)[field].trim() === value?.trim());
    return index < 0 ? null : tableDataList[index];
};
const asubCodeChange = (v: string) => {
    if (v.length < lengthLimit.value) {
        errorMsg.value = `${asubLengthArr[currentLevel.value].capital}级科目编码为${
            codeLengthRule.value?.codeLength[currentLevel.value - 1]
        }位`;
        return false;
    } else if ((checkAsubCodeIsUsedBase(String(v), "asubCode") || newlyIncreasedCode.includes(state.asubCode)) && v !== suggestCode.value) {
        errorMsg.value = "科目编码已被占用，建议使用编码:" + suggestCode.value;
        return false;
    } else {
        errorMsg.value = "";
    }
};
const changeSubStatus = (val: number | string | boolean): void => {
    if (val === 1) {
        if ((state.asubName === "库存现金" || state.asubName === "现金") && asubLengthArr[currentLevel.value].capital === "一") {
            ElConfirm("库存现金一级科目停用将影响资金模块使用,不允许停用!").then(() => {
                state.status = 0;
            });
        } else if (state.asubName === "银行存款" && asubLengthArr[currentLevel.value].capital === "一") {
            ElConfirm("银行存款一级科目停用将影响资金模块使用,不允许停用!").then(() => {
                state.status = 0;
            });
        } else {
            ElConfirm("亲，科目停用后不能再在凭证中使用哦，是否确认停用？").then((res: boolean) => {
                if (!res) {
                    state.status = 0;
                }
            });
        }
    }
};
const checkMeasureUnitLength = (value: string) => {
    if (value.length > 20) {
        ElNotify({
            type: "warning",
            message: "亲，计量单位不能超过20个字哦~",
        });
    }
    state.measureunit = value.slice(0, 20);
};
onMounted(() => {
    window.addEventListener("reloadCurrency", getCurrencyList);
    window.addEventListener("refreshAssistingAccountingType", () => {
        checkAASubmit();
        selectAuxiliaryVisible.value = false;
    });
});

onUnmounted(() => {
    window.removeEventListener("reloadCurrency", getCurrencyList);
    window.removeEventListener("refreshAssistingAccountingType", () => {
        checkAASubmit();
        selectAuxiliaryVisible.value = false;
    });
});

const showUnitList = ref<Array<{ id: string; value: string }>>([]);
watchEffect(() => {
    showUnitList.value = JSON.parse(JSON.stringify(unitList.value));
});
function unitFilterMethod(value: string) {
    showUnitList.value = commonFilterMethod(value, unitList.value, 'value');
}
</script>

<style scoped lang="less">
@import "@/style/Functions.less";
.has-data {
    .has-data-content {
        padding: 20px 50px;
    }
    .has-data-txt {
        padding: 0 10px;
        color: #000;
        line-height: 20px;
    }
    .has-data-expand {
        margin-top: 20px;
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
    .has-data-tip {
        font-size: 12px;
        text-align: left;
    }
    .has-data-mod {
        margin-top: 10px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .exchange {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        background: url(@/assets/Settings/exchange.png) center center no-repeat;
        background-size: 20px 28px;
        transform: translate(-50%, 0);
    }
    .mod-table {
        border: 1px solid var(--border-color);
        .mod-tr {
            display: flex;
            height: 36px;
            &.head {
                background-color: var(--table-title-color);
            } 
        }
        .mod-tr + .mod-tr {
            border-top: 1px solid var(--border-color);
        }
        .mod-td {
            width: 80px;
            padding: 0 5px;
            line-height: 36px;
            box-sizing: border-box;
            &.pl-15 {
                padding-left: 15px;
            }
        }
        .mod-td + .mod-td {
            border-left: 1px solid var(--border-color);
        }
    }
}
:deep(.el-checkbox) {
    font-weight: normal;
}
:deep(.el-textarea__inner) {
    z-index: 100;
    padding-top: 8px !important;
}
.autocomplete-input {
    .detail-el-autocomplete(240px);
}
.edit-content {
    width: 1000px !important;
    background-color: var(--white);
    overflow: hidden;
    margin: 0 auto;
    position: relative;
    border: 1px solid var(--slot-title-color);
    margin: 32px 0;
    & .error-msg {
        height: var(--line-height);
        margin-top: 40px;
        padding-left: 130px;
        color: var(--red);
        font-size: var(--font-size);
        line-height: var(--line-height);
        text-align: left;
    }

    & .line-item1 {
        height: 40px;
        margin-top: 14px;
        line-height: 40px;

        & .line-item-left {
            float: left;
            height: 40px;
            width: 50%;

            & .line-item-field {
                width: 268px;
                float: right;
                text-align: left;
                padding-left: 10px;
            }

            & .line-item-title {
                float: right;
                text-align: right;
                color: #333;
                font-size: 14px;
            }
        }

        & .line-item-right {
            float: left;
            height: 40px;
            width: 50%;

            & .line-item-title {
                width: 98px;
                float: left;
                text-align: right;
                line-height: 40px;
                color: #333;
                font-size: 14px;
            }

            & .line-item-field {
                float: left;
                text-align: left;
                padding-left: 10px;
            }
        }
        :deep(.el-input .el-input__inner) {
            font-size: var(--el-form-label-font-size);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .subject-edit-input {
            width: 240px;
            height: 38px;
            :deep(.el-input) {
                height: 38px;
            }
        }
    }

    & .line-item2 {
        height: 20px;
        margin-top: 20px;
        line-height: 20px;

        & .line-item-left {
            float: left;
            height: 20px;
            width: 50%;

            & .line-item-field {
                width: 268px;
                float: right;
                text-align: left;
                padding-left: 10px;
                height: 20px;

                :deep(.el-radio.el-radio--large) {
                    height: 20px;
                }
            }

            & .line-item-title {
                float: right;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 20px;
                text-align: right;
            }
        }

        & .line-item-right {
            float: left;
            height: 20px;
            width: 50%;

            & .line-item-title {
                width: 98px;
                float: left;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 20px;
                text-align: right;
            }

            & .line-item-field {
                float: left;
                text-align: left;
                padding-left: 10px;

                :deep(.el-radio.el-radio--large) {
                    height: 20px;
                }
            }
        }
    }

    & .line-item3 {
        // height: 20px;
        margin-top: 20px;
        display: flex;
        align-items: start;

        & .line-item-title {
            width: 208px;
            padding-right: 14px;
            text-align: right;
            float: left;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .question-icon {
                display: inline-block;
                height: 16px;
                width: 16px;
                background: url("@/assets/Settings/question.png") no-repeat;
                background-size: cover;
                vertical-align: top;
            }
        }
        & .aa-types-check-list {
            text-align: left;
            width: 62%;
            :deep(.el-checkbox-group) {
                display: flex;
                justify-content: start;
                flex-wrap: wrap;
            }
            :deep(.el-checkbox .el-checkbox__input.is-disabled + .el-checkbox__label) {
                color: var(--el-disabled-text-color);
                cursor: not-allowed;
            }
        }
    }

    .stamp {
        top: 247px;
        right: 30px;
        position: absolute;
        height: 80px;
        width: 159px;
        background-image: url(@/assets/Icons/060Settings.png);
        background-position: 0px -40px;
        overflow: hidden;
        background-repeat: no-repeat;
    }
}

.measure-unit-container {
    font-size: var(--h4);
    line-height: 32px;
    color: var(--font-color);

    .detail-el-select(100px, 22px);
}

.warning-icon {
    vertical-align: middle;
    color: rgb(249, 228, 6);
    font-size: 16px;
    padding-left: 5px;
}
.account-entry-content {
    color: var(--font-color);
    font-size: var(--font-size);
    line-height: 35px;
    .add-account-warn {
        text-align: center;
        background-color: #ffedec;
        font-size: 12px;
        color: #f21707;
        line-height: 32px;
    }
    .add-account-center {
        padding: 5px 30px 10px;

        .add-account-content {
            padding-bottom: 10px;
            .add-account-line {
                text-align: left;
                padding: 0 60px 0 90px;
                &.line1,
                &.line2 {
                    line-height: 30px;
                }
                &.last-line {
                    padding: 0;
                    padding-left: 90px;
                    line-height: 24px;
                    display: flex;
                }
                .add-account-title {
                    min-width: 70px;
                    display: inline-block;
                    text-align: right;
                    margin-right: 10px;
                }
                .add-account-input {
                    display: inline-block;
                }
                .bottom-tip-icon {
                    background-image: url(/src/assets/Settings/warnning-yellow.png);
                    background-repeat: no-repeat;
                    background-size: 100%;
                    width: 16px;
                    height: 16px;
                    margin-right: 4px;
                    display: inline-block;
                    position: relative;
                    top: 4px;
                }
                .bottom-tip {
                    color: #808080;
                }
            }

            .add-account-list {
                .add-account-line {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    padding: 6px 0;
                }

                .add-account-title {
                    width: 160px;
                    display: inline-block;
                    text-align: right;
                    margin-right: 10px;
                    line-height: 30px;
                }
            }
            .tips {
                display: flex;
                justify-content: center;
                align-items: flex-start;
                width: 370px;
                margin: 30px auto 0;
                font-size: 14px;
                line-height: 20px;
            }
        }
    }
    .add-account-btns {
        text-align: center;
        padding: 10px 0px;
        border-top: 1px solid var(--border-color);
    }
}
</style>
<style scoped lang="less">
body[erp] {
    .edit-content {
        height: auto !important;
        overflow: visible !important;
    }
    .account-entry-content {
        .add-account-warn {
            width: 80%;
            margin: 8px auto;
            border-radius: 4px;
        }
    }
    .stamp {
        top: 210px;
        right: 36px;
    }
}
</style>
