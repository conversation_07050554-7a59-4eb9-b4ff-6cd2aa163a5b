<template>
    <div class="new-date-picker">
        <el-date-picker
            v-model="newDate"
            type="date"
            placeholder="请选择日期"
            :size="'default'"
            :teleported="false"
            :clearable="false"
            value-format="YYYY-MM-DD"
        />
    </div>
</template>
<script lang="ts" setup>
import { watch, computed } from "vue";

const props = withDefaults(defineProps<{ selectDate: string }>(), {
    selectDate: "",
});

const emits = defineEmits<{
    (e: "update:selectDate", date: string): void;
}>();

const newDate = computed({
    get() {
        return props.selectDate;
    },
    set(value: string) {
        emits("update:selectDate", value);
    },
});

watch(newDate, () => {
    emits("update:selectDate", newDate.value);
});
</script>
<style lang="less" scoped>
@main-color: #44b449;
.new-date-picker {
    :deep(.el-date-editor) {
        width: 132px;
        .el-input__wrapper {
            position: relative;
            &.is-focus {
                border: 0;
                border: 1px solid @main-color;
                box-shadow: 0 0 0 1px @main-color;
            }
            &:hover {
                border: 0;
                border: 1px solid @main-color;
                box-shadow: 0 0 0 1px @main-color;
            }
            .el-input__prefix {
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }
    :deep(.el-date-picker__header) {
        height: 40px;
        position: relative;
        margin: 0;
        padding: 12px;
        border-bottom: 1px solid #e7e7e7;
        .el-date-picker__header-label {
            float: left;
            padding: 0;
            margin-top: 7px;
            margin-right: 20px;
        }
        .el-date-picker__prev-btn {
            position: absolute;
            top: 50%;
            right: 70px;
            transform: translateY(-50%);
            .d-arrow-left {
                display: none;
            }
        }
        .el-date-picker__next-btn {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            .d-arrow-right {
                display: none;
            }
        }
    }
    :deep(.el-picker-panel__content) {
        .today {
            .el-date-table-cell__text {
                color: @main-color;
                &::after {
                    content: "";
                    display: block;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: @main-color;
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
        .current {
            .el-date-table-cell__text {
                background-color: @main-color;
                color: #fff;
            }
            &:hover {
                .el-date-table-cell__text {
                    background-color: @main-color;
                    color: #fff;
                }
            }
        }
        td {
            width: 40px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            .el-date-table-cell__text {
                width: 40px;
                height: 40px;
                line-height: 40px;
            }
            &:hover {
                .el-date-table-cell__text {
                    color: @main-color;
                    background-color: #fff;
                }
            }
        }
    }
}
</style>
