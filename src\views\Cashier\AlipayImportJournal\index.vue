<template>
    <div class="title"></div>
    <div class="content">
        <div class="main-content">
            <div class="main-content-top">
                <img src="@/assets/Cashier/Alipay.png" alt="" />
                <div class="main-top-link">
                    <a class="main-content-change" @click="btnSwitch">更改绑定账号</a>|
                    <a class="link ml-10" @click="btnLogout">解除账号绑定</a>
                </div>
            </div>
            <div class="main-content-center">
                <div class="main-center-time">
                    <span class="label-option">选项：</span>
                    <el-radio-group v-model="importMethod">
                        <el-radio label="month">按月份导入</el-radio>
                        <el-radio label="date">按日期导入</el-radio>
                    </el-radio-group>
                    <div class="right">
                        <div class="month">
                            <el-select v-model="pid" :teleported="false" v-show="importMethod === 'month'">
                                <el-option
                                    v-for="item in periodList"
                                    :key="item.pid"
                                    :value="item.pid + ''"
                                    :label="item.year + '年' + item.sn + '月'"
                                />
                            </el-select>
                        </div>
                        <div class="date" v-show="importMethod === 'date'">
                            <el-date-picker
                                v-model="date"
                                type="date"
                                :clearable="false"
                                :teleported="false"
                                value-format="YYYY-MM-DD"
                                :disabled-date="disabledDateStart"
                            />
                        </div>
                    </div>
                </div>
                <div class="main-center-info">
                    <div>
                        <span class="highlight-red">提示：</span>
                        <span id="tipOne"> 1、账套的启用年月是{{ ass_date }}，请选择账套启用年月及后续的未结账月份的数据导入；</span>
                    </div>
                    <div class="main-info-two">
                        <span>2、请选择以前月份或日期的历史数据导入，暂不支持本月或本日的数据导入。</span>
                    </div>
                </div>
            </div>
            <div class="main-content-buttons">
                <a class="solid-button" @click="btnImportJournal">导入</a>
                <a class="button ml-10" @click="btnCancel">取消</a>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "AlipayImportJournal",
};
</script>
<script setup lang="ts">
import { ref } from "vue";
import { useRoute } from "vue-router";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";

import type { IPeriod } from "@/api/period";
import { getCookie, setCookie } from "@/util/cookie";
import { setTopLocationhref, globalWindowOpenPage, closeCurrentTab, globalWindowOpen, getUrlSearchParams } from "@/util/url";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useLoading } from "@/hooks/useLoading";
import { ElConfirm } from "@/util/confirm";
import { dayjs } from "element-plus";
import { alipayWebsite, alipayAppid } from "@/views/Cashier/CashOrDepositJournal/utils";
import { getAACompanyId, getGlobalToken } from "@/util/baseInfo";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { erpCloseTab } from "@/util/erpUtils";

const pid = ref("");
const date = ref("");
const periodList = ref<IPeriod[]>([]);
const importMethod = ref<"month" | "date">("month");
const route = useRoute();
const cdAccount = (route.query.cdAccount as string) || "";
const jType = (route.query.jType as string) || "";

const accountSetStore = useAccountSetStore();
const ass_date = ref("");
const handleInit = () => {
    periodList.value = useAccountPeriodStore().periodList || [];
    pid.value = periodList.value[0].pid + "";
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1 + "";
    const day = now.getDate() + "";
    const time = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    date.value = time;

    const asStartDate = accountSetStore?.accountSet?.asStartDate as string;
    const ass_year = asStartDate.split("-")[0];
    const ass_month = asStartDate.split("-")[1];
    ass_date.value = ass_year + "." + ass_month;

    const app_auth_code = (route.query.app_auth_code as string) || "";
    if (!app_auth_code) return;
    request({ url: "/api/Journal/GetAlipayCookie?app_auth_code=" + app_auth_code, method: "post" }).then((res: IResponseModel<string>) => {
        if (res.state !== 1000) return;
        const appAuthToken = res.data;
        setCookie("appAuthToken", appAuthToken, "d7");
        const params: any = {
            stay: true,
            cdAccount,
            jType,
            appasid: getGlobalToken(),
        };
        if (window.isAccountingAgent) {
            params.AACompanyId = getAACompanyId();
        }
        const replacePath = "/Cashier/AlipayImportJournal?" + getUrlSearchParams(params);
        useRouterArrayStoreHook().replaceCurrentRouter(replacePath);
    });
};

handleInit();

const btnSwitch = () => {
    setCookie("appAuthToken", "", "d60");
    const alipayParams = "cdAccount=" + cdAccount + "&jType=" + jType;
    const url = window.jFreeH5Host + "/Cashier/AlipayImportJournalRedirectRoute?stay=true&" + alipayParams;
    globalWindowOpen(alipayWebsite + "/oauth2/appToAppAuth.htm?app_id=" + alipayAppid + "&redirect_uri=" + encodeURIComponent(url));
    setTopLocationhref("https://auth.alipay.com/login/logout.htm", true);
};
const btnLogout = () => {
    setCookie("appAuthToken", "", "d60");
    BackToJournal("open");
    setTopLocationhref("https://auth.alipay.com/login/logout.htm", true);
};
const btnCancel = () => {
    BackToJournal("current");
};
let canImport = true;
const btnImportJournal = () => {
    if (!canImport) return;
    canImport = false;
    const appAuthToken = getCookie("appAuthToken");
    if (appAuthToken == undefined || appAuthToken == "") {
        ElNotify({ type: "warning", message: "支付宝授权失败，请授权后重新导入！" });
        canImport = true;
    } else {
        let import_date = "";
        if (importMethod.value === "month") {
            if (!pid.value) {
                ElNotify({ type: "warning", message: "导入月份不能为空！" });
                canImport = true;
                return false;
            } else {
                const periodItem = periodList.value.find((item) => item.pid + "" === pid.value);
                if (periodItem) {
                    import_date = periodItem?.year + "-" + (periodItem?.sn + "").padStart(2, "0");
                } else {
                    ElNotify({ type: "warning", message: "导入月份不能为空！" });
                    canImport = true;
                    return false;
                }
            }
        } else {
            if (!date.value) {
                ElNotify({ type: "warning", message: "导入日期不能为空！" });
                canImport = true;
                return false;
            } else {
                import_date = date.value;
            }
        }
        useLoading().enterLoading("数据导入中，请稍候...");
        const params = {
            appAuthToken: getCookie("appAuthToken"),
            date: import_date,
            jtype: jType,
            cdAccount: cdAccount,
        };
        request({ url: "/api/Journal/AlipayImport", method: "post", data: params })
            .then((res: IResponseModel<string>) => {
                const timer = setTimeout(() => {
                    canImport = true;
                    clearTimeout(timer);
                }, 1000);
                useLoading().quitLoading();
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: res.msg || "导入失败！" });
                    return false;
                }
                const data = res.data;
                if (data == "success") {
                    ElNotify({ type: "success", message: "导入成功了" });
                    setCookie("alipayType", "business", "d7");
                    setTimeout(function () {
                        BackToJournal("import");
                    }, 1000);
                } else if (data == "ieType") {
                    ElNotify({ type: "warning", message: "请先维护好收支类别数据后再导入！" });
                    return false;
                } else if (data == "alipay") {
                    ElNotify({ type: "warning", message: "提取支付宝数据失败，请您稍候重试！" });
                    return false;
                } else if (data == "bill_not_exist") {
                    ElNotify({ type: "warning", message: "您不是商户用户或者当前账单未生成，请前往支付宝商户页面查看详情！" });
                    return false;
                } else if (data == "error_empty") {
                    ElNotify({ type: "warning", message: "您导入的期间账单数据为空，请核对后再导入！" });
                    return false;
                } else if (data == "error_template") {
                    ElNotify({ type: "warning", message: "您导入的账单格式有误，请联系客服处理，谢谢！" });
                    return false;
                } else {
                    ElConfirm("您的支付宝导入异常，请联系客服处理，谢谢！", true);
                    return false;
                }
            })
            .catch(() => {
                const timer = setTimeout(() => {
                    canImport = true;
                    clearTimeout(timer);
                }, 1000);
                useLoading().quitLoading();
                ElConfirm("您的支付宝导入异常，请联系客服处理，谢谢！", true);
            });
    }
};

function BackToJournal(type: "open" | "current" | "import") {
    if (type == "open") {
        const urlPath = jType === "1010" ? "CashJournal" : "DepositJournal";
        const appendParams = window.isAccountingAgent ? "&AACompanyId=" + getAACompanyId() : "";
        let url = `/Cashier/${urlPath}?cdAccount=${cdAccount}&stay=true&appasid=${getGlobalToken()}${appendParams}`;
        if (window.isErp) {
            url = window.erpHost + `/#/Cashier/${urlPath}?cdAccount=${cdAccount}&stay=true`;
        }
        globalWindowOpen(url);
        return;
    } else {
        let url = "";
        let title = "";
        let path = "";
        if (jType == "1010") {
            path = "/Cashier/CashJournal";
            title = "现金日记账";
        } else {
            path = "/Cashier/DepositJournal";
            title = "银行日记账";
        }
        url = path + "?cdAccount=" + cdAccount;
        if (type === "import") {
            if (importMethod.value === "month") {
                const periodItem = periodList.value.find((item) => item.pid + "" === pid.value);
                if (periodItem) {
                    url += "&startDate=" + periodItem?.year + "-" + (periodItem?.sn + "").padStart(2, "0") + "-01";
                }
            } else {
                url += "&startDate=" + date.value;
            }
        }
        if (window.isErp) {
            erpCloseTab(path, title);
        }
        closeCurrentTab();
        setTimeout(() => {
            globalWindowOpenPage(url, title);
        });
    }
}
const asStartDate = useAccountSetStore().accountSet?.asStartDate ?? "";
function disabledDateStart(time: Date) {
    const accountStartDate = dayjs(asStartDate).valueOf();
    return time.getTime() < accountStartDate;
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
.main-content {
    font-size: var(--font-size);
    text-align: left;
}
.highlight-red {
    color: var(--red) !important;
}
.main-content-top {
    padding: 33px 0px 30px 59px;
    border-bottom: 1px solid var(--border-color);
    height: 40px;
    line-height: 40px;
    .main-top-link {
        display: inline-block;
        vertical-align: 12px;
        .main-content-change {
            outline: none;
            cursor: pointer;
            text-decoration: none;
            color: var(--link-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            margin-left: 30px;
            margin-right: 10px;
        }
    }
}
.main-content-center {
    .main-center-time {
        margin-top: 40px;
        margin-left: 360px;
        position: relative;
        font-family: 微软雅黑 !important;
        color: Gray;
        span.label-option {
            display: inline-flex;
            justify-content: flex-end;
            align-items: center;
            width: 50px;
            height: 30px;
            position: absolute;
            top: 0;
            left: -50px;
        }
        :deep(.el-radio-group) {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            height: 74px;
            justify-content: space-between;
        }
        .right {
            position: absolute;
            left: 120px;
            top: 0;
            height: 74px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
            & > div {
                height: 32px;
                display: flex;
                align-items: center;
                &.month {
                    .detail-el-select(132px, 30px);
                }
            }
        }
    }
    .main-center-info {
        margin-top: 60px;
        margin-left: 209px;
        color: gray;
        .main-info-two {
            margin-left: 46px;
            margin-top: 2px;
        }
    }
}
.main-content-buttons {
    text-align: center;
    margin-top: 40px;
    padding-bottom: 60px;
}
</style>
