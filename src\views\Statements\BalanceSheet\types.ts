export interface IBalanceSheet {
    statementID: number;
    lineID: number;
    assertLineType: number;
    pid: number;
    expand: number;
    fold: number;
    sortLineID: number;
    assetProName: string;
    assetNumber: number;
    assetTotal: string;
    assertInital: string;
    assetNote: string;
    parentID: number;
    debtsLineID: number;
    debitsLineType: number;
    debtsProName: string;
    debtsNumber: number;
    debtsTotal: string;
    debtsInital: string;
    debtsNote: string;
    debtsParentId: string;
    children?: IBalanceSheet[];
}

export interface IPeriod {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
}
