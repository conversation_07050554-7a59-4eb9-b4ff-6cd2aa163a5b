<template>
    <div class="slot-content">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <span class="highlight-red">*</span>
                <span>角色名称：</span>
                <el-input type="text" v-model="roleName" placeholder="请输入角色名称" :disabled="isDisabled" />
            </div>
            <div class="main-tool-right">
                <a class="button solid-button" @click="saveRoleInfo">保存</a>
                <a class="button ml-10" @click="handleCancel">取消</a>
            </div>
        </div>
        <div class="roleinfo-functions">
            <div class="root-function" v-for="rootItem in menuFunctionList" :key="rootItem.functionId">
                <div class="root-function-toolbar" @click="judgeShowOrHide">
                    <div class="root-function-title">{{ rootItem.functionName }}</div>
                    <div class="root-function-btn">
                        <div class="hide">收起</div>
                        <div class="show">展开</div>
                        <b></b>
                    </div>
                </div>
                <div class="root-function-content">
                    <div class="root-function-left">
                        <el-checkbox
                            v-model="checkList[rootItem.functionId - 1]"
                            :true-label="1"
                            :false-label="0"
                            @change="(checked) => handleRootChange(checked, rootItem.children)"
                        >
                            全选
                        </el-checkbox>
                    </div>
                    <div class="root-function-right">
                        <div class="page-function" v-for="parentItem in rootItem.children" :key="parentItem.functionId">
                            <div class="page-function-left">
                                <el-checkbox
                                    v-model="checkList[parentItem.functionId - 1]"
                                    :true-label="1"
                                    :false-label="0"
                                    :indeterminate="JudgeIsIndeterminate(parentItem.functionId)"
                                    @change="
                                        (checked) =>
                                            handleParentChange(checked, parentItem.children, parentItem.parentId, parentItem.functionId)
                                    "
                                >
                                    {{ parentItem.functionName }}
                                </el-checkbox>
                            </div>
                            <div class="page-function-right">
                                <el-checkbox
                                    v-for="item in parentItem.children"
                                    :key="item.functionId"
                                    v-model="checkList[item.functionId - 1]"
                                    :true-label="1"
                                    :false-label="0"
                                    @change="
                                        (checked) =>
                                            handleChildrenChange(
                                                checked,
                                                item.functionId,
                                                item.parentId,
                                                parentItem.parentId,
                                                item.functionId
                                            )
                                    "
                                >
                                    {{ item.functionName }}
                                </el-checkbox>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElNotify } from "@/util/notify";
import { request } from "@/util/service";
import { getUrlSearchParams } from "@/util/url";
import { MenuFunction, processMenuFunctions, findFunctionById } from "../utils";

import type { IMenuFunction } from "../types";

const props = defineProps<{ menuFunctionList: MenuFunction[] }>();

const menuFunctionList = computed(() => {
    return props.menuFunctionList;
});

const checkList = ref<number[]>([]);
const roleName = ref("");
const roleId = ref("");
const isDisabled = ref(false);
let EditType: "New" | "Edit" = "New";

const emit = defineEmits(["cancelRoleInfo", "saveRoleInfo","formChanged"]);

const saveRoleInfo = () => {
    if (roleName.value.trim() === "") {
        ElNotify({ type: "warning", message: "请输入角色名称" });
        return;
    }
    if (roleName.value.trim().length > 20) {
        ElNotify({ type: "warning", message: "角色名称不能超过20个字符" });
        return;
    }
    if (checkList.value.every((item) => item !== 1)) {
        ElNotify({ type: "warning", message: "请设置角色权限再保存" });
        return;
    }
    const functionsStase = checkList.value.reduce((obj: any, number, index) => {
        obj[index + 1] = number;
        return obj;
    }, {});
    const params: any = {
        roleName: roleName.value,
    };
    if (EditType === "Edit") params.roleId = roleId.value;
    request({
        url: "/api/PermissionsRole?" + getUrlSearchParams(params),
        method: EditType === "New" ? "post" : "put",
        data: JSON.stringify(functionsStase),
        headers: { "Content-Type": "application/json" },
    }).then((r: any) => {
        if (r.state === 1000 && r.data) {
            ElNotify({ type: "success", message: "保存成功" });
            isDisabled.value = false;
            emit("saveRoleInfo");
        } else if (r.state === 2000) {
            ElNotify({ type: "warning", message: r.msg });
        } else {
            ElNotify({ type: "warning", message: r.msg || "保存失败" });
        }
    });
};
const handleCancel = () => {
    isDisabled.value = false;
    checkList.value = [];
    emit("cancelRoleInfo");
};
const judgeShowOrHide = (e: Event) => {
    const target = e.target as HTMLDivElement;
    const root = target.closest(".root-function") as HTMLDivElement;
    root.classList.contains("hidden") ? root.classList.remove("hidden") : root.classList.add("hidden");
};
const handleRootChange = (check: any, children: IMenuFunction[]) => {
    const cloneCheckList = getCloneCheckList();
    const functionIds = getAllFunctionIds(children);
    functionIds.forEach((item) => (cloneCheckList[item - 1] = check ? 1 : 0));
    checkList.value = cloneCheckList;
};
const handleParentChange = (check: any, children: IMenuFunction[], rootId: number, functionId: number) => {
    // 如果是半选状态，直接直接转选中
    if (!check && JudgeIsIndeterminate(functionId)) {
        handleParentChange(!check, children, rootId, functionId);
        return;
    }
    const cloneCheckList = getCloneCheckList();
    const functionIds = getAllFunctionIds(children);
    functionIds.forEach((item) => (cloneCheckList[item - 1] = check ? 1 : 0));
    // 拿到根节点下的所有子节点
    const rootChildren = getAllFunctionIds(menuFunctionList.value.find((item) => item.functionId === rootId)?.children || []);

    if (check) {
        if (functionId == 159) {
            //资产卡片 => 资产初始化-查看
            const item = findFunctionById(menuFunctionList.value, 153);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (functionId == 185) {
            //总账对账差异明细 => 资产核对总账-查看
            const item = findFunctionById(menuFunctionList.value, 182);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (functionId == 69) {
            //票据询价贴现 => 票据管理-查看
            const item = findFunctionById(menuFunctionList.value, 61);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (functionId == 303) {
            //辅助核算 => 辅助核算类别-查看
            const item = findFunctionById(menuFunctionList.value, 310);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (functionId == 345) {
            //总账对账差异明细 => 核对总账-查看
            const item = findFunctionById(menuFunctionList.value, 79);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (functionId == 397) {
            //一键取票=> 风险分析
            riskSet();
        } else if (functionId == 399) {
            //发票风险分析 => 一键取票 
            const item = findFunctionById(menuFunctionList.value, 397);
            const item1 = findFunctionById(menuFunctionList.value, 398);
            item && (cloneCheckList[item.functionId - 1] = 1);
            item1 && (cloneCheckList[item1.functionId - 1] = 1);
        }
    } else {
        if (children && children.length > 0) {
            if (functionId == 152) {
                //资产卡片 => 资产初始化-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 159);
                const item = findFunctionById(menuFunctionList.value, 153);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (functionId == 181) {
                //总账对账差异明细 => 资产核对总账-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 185);
                const item = findFunctionById(menuFunctionList.value, 182);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (functionId == 60) {
                //票据询价贴现 => 票据管理-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 69);
                const item = findFunctionById(menuFunctionList.value, 61);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (functionId == 309) {
                //辅助核算 => 辅助核算类别-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 303);
                const item = findFunctionById(menuFunctionList.value, 310);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (functionId == 78) {
                //总账对账差异明细 => 核对总账-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 345);
                const item = findFunctionById(menuFunctionList.value, 79);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (functionId == 397) {
                //取消一键取票编辑权限=》发票风险编辑权限也取消
                const item = findFunctionById(menuFunctionList.value, 401);
                item && (cloneCheckList[item.functionId - 1] =  0);
            }
        }
    }

    function riskSet() {
        const item4 = findFunctionById(menuFunctionList.value, 399);
        if (item4) {
            cloneCheckList[item4.functionId - 1] = check ? 1 : 0;
            const functionIds = getAllFunctionIds(item4.children);
            functionIds.forEach((item) => (cloneCheckList[item - 1] = check ? 1 : 0));
        }
    }
    cloneCheckList[functionId - 1] = check ? 1 : 0;
    // 判断根节点下的所有子节点是否全部选中
    const isAllChecked = rootChildren.every((item) => cloneCheckList[item - 1] === 1);
    // 根节点选中状态同步
    cloneCheckList[rootId - 1] = isAllChecked ? 1 : 0;

    checkList.value = cloneCheckList;
};
const handleChildrenChange = (check: any, id: number, parentId: number, rootId: number, functionId: number) => {
    const clickItem = findChild(menuFunctionList.value, (item) => item.functionId === id) as IMenuFunction;
    const cloneCheckList = getCloneCheckList();
    // 判断父节点的选中状态
    const parentFunction = findChild(menuFunctionList.value, (item) => item.functionId === parentId) as IMenuFunction;
    const parentFunctionChildren = getAllFunctionIds(parentFunction?.children || []);

    if (check) {
        const brotherList = parentFunction.children;
        const viewItem = brotherList.find((item) => item.functionCode === "canview") as IMenuFunction;
        if (viewItem) cloneCheckList[viewItem.functionId - 1] = 1;

        // 用functionId
        if (clickItem.functionCode === "canimport") {
            // 选中的是导入，那么就把编辑权限也选中
            const editItem = brotherList.find((item) => item.functionCode === "canedit") as IMenuFunction;
            if (editItem) {
                cloneCheckList[editItem.functionId - 1] = 1;
            }
        }

        if (clickItem.parentId == 159) {
            //资产卡片 => 资产初始化-查看
            const item = findFunctionById(menuFunctionList.value, 153);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (clickItem.parentId == 185) {
            //总账对账差异明细 => 资产核对总账-查看
            const item = findFunctionById(menuFunctionList.value, 182);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (clickItem.parentId == 69) {
            //票据询价贴现 => 票据管理-查看
            const item = findFunctionById(menuFunctionList.value, 61);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (clickItem.parentId == 303) {
            //辅助核算 => 辅助核算类别-查看
            const item = findFunctionById(menuFunctionList.value, 310);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (clickItem.parentId == 345) {
            //总账对账差异明细 => 核对总账-查看
            const item = findFunctionById(menuFunctionList.value, 79);
            if (item) {
                cloneCheckList[item.functionId - 1] = 1;
            }
        } else if (clickItem.parentId == 397) {
            //一键取票=> + 风险分析
            riskSet();
        }else if (clickItem.functionId == 401) {
            //发票风险分析编辑 => 一键取票 
            const item = findFunctionById(menuFunctionList.value, 397);
            const item1 = findFunctionById(menuFunctionList.value, 398);
            item && (cloneCheckList[item.functionId - 1] = 1);
            item1 && (cloneCheckList[item1.functionId - 1] = 1);
        }
    } else {
        if (clickItem.functionCode === "canview") {
            // 点击的是查看权限
            // 如果存在其他权限选中，那么不允许取消查看权限
            const brotherList = parentFunction.children;
            const otherFunctionIdList = brotherList.filter((item) => item.functionCode !== "canview").map((item) => item.functionId);
            const isOtherChecked = otherFunctionIdList.some((item) => cloneCheckList[item - 1] === 1);
            if (isOtherChecked) {
                cloneCheckList[id - 1] = 1;
                return;
            }

            if (clickItem.functionId == 153) {
                //资产卡片 => 资产初始化-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 159);
                const item = findFunctionById(menuFunctionList.value, 153);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (clickItem.functionId == 182) {
                //总账对账差异明细 => 资产核对总账-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 185);
                const item = findFunctionById(menuFunctionList.value, 182);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (clickItem.functionId == 61) {
                //票据询价贴现 => 票据管理-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 69);
                const item = findFunctionById(menuFunctionList.value, 61);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (clickItem.functionId == 310) {
                //辅助核算 => 辅助核算类别-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 303);
                const item = findFunctionById(menuFunctionList.value, 310);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            } else if (clickItem.functionId == 79) {
                //总账对账差异明细 => 核对总账-查看
                const destParentItem = findFunctionById(menuFunctionList.value, 345);
                const item = findFunctionById(menuFunctionList.value, 79);
                if (destParentItem && item) {
                    cloneCheckList[item.functionId - 1] = cloneCheckList[destParentItem.functionId - 1];
                }
            }
        } else if (clickItem.functionCode === "canedit") {
            // 点击的是编辑权限
            // 如果存在导入权限选中，那么不允许取消编辑权限
            const brotherList = parentFunction.children;
            const importItem = brotherList.find((item) => item.functionCode === "canimport") as IMenuFunction;
            const isImportChecked = importItem && cloneCheckList[importItem.functionId - 1] === 1;
            if (isImportChecked) {
                cloneCheckList[id - 1] = 1;
                return;
            }

            if (clickItem.functionId == 398) {
                //取消一键取票编辑权限=》发票风险编辑权限也取消
                const item = findFunctionById(menuFunctionList.value, 401);
                item && (cloneCheckList[item.functionId - 1] =  0);
                const item1 = findFunctionById(menuFunctionList.value, 389);
                const item2 = findFunctionById(menuFunctionList.value, 390);
                const item3 = findFunctionById(menuFunctionList.value, 391);
                item1 && (cloneCheckList[item1.functionId - 1] =  0);
                item2 && (cloneCheckList[item2.functionId - 1] =  0);
                item3 && (cloneCheckList[item3.functionId - 1] =  0);

            }
        }
    }

    function riskSet() {
        const item4 = findFunctionById(menuFunctionList.value, 399);
        if (item4) {
            cloneCheckList[item4.functionId - 1] = check ? 1 : 0;
            const functionIds = getAllFunctionIds(item4.children);
            functionIds.forEach((item) => (cloneCheckList[item - 1] = check ? 1 : 0));
        }
    }

    // 判断父节点选中状态同步
    const isHasChecked = parentFunctionChildren.some((item) => cloneCheckList[item - 1] && cloneCheckList[item - 1] === 1);
    cloneCheckList[parentId - 1] = isHasChecked ? 1 : 0;

    // 判断根节点的选中状态
    const rootFunction = menuFunctionList.value.find((item) => item.functionId === rootId);
    const rootChildren = getAllFunctionIds(rootFunction?.children || []);
    const isRootAllChecked = rootChildren.every((item) => cloneCheckList[item - 1] && cloneCheckList[item - 1] === 1);
    cloneCheckList[rootId - 1] = isRootAllChecked ? 1 : 0;

    checkList.value = cloneCheckList;
};
const setEditType = (val: "New" | "Edit") => (EditType = val);
const handleInit = (roleid: string, rolename: string, functionState?: number[]) => {
    roleId.value = roleid;
    roleName.value = rolename;
    // 传入权限数组
    let editFunctionState: number[] = [];
    const newList = processMenuFunctions(menuFunctionList.value.slice());
    const maxFunctionId = findMaxFunctionId(newList);
    const initFunctionState = new Array(maxFunctionId).fill(0);
    editFunctionState = functionState === undefined || functionState.length === 0 ? initFunctionState : functionState.slice();
    // 所有根级权限节点
    menuFunctionList.value.forEach((item) => {
        const rootId = item.functionId;
        const allIds = getAllFunctionIds(item.children);
        item.children.forEach((parentItem) => {
            const parentId = parentItem.functionId;
            const childrenIds = getAllFunctionIds(parentItem.children);
            const isAllChecked = childrenIds.every((item) => editFunctionState[item - 1] === 1);
            editFunctionState[parentId - 1] = isAllChecked ? 1 : 0;
        });
        const isAllChecked = allIds.every((item) => editFunctionState[item - 1] === 1);
        editFunctionState[rootId - 1] = isAllChecked ? 1 : 0;
    });
    checkList.value = editFunctionState;
};
const setDisabled = (type: boolean) => (isDisabled.value = type);

defineExpose({ handleInit, setEditType, setDisabled });

const getCloneCheckList = () => checkList.value;
const findMaxFunctionId = (list: IMenuFunction[], lastListItem?: IMenuFunction): number => {
    list = list.sort((a, b) => a.functionId - b.functionId);
    lastListItem = list[list.length - 1];
    if (lastListItem.children.length > 0) {
        const maxChildFunctionId = findMaxFunctionId(lastListItem.children);
        return Math.max(lastListItem.functionId, maxChildFunctionId);
    }
    return lastListItem.functionId;
};
const getAllFunctionIds = (list: IMenuFunction[], functionIds: number[] = []): number[] => {
    list.forEach((item) => {
        functionIds.push(item.functionId);
        if (item.children.length > 0) {
            getAllFunctionIds(item.children, functionIds);
        }
    });
    return functionIds;
};

const findChild = (list: IMenuFunction[], condition: (item: IMenuFunction) => boolean): IMenuFunction | undefined => {
    let result: IMenuFunction | undefined;
    list.forEach((item) => {
        if (condition(item)) {
            result = item;
        } else if (item.children.length > 0) {
            const childResult = findChild(item.children, condition);
            if (childResult) result = childResult;
        }
    });
    return result;
};

function findFunctionsByParentId(data: MenuFunction[], parentId: number): MenuFunction[] {
    const result: MenuFunction[] = [];

    for (const item of data) {
        if (item.parentId === parentId) {
            result.push(item);
        }
        if (item.children.length > 0) {
            const foundItems = findFunctionsByParentId(item.children, parentId);
            result.push(...foundItems);
        }
    }

    return result;
}

const JudgeIsIndeterminate = (functionId: number) => {
    const cloneCheckList = getCloneCheckList();
    const parentFunction = findFunctionsByParentId(menuFunctionList.value, functionId);
    const fids = parentFunction.map((item) => item.functionId);
    const isAllChecked = fids.every((item) => cloneCheckList[item - 1] && cloneCheckList[item - 1] === 1);
    if (isAllChecked) {
        return false;
    } else if (fids.every((item) => !cloneCheckList[item - 1] || cloneCheckList[item - 1] === 0)) {
        return false;
    } else {
        checkList.value[functionId - 1] = 1;
        return true;
    }
};
watch(
    checkList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.content {
    .slot-content {
        overflow: hidden;
        background-color: var(--white);
        height: 100%;
        margin-bottom: -70px;
        .main-tool-bar {
            flex-shrink: 0;
            padding: 20px 48px 20px 20px;
            border-bottom: 1px solid var(--border-color);
            .main-tool-left {
                font-size: var(--font-size);
                line-height: 22px;
                color: var(--font-color);
                .detail-el-input(200px, 28px);
            }
        }
        .roleinfo-functions {
            flex: 1;
            padding: 0 50px 58px;
            display: flex;
            align-items: stretch;
            flex-direction: column;
            overflow: auto;
            height: 0;
            .root-function {
                display: flex;
                flex-direction: column;
                margin-top: 28px;
                border-radius: 6px 6px 0px 0px;
                border: 1px solid #dfe4eb;
                overflow: hidden;
                flex-shrink: 0;
                &.hidden {
                    .root-function-content {
                        display: none;
                    }
                    .root-function-toolbar {
                        .root-function-btn {
                            .hide {
                                display: none;
                            }
                            .show {
                                display: inline;
                            }
                            b {
                                border-bottom: none;
                                border-left: 4px solid transparent;
                                border-right: 4px solid transparent;
                                border-top: 6px solid rgba(0, 0, 0, 0.45);
                            }
                        }
                    }
                }
                .root-function-toolbar {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    background-color: #f4f6f4;
                    padding-left: 26px;
                    padding-right: 20px;
                    height: 44px;
                    box-sizing: border-box;
                    border-bottom: 1px solid #dfe4eb;
                    cursor: pointer;
                    .root-function-title {
                        font-size: var(--font-size);
                        font-weight: 500;
                        color: rgba(0, 0, 0, 0.85);
                        line-height: 20px;
                    }
                    .root-function-btn {
                        font-size: 13px;
                        color: #666666;
                        line-height: 18px;
                        display: flex;
                        align-items: center;
                        .hide {
                            display: inline;
                        }
                        .show {
                            display: none;
                        }
                        b {
                            margin-left: 10px;
                            height: 0;
                            width: 0;
                            border-bottom: 6px solid rgba(0, 0, 0, 0.45);
                            border-left: 4px solid transparent;
                            border-right: 4px solid transparent;
                        }
                    }
                }
                .root-function-content {
                    display: flex;
                    align-items: stretch;
                    :deep(.el-checkbox) {
                        &.is-checked {
                            .el-checkbox__label {
                                color: var(--font-color) !important;
                            }
                        }
                    }
                    .root-function-left {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 110px;
                        flex-shrink: 0;
                        border-right: 1px solid #dfe4eb;
                    }
                    .root-function-right {
                        display: flex;
                        flex-direction: column;
                        flex: 1;
                        width: 0;
                        .page-function {
                            min-height: 44px;
                            box-sizing: border-box;
                            border-bottom: 1px solid #dfe4eb;
                            display: flex;
                            align-items: center;
                            &:last-child {
                                border-bottom: none;
                            }
                            .page-function-left {
                                display: flex;
                                width: 272px;
                                flex-shrink: 0;
                                padding-left: 18px;
                                box-sizing: border-box;
                            }
                            .page-function-right {
                                flex: 1;
                                width: 0;
                                display: flex;
                                align-items: center;
                                flex-wrap: wrap;
                                padding-top: 6px;
                                padding-bottom: 6px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
