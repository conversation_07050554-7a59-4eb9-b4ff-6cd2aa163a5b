<template>
    <el-dialog title="报表分享" width="456px" v-model="shareDialogShow" center class="custom-confirm dialogDrag" id="sheetShareDialog">
        <div class="dialog-content" v-dialogDrag>
            <div class="dialog-main">
                <div class="img-container" style="padding-left: 165px; padding-top: 35px">
                    <img
                        :src="shareReportHost"
                        class="img"
                        style="display: block; width: 126px; height: 126px; background: #d8d8d8; color: #ffffff"
                        alt="扫一扫手机查看报表"
                        referrer="no-referrer"
                    />
                </div>
                <div
                    class="text-desc"
                    style="text-align: left; margin-top: 7px; margin-bottom: 30px; font-size: 14px; color: #333333; padding-left: 165px"
                >
                    微信扫一扫查看报表
                </div>
            </div>
            <div class="buttons" :style="isErp ? '' : 'border-top:1px solid var(--border-color)'">
                <a class="button solid-button" @click="shareDialogShow = false">确定</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
const isErp = ref(window.isErp);
const shareDialogShow = ref(true);

const props = withDefaults(
    defineProps<{
        shareReportHost: string;
    }>(),
    {
        shareReportHost: "",
    }
);
</script>

<style lang="less" scoped></style>
