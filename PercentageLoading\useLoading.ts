import { useLoadingStoreHook } from "@/store/modules/loading"
import { LoadingArg, percentageLoading } from "./usePercentageLoading"

export const useLoading = () => {
  const isLoading = computed(() => {
    return useLoadingStoreHook().loading > 0
  })
  const enterLoading = (title: string) => {
    percentageLoading()
    useLoadingStoreHook().title = title
    useLoadingStoreHook().loading = 1
  }
  const enterSyncLoading = (
    loadingArg: LoadingArg = {
      showConfirmBtn: true,
      width: 550,
      dialogTitle: "提示",
      alignCenter: true,
      strokeWidth: 22,
      bottomTipMsg: "正在申报中，可能需要几分钟，您可以先关闭此弹窗，申报完成后会通过任务中心通知您~",
      percentageShowText: false,
    },
  ) => {
    percentageLoading(loadingArg)
    useLoadingStoreHook().loading = 1
  }
  const quitLoading = () => {
    useLoadingStoreHook().loading = 0
  }

  // loadingWrapper方法，自动完成进入/退出loading的设置
  const loadingWrapper = <T>(promise: Promise<T>) => {
    return new Promise<void>((resolve, reject) => {
      // 进入loading
      enterLoading("")
      promise.finally(() => {
        // 退出loading
        quitLoading()
        resolve()
      })
    })
  }
  return {
    isLoading,
    enterLoading,
    quitLoading,
    loadingWrapper,
    enterSyncLoading,
  }
}
