import { nextTick, type Directive } from "vue";

export const midEllipsis: Directive = {
    mounted(el) {
      nextTick(()=>{
        const ipt = el?.querySelector(".el-input__inner") || el?.querySelector("input");
        const text = ipt.value;
        const offsetWidth = (ipt as HTMLElement).offsetWidth;
        const scrollWidth = ipt?.scrollWidth;
        if (offsetWidth < scrollWidth) {
          const truncatedText = text.slice(0, 4) + '...'+text.slice(-4); // 
          ipt.value = truncatedText;
          el.setAttribute('title', text);
        }
      })
    }
  };