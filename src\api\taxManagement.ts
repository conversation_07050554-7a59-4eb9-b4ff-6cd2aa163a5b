import { IResponseModel, request } from "@/utils/service"
import { EnumUpdateStatus } from "@/views/TaxManagement/constants"
import { ITaxTypeEditParams } from "@/views/TaxManagement/types"

// 获取税费种列表
export interface ITaxTypeQueryOutput {
  declarationTypeName?: string // 申报种类 name
  taxPeriodName?: string
  optionName?: string
  sourceName?: string
  categoryCount?: number
  parentCode?: string // 上级Typeid
  asId?: number // 账套id
  projectCode: string // 征收项目
  projectName?: string // 征收项目name
  declarationType: number // 申报种类id
  taxPeriod: number // 申报期限/纳税期限 0=(月,月) 1=(季,季) 2=(年,年) 3=(半年,半年) 4=(次,次) = ['0', '1', '2', '3', '4']
  option: number // 申报选项 0=(申报,申报) 1=(无需申报,无需申报) = ['0', '1']
  confirmStartDate: string // 认定有效期起
  confirmEndDate: string // 认定有效期止
  source?: number // 来源 0=(FromTaxBureau,税局获取) 1=(FromUser,手动新增) = ['0', '1']
  order?: number // 排序
  existDeclareRecord?: boolean // 申报记录是否填写
}

export interface ICustomTaxType {
  code?: string
  name?: string
  declarationType?: string[]
  taxPeriod?: string[]
  source?: number
}

export interface ITaxTypeCategory {
  projectName?: string
  confirmStartDate?: string
  confirmEndDate?: string
}

export interface ITaxChangeLog {
  createdBy?: number
  createdByName?: string
  createdDate?: string
  imageUrl?: string
  log?: string
}
// 获取税费种列表
export function getTaxList(): Promise<IResponseModel<ITaxTypeQueryOutput[]>> {
  return request({
    url: "/api/type/list",
    method: "get",
  })
}
//  获取自定义列表
export function getCustomList(): Promise<IResponseModel<Array<ICustomTaxType>>> {
  return request({
    url: "/api/type/custom-list",
    method: "get",
  })
}
// 获取品目列表
export function getCategorys(code: string): Promise<IResponseModel<Array<ITaxTypeCategory>>> {
  return request({
    url: `/api/type/${code}/categorys`,
    method: "get",
  })
}

// 更新税费种
export function updateTaxtype(params: any): Promise<IResponseModel<string>> {
  return request({
    url: "/api/type/refresh",
    method: "post",
    params,
  }) as any
}

// 批量删除税费种
// delete /api/type/delete-batch
export function deleteTaxtypeMany(params: any): Promise<IResponseModel<boolean>> {
  return request({
    url: "/api/type/batch",
    method: "delete",
    params,
  })
}

// post /api/TaxType
// 税费种新增
export function addTaxtype(data: any): Promise<IResponseModel<boolean>> {
  return request({
    url: "/api/type",
    method: "post",
    data,
  })
}

// 税费种编辑
export function editTaxtype(data: any, params: ITaxTypeEditParams): Promise<IResponseModel<boolean>> {
  return request({
    url: `/api/type/${params.code}/${params.period}`,
    method: "put",
    data,
    params,
  })
}

export enum LatestParamsType {
  "获取税费种信息" = 305,
  "申报结果" = 326,
  "小规模纳税人增值税申报" = 301,
  "待定" = 302,
  "待定1" = 303,
  "一般纳税人增值税申报" = 311,
  "企业所得税季报查询" = 321,
  "企业所得税季报提交" = 322,
  "获取纳税人基本信息" = 332,
  "缴款信息查询" = 331,
  "未申报信息查询" = 341,
  "未勾选发票" = 425,
  "已勾选发票抵扣" = 426,
  "已认证不抵扣发票" = 427,
  "已认证抵扣发票" = 3,
  "已勾选发票不抵扣" = 10426,
}
// get /api/tax/log/latest
// 税务变更日志最新时间
export function getLatestUpdateTime({ type }: { type: number }): Promise<IResponseModel<string>> {
  return request({
    url: "/api/task/latest",
    method: "get",
    params: { type },
  })
}

// get /api/tax/log/list
// 税务变更日志列表
export function getTaxLogList(params: any): Promise<IResponseModel<PageList<ITaxChangeLog>>> {
  return request({
    url: "/api/log/type-list",
    method: "get",
    params,
  })
}

// get /api/task/status
// 获取更新任务状态
// 返回值=1，则成功；2 成功有变动；3成功没变动；-1 失败
export async function checkUpdateStatus(params: { taskId: string }): Promise<IResponseModel<EnumUpdateStatus>> {
  return await request({
    url: "/api/task/status",
    method: "get",
    params,
  })
}

// 检查税费种code存在申报记录的数量
export function checkTaxtypeCode(params: { code: any; period: any }): Promise<IResponseModel<number>> {
  return request({
    url: `/api/type/check/${params.code}`,
    method: "get",
    params,
  })
}
