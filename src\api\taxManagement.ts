import { IResponseModel, request } from "@/utils/service"
import { EnumUpdateStatus } from "@/views/TaxManagement/constants"
import { ITaxTypeEditParams } from "@/views/TaxManagement/types"

// 获取税费种列表
export interface ITaxTypeQueryOutput {
  declarationTypeName?: string // 申报种类 name
  taxPeriodName?: string
  optionName?: string
  sourceName?: string
  categoryCount?: number
  parentCode?: string // 上级Typeid
  asId?: number // 账套id
  projectCode: string // 征收项目
  projectName?: string // 征收项目name
  declarationType: number // 申报种类id
  taxPeriod: number // 申报期限/纳税期限 0=(月,月) 1=(季,季) 2=(年,年) 3=(半年,半年) 4=(次,次) = ['0', '1', '2', '3', '4']
  option: number // 申报选项 0=(申报,申报) 1=(无需申报,无需申报) = ['0', '1']
  confirmStartDate: string // 认定有效期起
  confirmEndDate: string // 认定有效期止
  source?: number // 来源 0=(FromTaxBureau,税局获取) 1=(FromUser,手动新增) = ['0', '1']
  order?: number // 排序
  existDeclareRecord?: boolean // 申报记录是否填写
}

export interface ICustomTaxType {
  code?: string
  name?: string
  declarationType?: string[]
  taxPeriod?: string[]
  source?: number
}

export interface ITaxTypeCategory {
  projectName?: string
  confirmStartDate?: string
  confirmEndDate?: string
}

export interface ITaxChangeLog {
  createdBy?: number
  createdByName?: string
  createdDate?: string
  imageUrl?: string
  log?: string
}
// 获取税费种列表
export async function getTaxList() {
  return (await request({
    url: "/api/type/list",
    method: "get",
  })) as any as Promise<IResponseModel<ITaxTypeQueryOutput[]>>
}
//  获取自定义列表
export async function getCustomList() {
  return (await request({
    url: "/api/type/custom-list",
    method: "get",
  })) as any as Promise<IResponseModel<Array<ICustomTaxType>>>
}
// 获取品目列表
export async function getCategorys(code: string) {
  return (await request({
    url: `/api/type/${code}/categorys`,
    method: "get",
  })) as any as Promise<IResponseModel<Array<ITaxTypeCategory>>>
}

// 税种初始化
export async function initTaxtype(data: any) {
  return (await request({
    url: "/api/type/init",
    method: "post",
    data,
  })) as any
}

// 更新税费种
export async function updateTaxtype(params: any) {
  return (await request({
    url: "/api/type/refresh",
    method: "post",
    params,
  })) as any
}

// 删除税费种
export async function deleteTaxtype(data: any) {
  return (await request({
    url: "/api/type",
    method: "delete",
    data,
  })) as any
}
// 批量删除税费种
// delete /api/type/delete-batch
export async function deleteTaxtypeMany(params: any) {
  return (await request({
    url: "/api/type/batch",
    method: "delete",
    params,
  })) as any as Promise<IResponseModel<boolean>>
}

// post /api/TaxType
// 税费种新增
export async function addTaxtype(data: any) {
  return (await request({
    url: "/api/type",
    method: "post",
    data,
  })) as any as Promise<IResponseModel<boolean>>
}

// 税费种编辑
export async function editTaxtype(data: any, params: ITaxTypeEditParams) {
  return (await request({
    url: `/api/type/${params.code}/${params.period}`,
    method: "put",
    data,
    params,
  })) as any as Promise<IResponseModel<boolean>>
}

export enum LatestParamsType {
  "获取税费种信息" = 305,
  "申报结果" = 326,
  "小规模纳税人增值税申报" = 301,
  "待定" = 302,
  "待定1" = 303,
  "一般纳税人增值税申报" = 311,
  "企业所得税季报查询" = 321,
  "企业所得税季报提交" = 322,
  "获取纳税人基本信息" = 332,
  "缴款信息查询" = 331,
  "未申报信息查询" = 341,
  "未勾选发票" = 425,
  "已勾选发票抵扣" = 426,
  "已认证不抵扣发票" = 427,
  "已认证抵扣发票" = 3,
  "已勾选发票不抵扣" = 10426,
}
// get /api/tax/log/latest
// 税务变更日志最新时间
export async function getLatestUpdateTime({ type }: { type: number }) {
  return (await request({
    url: "/api/task/latest",
    method: "get",
    params: { type },
  })) as any as Promise<IResponseModel<string>>
}

// get /api/tax/log/list
// 税务变更日志列表
export async function getTaxLogList(params: any) {
  return (await request({
    url: "/api/log/type-list",
    method: "get",
    params,
  })) as any as Promise<IResponseModel<PageList<ITaxChangeLog>>>
}

// get /api/basic/tips
// 是否显示提示信息
// export async function getTips() {
//   return (await request({
//     url: "/api/basic/tips",
//     method: "get",
//   })) as any as Promise<IResponseModel<boolean>>
// }

// get /api/type/tax-period
// 获取税种征收期
export async function getTaxPeriod() {
  return (await request({
    url: "/api/type/tax-period",
    method: "get",
  })) as any
}

// get /api/task/status
// 获取更新任务状态
// 返回值=1，则成功；2 成功有变动；3成功没变动；-1 失败
export async function checkUpdateStatus(params: { taskId: string }) {
  return (await request({
    url: "/api/task/status",
    method: "get",
    params,
  })) as any as Promise<IResponseModel<EnumUpdateStatus>>
}
// // 对企业会计准则税费种更新 废弃

// // put /api/basic/account-standard
// export async function updateAccountStandard(params: { excuted: number }) {
//   return (await request({
//     url: "/api/basic/account-standard",
//     method: "put",
//     params,
//   })) as any
// }
// get /api/type/check/{code}
// 检查税费种code存在申报记录的数量
export async function checkTaxtypeCode(params: { code: any; period: any }) {
  return (await request({
    url: `/api/type/check/${params.code}`,
    method: "get",
    params,
  })) as any as Promise<IResponseModel<number>>
}
