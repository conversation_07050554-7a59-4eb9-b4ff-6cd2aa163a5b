<template>
    <div class="voucherButton" v-if="isBottomBtn !== true">
        <div class="toolbar-left">
            <a
                v-permission="['voucher-canedit']"
                class="button solid-button"
                :class="{ disabled: !edited }"
                @click="saveAndNew()"
                v-show="isNewVoucher && isNewVoucherPage"
                >保存并新增</a
            >
            <a v-permission="['voucher-canedit']" class="button solid-button" @click="defaultNewVoucher()" v-show="!isNewVoucher">新增</a>
            <a
                class="button solid-button"
                v-if="checkPermission(['voucher-canedit', props.modulePermission]) || isGenerateVoucherPage"
                :class="{ disabled: !edited, 'ml-10': !isNewVoucher || isNewVoucherPage }"
                @click="save"
                v-show="!voucherReadonly"
                >保存</a
            >
            <Dropdown :btnTxt="'打印'" :downlistWidth="85" v-permission="['voucher-canprint']" :class="(isNewVoucher || edited) && !voucherReadonly ?' disabled' : '' ">
                <li :class="{ disabled: (isNewVoucher || edited) && !voucherReadonly }" @click="directPrint">直接打印</li>
                <li :class="{ disabled: (isNewVoucher || edited) && !voucherReadonly}" @click="showPrintVoucherDialog">打印设置</li>
            </Dropdown>
            <a
                v-permission="['voucher-cancheck']"
                v-show="!edited && !isNewVoucher && checkNeeded && !voucherReadonly"
                class="button ml-10"
                @click="handleAuditOrUnAudit('audit')"
            >
                审核
            </a>
            <a
                v-permission="['voucher-canuncheck']"
                v-show="checkNeeded && voucherModel?.approveStatus === 1 && voucherModel?.pstatus !== 3"
                class="button ml-10"
                @click="handleAuditOrUnAudit('unAudit')"
            >
                取消审核
            </a>
            <a
                v-show="!edited && !isNewVoucher && !voucherReadonly"
                class="button ml-10"
                @click="handleDeleteVoucher"
                @mousedown="$event.stopPropagation()"
                v-if="
                    checkPermission([
                        'voucher-candelete',
                        props.modulePermission === 'scmvoucher-cancreatevoucher' ? 'scmvoucher-candelete' : '',
                    ])
                "
            >
                删除
            </a>
            <Dropdown :btnTxt="'更多'" :downlistWidth="118">
                <li v-permission="['vouchertemplate-canedit']" @click="handleSaveAsTemplate">保存为模板</li>
                <li v-permission="['vouchertemplate-canview']" @click="showVoucherTemplates" v-show="!voucherReadonly">从模板中加载</li>
                <li v-permission="['voucher-canedit']" @click="saveVoucherDraft">保存为草稿</li>
                <li v-permission="['voucher-canedit']" @click="loadVoucherDraftDialog?.showVoucherDrafts()" v-show="!voucherReadonly">
                    从草稿中加载
                </li>
                <li v-permission="['voucher-canedit']" v-show="!edited && !isNewVoucher" @click="handleCopy">复制</li>
                <li v-permission="['voucher-canedit']" v-show="!edited && !isNewVoucher" @click="handleRed(true)">{{voucherModel.rf.length ? '查看' : ''}}红冲</li>
                <li v-permission="['voucher-canedit']" @click="clearVoucherDialogShow = true" v-show="!voucherReadonly">清空凭证</li>
                <li @click="showVoucherSettings()">选项</li>
            </Dropdown>
            <a v-show="showCancel" class="button ml-10" @click="back" @mousedown="$event.stopPropagation()">
                {{ cancelTxt }}
            </a>
        </div>
        <div class="toolbar-between-title" v-if="voucherTitle">
            {{ voucherTitle }}
        </div>
        <div class="toolbar-right">
            <div class="keyboard-block" v-if="showKeysTip">
                <div class="keyboard-icon">
                    <div class="quick-btns-tips">
                        <table cellspacing="0" cellpadding="0">
                            <thead>
                                <tr>
                                    <td colspan="4">凭证快捷键</td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>保存</td>
                                    <td>Ctrl + S</td>
                                    <td>上一行</td>
                                    <td>&#8593;</td>
                                </tr>
                                <tr>
                                    <td>保存并新增</td>
                                    <td>F12</td>
                                    <td>前一格</td>
                                    <td>&#8592;</td>
                                </tr>
                                <tr>
                                    <td>新增</td>
                                    <td>Alt + N</td>
                                    <td>下一行</td>
                                    <td>&#8595;</td>
                                </tr>
                                <tr>
                                    <td>前一张</td>
                                    <td>PgUp</td>
                                    <td>后一格</td>
                                    <td>&#8594;</td>
                                </tr>
                                <tr>
                                    <td>后一张</td>
                                    <td>PgDn</td>
                                    <td>余额反算</td>
                                    <td>F9</td>
                                </tr>
                                <tr>
                                    <td>跳转单元格或换行</td>
                                    <td>Enter</td>
                                    <td>新增一行</td>
                                    <td>Ctrl + '+'</td>
                                </tr>
                                <tr>
                                    <td>自动平衡借贷方金额</td>
                                    <td>双击鼠标 或 =</td>
                                    <td>删除一行</td>
                                    <td>Ctrl + '-'</td>
                                </tr>
                                <tr>
                                    <td>借贷方金额互换</td>
                                    <td>空格键</td>
                                    <td>删除当前凭证</td>
                                    <td>Ctrl + Delete</td>
                                </tr>
                                <tr>
                                    <td>从凭证模板引入</td>
                                    <td>Ctrl + Q</td>
                                    <td>另存为凭证模板</td>
                                    <td>Ctrl + E</td>
                                </tr>
                                <tr>
                                    <td>跳转至附单据数栏</td>
                                    <td>F4</td>
                                    <td>复制上一格</td>
                                    <td>F3</td>
                                </tr>
                                <tr>
                                    <td>跳转至记账日期</td>
                                    <td>Ctrl + L</td>
                                    <td>撤销</td>
                                    <td>Ctrl + Z</td>
                                </tr>
                                <tr>
                                    <td>打印凭证</td>
                                    <td>Ctrl + P</td>
                                    <td>恢复</td>
                                    <td>Ctrl + Y</td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="buttons">
                            <a class="button mr-20" @click="handleDownload">下载</a>
                            <a class="button solid-button" @click="handlePrint">打印</a>
                        </div>
                    </div>
                </div>
                <div class="keyboard-content">快捷键</div>
            </div>
            <div class="switch-btn" v-if="showSwitchBtn && checkPermission(['voucher-canview'])">
                <div class="prev-btn" :class="{ disabled: switchInfo?.hasPrev === false }" @click="$emit('preVoucher')">
                    <span class="btn-text">上一页</span> <span class="btn-icon"></span>
                </div>
                <div class="next-btn" :class="{ disabled: switchInfo?.hasNext === false }" @click="$emit('nextVoucher')">
                    <span class="btn-icon"></span><span class="btn-text">下一页</span>
                </div>
            </div>
        </div>
    </div>
    <div class="voucherButton centerBtn" v-if="isBottomBtn === true">
        <a
            v-if="checkPermission(['voucher-canedit', props.modulePermission]) || isGenerateVoucherPage"
            class="button solid-button"
            :class="{ disabled: !edited }"
            @click="save"
            @mousedown="$event.stopPropagation()"
            v-show="!voucherReadonly"
            >保存</a
        >
        <a v-show="showCancel" class="button ml-10" @click="back" @mousedown="$event.stopPropagation()">
            {{ cancelTxt }}
        </a>
        <Dropdown :btnTxt="'更多'" :downlistWidth="100" :showTop="true">
            <li v-permission="['voucher-canprint']" v-show="!isNewVoucher" @click="showPrintVoucherDialog()">打印</li>
            <li
                v-permission="['voucher-cancheck']"
                v-show="!edited && !isNewVoucher && checkNeeded && !voucherReadonly"
                @click="handleAuditOrUnAudit('audit')"
            >
                审核
            </li>
            <li
                v-permission="['voucher-canuncheck']"
                v-show="checkNeeded && voucherModel?.approveStatus === 1 && voucherModel?.pstatus !== 3"
                @click="handleAuditOrUnAudit('unAudit')"
            >
                取消审核
            </li>
            <li v-permission="['voucher-candelete']" v-show="!edited && !isNewVoucher && !voucherReadonly" @click="handleDeleteVoucher">
                删除
            </li>
            <li v-permission="['voucher-canedit']" v-show="!edited && !isNewVoucher" @click="handleCopy">复制</li>
            <li @click="showVoucherSettings()">选项</li>
        </Dropdown>
    </div>
    <el-dialog v-model="voucherSettingsInfo.display" title="选项" center width="500px" class="custom-confirm dialogDrag">
        <div class="voucher-settings-container" v-dialogDrag>
            <div class="voucher-settings-content">
                <div class="voucher-settings-item">
                    <div class="item-title">自动填补凭证断号：</div>
                    <div class="item-content">
                        <el-radio-group v-model="voucherSettingsInfo.model.autoSupplementVNumStatus">
                            <el-radio :label="1">启用</el-radio>
                            <el-radio :label="2">不启用</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="voucher-settings-item">
                    <div class="item-title">录入金额后不反算单价、汇率：</div>
                    <div class="item-content">
                        <el-radio-group v-model="voucherSettingsInfo.model.autoCalcVoucherStatus">
                            <el-radio :label="1">启用</el-radio>
                            <el-radio :label="2">不启用</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="voucher-settings-item">
                    <div class="item-title">原币允许录入为0：</div>
                    <div class="item-content">
                        <el-radio-group v-model="voucherSettingsInfo.model.allowFcZeroStatus">
                            <el-radio :label="1">启用</el-radio>
                            <el-radio :label="2">不启用</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="voucher-settings-item">
                    <div class="item-title">按科目方向定位借贷方：</div>
                    <div class="item-content">
                        <el-radio-group v-model="voucherSettingsInfo.model.amountDirectionByAsubStatus">
                            <el-radio :label="1">启用</el-radio>
                            <el-radio :label="2">不启用</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="voucher-settings-item">
                    <div class="item-title">科目默认AI推荐： 
                        <BubbleTip :bubble-width="256" :bubble-top="16">
                            <template #content>
                                <div class="voucher-help"></div>
                            </template>
                            <template #tips>
                                <div>默认AI推荐，选择科目时默认AI推荐科目</div>
                                <div>不默认AI推荐，选择科目时默认全部科目</div>
                            </template>
                        </BubbleTip>
                    </div>
                    <div class="item-content">
                        <el-radio-group v-model="voucherSettingsInfo.model.defaultAISuggestionStatus">
                            <el-radio :label="1">默认</el-radio>
                            <el-radio :label="2">不默认</el-radio>
                        </el-radio-group>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button" @click="voucherSettingsInfo.display = false">取消</a>
                <a class="button solid-button ml-10" @click="saveVoucherSettings()">确定</a>
            </div>
        </div>
    </el-dialog>
    <!-- <PrintDialog v-model:printDialogShow="printDialogDisplay" :printVoucherCountTypeShow="false" @confirm-print="printVoucher" /> -->
   <PrintSettingDialog v-model:printDialogShow="printDialogDisplay" :printVoucherCountTypeShow="false" @confirm-print="printVoucher" @senior-print="seniorPrint"/>
    <SaveVoucherTemplate @save-voucher-template="saveVoucherTemplate" ref="saveVoucherTemplateDialog"></SaveVoucherTemplate>
    <LoadVoucherTemplate
        @on-select="loadVoucherTemplate"
        ref="loadVoucherTemplateDialog"
        @exitFullScreen="exitFullScreen"
    ></LoadVoucherTemplate>
    <LoadVoucherDraft @on-select="loadVoucherDraft" ref="loadVoucherDraftDialog"></LoadVoucherDraft>
    <el-dialog v-model="clearVoucherDialogShow" title="提示" center width="400px" class="custom-confirm dialogDrag">
        <div class="dialog-content" v-dialogDrag>
            <div class="dialog-content-body">清空凭证将删除所有的凭证行数据、关联的附件、备注等。确定要清空凭证吗？</div>
            <div class="buttons" style="border-top: 1px solid var(--border-color)">
                <a class="button solid-button" @click="clearVoucher">清空</a>
                <a class="button ml-10" @click="clearVoucherDialogShow = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <DialogHandleRed ref="dialogHandleRedRef" @confirmOffset="handleRed(false)" />
</template>

<script setup lang="ts">
import { ref, toRef, computed, reactive, nextTick } from "vue";
import {
    CopyVoucherQueryParams,
    VoucherModel,
    VoucherEntryModel,
    OffsetVoucherQueryParams,
    EditVoucherQueryParams,
    VoucherSaveModel,
    VoucherSettingsModel,
    VoucherSwitchInfoModel,
    VoucherDraftSaveParams,
    VoucherTemplateSaveParams,
    VoucherQueryParams,
    VoucherTemplateModel,
} from "@/components/Voucher/types";
import {
    getVoucherPrintInfo,
    getVoucherSeniorPrintInfo,
    printParams,
} from "@/components/Voucher/utils";
import type { VoucherTemplateSaveModel } from "@/api/voucherTemplate";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useVoucherSettingsStore } from "@/store/modules/voucherSettings";
import { request, type IResponseModel } from "@/util/service";
import Dropdown from "@/components/Dropdown/index.vue";
import { ElNotify } from "@/util/notify";
import { ElAlert } from "@/util/confirm";
import { getGlobalToken } from "@/util/baseInfo";
import { useRoute } from "vue-router";
import PrintSettingDialog from "@/views/Voucher/components/PrintSettingDialog/index.vue";
import { getUrlSearchParams, globalPrint, globalWindowOpenPage,globalDirectPrint,globalExport } from "@/util/url";
import SaveVoucherTemplate from "@/views/Voucher/components/SaveVoucherTemplate/index.vue";
import LoadVoucherTemplate from "@/views/Voucher/components/LoadVoucherTemplate/index.vue";
import LoadVoucherDraft from "@/views/Voucher/components/LoadVoucherDraft/index.vue";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { editConfirm } from "@/util/editConfirm";
import { checkPermission } from "@/util/permission";
import DialogHandleRed from "@/views/Voucher/VoucherList/components/DialogHandleRed.vue";
import { getGlobalLodash } from "@/util/lodash";
import BubbleTip from "@/components/BubbleTip/index.vue";
import { isLemonClient } from "@/util/lmClient";

const props = withDefaults(
    defineProps<{
        queryParams: VoucherQueryParams | undefined;
        voucherModel: any;
        checkAllVoucherLines: Function;
        rebindVoucherlineOnScroll: Function;
        edited: boolean;
        clearVoucher: Function;
        saveVoucher: Function;
        voucherTitle: string | undefined;
        loadVoucherTemplate?: Function | undefined;
        loadVoucherDraft?: Function | undefined;
        newVoucher?: Function | undefined;
        showCancel?: boolean;
        cancelTxt?: string;
        isBottomBtn?: boolean;
        showKeysTip?: boolean; //是否显示快捷键提示
        showSwitchBtn?: boolean; //是否显示切换按钮
        switchInfo?: VoucherSwitchInfoModel; //当前凭证的切换信息
        isGenerateVoucherPage?: boolean; //生成凭证权限和凭证编辑权限区分
        modulePermission?: string; //模块权限
        resetWarningRow?: Function; //重置警告行
    }>(),
    {
        edited: true,
        showCancel: false,
        cancelTxt: "返回",
        isBottomBtn: false,
        showKeysTip: false,
        showSwitchBtn: false,
        isGenerateVoucherPage: false,
        modulePermission: "",
    }
);

const emit = defineEmits([
    "clearVoucher",
    "update:queryParams",
    "update:voucherModel",
    "save",
    "deleteVoucher",
    "back",
    "saveAndNew",
    "preVoucher",
    "nextVoucher",
]);
const voucherQueryParams = computed({
    get() {
        return props.queryParams;
    },
    set(value: any) {
        emit("update:queryParams", value);
    },
});
const route = useRoute();
const currentPath = ref(route.path);
const accountSet = toRef(useAccountSetStore(), "accountSet");
const checkNeeded = computed(() => accountSet.value?.checkNeeded === 1); //开启审核
const printDialogDisplay = ref(false);
const clearVoucherDialogShow = ref(false);
const voucherModel = computed({
    get() {
        return props.voucherModel;
    },
    set(value: VoucherModel) {
        emit("update:voucherModel", value);
    },
});
const saveVoucherTemplateDialog = ref<InstanceType<typeof SaveVoucherTemplate>>(); //保存凭证模板
const loadVoucherTemplateDialog = ref<InstanceType<typeof LoadVoucherTemplate>>(); //加载凭证模板
const loadVoucherDraftDialog = ref<InstanceType<typeof LoadVoucherDraft>>(); //加载凭证草稿

const isNewVoucherPage = computed(() => currentPath.value === "/Voucher/NewVoucher");
const isNewVoucher = computed(() => voucherModel.value?.vid === 0);
const voucherReadonly = computed(() => voucherModel.value?.approveStatus === 1 || voucherModel.value?.pstatus === 3);
const edited = computed(() => props.edited);
//保存并新增
const saveAndNew = () => {
    emit("saveAndNew");
};
//新增按钮 默认打开新增凭证页面
const defaultNewVoucher = () => {
    if (!checkPermission(["voucher-canedit"]) || isNewVoucher.value) return;
    if (typeof props.newVoucher === "function" && isNewVoucherPage.value) {
        props.newVoucher();
        return;
    }
    globalWindowOpenPage("/Voucher/NewVoucher?r=" + Math.random(), "新增凭证");
};
//保存
const save = () => {
    if (!edited.value) {
        ElNotify({
            message: "亲，凭证没有修改，不用重新保存哦~",
            type: "warning",
        });
        return;
    }
    emit("save");
};
//删除
const handleDeleteVoucher = () => {
    emit("deleteVoucher");
};
//返回
const back = () => {
    emit("back");
};

const directPrint = async () => {
    printDialogDisplay.value = false;
    const { pid, vid } = voucherModel.value;

    if (voucherModel.value && pid && vid) {
        const printType = localStorage.getItem(getGlobalToken() + "-printType");
        let printInfo;
        let printUrl = "";
        if (!printType || printType === "default") {
            printInfo = { ...getVoucherPrintInfo(), async: !isLemonClient() };
            printUrl = "/api/Voucher/DirectPrint?";
        } else {
            printInfo = { ...printParams(getVoucherSeniorPrintInfo()), isSeniorPrint: true }
            printUrl = "/api/Voucher/DirectSeniorPrint?";
            const check = await useVoucherSettingsStore().checkSeniorSettings(printInfo.pageType);
            if (!check) return
        }
        const queryParams = { vids: vid, pids: pid };
        globalDirectPrint(window.printHost + printUrl + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams(queryParams))
    } else {
        ElNotify({
            message: "保存后的凭证才能打印哦！",
            type: "warning",
        });
    }
}
const showPrintVoucherDialog = () => {
    const { pid, vid } = voucherModel.value;
    if ((voucherModel.value && pid && vid && !edited.value) || voucherReadonly.value) {
        printDialogDisplay.value = true;
    } else {
        ElNotify({
            message: "保存后的凭证才能打印哦！",
            type: "warning",
        });
    }
};
//打印凭证
//和高级打印整一起
const printVoucher = (printInfo: any) => {
    printDialogDisplay.value = false;
    const { pid, vid } = voucherModel.value;
    if (voucherModel.value && pid && vid) {
        const queryParams = { vids: vid, pids: pid };
        globalPrint(window.printHost + "/api/Voucher/Print?" + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams(queryParams));
    }
};
const seniorPrint = async (printInfo: any) => {
    printDialogDisplay.value = false;
    const { pid, vid } = voucherModel.value;
    if (voucherModel.value && pid && vid) {
        const queryParams = { vids: vid, pids: pid };
        const check = await useVoucherSettingsStore().checkSeniorSettings(printInfo.pageType)
        if (!check) return
        globalPrint(window.printHost + "/api/Voucher/SeniorPrint?" + getUrlSearchParams(printInfo) + "&" + getUrlSearchParams(queryParams));
    }
};
function handleAuditOrUnAudit(type: "audit" | "unAudit") {
    if (edited.value && type === "audit") {
        ElNotify({ type: "warning", message: "保存后的凭证才能审核哦！" });
        return;
    }
    const pid = voucherModel.value?.pid ?? 0;
    const vid = voucherModel.value?.vid ?? 0;
    const list = [{ pid, vid }];
    confirmApproveVouchers(type, list);
}
function confirmApproveVouchers(type: "audit" | "unAudit", queryParamsList: { pid: number; vid: number }[]) {
    const confirmMessage = type === "audit" ? "亲，确认要审核吗？" : "亲，确认要取消审核吗？";
    const errorMessage = "亲，" + (type === "audit" ? "" : "取消") + "审核失败啦！请联系侧边栏客服！";
    const url = type === "audit" ? "/api/Voucher/ApproveVouchers" : "/api/Voucher/UnApproveVouchers";
    ElAlert({ message: confirmMessage }).then((r: any) => {
        if (r) {
            request({
                url,
                method: "post",
                headers: { "content-type": "application/json" },
                data: JSON.stringify(queryParamsList),
            })
                .then((res: any) => {
                    if (res.state != 1000 || !res.data) {
                        ElNotify({ type: "warning", message: errorMessage });
                        return;
                    }
                    if (type === "audit") {
                        if (res.data.success === 1) {
                            ElNotify({ type: "success", message: "已经通过审核" });
                            handleCheck(queryParamsList[0].vid, queryParamsList[0].pid);
                            window.dispatchEvent(new CustomEvent("reloadVoucherList", { detail: {type: "approve" } }));
                        } else if (res.data.jump === 1) {
                            ElNotify({ type: "warning", message: "科目或辅助核算不完整，不能审核。" });
                        } else {
                            ElNotify({ type: "warning", message: "亲，审核失败啦！请联系侧边栏客服！" });
                        }
                    } else {
                        ElNotify({ type: "success", message: "审核已取消" });
                        handleEdit(queryParamsList[0].vid, queryParamsList[0].pid);
                        window.dispatchEvent(new CustomEvent("reloadVoucherList", { detail: {type: "approve" } }));
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: errorMessage });
                })
                .finally(() => {});
        }
    });
}
//检查凭证
const handleCheck = (vid: number, pid: number) => {
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
};
const handleEdit = (vid: number, pid: number) => {
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
};
//清空凭证
const clearVoucher = () => {
    props.clearVoucher();
    clearVoucherDialogShow.value = false;
};

//保存为凭证模板
const saving = ref(false);
const handleSaveAsTemplate = () => {
    if (saving.value) return;
    saving.value = true;
    props.saveVoucher(
        new VoucherTemplateSaveParams(0, "",(res: IResponseModel<VoucherTemplateSaveModel>) => {
            saving.value = false;
            if (res.state === 1000) {
                saveVoucherTemplateDialog.value?.showSaveVoucherTemplateDialog();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            }
        })
    );
};
function saveVoucherTemplate(vtId: number, vtName: string, saveAmount: boolean) {
    if (saving.value) {
        return;
    }
    saving.value = true;
    props.saveVoucher(
        new VoucherTemplateSaveParams(vtId, vtName, (res: IResponseModel<VoucherTemplateSaveModel>) => {
            if (res.state === 1000) {
                ElNotify({
                    message: "亲，保存成功啦！",
                    type: "success",
                });
                saveVoucherTemplateDialog.value?.close();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
            saving.value = false;
        }, saveAmount)
    );
}

//从凭证模板中加载
const isEmptyVoucher = computed(() =>
    voucherModel.value.voucherLines.every((item: VoucherEntryModel) => {
        return item.description.trim() === "" && item.asubName.trim() === "" && item.credit === 0 && item.debit === 0;
    })
);
let confirmHandle = () => {};
let cancelHandle = () => {};
const exitFullScreen = () => {
    useFullScreenStore().changeFullScreenStage(false);
};
const loadVoucherTemplate = (vtId: number) => {
    confirmHandle = () => {
        if (isNewVoucherPage.value && typeof props.loadVoucherTemplate === "function") {
            //新增凭证页面直接用已有的加载凭证模板方法
            // voucherQueryParams.value = new LoadVoucherTemplateQueryParams(vtId);
            props.loadVoucherTemplate(vtId);
        } else if (!checkPermission(["voucher-canedit"])) {
            ElNotify({
                message: "亲，您没有新增凭证的权限！",
                type: "warning",
            });
        } else {
            globalWindowOpenPage(`/Voucher/NewVoucher?vtid=${vtId}&r=${Math.random()}`, "新增凭证");
        }
    };
    cancelHandle = () => {
        request({
            url: `/api/VoucherTemplate?vtId=${vtId}`,
            method: "get",
        }).then((res: IResponseModel<VoucherTemplateModel>) => {
            if (res.state === 1000) {
                voucherModel.value.voucherLines = res.data.voucherLines;
                props.resetWarningRow && props.resetWarningRow();
                props.checkAllVoucherLines();
                nextTick(() => {
                    props.rebindVoucherlineOnScroll();
                });
            }
        });
    };
    if (isEmptyVoucher.value) {
        cancelHandle();
    } else {
        editConfirm("coverVoucher", confirmHandle, cancelHandle);
    }
};

//保存成草稿
const saveVoucherDraft = () => {
    if (saving.value) {
        return;
    }
    saving.value = true;
    props.saveVoucher(
        new VoucherDraftSaveParams((res: IResponseModel<VoucherSaveModel>) => {
            if (res.state === 1000) {
                ElNotify({
                    message: "亲，保存成功啦！",
                    type: "success",
                });
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
            saving.value = false;
        })
    );
};
//草稿中加载
const loadVoucherDraft = (pid: number, vid: number) => {
    const draftParams = {
        pid,
        vdid: vid,
        r: Math.random(),
    };
    confirmHandle = () => {
        if (isNewVoucherPage.value && typeof props.loadVoucherDraft === "function") {
            props.loadVoucherDraft(pid, vid);
        } else {
            globalWindowOpenPage("/Voucher/NewVoucher?" + getUrlSearchParams(draftParams), "新增凭证");
        }
    };
    cancelHandle = () => {
        request({
            url: `/api/Voucher/GetVoucherFromVoucherDraft?pId=${pid}&vId=${vid}`,
            method: "post",
        }).then((res: IResponseModel<VoucherModel>) => {
            if (res.state === 1000) {
                voucherModel.value.note = res.data.note;
                voucherModel.value.voucherLines = res.data.voucherLines;
                props.resetWarningRow && props.resetWarningRow();
                props.checkAllVoucherLines();
                nextTick(() => {
                    props.rebindVoucherlineOnScroll();
                });
            }
        });
    };

    if (isEmptyVoucher.value) {
        cancelHandle();
    } else {
        editConfirm("coverVoucher", confirmHandle, cancelHandle);
    }
};

//复制
const handleCopy = () => {
    const { pid, vid } = voucherModel.value;
    if (isNewVoucherPage.value) {
        voucherQueryParams.value = new CopyVoucherQueryParams(pid, vid);
    } else {
        globalWindowOpenPage(`/Voucher/NewVoucher?vcid=${vid}&pid=${pid}&r=${Math.random()}`, "新增凭证");
    }
};
//红冲
const dialogHandleRedRef = ref<InstanceType<typeof DialogHandleRed>>();
const handleRed = (confirmFlag: boolean = true) => {
    const { pid, vid, rf } = voucherModel.value;
    if (confirmFlag && rf && rf.length > 0) {
        dialogHandleRedRef.value?.handleShowRedVoucher(rf);
        return;
    }
    if (isNewVoucherPage.value) {
        voucherQueryParams.value = new OffsetVoucherQueryParams(pid, vid);
    } else {
        globalWindowOpenPage(`/Voucher/NewVoucher?vrid=${vid}&pid=${pid}&r=${Math.random()}`, "新增凭证");
    }
};
//选项
const voucherSettingsStore = useVoucherSettingsStore();
const voucherSettings = computed(() => voucherSettingsStore.voucherSettings ?? new VoucherSettingsModel());
const voucherSettingsInfo = reactive<{ display: boolean; model: VoucherSettingsModel }>({
    display: false,
    model: new VoucherSettingsModel(),
});
const showVoucherSettings = () => {
    voucherSettingsInfo.model = new VoucherSettingsModel();
    voucherSettingsInfo.model.autoSupplementVNumStatus =
        voucherSettings.value.autoSupplementVNumStatus === 0 ? 2 : voucherSettings.value.autoSupplementVNumStatus;
    voucherSettingsInfo.model.autoCalcVoucherStatus =
        voucherSettings.value.autoCalcVoucherStatus === 0 ? 2 : voucherSettings.value.autoCalcVoucherStatus;
    voucherSettingsInfo.model.allowFcZeroStatus =
        voucherSettings.value.allowFcZeroStatus === 0 ? 2 : voucherSettings.value.allowFcZeroStatus;
    voucherSettingsInfo.model.amountDirectionByAsubStatus =
        voucherSettings.value.amountDirectionByAsubStatus === 0 ? 2 : voucherSettings.value.amountDirectionByAsubStatus;
    voucherSettingsInfo.model.defaultAISuggestionStatus =
        voucherSettings.value.defaultAISuggestionStatus === 0 ? 1 : voucherSettings.value.defaultAISuggestionStatus;
    voucherSettingsInfo.display = true;
};
const saveVoucherSettings = () => {
    request({
        url: "/api/VoucherSettings",
        method: "put",
        data: voucherSettingsInfo.model,
    }).then(async (res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({
                message: "保存成功",
                type: "success",
            });
            voucherSettingsInfo.display = false;
            await voucherSettingsStore.getVoucherSettings();
        }
    });
};

function downloadFn() {
    globalExport("https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/%E5%87%AD%E8%AF%81-%E5%BF%AB%E6%8D%B7%E9%94%AE.png");
}
function printFn() {
    globalPrint("https://lemondisk.oss-cn-beijing.aliyuncs.com/Others/%E5%87%AD%E8%AF%81-%E5%BF%AB%E6%8D%B7%E9%94%AE.pdf");
}
const _ = getGlobalLodash();
const handleDownload = _.debounce(downloadFn, 500);
const handlePrint = _.debounce(printFn, 500);
function showVoucherTemplates() {
    if (voucherReadonly.value || !checkPermission(["vouchertemplate-canview"])) return;
    loadVoucherTemplateDialog.value?.showVoucherTemplates();
}
function saveAsTemplate() {
    if (!checkPermission(["vouchertemplate-canedit"])) return;
    handleSaveAsTemplate();
}
function deleteVoucher() {
    if (edited.value || isNewVoucher.value || voucherReadonly.value || !checkPermission(["voucher-candelete"])) return;
    handleDeleteVoucher();
}
function close() {
    voucherSettingsInfo.display = false;
    printDialogDisplay.value = false;
    saveVoucherTemplateDialog.value?.close();
    loadVoucherTemplateDialog.value?.close();
    loadVoucherDraftDialog.value?.close();
    clearVoucherDialogShow.value = false;
    dialogHandleRedRef.value?.close();
}
defineExpose({
    showPrintVoucherDialog,
    showVoucherTemplates,
    saveAsTemplate,
    deleteVoucher,
    save,
    defaultNewVoucher,
    close,
});
</script>

<style lang="less" scoped>
@import "@/style/voucher/voucher.less";
.voucherButton {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    z-index: 1000;
    .dropdown-button {
        margin-left: 10px;
    }
    &.centerBtn {
        justify-content: center;
        z-index: 3;
    }
    .toolbar-left {
        & > :not([style*="display: none;"]):first-child {
            margin-left: 0 !important;
        }
    }
    .toolbar-between-title {
        font-weight: 600;
        color: var(--font-color);
        font-size: var(--h3);
    }
    .toolbar-right {
        .keyboard-block {
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;

            .keyboard-icon {
                width: 24px;
                height: 20px;
                background-repeat: no-repeat;
                background-size: 100%;
                background-image: url("@/assets/voucher/keyboard-icon.png");
                background-position: center;

                .quick-btns-tips {
                    text-align: left;
                    display: none;
                    position: absolute;
                    top: 20px;
                    right: 0;
                    flex-direction: column;
                    background-color: var(--white);
                    padding: 10px 10px 0;
                    border: 1px solid var(--border-color);
                    box-shadow: 0px 2px 2px 0px rgba(173, 187, 200, 0.19), 0px 4px 4px 0px rgba(64, 97, 121, 0.05),
                        0px 8px 8px 0px rgba(234, 239, 243, 0.05), 0px 16px 16px 0px rgba(215, 217, 219, 0.05),
                        0px 32px 32px 0px rgba(234, 239, 243, 0.3);
                    z-index: 1;

                    table {
                        margin-top: 7px;
                        border: 1px solid var(--border-color);
                        border-right: none;
                        border-bottom: none;
                        width: 475px;

                        thead {
                            tr {
                                td {
                                    font-weight: bold;
                                    background-color: var(--table-title-color);
                                    text-align: center;
                                    color: var(--main-color);
                                    padding-left: 0;
                                }
                            }
                        }

                        tr {
                            td {
                                height: 31px;
                                color: var(--font-color);
                                font-size: var(--font-size);
                                line-height: var(--line-height);
                                padding-left: 10px;
                                border-bottom: 1px solid var(--border-color);
                                border-right: 1px solid var(--border-color);
                                &:nth-child(2n) {
                                    text-align: center;
                                }
                            }
                        }
                    }
                }
            }

            .keyboard-content {
                color: var(--font-color);
                line-height: 20px;
                font-size: var(--font-size);
                margin-left: 6px;
            }

            &:hover {
                .keyboard-icon {
                    background-image: url("@/assets/voucher/keyboard-icon-hover.png");

                    .quick-btns-tips {
                        display: flex;
                    }
                }

                .keyboard-content {
                    color: var(--light-green);
                }
            }
        }
        .switch-btn {
            display: flex;
            align-items: center;

            .prev-btn,
            .next-btn {
                height: 17px;
                cursor: pointer;
                display: flex;
                align-items: center;
                .btn-text {
                    padding: 0 10px;
                    line-height: 16px;
                }

                .btn-icon {
                    width: 17px;
                    height: 17px;
                    background-image: url(/src/assets/Voucher/prev.png);
                    background-repeat: no-repeat;
                    background-size: 100%;
                }
            }

            .prev-btn {
                margin-left: 24px;
                .btn-icon {
                    background-image: url("@/assets/voucher/prev.png");
                }

                &:hover {
                    color: var(--main-color);
                    .btn-icon {
                        background-image: url("@/assets/voucher/prev-hover.png");
                    }
                }

                &.disabled {
                    color: #999;
                    .btn-icon {
                        background-image: url("@/assets/voucher/prev-disabled.png");
                    }
                }
            }

            .next-btn {
                margin-left: 20px;
                .btn-icon {
                    background-image: url("@/assets/voucher/next.png");
                }
                .btn-text {
                    padding-right: 0;
                }

                &:hover {
                    color: var(--main-color);
                    .btn-icon {
                        background-image: url("@/assets/voucher/next-hover.png");
                    }
                }

                &.disabled {
                    color: #999;
                    .btn-icon {
                        background-image: url("@/assets/voucher/next-disabled.png");
                    }
                }
            }
        }
    }
}
.voucher-settings-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .voucher-settings-content {
        padding: 40px 60px;
        display: flex;
        flex-direction: column;

        .voucher-settings-item {
            display: flex;
            align-items: center;

            .item-title {
                display: flex;
                width: 200px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                
                .voucher-help {
                    width: 14px;
                    height: 14px;
                    cursor: pointer;
                    background-repeat: no-repeat;
                    background-size: 100%;
                    background-image: url("@/assets/Voucher/help.png");

                    &:hover {
                        background-image: url("@/assets/Voucher/help-hover.png");
                    }
                }
            }

            & + .voucher-settings-item {
                margin-top: 16px;
            }
        }
    }
}
</style>
