import type { VoucherEntryModel } from "@/components/Voucher/types";

export interface IVoucherRecyclebin {
    rid: number;
    voucherModel: IVoucherModel;
}

export interface IVoucherModel {
    pid: number;
    vid: number;
    vgName: string;
    vnum: number;
    vdate: string;
    vtype: number;
    attachments: number;
    preparedBy: string;
    preparedDate: string;
    approvedBy: string;
    approvedDate: string;
    approveStatus: number;
    note: string;
    status: number;
    faStatus: number;
    vstatus: number;
    isContaintedFaAsub: boolean;
    voucherLines: Array<IVoucherLine>;
    source: IVoucherSource;
    sourceWay: Array<number>;
    rf: Array<IRf>;
}
export interface IVoucherSource {
    [k: string]: Array<any>;
}

export interface IRf {
    pid: string;
    vid: string;
    vdate: string;
    vgid: string;
    vnum: string;
}

export interface IVoucherLine {
    vid: number;
    veId: number;
    aacode: string;
    assistingAccounting: number;
    asubId: number;
    asubCode: string;
    asubName: string;
    asubAAName: string;
    debit: number;
    credit: number;
    description: string;
    descriptionForPc: string;
    fcId: number;
    fcCode: string;
    fcName: string;
    fcAmount: number;
    fcRate: number;
    foreigncurrency: number;
    measureUnit: string;
    quantity: number;
    price: number;
    quantityAccounting: number;
}

export interface IVoucherGroup {
    id: number;
    isDefault: boolean;
    name: string;
    title: string;
}

export interface ISearchParams {
    description: string;
    asubCode: string;
    startAmount: string;
    endAmount: string;
    selectorType: string;
    startPId: number;
    endPId: number;
    startDate: string;
    endDate: string;
    approvalStatus: string;
    vgId: string;
    prepareBy: string;
    startVNum: string;
    endVNum: string;
    sortColumn: string;
    sortType: string;
    note: string;
    searchInfo: string;
    onlyTempVoucher: boolean;
    aaType: string;
    aaeid: string;
    quantityType: string;
    fcType: string;
    asubDirection: string;
}

export interface ISubjectItem {
    label: string;
    value: number;
}

export interface ICombineInfo {
    debit: boolean;
    credit: boolean;
    selectList: Array<IVoucherModel>;
}

export interface IMergeBack {
    voucherLines: VoucherEntryModel[];
    attachFileIds: string;
    attachFiles: any[];
    pid: number;
    vid: number;
    vgId: number;
    vgName: string;
    vnum: number;
    vdate: string;
    vtype: number;
    attachments: number;
    preparedBy: string;
    preparedDate: string;
    approvedBy: string;
    approvedDate: string;
    approveStatus: number;
    note: string;
    pstatus: number;
}
