
import { Option } from "@/components/SelectCheckbox/types";

export const selectKey = "journalSelect";
export interface ITableItem {
    j_type: string;
    cd_account: string;
    cd_account_in: string;
    line_sn: string;
    ac_no: string;
    ac_name: string;
    bank_account: string;
    line_sn_name: string;
    cd_date: string;
    fc_name: string;
    description: string;
    ie_type: string;
    ie_type_name: string;
    income: string;
    income_rate: string;
    income_standard: string;
    expenditure: string;
    expenditure_rate: string;
    expenditure_standard: string;
    amount: string;
    amount_rate: string;
    amount_standard: string;
    amount_data: string;
    amountstd_data: string;
    opposite_party: string;
    opposite_party_no: string;
    opposite_party_uscc: string;
    opposite_party_int: string;
    opposite_party_bank: string;
    payment_method: string;
    payment_method_name: string;
    receipt_no: string;
    note: string;
    flag: string;
    checked: string;
    erp_offset: string;
    type: string;
    created_date: string;
    tran_no: string;
    modified_date: string;
    p_id: string;
    v_id: string;
    v_num: string;
    v_date: string;
    v_num2: string;
    receiptCount: string;
    project: string;
    project_name: string;
    department: string;
    department_name: string;
    index: number;
    showAll?: boolean;
    onlyShowDelete?: boolean;
    changeDate?: boolean;
    oldCdDate?: string;
    oldCDAccount?: string;
    defaultClickItem?: boolean;
    isCopyData?: boolean;
    isInsertData?: boolean;
    changeDataPut?: boolean;
    originalDate?: string;
    showFromBankReceiptMessage?: string;
    canMatchIEType?: boolean;
    [selectKey]?: boolean;
}

export interface IIETypeItem {
    subkey: string;
    value1: string;
    value2: string;
    subsubkey?: string;
    haschild?: number;
}

export interface IPaymentMethodItem {
    id: string;
    value: string;
    purview: "True" | "False";
}

export interface ICompanyItem {
    aaAcronym: string;
    aaName: string;
    aaNum: string;
    address: string;
    contact: string;
    mobilePhone: string;
    note: string;
    option: boolean;
    taxNumber: string;
    type: string;
    aaeId: number;
}

export interface IProjectItem {
    department: string;
    owner: string;
    mobilePhone: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}

export interface IDepartmentItem {
    manager: string;
    mobilePhone: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}

export interface IPayMethod {
    subkey: string;
    value1: string;
    value2: string;
    matchKeys: Array<string>;
}

export interface IRowItemEditInfo {
    CD_DATE: string;
    DESCRIPTION: string;
    IE_TYPE: string;
    OPPOSITE_PARTY: string;
    OPPOSITE_PARTY_USCC: string;
    OPPOSITE_PARTY_NO: string;
    OPPOSITE_PARTY_INT: string;
    PROJECT: string;
    DEPARTMENT: string;
    INCOME: string;
    INCOME_RATE: string;
    INCOME_STANDARD: string;
    EXPENDITURE: string;
    EXPENDITURE_RATE: string;
    EXPENDITURE_STANDARD: string;
    PAYMENT_METHOD: string;
    RECEIPT_NO: string;
    NOTE: string;
    AAE_ID: string;
    ACCOUNT: string;
    ACCOUNT_NAME: string;
    ACCOUNT_NO: string;
    IE_TYPE_NAME: string;
    OPPOSITE_PARTY_BANK: string;
}

export interface IGenerateVoucherPreCheckResult {
    //结果
    result: boolean;
    //错误消息
    msg: string;
    //错误类型 Account|NoTemplate|ErrorTemplate
    errorType: "Account" | "NoTemplate" | "ErrorTemplate";
    //错误账号类型
    acType: number;
    //错误账号id
    acId: number;
    //收入/支出
    ietype: number;
    //收支类别Id
    ietypeId: number;
    //凭证模板Id
    templateId: number;
}

export interface IJournalBack {
    asId: number;
    j_type: string;
    cd_account: string;
    cd_no: string;
    cd_accountname: string;
    line_sn: string;
    cd_date: string;
    cdDateText: string;
    desription: string;
    ie_type: string;
    ie_typename: string;
    income: number;
    expenditure: number;
    opposite_party: string;
    opposite_party_uscc: string;
    oppositePartyText: string;
    payment_method: string;
    paymentMethodText: string;
    receipt_no: string;
    note: string;
    isIncome: boolean;
    createdDate: string;
    income_standard: number;
    expenditure_standard: number;
    fc_rate: number;
    opposite_party_bank: string;
}

export interface ICashierSubject {
    asubId: number;
    asubNum: string;
    acId: number;
    acName: string;
    bankAccount: string;
}

export interface IJournalImportResult {
    result: string;
    error: string;
    startDate: string;
    endDate: string;
    acid: number;
    acstate: number;
}

export interface IUnitUpdateParams {
    opposite_party: string;
    opposite_party_int: number;
    departmentId?: string;
}

interface IBaseSaveAAParams {
    code: string;
    name: string;
    note: string;
}
export class ProjectSaveEmptyParams {
    constructor(params: IBaseSaveAAParams) {
        const { code, name, note } = params;
        this.aaNum = code;
        this.aaName = name;
        this.entity.note = note.trim();
    }
    aaNum = "";
    aaName = "";
    uscc = "";
    status = 0;
    hasEntity = true;
    ifvoucher = true;
    entity = {
        epartment: "",
        owner: "",
        mobilePhone: "",
        startDate: "",
        endDate: "",
        note: "",
    };
}

export class DepartmentSaveEmptyParams {
    constructor(params: IBaseSaveAAParams) {
        const { code, name, note } = params;
        this.aaNum = code;
        this.aaName = name;
        this.entity.note = note.trim();
    }
    aaNum = "";
    aaName = "";
    uscc = "";
    status = 0;
    hasEntity = true;
    ifvoucher = true;
    entity = {
        manager: "",
        mobilePhone: "",
        startDate: "",
        endDate: "",
        note: "",
    };
}
export class EmployeeSaveEmptyParams {
    constructor(params: IBaseSaveAAParams) {
        const { code, name, note } = params;
        this.aaNum = code;
        this.aaName = name;
        this.entity.note = note.trim();
    }
    aaNum = "";
    aaName = "";
    uscc = "";
    status = 0;
    hasEntity = true;
    ifvoucher = true;
    entity = {
        birthday: "",
        departmentId: "",
        departmentName: "",
        endDate: "",
        gender: "1",
        mobilePhone: "",
        note: "",
        position: "",
        startDate: "",
        title: "",
    };
}
export enum CashIEType {
    Income = 10050,
    Expenditure = 10060,
}

export enum CashAAType {
    IEType = 10000,
    Costumer = 10001,
    Vendor = 10002,
    Employee = 10003,
    Department = 10004,
    Project = 10005,
    Description = 10006,
    Unit = 30006, // 10001 + 10002 + 10003
    PayMethod = 40001,
}

export type ImportType = "normal" | "bank" | "alipay" | "wechat" | "enterpriseWechat" | "subsidiaryLedger" | "receipt" | "";

interface IArrayLineSn {
    cdAccount: string;
    date: string;
    created_date: string;
    line_sn: string;
}

export interface IParameters {
    lineSnList: Array<IArrayLineSn>;
    skip: number;
}

export interface IDifferenceInfo {
    differentAmount: number;
    differentAmountStandard: number;
    differentIncome: number;
    differentIncomeStandard: number;
    differentExpenditure: number;
    differentExpenditureStandard: number;
}

export interface ISearchInfo {  
    startPid: string;  
    endPid: string;  
    startVDate: string;  
    endVDate: string;  
    vgId: number;  
    startVNum: string;  
    endVNum: string;  
    IE_TYPE: number[];  
    PAYMENT_METHOD: number[];  
    OPPOSITE_PARTY: string;
    RECEIPT_NO: string;
    PROJECT: number[];
    DEPARTMENT: number[];
    DESCRIPTION: string;
    NOTE: string;
    Amount_s: string;
    Amount_e: string;
    checkDate: string;
    startDate: string;
    currencyType: string;
    HasVoucher: string; 
    TRAN_NO: string;
} 

export interface ISOption {  
    IE_TYPE: Option[];  
    PAYMENT_METHOD: Option[];  
    OPPOSITE_PARTY: Option[];
    PROJECT: Option[];
    DEPARTMENT: Option[];
} 
export interface IFSearchItem {  
    DESCRIPTION: string;
    NOTE: string;
    RECEIPT_NO: string;
    IE_TYPE: number[];  
    PAYMENT_METHOD: number[];  
    OPPOSITE_PARTY: "";
    PROJECT: number[];
    DEPARTMENT: number[];
} 
