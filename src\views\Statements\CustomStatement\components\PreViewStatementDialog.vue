<template>
    <el-dialog
        v-model="preViewDialogShow"
        :title="`自定义报表 (${direction === 1 ? '纵' : '横'}向预览)`"
        width="1098px"
        class="preview-statement-module-dialog custom-confirm dialogDrag"
        destroy-on-close
    >
        <div class="dialog-content" v-dialogDrag>
            <div class="preview-statement-module-dialog-container">
                <div class="tool-bar"><a class="button" @click="preViewDialogShow = false">关闭</a>单位：元</div>
                <div class="preview-table-container">
                    <Table
                        class="subject-table"
                        ref="accountSubjectTableRef"
                        :data="tableData"
                        :columns="columns"
                        :fit="true"
                        v-if="direction === 1"
                        max-height="370"
                        :scrollbar-show="true"
                        :highlight-current-row="false"
                        :show-overflow-tooltip="true"
                    >
                        <template #append v-if="needTotalLine">
                            <tr class="el-table__row">
                                <td class="el-table__cell sum-cell">合计</td>
                            </tr>
                        </template>
                    </Table>
                    <Table
                        class="preview-table"
                        ref="preViewTable2"
                        :data="columns"
                        :columns="columnsTransverse"
                        :fit="true"
                        :show-header="false"
                        v-else
                        max-height="370"
                        :scrollbar-show="true"
                        :highlight-current-row="false"
                        :show-overflow-tooltip="true"
                    >
                        <template #headerCol>
                            <el-table-column min-width="148" align="left" header-align="center" class-name="col-header" :resizable="false">
                                <template #default="scope">
                                    {{ scope.row.label }}
                                </template>
                            </el-table-column>
                        </template>
                        <template v-for="item in tableData" #[item.asubId]  :key="item.asubId">
                            <el-table-column min-width="148" align="left" header-align="center" :resizable="false">
                                <template #default="scope">
                                    {{ item[scope.row.prop] }}
                                </template>
                            </el-table-column>
                        </template>
                        <template v-if="needTotalLine" #totalCol>
                            <el-table-column min-width="148" align="left" header-align="center" :resizable="false">
                                <template #default="scope">
                                    {{ scope.row.label === "科目编码" ? "合计" : "" }}
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, toRef, watchEffect } from "vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import type { ICreateTemplateColumnsRight, ICreateTemplateTable } from "../types";
const accountSubjectStore = useAccountSubjectStore();
const subjectDataAll = toRef(accountSubjectStore, "accountSubjectListWithoutDisabled");
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    previewColumns: {
        type: Array<ICreateTemplateColumnsRight>,
        required: true,
    },

    previewTableData: {
        type: Array<ICreateTemplateTable>,
        default: [],
    },
    direction: {
        type: Number,
        default: 1,
    },
    addChildAsubs: {
        type: Boolean,
        required: true,
    },
    addLeafAsubs: {
        type: Boolean,
        required: true,
    },
    needTotalLine: {
        type: Boolean,
        required: true,
    },
});
const emits = defineEmits(["update:modelValue", "addAsubSure"]);

const preViewDialogShow = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
const columnsTransverse = ref([{ slot: "headerCol" }]);
const preViewTable2 = ref();
const columns = computed((): IColumnProps[] => {
    return props.previewColumns.slice().map((v: ICreateTemplateColumnsRight, i: number) => {
        const column: any = {};
        for (const key in v) {
            if (key === "width") {
                column.minWidth = v[key];
            } else {
                column[key] = (v as any)[key];
            }
        }
        column.prop = v.property;
        return column;
    });
});
const tableData = computed((): IAccountSubjectModel[] => {
    let targetData: IAccountSubjectModel[] = [];
    props.previewTableData.forEach((v: any, i: number) => {
        if (props.addLeafAsubs) {
            subjectDataAll.value.filter((item: IAccountSubjectModel) => {
                if (item.asubCode.indexOf(v.asubCode) === 0) {
                    targetData.push(item);
                    return item;
                }
            });
        } else if (props.addChildAsubs) {
            subjectDataAll.value.filter((item: IAccountSubjectModel) => {
                if (item.asubCode.indexOf(v.asubCode) === 0 && item.asubLevel <= v.asubLevel + 1) {
                    targetData.push(item);
                    return item;
                }
            });
        } else {
            targetData.push(v);
        }
    });
    return targetData;
});
watchEffect(() => {
    if (props.direction === 2) {
        columnsTransverse.value = [{ slot: "headerCol" }];
        tableData.value.forEach((v, i) => {
            columnsTransverse.value.push({ slot: String(v.asubId) });
        });
        if (props.needTotalLine) {
            columnsTransverse.value.push({ slot: "totalCol" });
        }
    }
});
</script>

<style lang="less" scoped>
.preview-statement-module-dialog {
    .preview-statement-module-dialog-container {
        .tool-bar {
            padding: 16px 20px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            line-height: 22px;
            text-align: right;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .preview-table-container {
            padding: 0 20px 16px;
            max-height: 370px;
            :deep(.el-table td.el-table__cell.sum-cell) {
                border: none;
                padding-left: 10px;
            }
            :deep(.el-table__empty-block) {
                width: 1000px !important;
                min-height: 200px;
            }
            .preview-table {
                :deep(.col-header) {
                    background-color: var(--table-title-color);
                    font-weight: bold;
                }
            }
        }
    }
}
</style>
