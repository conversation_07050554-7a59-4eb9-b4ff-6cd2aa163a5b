<template>
    <el-dialog v-model="fetchInvoiceTaskShow" :title="'一键取票记录'" center width="950" class="dialogDrag">
        <div class="fetch-invoice-task-box" v-dialogDrag>
            <div class="fetch-invoice-task-box-body">
                <div class="tips">
                    以下为您一键获取{{ invoiceCategoryText }}发票的记录，请您仔细核对。温馨提示：请您注意获取时间和开票日期，避免重复提交
                </div>
                <Table
                    :data="invoiceTaskList"
                    :columns="columns"
                    :page-is-show="true"
                    :scrollbarShow="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :maxHeight="300"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    @refresh="handleRerefresh"
                    :objectSpanMethod="spanMethod"
                    :showOverflowTooltip="true"
                    :tableName="setModule"
                    @cell-mouse-enter="mouseEnter"
                    @cell-mouse-leave="mouseLeave"
                    :row-class-name="rowClassName"
                >
                    <template #modifiedDate>
                        <el-table-column
                            label="获取日期" 
                            min-width="80" 
                            align="left" 
                            header-align="left" 
                            prop="modifiedDate"
                            :width="getColumnWidth(setModule, 'modifiedDate')"
                        >
                            <template #default="scope">
                                <span>
                                    {{ dayjs(scope.row.modifiedDate).format("YYYY-MM-DD HH:mm") }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceDate>
                        <el-table-column 
                            label="开票日期" 
                            min-width="120" 
                            align="left" 
                            header-align="left"
                            prop="invoiceDate" 
                            :width="getColumnWidth(setModule, 'invoiceDate')"
                        >
                            <template #default="scope">
                                <span>
                                    {{ parseDateText(scope.row.startDate) }} 至
                                    {{ parseDateText(scope.row.endDate) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceStatus>
                        <el-table-column 
                            label="发票状态" 
                            min-width="50" 
                            align="left" 
                            header-align="left"
                            prop="invoiceStatus"
                            :width="getColumnWidth(setModule, 'invoiceStatus')"
                        >
                            <template #default="scope">
                                <span> {{ invoiceStatusEnum[scope.$index % 3] }}发票 </span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceCount>
                        <el-table-column 
                            label="份数" 
                            min-width="40" 
                            align="left" 
                            header-align="left"
                            prop="invoiceCount" 
                            :width="getColumnWidth(setModule, 'invoiceCount')"
                        >
                            <template #default="scope">
                                <span v-if="invoiceCategory === '10070'">{{ scope.row.salesCount }}</span>
                                <span v-else-if="invoiceCategory === '10080'">{{ scope.row.purchaseCount }}</span>
                                <span v-else>{{ scope.row.salesCount + scope.row.purchaseCount }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceAmount>
                        <el-table-column 
                            label="价税合计（元）" 
                            min-width="80" 
                            align="left" 
                            header-align="left" 
                            prop="invoiceAmount"
                            :width="getColumnWidth(setModule, 'invoiceAmount')"
                        >
                            <template #default="scope">
                                <span>{{
                                        formatMoney(
                                            invoiceCategory === "10070"
                                                ? scope.row.salesTotal
                                                : invoiceCategory === "10080"
                                                ? scope.row.purchaseTotal
                                                : scope.row.salesTotal + scope.row.purchaseTotal, true, true
                                        )
                                    }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #invoiceTaskStatus>
                        <el-table-column 
                            label="获取发票" 
                            min-width="60" 
                            align="left" 
                            header-align="left"
                            prop="invoiceTaskStatus"
                            :width="getColumnWidth(setModule, 'invoiceTaskStatus')"
                        >
                            <template #default="scope">
                                <span v-if="scope.row.invoiceTaskStatus === 0 && scope.row.status < 2">{{
                                        invoiceTaskStatusEnum[scope.row.status]
                                    }}</span>
                                    <el-tooltip
                                        v-else-if="scope.row.invoiceTaskStatus === 0 && scope.row.status >= 2"
                                        effect="light"
                                        placement="bottom-start"
                                    >
                                        <template #content>失败原因：{{ scope.row.remark }}</template
                                        ><span>
                                            <img class="status-icon" src="@/assets/Invoice/warning.png" />
                                            {{ invoiceTaskStatusEnum[scope.row.status] }}</span
                                        >
                                    </el-tooltip>
                                    <span v-else-if="scope.row.invoiceTaskStatus < 2">{{
                                        invoiceTaskStatusEnum[scope.row.invoiceTaskStatus]
                                    }}</span>
                                    <span v-else-if="scope.row.invoiceTaskStatus === 2">
                                        <img class="status-icon" src="@/assets/Invoice/pass.png" />
                                        {{ invoiceTaskStatusEnum[scope.row.invoiceTaskStatus] }}</span
                                    >
                                    <el-tooltip v-else effect="light" placement="bottom-start">
                                        <template #content>失败原因：{{ scope.row.invoiceTaskRemark }}</template
                                        ><span>
                                            <img class="status-icon" src="@/assets/Invoice/warning.png" />
                                            {{ invoiceTaskStatusEnum[scope.row.invoiceTaskStatus] }}</span
                                        >
                                    </el-tooltip>
                            </template>
                        </el-table-column>
                    </template>
                    <template #deductionTaskStatus>
                        <el-table-column 
                            label="获取勾选认证" 
                            min-width="70" 
                            align="left" 
                            header-align="left"
                            prop="deductionTaskStatus"
                            :width="getColumnWidth(setModule, 'deductionTaskStatus')"
                        >
                            <template #default="scope">
                                <span v-if="scope.row.deductionTaskStatus === 0"> --- </span>
                                    <span v-else-if="scope.row.deductionTaskStatus === 1">{{
                                        invoiceTaskStatusEnum[scope.row.deductionTaskStatus]
                                    }}</span>
                                    <span v-else-if="scope.row.deductionTaskStatus === 2">
                                        <img class="status-icon" src="@/assets/Invoice/pass.png" />
                                        {{ invoiceTaskStatusEnum[scope.row.deductionTaskStatus] }}</span
                                    >
                                    <span
                                        v-else-if="
                                            scope.row.deductionTaskStatus === 3 &&
                                            scope.row.deductionTaskRemark.indexOf('本功能仅提供') >= 0
                                        "
                                        >---
                                    </span>
                                    <el-tooltip v-else effect="light" placement="bottom-start">
                                        <template #content>失败原因：{{ scope.row.deductionTaskRemark }}</template
                                        ><span>
                                            <img class="status-icon" src="@/assets/Invoice/warning.png" />
                                            {{ invoiceTaskStatusEnum[scope.row.deductionTaskStatus] }}</span
                                        >
                                    </el-tooltip>
                            </template>
                        </el-table-column>
                    </template>
                    <template #fileTaskStatus>
                        <el-table-column 
                            label="获取原件" 
                            min-width="60" 
                            align="left" 
                            header-align="left"
                            prop="fileTaskStatus"
                            :width="getColumnWidth(setModule, 'fileTaskStatus')" 
                        >
                            <template #default="scope">
                                <span v-if="scope.row.fileTaskStatus === 0"> --- </span>
                                    <el-tooltip v-else-if="scope.row.fileTaskStatus === 1" effect="light" placement="bottom-start">
                                        <template #content>数电发票的发票原件获取时间较长，正在持续同步获取中，请稍后</template
                                        ><span>
                                            <img class="status-icon" src="@/assets/Invoice/warning.png" />
                                            {{ invoiceTaskStatusEnum[scope.row.fileTaskStatus] }}</span
                                        >
                                    </el-tooltip>
                                    <span v-else-if="scope.row.fileTaskStatus === 2">
                                        <img class="status-icon" src="@/assets/Invoice/pass.png" />
                                        {{ invoiceTaskStatusEnum[scope.row.fileTaskStatus] }}</span
                                    >
                                    <el-tooltip v-else effect="light" placement="bottom-start">
                                        <template #content>失败原因：{{ scope.row.fileTaskRemark }}</template
                                        ><span>
                                            <img class="status-icon" src="@/assets/Invoice/warning.png" />
                                            {{ invoiceTaskStatusEnum[scope.row.fileTaskStatus] }}</span
                                        >
                                    </el-tooltip>
                            </template>
                        </el-table-column>
                    </template>
                    <template #createdByName>
                        <el-table-column 
                            label="操作人" 
                            min-width="80" 
                            align="left" 
                            header-align="left" 
                            :resizable="false"
                        >
                            <template #default="scope">
                                <span> {{ scope.row.createdByName }} </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="fetch-invoice-task-box-footer buttons">
                <a @click="fetchInvoiceTaskShow = false" class="button solid-button">知道了</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import type { IInvoiceTaskDisplay, IInvoiceTaskResult, IInvoiceTaskResultList, IInvoiceTaskValue } from "../types";
import Table from "@/components/Table/index.vue";
import dayjs from "dayjs";
import { usePagination } from "@/hooks/usePagination";
import { convertDeductionTaskRemark, convertFileTaskRemark, convertInvoiceTaskRemark } from "../utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { formatMoney } from "@/util/format";

const setModule = "FetchInvoiceTask";
const columns = [
    { slot: "modifiedDate" },
    { slot: "invoiceDate" },
    { slot: "invoiceStatus" },
    { slot: "invoiceCount" },
    { slot: "invoiceAmount" },
    { slot: "invoiceTaskStatus" },
    { slot: "deductionTaskStatus" },
    { slot: "fileTaskStatus" },
    { slot: "createdByName" },
];

const invoiceStatusEnum = ["正常", "作废", "红冲"];
const invoiceTaskStatusEnum = ["状态异常", "获取中", "获取成功", "获取失败", "获取超时"];

const props = defineProps({
    invoiceCategory: {
        type: String,
        required: true,
    },
    fetchInvoiceTaskShow: {
        type: Boolean,
        default: false,
    },
});

let invoiceCategory = computed(() => {
    return props.invoiceCategory;
});
let invoiceCategoryText = computed(() => {
    return invoiceCategory.value === "10070" ? "销项" : invoiceCategory.value === "10080" ? "进项" : "进销项";
});
const spanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
    if (columnIndex < 2 || 4 < columnIndex) {
        if (rowIndex % 3 === 0) {
            return {
                rowspan: 3,
                colspan: 1,
            };
        } else {
            return {
                rowspan: 0,
                colspan: 0,
            };
        }
    }
};

const emit = defineEmits(["update:fetchInvoiceTaskShow"]);
const fetchInvoiceTaskShow = computed({
    get() {
        return props.fetchInvoiceTaskShow;
    },
    set(value) {
        emit("update:fetchInvoiceTaskShow", value);
    },
});

const invoiceTaskList = ref<Array<IInvoiceTaskDisplay>>();

const parseDateText = (date: number) => {
    let dateText = date.toString();
    return dateText.substring(0, 4) + "-" + dateText.substring(4, 6) + "-" + dateText.substring(6, 8);
};

onMounted(() => {
    paginationData.pageSizes = [10];
    paginationData.pageSize = 10;
});

watch(
    () => props.fetchInvoiceTaskShow,
    (newVal: boolean) => {
        if (newVal) {
            loadTableData();
        }
    }
);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], (v) => {
    loadTableData();
});

function loadTableData() {
    let params: ISearchParams = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        status: 0,
    };
    request({
        url: `/api/TaxBureau/GetSummaryTask`,
        params,
    }).then((res: IResponseModel<IInvoiceTaskResultList>) => {
        if (res.state === 1000) {
            const tempList = res.data.data;
            let list: Array<IInvoiceTaskDisplay> = [];

            paginationData.total = res.data.count;

            if (tempList && tempList.length > 0) {
                for (let i = 0; i < tempList.length; i++) {
                    list.push({
                        asId: tempList[i].asId,
                        createdBy: tempList[i].createdBy,
                        createdByName: tempList[i].createdByName,
                        createdDate: tempList[i].createdDate,
                        startDate: tempList[i].startDate,
                        endDate: tempList[i].endDate,
                        modifiedDate: tempList[i].modifiedDate,
                        status: tempList[i].status,
                        remark: convertInvoiceTaskRemark(tempList[i].remark),
                        invoiceTaskStatus: tempList[i].invoiceTaskStatus,
                        invoiceTaskRemark: convertInvoiceTaskRemark(tempList[i].invoiceTaskRemark),
                        deductionTaskStatus: tempList[i].deductionTaskStatus,
                        deductionTaskRemark: convertDeductionTaskRemark(tempList[i].deductionTaskRemark),
                        fileTaskStatus: tempList[i].fileTaskStatus,
                        fileTaskRemark: convertFileTaskRemark(tempList[i].fileTaskRemark),
                        purchaseTotal: tempList[i].purchaseNormalTotal,
                        purchaseTax: tempList[i].purchaseNormalTax,
                        purchaseAmount: tempList[i].purchaseNormalAmount,
                        purchaseCount: tempList[i].purchaseNormalCount,
                        salesTotal: tempList[i].salesNormalTotal,
                        salesTax: tempList[i].salesNormalTax,
                        salesAmount: tempList[i].salesNormalAmount,
                        salesCount: tempList[i].salesNormalCount,
                    });

                    list.push({
                        asId: tempList[i].asId,
                        createdBy: tempList[i].createdBy,
                        createdByName: tempList[i].createdByName,
                        createdDate: tempList[i].createdDate,
                        startDate: tempList[i].startDate,
                        endDate: tempList[i].endDate,
                        modifiedDate: tempList[i].modifiedDate,
                        status: tempList[i].status,
                        remark: convertInvoiceTaskRemark(tempList[i].remark),
                        invoiceTaskStatus: tempList[i].invoiceTaskStatus,
                        invoiceTaskRemark: convertInvoiceTaskRemark(tempList[i].invoiceTaskRemark),
                        deductionTaskStatus: tempList[i].deductionTaskStatus,
                        deductionTaskRemark: convertDeductionTaskRemark(tempList[i].deductionTaskRemark),
                        fileTaskStatus: tempList[i].fileTaskStatus,
                        fileTaskRemark: convertFileTaskRemark(tempList[i].fileTaskRemark),
                        purchaseTotal: tempList[i].purchaseInvalidTotal,
                        purchaseTax: tempList[i].purchaseInvalidTax,
                        purchaseAmount: tempList[i].purchaseInvalidAmount,
                        purchaseCount: tempList[i].purchaseInvalidCount,
                        salesTotal: tempList[i].salesInvalidTotal,
                        salesTax: tempList[i].salesInvalidTax,
                        salesAmount: tempList[i].salesInvalidAmount,
                        salesCount: tempList[i].salesInvalidCount,
                    });

                    list.push({
                        asId: tempList[i].asId,
                        createdBy: tempList[i].createdBy,
                        createdByName: tempList[i].createdByName,
                        createdDate: tempList[i].createdDate,
                        startDate: tempList[i].startDate,
                        endDate: tempList[i].endDate,
                        modifiedDate: tempList[i].modifiedDate,
                        status: tempList[i].status,
                        remark: convertInvoiceTaskRemark(tempList[i].remark),
                        invoiceTaskStatus: tempList[i].invoiceTaskStatus,
                        invoiceTaskRemark: convertInvoiceTaskRemark(tempList[i].invoiceTaskRemark),
                        deductionTaskStatus: tempList[i].deductionTaskStatus,
                        deductionTaskRemark: convertDeductionTaskRemark(tempList[i].deductionTaskRemark),
                        fileTaskStatus: tempList[i].fileTaskStatus,
                        fileTaskRemark: convertFileTaskRemark(tempList[i].fileTaskRemark),
                        purchaseTotal: tempList[i].purchaseRedTotal,
                        purchaseTax: tempList[i].purchaseRedTax,
                        purchaseAmount: tempList[i].purchaseRedAmount,
                        purchaseCount: tempList[i].purchaseRedCount,
                        salesTotal: tempList[i].salesRedTotal,
                        salesTax: tempList[i].salesRedTax,
                        salesAmount: tempList[i].salesRedAmount,
                        salesCount: tempList[i].salesRedCount,
                    });
                }
            }

            invoiceTaskList.value = list;
        } else {
            invoiceTaskList.value = [];
        }
    });
}

interface ISearchParams {
    pageIndex: number;
    pageSize: number;
    status: number;
}

const rowClassName = (data: { row: any; rowIndex: number }) => {
    if (hoverRow.value && dayjs(data.row.modifiedDate).format("YYYY-MM-DD HH:mm") === dayjs(hoverRow.value?.modifiedDate).format("YYYY-MM-DD HH:mm")) {
        return "hover";
    }
};
const hoverRow = ref();
const mouseEnter = (row: any, column: any, cell: HTMLTableCellElement, event: Event) => {
    hoverRow.value = row;
};

const mouseLeave = () => {
    hoverRow.value = undefined;
};
</script>

<style lang="less" scoped>
.fetch-invoice-task-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: stretch;

    .fetch-invoice-task-box-body {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: stretch;
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        text-align: center;
        min-height: 200px;
        padding: 20px;

        .tips {
            margin-bottom: 20px;
            text-align: left;
        }
        .status-icon {
            margin-right: 2px;
            width: 16px;
            vertical-align: middle;
        }

        :deep(.custom-table tbody tr) {
            td {
                .cell {
                    padding: 0 4px;
                    font-size: 12px;
                    white-space: nowrap;
                }
            }
        }
        :deep(.el-table--enable-row-hover .el-table__body tr) {
            &.hover {
                td.el-table__cell {
                    background-color: var(--table-hover-color);
                }
            }
        }
    }

    .fetch-invoice-task-box-footer {
        padding: 20px 0;
        text-align: center;
    }
}
</style>
