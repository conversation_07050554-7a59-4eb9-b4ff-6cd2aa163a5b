<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:acid="autoBankForm.acid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :order-account-show="true"
        :bank-name="props.bankName"
        :uscc-number="props.usccNumber"
        @save-success="saveSuccessHandle"
        @confirm-bank="confirmBank"
    />
    <div class="slot-content align-center edit-temp-table" style="height: 350px" v-show="confirmTable">
        <div class="slot-content-mini edit-temp-table-content psbc-bank">
            <div class="autoBankSelect">
                <div class="autoBankItem">
                    <span style="font-weight: 600">第一步：</span>打开IE或Edge浏览器使用签约U盾登录邮政储蓄网上银行
                </div>
                <div class="autoBankItem">
                    <span style="font-weight: 600">第二步：</span>使用经办员角色登录邮政储蓄银行网银端，在“付款业务
                </div>
                <div class="autoBankItem">
                    <div style="margin-left: 56px; margin-top: -8px">-云直联--服务授权”进行签约</div>
                </div>
                <div class="autoBankItem">
                    <p style="margin-left: 56px; margin-top: -8px; font-size: 12px; color: rgba(102, 102, 102, 1)">
                        合作平台选择：深圳易财信息技术有限公司
                    </p>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第三步：</span>登录一级审核员账号，前往【首页-待审核交易】</div>
                <div class="autoBankItem">
                    <div style="margin-left: 56px; margin-top: -8px">找到对应的服务授权，进行审批</div>
                </div>
                <div class="autoBankItem">
                    <p style="margin-left: 56px; margin-top: -8px; font-size: 12px; color: rgba(102, 102, 102, 1)">交易类型为：服务授权</p>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第四步：</span>在柠檬云中点击立即授权，确定完成授权</div>
            </div>
            <div class="autoBankDescription">
                <div style="margin-left: 40px; padding-top: 20px; line-height: 16px">
                    <span style="font-weight: 500">邮政储蓄银行网银地址：</span>
                    <span class="copy-file copy-key" data-target="psbc-apply-url" @click="handleCopy">复制</span>
                </div>
                <p class="copy-key pub-key" data-target="psbc-apply-url" @click="handleCopy">https://corpebank.psbc.com</p>
            </div>
            <div class="authButton" style="margin-bottom: 20px; margin-top: 50px">
                <a class="button back-button" attr-click="1" @click="handleBack">上一步</a>
                <a class="solid-button-large" attr-click="1" @click="JumpToBankAuthorizePage">立即授权</a>
            </div>
            <div class="autoBankToOpen" style="height: 16px">&nbsp;</div>
            <CommonBottom></CommonBottom>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watchEffect, onMounted, watch, nextTick } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { BankType } from "@/constants/bankKey";
import { GetBankLink } from "@/util/bankType";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams } from "@/util/url";
import { appendStyle, copyText, handleCheck, upgradeApi, getSuccessMsg } from "../utils";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, IBankHandleResult, CMBGradeResult } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import CommonBottom from "./CommonBottom.vue";
import { replaceAll } from "@/util/common";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = [
    "1、请确认公司名称和统一社会信用代码",
    "2、选择对应的银行账户，并确认对应的银行账号",
    "3、在电脑上插入“邮政储蓄银行”U盾",
    "4、点击立即授权",
];

const props = defineProps({
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    bankName: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const currencyList = computed(() => props.currencyList);
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const impowerValue = ref("立即授权");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    mainaccno: "",
});

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "申请中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};
let canNextAuthorized = true;
const authorizedText = ref("立即授权");
const handleAuthorizedLock = () => {
    canNextAuthorized = false;
    authorizedText.value = "申请中...";
};
const handleAuthorizedUnLock = () => {
    canNextAuthorized = true;
    authorizedText.value = "立即授权";
};
const confirmTable = ref(false);
const handleBack = () => {
    showMain.value = true;
    confirmTable.value = false;
    handleAuthorizedUnLock();
    handleConfirmUnLock();
};
const handleToConfirm = () => {
    showMain.value = false;
    confirmTable.value = true;
    handleConfirmUnLock();
};
const confirmBank = () => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    handleConfirmLock();
    props.checkAuthorization(props.bankType, replaceAll(autoBankForm.mainaccno, " ", ""), () => {
        request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
                handleConfirmUnLock();
                return;
            }
            handleQuery();
        });
    });
};
const handleQuery = () => {
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Query");
    });
};

const JumpToBankAuthorizePage = () => {
    ElConfirm("是否已前往“邮储银行”完成签约确认？").then((r: boolean) => {
        upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
            if(r) {
                handleResult(res, "Apply");
            }
        });
    });  
};
function handleResult(res: IResponseModel<CMBGradeResult>, type: string) {
    const { successMsg, acctNo,  acname} =  getSuccessMsg(autoBankForm, bankAccountList.value);
    if (res.state !== 1000) {
        ElNotify({ type: "warning", message: res.msg || "授权签约失败，请重试！" });
        handleConfirmUnLock();
        return;
    }
    const result = res.data;
    if (result.status === 1 || result.status === 2) {
        //已签约成功
        ElConfirm(appendStyle(successMsg), true, () => {}, "授权成功").then(() => {
            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
        });
        handleConfirmUnLock();
        return;
    }
    if (result.status === 3 && type === "Query") { //签约中查询
        handleToConfirm();
        handleConfirmUnLock();
        return;
    } 
    if (result.status === 3 && type === "Apply") { //签约中申请
        ElNotify({ type: "warning", message: "未查询到本银行账户“" + acname + "”账号“" + acctNo + "”邮储银行的授权信息。" });
        handleConfirmUnLock();
        return;
    } 
    if (result.status === 4) {
        let errorMsg = result.message;
        if (result.data && result.data.Msg) {
            errorMsg = result.data.Msg;
        }
        ElConfirm(errorMsg || "授权失败，请重试！", true, () => {}, "授权失败");
        handleConfirmUnLock();
        return;
    }
    if (result.status === 5 || result.status === 6) {
        //其他账套成功签约/其他账套签约中
        ElConfirm(result.message, true, () => {}, "授权失败");
        handleConfirmUnLock();
        return;
    }
}

const bankHelpLink = ref("");
onMounted(() => {
    let res = GetBankLink(props.bankType);
    bankHelpLink.value = res;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});

watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);
const handleCopy = () => {
    copyText("https://corpebank.psbc.com");
    ElNotify({ type: "success", message: "复制成功" });
};

defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
</style>
