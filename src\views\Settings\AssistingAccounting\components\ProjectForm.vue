<template>
    <div class="add-content">
        <el-form :model="formList" label-width="222px" class="formRef" ref="formRef">
            <div class="isRow">
                <el-form-item label="项目编码：">
                    <div class="row">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Code, $event, '项目编码', 'aaNum', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                            v-model="formList.aaNum"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="项目名称：">
                    <div class="row">
                        <el-input
                            v-model="formList.aaName"
                            class="input-ellipsis"
                            @blur="inputTypeBlur('aaNameInputRef')"
                            v-if="aaNameTextareaShow"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            @input="limitInputLength(formList.aaName,'项目名称')"
                            @focus="inputTypeFocus()"
                            resize="none"
                            ref="aaNameInputRef"
                        />
                        <Tooltip :content="formList.aaName" :isInput="true" placement="right" v-else>    
                            <input 
                                @focus="inputTypeFocus(1)"
                                ref="aaNameInputRef"
                                type="text"
                                class="input-ellipsis"
                                v-model="formList.aaName"
                            />
                        </Tooltip>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="负责部门：">
                    <div class="row">
                    <Tooltip :content="formList.aaEntity.department" :is-input="true" placement="right"  v-if="!isErp">
                        <input
                            type="text"
                            class="input-ellipsis"
                            v-model="formList.aaEntity.department"
                            @input="handleAAInput(LimitCharacterSize.Name, $event, '负责部门', 'department', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                        />
                    </Tooltip>
                        <Select 
                            v-else 
                            v-model="formList.aaEntity.department" 
                            :teleported="false" 
                            :fit-input-width="true"
                            :filterable="true"
                            :filter-method="departFilterMethod"
                        >
                            <Option 
                                v-for="(item, index) in showdepartmentList" 
                                :key="index" 
                                :value="item.value" 
                                :label="item.label" 
                            />
                        </Select>
                    </div>
                </el-form-item>
                <el-form-item label="负责人：">
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.owner" :is-input="true" placement="right" v-if="!isErp">
                            <input
                                type="text"
                                class="input-ellipsis"
                                v-model="formList.aaEntity.owner"
                                @input="handleAAInput(LimitCharacterSize.Name, $event, '负责人', 'owner', changeFormListData)"
                                @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                            />
                        </Tooltip>
                        <Select 
                            v-else 
                            v-model="formList.aaEntity.owner" 
                            :teleported="false" 
                            :fit-input-width="true"
                            :filterable="true"
                            :filter-method="employeeFilterMethod"
                        >
                            <Option 
                                v-for="(item, index) in showemployeeList" 
                                :key="index" 
                                :value="item.value" 
                                :label="item.label" 
                            />
                        </Select>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="手机：">
                    <div class="row">
                        <input
                            type="text"
                            v-model="formList.aaEntity.mobilePhone"
                            @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机', 'mobilePhone', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Phone, $event)"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="开始日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.aaEntity.startDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateStart"
                        />
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item :label="endDateText">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.aaEntity.endDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateEnd"
                        />
                    </div>
                </el-form-item>
            </div>
            <el-form-item label="备注：">
                <div class="max">
                    <el-input
                        v-model="formList.aaEntity.note"
                        class="more-long input-ellipsis"
                        @blur="inputTypeBlur('noteInputRef')"
                        v-if="noteTextareaShow"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        type="textarea"
                        maxlength="1024"
                        @input="limitInputLength(formList.aaEntity.note,'备注')"
                        @focus="inputTypeFocus()"
                        resize="none"
                        ref="aaNameInputRef"
                    />
                    <div v-else class='note-tooltip-width'>
                        <Tooltip :content="formList.aaEntity.note" :isInput="true" placement="bottom">
                            <input
                                @focus="inputTypeFocus(2)"
                                ref="aaNameInputRef"
                                type="text"
                                v-model="formList.aaEntity.note"
                                class="more-long input-ellipsis"
                            />
                        </Tooltip>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="是否启用：" class="status">
                <el-checkbox label="启用" v-model="formList.aaEntity.status" @change="handleStatusChange" />
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">返回</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, watch, watchEffect } from "vue";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste, getNextAaNum, textareaBottom } from "../utils";
import Tooltip from "@/components/Tooltip/index.vue";
import { dayjs } from "element-plus";
import { ValidataProject } from "../validator";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import type { IProjectOrDepartmentItem } from "../type";
import Select from "@/components/Select/index.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getGlobalLodash } from "@/util/lodash";
import Option from "@/components/Option/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => (EditType = val);
const isErp = ref(window.isErp);
const _ = getGlobalLodash()
const formList = reactive<any>({
    aaNum: "",
    aaName: "",
    aaeID: "",
    USCC: "",
    hasEntity: true,
    aaEntity: {
        department: "",
        owner: "",
        mobilePhone: "",
        startDate: "",
        endDate: "",
        note: "",
        status: true,
    },
});
const changeFormListData = (key: string, val: string) => {
    const keys = Object.keys(formList);
    const aaEntityKeys = Object.keys(formList.aaEntity);
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            formList[key] = val;
            return;
        }
    }
    for (let i = 0; i < aaEntityKeys.length; i++) {
        if (aaEntityKeys[i] === key) {
            formList.aaEntity[key] = val;
            break;
        }
    }
};
const formRef = ref<any>(null);
const emit = defineEmits(["formCancel", "formChanged", "formCancelEdit"]);
const endDateText = ref("结束日期：");
if (window.isErp) {
    endDateText.value = "验收日期：";
}

let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    const aaeID = EditType === "Edit" ? formList.aaeID : 0;
    const aaNum = formList.aaNum;
    const aaName = formList.aaName;
    createCheck(10005, aaeID, aaNum, aaName, Save);
};
const aaNameTextareaShow=ref(false)
const noteTextareaShow=ref(false)
const aaNameInputRef=ref()
const inputTypeBlur = (value:string) => {
    switch (value) {
        case 'aaNameInputRef':
            aaNameTextareaShow.value = false;
            break;
        case 'noteInputRef':
            noteTextareaShow.value = false;
            break;
    }
};
const inputTypeFocus = (num?:number) => {
    textareaBottom(formRef)
    switch (num) {
        case 1:
            aaNameTextareaShow.value = true;
            break;
        case 2:
            noteTextareaShow.value = true;
            break;
    }
    nextTick(()=>{
        if(num){
            getTextareaFocus(num)
        }
    })
};
const getTextareaFocus = (num:number) => {
    switch (num) {
        case 1:
        case 2:
            aaNameInputRef.value.focus();
            break;
    }
};
function limitInputLength(val: string,label: string) {
    switch (label) {
        case '备注':
            if (val.length === 1024) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过1024个字哦~` });
            }
            break;
        case '项目名称':
            if (val.length === 256) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
            }
            break;
    }
}
const Save = () => {
    const entityParams = {
        department: formList.aaEntity.department,
        owner: formList.aaEntity.owner,
        mobilePhone: formList.aaEntity.mobilePhone,
        startDate: formList.aaEntity.startDate,
        endDate: formList.aaEntity.endDate,
        note: formList.aaEntity.note,
    };
    const params = {
        entity: entityParams,
        aaNum: formList.aaNum,
        aaName: formList.aaName,
        uscc: formList.USCC,
        status: formList.aaEntity.status ? 0 : 1,
        hasEntity: formList.hasEntity,
        ifvoucher: true,
    };
    const urlPath = EditType === "Edit" ? "Project?aaeid=" + formList.aaeID : "Project";
    if (ValidataProject(entityParams, params.aaNum, params.aaName)) {
        isSaving = true;
        const backParams: any = _.cloneDeep(params);
        if (isErp.value) {
            backParams.entity.departmentId = backParams.entity.department;
            backParams.entity.ownerId = backParams.entity.owner;
            backParams.entity.department = "";
            backParams.entity.owner = "";
        }
        request({
            url: "/api/AssistingAccounting/" + urlPath,
            method: EditType === "New" ? "post" : "put",
            headers: { "Content-Type": "application/json" },
            data: JSON.stringify(isErp.value ? backParams : params),
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000 || "Failed" === res.data) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    isSaving = false;
                    return;
                }
                ElNotify({ type: "success", message: "保存成功" });
                useAssistingAccountingStore().getAssistingAccounting();
                if (EditType === "New") {
                    getNextAaNum(10005)
                        .then((res: IResponseModel<string>) => {
                            resetForm();
                            formList.aaNum = res.data;
                            emit("formCancelEdit");
                        })
                        .finally(() => {
                            isSaving = false;
                        });
                } else {
                    handleCancel();
                    isSaving = false;
                }
                window.dispatchEvent(new CustomEvent("updateAssistingAccounting"));
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
                isSaving = false;
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};
const resetForm = () => {
    const initParams = {
        aaNum: "",
        aaName: "",
        aaeID: "",
        USCC: "",
        hasEntity: true,
        aaEntity: {
            department: "",
            owner: "",
            mobilePhone: "",
            startDate: "",
            endDate: "",
            note: "",
            status: true,
        },
    };
    editForm(initParams);
};
const editForm = (data: any) => {
    Object.keys(data).forEach((key) => {
        if (Object.keys(formList).includes(key)) {
            formList[key] = data[key];
        } else if (Object.keys(formList.aaEntity).includes(key)) {
            if (key === "department" || key === "owner") {
                formList.aaEntity[key] = data[key] ? data[key].toString() : "";
                return;
            }
            formList.aaEntity[key] = data[key];
        }
    });
};
watch(
    formList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
defineExpose({ resetForm, editForm, changeType });
function disabledDateStart(time: Date) {
    let endDate = dayjs(formList.aaEntity.endDate).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(formList.aaEntity.startDate).valueOf();
    return time.getTime() < startDate;
}
const handleStatusChange = (check: any) => {
    if (!check) {
        ElConfirm("亲，辅助核算项目停用后不能再在凭证中使用哦，是否确认停用？").then((r: boolean) => {
            formList.aaEntity.status = !r;
        });
    }
};

const getDepartmentList = () => request({ url: "/api/AssistingAccounting/DepartmentList?showAll=false&onlyLeaf=true" });
const getEmployeeList = () => request({ url: "/api/AssistingAccounting/EmployeeList?showAll=false" });
const departmentList = ref<IProjectOrDepartmentItem[]>([]);
const employeeList = ref<IProjectOrDepartmentItem[]>([]);
const getDepartmentAndEmployeeList = () => {
    Promise.all([getDepartmentList(), getEmployeeList()]).then((res) => {
        departmentList.value = res[0].data;
        employeeList.value = res[1].data;
    });
};
getDepartmentAndEmployeeList();

const departmentListAll = ref<any[]>([]);
const showdepartmentList = ref<any[]>([]);
watchEffect(() => {
    departmentListAll.value = departmentList.value.map((item) => {
        return {
            value: item.aaeid + '',
            label: item.aaname
        }
    });
    departmentListAll.value.unshift({
        value: "",
        label: "请选择"
    });
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentListAll.value));
});
function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentListAll.value, 'label');
}
const employeeListAll = ref<any[]>([]);
const showemployeeList = ref<any[]>([]);
watchEffect(() => {
    employeeListAll.value = employeeList.value.map((item) => {
        return {
            value: item.aaeid + '',
            label: item.aaname
        }
    });
    employeeListAll.value.unshift({
        value: "",
        label: "请选择"
    });
    showemployeeList.value = JSON.parse(JSON.stringify(employeeListAll.value));
});
function employeeFilterMethod(value: string) {
    showemployeeList.value = commonFilterMethod(value, employeeListAll.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
:deep(.el-textarea__inner){
    z-index: 1000;
}
.add-content {
    :deep(.el-form) {
        .isRow {
            &:first-child {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
}
.formRef {
    input {
        .detail-original-input(188px, 32px);
        &.middle {
            .detail-original-input(288px, 32px);
        }
        &.more-long {
            .detail-original-input(598px, 32px);
        }
        &.big {
            .detail-original-input(698px, 32px);
        }
    }
    .isRow {
        display: flex;
        align-items: center;
    }
}

</style>
