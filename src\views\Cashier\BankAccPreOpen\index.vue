<template>
    <div class="content" :class="isErp ? 'erp' : ''">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">预约开户</div>
                    <div class="slot-mini-content">
                        <el-row justify="center">
                            <div class="top-link">
                                <div class="video-guide mr-20" @click="helpVideo">
                                    <img v-if="!isErp" src="@/assets/Icons/video.png" alt="视频" style="height: 16px; margin-right: 5px" />
                                    <img
                                        v-if="isErp"
                                        src="@/assets/Icons/video-blue.png"
                                        alt="视频"
                                        style="height: 16px; margin-right: 5px"
                                    />
                                    <a> 操作视频 </a>
                                </div>
                                <div class="progress-btn" @click="toProgress">
                                    进度查询<span class="right-icon"><img src="@/assets/Cashier/right.png" /></span>
                                </div>
                            </div>
                        </el-row>
                        <el-row justify="center">
                            <div class="line-banner">
                                <img src="@/assets/Cashier/openbanner.png" style="width: 1228px" />
                                <div class="banner-button" @click="toOpenAccount">立即开户</div>
                            </div>
                        </el-row>
                        <div class="step-content">
                            <el-row>
                                <el-col :span="24" class="line-title">预约开户流程</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24" class="line-title-info">三步预约，实时跟踪</el-col>
                            </el-row>
                            <el-row justify="center">
                                <div class="step-item" :class="item.className" v-for="item in stepDivList" :key="item.num">
                                    <span class="step-num">{{ item.num }}</span>
                                    <span class="step-title">{{ item.title }}</span>
                                    <span class="step-word">{{ item.word }}</span>
                                </div>
                            </el-row>
                        </div>
                        <div class="open-content">
                            <el-row>
                                <el-col :span="24" class="line-title">企业在线预约、一键提交资料、全程专人服务</el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24" class="line-title-info">方便快捷，预约开户新体验</el-col>
                            </el-row>
                            <el-row justify="center">
                                <el-card body-class="card">
                                    <div class="card-head">
                                        <img class="card-icon" src="@/assets/Cashier/fast.png" alt="" />
                                        <span class="card-title">极速办理</span>
                                    </div>
                                    <div class="card-content">
                                        <span>在线自由选择开户网点预约办理</span>
                                        <span>现场提交资料最快 30分钟</span>
                                        <span>全国地区多个网点支持</span>
                                    </div>
                                    <span class="card-footer" @click="toOpenAccount">立即预约开户></span>
                                </el-card>
                                <el-card body-class="card">
                                    <div class="card-head">
                                        <img class="card-icon" src="@/assets/Cashier/manager.png" alt="" />
                                        <span class="card-title">专属客户经理</span>
                                    </div>
                                    <div class="card-content">
                                        <span>预约成功后专属客户经理为您服务</span>
                                        <span>享受高效的贴心服务</span>
                                    </div>
                                    <span class="card-footer" @click="toOpenAccount">立即预约开户></span>
                                </el-card>
                                <el-card body-class="card">
                                    <div class="card-head">
                                        <img class="card-icon" src="@/assets/Cashier/gift.png" alt="" />
                                        <span class="card-title">专属福利</span>
                                    </div>
                                    <div class="card-content">
                                        <span>年费、网银管理费首年免费</span>
                                        <span>专人跟进开户全流程，为您保驾护航</span>
                                    </div>
                                    <span class="card-footer" @click="toOpenAccount">立即预约开户></span>
                                </el-card>
                            </el-row>
                        </div>
                    </div>
                </div>
            </template>
            <template #step_one>
                <div class="slot-content">
                    <StepOne @cancel-pre-open="handleCancelPreOpen" ref="stepOneRef" @next-step="toStepTwo" />
                </div>
            </template>
            <template #step_two>
                <div class="slot-content">
                    <StepTwo ref="stepTwoRef" @back="back" @next-step="tobankInfo" @to-progress="toProgress" />
                </div>
            </template>
            <template #psbc>
                <div class="slot-content">
                    <PSBCPreOpen ref="PSBCPreOpenRef" @back="backToBankStep" @success="proOpenSuccess" :systemType="systemType" />
                </div>
            </template>
            <template #detail>
                <div class="slot-content">
                    <PSBCPreOpenDetail
                        v-show="showDetailBankKey === BankType.PSBC"
                        :data="PSBCdetailInfo"
                        @back="currentSlot = 'progress'"
                    />
                    <SPDBPreOpenDetail
                        v-show="showDetailBankKey === BankType.SPDB"
                        :data="SPDBdetailInfo"
                        @back="currentSlot = 'progress'"
                    />
                    <PAPreOpenDetail v-show="showDetailBankKey === BankType.PABANK" :data="PADetailInfo" @back="currentSlot = 'progress'" />
                    <CMBPreOpenDetail v-show="showDetailBankKey === BankType.CMB" :data="CMBDetailInfo" @back="currentSlot = 'progress'" />
                </div>
            </template>
            <template #spdb>
                <div class="slot-content">
                    <SPDBPreOpenVue
                        ref="SPDBPreOpenRef"
                        @back="backToBankStep"
                        @success="proOpenSuccess"
                        :province-data="provinceList"
                        :systemType="systemType"
                    />
                </div>
            </template>
            <template #cmb>
                <div class="slot-content">
                    <CMBPreOpen
                        ref="CMBPreOpenRef"
                        @back="backToBankStep"
                        @success="proOpenSuccess"
                        :province-data="provinceList"
                        :systemType="systemType"
                    />
                </div>
            </template>
            <template #pa>
                <div class="slot-content">
                    <PAPreOpen ref="PAPreOpenRef" @back="backToBankStep" @success="proOpenSuccess" :systemType="systemType" />
                </div>
            </template>
            <template #progress>
                <div class="slot-content">
                    <PreOpenProgress @back="back" @toDetail="toDetail" ref="progressRef" :systemType="systemType" />
                </div>
            </template>
        </ContentSlider>

        <el-dialog 
            title="提示" 
            width="440px" 
            center 
            v-model="dialogVisible"  
            class="custom-confirm dialogDrag"
            @closed="visibleClick" 
        >
            <div class="webbank-content-tip" v-dialogDrag>
                <div class="content-main">
                    <div style="text-align: left;">您的开户基础信息资料已提交至微众银行，待审核通过后便可进行线上开户处理。您可以前往【预约开户-进度查询】查询审核进度，或关注柠檬云公众号获取审核结果。</div>
                    <img src="@/assets/Aside/gywmewm.png" alt="">
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="dialogVisible = false">确定</a>
                    <a class="button ml-10" @click="dialogVisible = false">取消</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "BankAccPreOpen",
};
</script>

<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import { onMounted, ref, computed, onUnmounted } from "vue";
import { request, type IResponseModel } from "@/util/service";
import type { IArea, IBankDetail, IPSBCDetail, IPADetail, ISPDBDetail, IBaseCompanyInfo, CMBCompanyInfoModel } from "./types";
import PreOpenProgress from "./components/PreOpenProgress.vue";
import StepOne from "./components/StepOne.vue";
import StepTwo from "./components/StepTwo.vue";
import PSBCPreOpen from "./components/PSBCPreOpen.vue";
import PSBCPreOpenDetail from "./components/PSBCPreOpenDetail.vue";
import SPDBPreOpenVue from "./components/SPDBPreOpen.vue";
import SPDBPreOpenDetail from "./components/SPDBPreOpenDetail.vue";
import PAPreOpen from "./components/PAPreOpen.vue";
import PAPreOpenDetail from "./components/PAPreOpenDetail.vue";
import CMBPreOpen from "./components/CMBPreOpen.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { BankType } from "@/constants/bankKey";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import CMBPreOpenDetail from "./components/CMBPreOpenDetail.vue";
import { globalWindowOpen } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { getGlobalLodash } from "@/util/lodash";

const { debounce } = getGlobalLodash();
const routerArrayStore = useRouterArrayStoreHook();
const isErp = window.isErp;
const stepDivList = [
    { num: "01", title: "第一步", word: "进入柠檬云平台选择“我要开户”", className: "bg-step-one" },
    { num: "02", title: "第二步", word: "在线选择开户网点并提交资料", className: "bg-step-two" },
    { num: "03", title: "第三步", word: "确认信息后带资料前往网点", className: "bg-step-three" },
];
const slots = ["main", "step_one", "step_two", "progress", "psbc", "cmb", "detail", "spdb", "pa"];
const currentSlot = ref("main");
const systemType = computed(() => {
    if (window.isProSystem) {
        return 1;
    } else if (window.isErp) {
        return 2;
    } else {
        return 0;
    }
});
const asId = useAccountSetStore().accountSet?.asId;
const progressRef = ref();
const toProgress = () => {
    currentSlot.value = "progress";
};

//去开户 获取暂存信息 没有就从账套信息获取
const toOpenAccount = () => {
    request({
        url: window.preOpenBankUrl + `/api/PreOpen/GetNotApplyData`,
        method: "get",
        params: {
            asId,
            system: systemType.value,
        },
    })
        .then((res: any) => {
            stepOneRef.value?.reset();
            if (res.id) {
                stepOneRef.value?.initStepOneBaseInfo(res.content);
                switch (res.bankType) {
                    case BankType.PSBC:
                        PSBCPreOpenRef.value?.initCancelPSBCInfo(res.content);
                        stepTwoRef.value?.selectBank("psbc");
                        break;
                    case BankType.SPDB:
                        SPDBPreOpenRef.value?.initCancelSPDBInfo(res.content);
                        stepTwoRef.value?.selectBank("spdb");
                        break;
                    case BankType.PABANK:
                        PAPreOpenRef.value?.initCancelPAInfo(res.content);
                        stepTwoRef.value?.selectBank("pa");
                        break;
                    case BankType.CMB:
                        CMBPreOpenRef.value?.initCancelCMBInfo(res.content);
                        stepTwoRef.value?.selectBank("cmb");
                        break;
                    default:
                        break;
                }
            } else {
                stepOneRef.value?.getAccountData();
            }
        })
        .catch((error) => {
            console.log(error);
            stepOneRef.value?.getAccountData();
        })
        .finally(() => {
            document.querySelector(".router-container")!.scrollTop = 0;
            currentSlot.value = "step_one";
        });
};

const back = () => {
    if (currentSlot.value == "detail") {
        currentSlot.value = "progress";
    } else if (currentSlot.value == "step_two") {
        currentSlot.value = "step_one";
    } else {
        currentSlot.value = "main";
    }
};
const proOpenSuccess = () => {
    progressRef.value?.getPreOpenList();
    currentSlot.value = "main";
};
const backToBankStep = (data: any) => {
    currentSlot.value = "step_two";
    if (data) {
        stepOneRef.value.companyInfo = Object.assign(stepOneRef.value.companyInfo, data);
    }
};

//第一步基础信息
const stepOneRef = ref();
const preOpenBaseInfo = ref<IBaseCompanyInfo>({
    companyName: "",
    socialCreditCode: "",
    legalPersonName: "",
    legalPhone: "",
    rightLegalPersonName: "",
    accountType: 0,
    taxProvinceName: "",
    taxProvinceCode: "",
});
const toStepTwo = () => {
    preOpenBaseInfo.value = stepOneRef.value.companyInfo;
    currentSlot.value = "step_two";
};

//第二步选择银行
const stepTwoRef = ref();
const SPDBPreOpenRef = ref();
const PAPreOpenRef = ref();
const PSBCPreOpenRef = ref();
const CMBPreOpenRef = ref<InstanceType<typeof CMBPreOpen>>();
const tobankInfo = (data: string) => {
    switch (data) {
        case "spdb":
            SPDBPreOpenRef.value.initBaseCompanyInfo(preOpenBaseInfo.value);
            currentSlot.value = "spdb";
            break;
        case "pa":
            PAPreOpenRef.value.getPABranch();
            PAPreOpenRef.value.queryPAArea();
            PAPreOpenRef.value.initBaseCompanyInfo(preOpenBaseInfo.value);
            currentSlot.value = "pa";
            break;
        case "psbc":
            PSBCPreOpenRef.value.initBaseCompanyInfo(preOpenBaseInfo.value);
            currentSlot.value = "psbc";
            break;
        case "cmb":
            CMBPreOpenRef.value?.initBaseCompanyInfo(preOpenBaseInfo.value);
            currentSlot.value = "cmb";
            break;
        case "webank":
            handleWebankApplyFn();
            break;
        default:
            currentSlot.value = data;
            break;
    }
};
//取消预约,保存填写，重置表单，返回主页
const handleCancelPreOpen = () => {
    currentSlot.value = "main";
    PAPreOpenRef.value.resetInfo();
    PSBCPreOpenRef.value.resetInfo();
    SPDBPreOpenRef.value.resetInfo();
};

const showDetailBankKey = ref(0);
const PSBCdetailInfo = ref<IBankDetail<IPSBCDetail>>();
const PADetailInfo = ref<IBankDetail<IPADetail>>();
const SPDBdetailInfo = ref<IBankDetail<ISPDBDetail>>();
const CMBDetailInfo = ref<CMBCompanyInfoModel>();

const toDetail = (preOpenId: string, bankTypeId: number) => {
    //获取对应银行的预约详情
    getBankDetail(preOpenId).then((res: any) => {
        showDetailBankKey.value = bankTypeId;
        currentSlot.value = "detail";
        switch (bankTypeId) {
            case BankType.PSBC:
                PSBCdetailInfo.value = res;
                break;
            case BankType.SPDB:
                SPDBdetailInfo.value = res;
                break;
            case BankType.PABANK:
                PADetailInfo.value = res;
                break;
            case BankType.CMB:
                CMBDetailInfo.value = res.content;
                break;
            default:
                break;
        }
    });
};
const getBankDetail = (preId: string) => {
    return request({
        url: window.preOpenBankUrl + `/api/PreOpen/GetDetailsInfo`,
        method: "get",
        params: {
            id: preId,
        },
    });
};
const provinceList = ref<IArea[]>([]);
const getProvinces = () => {
    request({ url: "/api/City/GetProvinces", method: "post" }).then((res: IResponseModel<IArea[]>) => {
        if (res.state === 1000) {
            provinceList.value = res.data;
        }
    });
};
//更新进度
const updateProgress = () => {
    request({
        url: window.preOpenBankUrl + `/api/PreOpen/Progress`,
        method: "get",
        params: {
            asId,
        },
    });
};

onMounted(() => {
    updateProgress();
    getProvinces();
});

const leaveValidator = routerArrayStore.registerLeaveValidator("/Cashier/BankAccPreOpen", () => {
    if (["psbc", "spdb", "pa", "cmb"].includes(currentSlot.value)) {
        if (
            !confirm(
                "为了方便您的操作，系统已经自动保存了您填写的信息，下次进入时，系统会自动带出上次填写的内容，是否继续退出预约开户页面？"
            )
        ) {
            return false;
        }
        switch (currentSlot.value) {
            case "psbc":
                PSBCPreOpenRef.value?.saveCancelInfo();
                break;
            case "spdb":
                SPDBPreOpenRef.value?.saveCancelInfo();
                break;
            case "pa":
                PAPreOpenRef.value?.saveCancelInfo();
                break;
            case "cmb":
                CMBPreOpenRef.value?.saveCancelInfo();
                break;
            default:
                break;
        }
    }
    return true;
});
onUnmounted(() => {
    leaveValidator.dispose();
});
function helpVideo() {
    let url = "";
    if (window.isErp) {
        url = "https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=*********";
    } else {
        url = "https://help.ningmengyun.com/#/jz/videoPlayer?qType=*********";
    }
    globalWindowOpen(url);
}
//微众
const dialogVisible = ref(false);
const getWeBankParams = () => {
    const content = {
        companyName: preOpenBaseInfo.value.companyName,
        unifiedNumber: preOpenBaseInfo.value.socialCreditCode,
        legalName: preOpenBaseInfo.value.legalPersonName,
        legalMobile: preOpenBaseInfo.value.legalPhone,
        accountType: preOpenBaseInfo.value.accountType,
        appointmentTime: new Date(), //当前申请时间
    };
    return {
        id: "",
        asId: asId + "",
        system: systemType.value,
        content,
    };
};
const handleWebankApply = () => {
    const data = {
        ...getWeBankParams(),
    };
    request({
        url: window.preOpenBankUrl + "/api/WeBankPreOpen/Open",
        method: "post",
        data,
    }).then((res: any) => {
        if (res.state === 1000) {
            dialogVisible.value = true;
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    });
};
const handleWebankApplyFn = debounce(handleWebankApply, 500)
const visibleClick = () => {
    dialogVisible.value = false;
    progressRef.value?.getPreOpenList();
    toProgress();
};
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.content {
    &.erp {
        .main-content,
        .slot-content {
            overflow: unset !important;
            .slot-mini-content {
                overflow: unset;
            }
        }
    }
    .main-content {
        color: #333;
        .slot-mini-content {
            .top-link {
                width: 1228px;
                margin: 20px 0px;
                display: flex;
                justify-content: end;
                .progress-btn {
                    width: 88px;
                    cursor: pointer;
                    .right-icon {
                        margin-left: 4px;
                    }
                }
            }
            .line-banner {
                margin-bottom: 56px;
                position: relative;
                .banner-button {
                    width: 200px;
                    height: 50px;
                    background: #ffffff;
                    border-radius: 25px;
                    color: #ff6500;
                    font-size: 24px;
                    line-height: 50px;
                    position: absolute;
                    left: 100px;
                    top: 156px;
                    cursor: pointer;
                }
            }

            .step-content {
                margin-bottom: 54px;
                .step-item {
                    box-sizing: border-box;
                    width: 370px;
                    height: 200px;
                    box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
                    border-radius: 4px;
                    border: 1px solid #e4e7ed;
                    overflow: hidden;
                    margin: 20px 20px 0px;
                    position: relative;
                    padding: 20px 10px 20px 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-end;
                    background-size: auto;
                    background-position: calc(100% - 20px) 20px;
                    background-repeat: no-repeat;

                    .step-num {
                        position: absolute;
                        font-size: 120px;
                        color: rgba(255, 218, 170, 0.5);
                        line-height: 168px;
                    }
                    .step-title,
                    .step-word {
                        z-index: 1;
                        padding-left: 66px;
                        text-align: left;
                    }
                    .step-title {
                        font-size: 24px;
                        line-height: 33px;
                    }
                    .step-word {
                        margin-top: 16px;
                        font-size: 18px;
                        line-height: 24px;
                    }

                    &.bg-step-one {
                        background-image: url("@/assets/Cashier/step_one.png");
                    }
                    &.bg-step-two {
                        background-image: url("@/assets/Cashier/step_two.png");
                    }
                    &.bg-step-three {
                        background-image: url("@/assets/Cashier/step_three.png");
                    }
                }
            }
            .open-content {
                padding-bottom: 79px;
                :deep(.el-card) {
                    margin: 30px 30px 0px 30px;
                    width: 342px;
                    height: 310px;
                    &.is-always-shadow {
                        box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
                    }
                    .card {
                        padding: 25px 0px 27px 44px;
                        text-align: left;
                        display: flex;
                        flex-direction: column;
                        &:hover {
                            cursor: pointer;
                            background-color: rgba(255, 150, 0, 0.05);
                        }
                        .card-head {
                            height: 93px;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            .card-icon {
                                width: 28px;
                            }
                            .card-title {
                                height: 28px;
                                font-size: 20px;
                                font-weight: 500;
                                line-height: 28px;
                            }
                        }
                        .card-content {
                            margin-top: 20px;
                            margin-bottom: 28px;
                            height: 96px;
                            font-size: 15px;
                            font-weight: 400;
                            line-height: 24px;
                            display: flex;
                            flex-direction: column;

                            span {
                                margin-bottom: 12px;
                            }
                        }
                        .card-footer {
                            color: #ff6500;
                            font-size: 16px;
                            font-weight: 500;
                            line-height: 22px;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}
.line-title {
    height: 33px;
    font-size: 24px;
    font-weight: 500;
    line-height: 33px;
}
.line-title-info {
    height: 24px;
    font-size: 20px;
    font-weight: 400;
    line-height: 24px;
    margin-top: 8px;
    margin-bottom: 14px;
}
.slot-content {
    .slot-mini-content {
        width: 1000px;
    }
}
.video-guide {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    padding: 0;
    cursor: pointer;
}
.webbank-content-tip {
    .content-main {
        padding: 20px;
        text-align: center;
        img {
            margin: 20px auto 0;
            display: block;
            width: 182px;
            height: 205px;
        }
    }
    .buttons {
        padding: 20px;
        text-align: center;
        border-top: 1px solid var(--border-color);
        .button {
            width: 100px;
        }
    }
}
</style>
