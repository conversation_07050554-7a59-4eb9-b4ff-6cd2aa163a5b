<template>
    <div :class="{ 'erp-voucher-content': isErp }">
        <div class="title">{{ vgName }}</div>
        <div class="check-voucher-center" :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in' }">
            <div class="check-voucher-center-top"></div>
            <div class="check-voucher-center-center" style="box-sizing: border-box">
                <voucher
                    v-model:query-params="voucherQueryParams"
                    @voucherVgIdChanged="setTitle"
                    @zoom="zoomCallback"
                    @voucher-changed="voucherChanged"
                    @load-success="voucherLoadSuccess"
                    ref="voucher"
                    @save="saveVoucher"
                    @delete-voucher="deleteVoucher"
                    @back="voucherCancel"
                    :edited="edited"
                    :showCancel="true"
                    :hiddenLine="true"
                ></voucher>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, toRef, watch } from "vue";
import Voucher from "@/components/Voucher/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useRoute } from "vue-router";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { EditVoucherQueryParams, VoucherSaveModel, VoucherSaveParams } from "@/components/Voucher/types";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { ElConfirm } from "@/util/confirm";

interface IProps {
    title?: string;
    changedTimes?: number;
}
const props = withDefaults(defineProps<IProps>(), {
    title: "",
});

const isErp = ref(window.isErp);
const emit = defineEmits(["voucherCancel", "hasChanged", "load-data"]);
const voucherQueryParams = ref();
const zoomState = ref<"in" | "out">("in");
const zoomCallback = ref((_zoomState: "in" | "out"): void => {
    zoomState.value = _zoomState;
});
const voucherGroupList = ref(useVoucherGroupStore().voucherGroupList);
let vgName = ref("");

function setTitle(vgId: number) {
    vgName.value = voucherGroupList.value.find((item) => item.id === vgId)?.title || "查看凭证";
    if (props.title) {
        vgName.value = props.title;
    }
}

// 改变次数，第一次进入会触发一次
function voucherChanged(val: boolean) {
    emit("hasChanged");
    if (!inited) return;
    edited.value = true;
}

function initVoucherData(pid: number, vid: number) {
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
    resetInit();
}

let editVouchersSave = ref();
let voucher = ref<InstanceType<typeof Voucher>>();
let isSaving = false;
function saveVoucher() {
    if (isSaving) return;
    isSaving = true;
    // 去除加载进度条，防止弹窗被遮挡
    voucher.value?.saveVoucher(
        new VoucherSaveParams(1010, (res: IResponseModel<VoucherSaveModel>) => {
            isSaving = false;
            if (res.state === 1000) {
                const voucherModel = voucher.value?.getVoucherModel();
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                    emit("voucherCancel");
                    emit("load-data");
                } else {
                    ElNotify({
                        message: "亲，保存成功啦！",
                        type: "success",
                    });
                    emit("voucherCancel");
                    emit("load-data");
                }
                resetInit();
                useFullScreenStore().changeFullScreenStage(false);
                voucherQueryParams.value = new EditVoucherQueryParams(res.data.pid, res.data.vid);
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
        })
    );
}
function voucherCancel() {
    voucher.value?.removeEventListener();
    resetInit();
    useFullScreenStore().changeFullScreenStage(false);
    emit("voucherCancel");
}
const deleteVoucher = () => {
    const { pid, vid } = voucher.value!.getVoucherModel();
    ElConfirm("确定要删除吗").then((r: boolean) => {
        if (r) {
            request({
                url: "/api/Voucher?pId=" + pid + "&vId=" + vid,
                method: "delete",
            }).then((res: any) => {
                if (res.state !== 1000) {
                    ElNotify({ type: "warning", message: "删除失败" });
                    return;
                }
                ElNotify({ type: "success", message: "删除成功" });
                voucherCancel();
                emit("load-data");
                useFullScreenStore().changeFullScreenStage(false);
            });
        }
    });
};
let inited = false;
const edited = ref(false);
const resetInit = () => {
    inited = false;
    edited.value = false;
};
const voucherLoadSuccess = () => {
    inited = true;
};
const route = useRoute();
const currentPath = ref(route.path);
const routerArrayStore = useRouterArrayStoreHook();
watch(edited, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
defineExpose({
    initVoucherData,
});
</script>

<style lang="less" scoped>
.check-voucher-center {
    margin: 0 auto;
    // overflow: hidden;
    background-color: var(--white);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    // height: calc(var(--voucher-min-height) + 70px);
    // height: ~"max(calc(var(--voucher-min-height) + 70px), calc(100vh - var(--content-padding-bottom) - var(--title-height)))";
    .check-voucher-center-top {
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        align-self: center;
        flex-shrink: 0;
    }
    .check-voucher-center-center {
        // height: 0;
        // flex: 1;
    }
    :deep(.voucher-container) {
        padding-top: 0;
    }

    &.zoom-in {
        .check-voucher-center-top {
            width: 1050px;
        }
    }

    &.zoom-out {
        .check-voucher-center-top {
            align-self: stretch;
            padding: 20px 54px;
        }
    }
}
.erp-voucher-content {
    height: 100%;
    overflow-y: auto;
}
</style>
