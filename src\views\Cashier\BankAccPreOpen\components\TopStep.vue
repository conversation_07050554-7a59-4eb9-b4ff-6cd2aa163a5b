<template>
    <div class="step" :class="isErp ? 'erp' : ''">
        <div class="step-content finished">
            <div class="step-one step-icon"></div>
            <span class="step-title">填写基本信息</span>
        </div>
        <div class="step-line"></div>
        <div class="step-content" :class="props.stepNumber >= 2 ? 'finished' : ''">
            <div class="step-two step-icon"></div>
            <span class="step-title">选择开户银行</span>
        </div>
        <div class="step-line"></div>
        <div class="step-content" :class="props.stepNumber >= 3 ? 'finished' : ''">
            <div class="step-three step-icon"></div>
            <span class="step-title">选择开户网点</span>
        </div>
        <div class="step-line"></div>
        <div class="step-content" :class="props.stepNumber >= 4 ? 'finished' : ''">
            <div class="step-four step-icon"></div>
            <span class="step-title">补充更多信息</span>
        </div>
    </div>
</template>
<script setup lang="ts">
const props = withDefaults(
    defineProps<{
        stepNumber: number;
    }>(),
    {
        stepNumber: 1,
    }
);
const isErp = window.isErp;
</script>
<style scoped lang="less">
.step {
    margin-top: 23px;
    margin-bottom: 28px;
    display: flex;
    justify-content: center;
    align-items: center;
    .step-content {
        display: flex;
        align-items: center;
        .step-icon {
            width: 29px;
            height: 30px;
            background-size: contain;
            background-repeat: no-repeat;
        }
        .step-one {
            background-image: url("@/assets/Cashier/step1_finished.png");
        }
        .step-two {
            background-image: url("@/assets/Cashier/step2_process.png");
        }
        .step-three {
            background-image: url("@/assets/Cashier/step3_process.png");
        }
        .step-four {
            background-image: url("@/assets/Cashier/step4_process.png");
        }
        &.finished {
            .step-two {
                background-image: url("@/assets/Cashier/step2_finished.png");
            }
            .step-three {
                background-image: url("@/assets/Cashier/step3_finished.png");
            }
            .step-four {
                background-image: url("@/assets/Cashier/step4_finished.png");
            }
            .step-title {
                color: #333;
            }
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;

            display: flex;
            justify-content: center;
            align-items: center;

            font-size: 12px;
        }
        .step-title {
            margin-left: 8px;
            font-size: 14px;
            color: #666666;
        }
    }
    .step-line {
        width: 48px;
        height: 1px;
        background: #e6e9ed;
        border-radius: 1px;
        margin: 0 12px;
    }

    &.erp {
        .step-content {
            &.finished {
                .step-one {
                    background-image: url("@/assets/Cashier/step1_erp_finished.png");
                }
                .step-two {
                    background-image: url("@/assets/Cashier/step2_erp_finished.png");
                }
                .step-three {
                    background-image: url("@/assets/Cashier/step3_erp_finished.png");
                }
                .step-four {
                    background-image: url("@/assets/Cashier/step4_erp_finished.png");
                }
            }
        }
    }
}
</style>
