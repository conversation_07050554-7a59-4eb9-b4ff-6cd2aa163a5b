export interface ISoftWare {
    value: string;
    key: number;
}
export interface ISoftWareItem {
    // 软件id
    softCompanyId: number;
    // 软件名
    softCompanyName: string;
    // 软件code
    softCompanyCode: string;
    // 0：云端；1：导账工具；2：科目余额表
    type: number;
}
export interface ISoftWareListItem {
    softTypeId: number;
    softTypeName: string;
    softCompanyModels: ISoftWareItem[];
}
export interface ICurrentAccountInfo {
    asName: string;
    accountStartDate?: string;
    accountStartDateYear: number;
    accountStartDateMonth: number;
    accountStandard: number;
    taxType: number;
}

export interface IImportFromOtherRequest {
    software: number;
    asNew: number;
    asName: string;
    startYear: string;
    startMonth: string;
    accountingStandard: number;
    taxType: number;
    isZeroImport: string;
}

export interface IImportFromOtherResponse {
    code: number;
    isShow: boolean;
    message: string;
    data: IImportApiResultModel;
}

interface IImportApiResultModel {
    asId: number;
    asName: string;
    startDate: string;
    accountingStandardName: string;
    errorMessage: IErrorMessageModel[];
    errorMessageGroup: IErrorMessageGroupModel[];
    resultMessage: string;
    importInfoId?: string;
}

interface IErrorMessageModel {
    errorType: number;
    errorTypeName: string;
    message: string;
}

export interface IErrorMessageGroupModel {
    errorType: number;
    errorTypeName: string;
    messageList: string[];
}

export type ImportModeType = "init" | "importTool" | "trailBalance" | "historyRecord" | "statusError";

export interface IAccountSetInfo {
    id: string;
    name: string;
    companyName: string;
    companyId: string;
    taxType: number;
    customerName: string;
    accType: number;
    databaseId: string;
    // 增值税种类，1小规模纳税人，2一般纳税人
    taxtype_nm: number;
    accountingStandard: number;
    accountingStandardName: string;
    startDate: "";
    accountsetId: string;
    userName: string;
    userId: string;
    companyNumber: string;
    ztId: string;
    startYM: string;
    host: string;
    currentPeriod: string;
    zt: string;
    url: string;
    // 是否已导入账套 0未导入 1导入 -1 无法判断
    isImport: number;
}

export interface IAccountSetData {
    code: number;
    message: string;
    data: IAccountSetInfo[];
}
export interface IImportListItem {
    id: number;
    appAsId: string;
    as_id: number;
    user_sn: string;
    import_as_id: string;
    import_as_name: string; //账套名称
    status: number;
    group_id: string;
    software_id: string;
    software_name: NamedCurve;
    error_message: string;
    message: string;
    progress: string;
    accountingstandard: number;
    isover: number;
    created_date: string;
}

export interface IImportData {
    code: number;
    message: string;
    data: IImportListItem[];
}

export interface IGetProgress {
    code: number;
    message: string;
    data: IImportListItem;
}

export interface IHistoryListItem {
    // 软件名
    softCompanyName: string;
    // 账套名称
    as_name: string;
    // 状态0:正在导入；1:导入成功；2：部分导入；3：导入失败
    status: number;
    // 错误信息
    error_msg: string;
    // 时间
    created_date: string;
    // 0: "未知",1: "导账工具";2: "科目余额表导入";3: "在线导账";
    importSourceType: number;
    appAsId: string;
}
export interface IErrorInfo{
    count: number;
    asName:string;
    appAsId: string;
    errorList: IErrorMsg[];
}
export interface IErrorReasonParams{
    appAsId: string;
    asName: string;
    errorMsgList: IErrorMsg[];
}
export interface IErrorMsg {
    Message: string;
    ErrorType: "0" | "1" | "2" | "4" | "8" | "16" | "32" | "64" | "128";
}

export interface ISoftWareSuggest {
    softTypeId: number;
    softTypeName: string;
}

export interface IHistoryParams {
    startDate: string;
    endDate: string;
    // 导账来源类型2:科目余额表;3:在线导账
    importType: number;
    // 状态0:正在导入；1:导入成功；2：部分导入；3：导入失败
    importStatus: number;
    searchKeyWord: string;
    currentPage: number;
    pageSize: number;
}
