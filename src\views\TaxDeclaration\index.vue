<template>
  <div
    class="content"
    v-mask="{ message: maskMessage, visible: maskVisible }">
    <content-slider
      :slots="slots"
      :currentSlot="currentSlot">
      <template #main>
        <div class="main-content">
          <div class="main-content-header">
            <div class="left-content">
              <el-card>
                <el-icon class="waring-filled"><WarningFilled /></el-icon>
                <span>温馨提示：</span>
                <div>
                  <div v-if="isDuringPeriod">
                    <span>
                      1、当期税款所属期的申报时间在{{ addTaxPeriod.endDate.slice(0, 11) }}截止，还有{{
                        new Date(addTaxPeriod.endDate).getDate() - new Date().getDate()
                      }}天，请及时进行申报
                    </span>
                    <el-tooltip
                      :content="bookContent"
                      raw-content
                      placement="bottom-start"
                      effect="light">
                      <img src="@/assets/Icons/book.png" />
                    </el-tooltip>
                    <br />
                  </div>
                  <span>{{ isDuringPeriod ? "2、" : "" }}如需增加或删除【税种申报】下的税种信息，可在【税费种管理】中进行调整</span>
                </div>
              </el-card>
            </div>
            <div class="right-content">
              <span>
                <img
                  src="@/assets/Icons/video.png"
                  alt="" />
                视频教程
              </span>
              <span>征收类型：服务型</span>
              <a
                class="button mr-10"
                @click="accountReconciliation">
                账表核对
              </a>
              <el-tooltip
                effect="light"
                :disabled="!isDeclarationResultUpdated"
                :content="`${lastUpdateTime}更新成功`">
                <a
                  class="button mr-10"
                  style="width: 100px"
                  @click="updateDeclarationResult(false)">
                  更新申报结果
                </a>
              </el-tooltip>
              <a
                class="button solid-button"
                :class="{ disabled: !isCurrentPeriod && !data.filter((item) => item.status === 10).length ? true : false }"
                @click="handleDeclaration">
                申报
              </a>
            </div>
          </div>
          <div class="main-content-body">
            <LMTable
              row-key="id"
              :columns="overviewColumns"
              :data="data"
              :cell-class-name="cellClassName"
              style="overflow-y: auto">
              <template #declareTypeName="{ slotColumn }">
                <el-table-column v-bind="slotColumn">
                  <template #default="{ row }">
                    <span :class="{ 'is-copy-tax': 'isCopyTax' in row && !row.isCopyTax }">
                      {{ row.declareTypeName }}
                    </span>
                  </template>
                </el-table-column>
              </template>
              <template #status="{ slotColumn }">
                <el-table-column v-bind="slotColumn">
                  <template #default="{ row }">
                    <span>{{ status[row.status] }}</span>
                    <el-image
                      v-if="[30, 40, 41, 50, 100].includes(row.status)"
                      class="picture"
                      :src="screenshotPicUrl"></el-image>
                  </template>
                </el-table-column>
              </template>
              <template #operation>
                <el-table-column prop="operation">
                  <template #header>操作</template>
                  <template #default="{ row }">
                    <el-button
                      v-if="row.status === 0"
                      text
                      @click="handleFillInClick(row)">
                      填写
                    </el-button>
                    <el-button
                      v-if="[10, 100].includes(row.status)"
                      text
                      @click="handleEdit(row)">
                      编辑
                    </el-button>
                    <el-button
                      v-if="[10, 100].includes(row.status)"
                      text
                      @click="handleDelete(row)">
                      删除
                    </el-button>
                    <el-button
                      v-if="[20, 30, 40, 41, 42].includes(row.status)"
                      text
                      @click="handleShow(row)">
                      查看
                    </el-button>
                    <el-button
                      v-if="[40, 42].includes(row.status)"
                      text
                      @click="handleContributions">
                      缴款
                    </el-button>
                    <el-button
                      v-if="row.status === 50"
                      text
                      @click="handleSupplementDeclare">
                      补申报
                    </el-button>
                  </template>
                </el-table-column>
              </template>
              <template #feedback>
                <el-table-column>
                  <template #header>反馈</template>
                  <template #default="{ row }">
                    <el-button
                      text
                      @click="handleDetails(row)">
                      详情
                    </el-button>
                  </template>
                </el-table-column>
              </template>
            </LMTable>
            <div class="footer">
              <div
                class="box"
                v-for="(item, index) in footerContent"
                :key="index">
                <a
                  v-for="(innerItem, innerIndex) in item"
                  :key="innerIndex"
                  @click="handleALinkClick(innerItem)">
                  {{ innerItem.label }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #fillIn>
        <div
          class="main-content"
          ref="fullScreenEle">
          <div class="main-content-header">
            <div class="left-content">
              <span class="select-title">税种：</span>
              <el-select
                v-model="declarationTaxInfo"
                value-key="declareTypeName"
                @change="changeTaxCategory">
                <el-option
                  v-for="item in taxCategoryList"
                  :key="item.declareTypeName"
                  :label="item.declareTypeName"
                  :value="item" />
              </el-select>
              <el-button
                text
                style="margin-left: 15px"
                @click="toggle">
                {{ isFullscreen ? "退出全屏" : "全屏填写" }}
              </el-button>
            </div>
            <div class="right-content">
              <a
                class="button mr-10"
                @click="handleExit">
                退出
              </a>
              <a class="button mr-10">重置</a>
              <a
                class="button mr-10"
                @click="save">
                保存
              </a>
              <a
                class="button mr-10"
                v-if="Object.values(HasQuickFillTaxType).includes(currentTaxCategory as HasQuickFillTaxType)"
                @click="declarationOverview">
                申报总览
              </a>
              <a
                class="button mr-10"
                v-if="currentTaxCategory.includes('财务报表')"
                @click="formFillSettings">
                填表设置
              </a>
              <a
                class="button solid-button"
                type="primary">
                申报
              </a>
            </div>
          </div>
          <template v-if="currentSlot === 'fillIn'">
            <component
              :is="fillInComponent"
              ref="dynamicFillInComponent"
              :current-tax-info="declarationTaxInfo"></component>
          </template>
        </div>
      </template>
      <template #quickFillIn>
        <quick-fill-in
          v-if="currentSlot === 'quickFillIn'"
          v-model:current-slot="currentSlot"
          v-model:current-tax-category="currentTaxCategory"
          :current-tax-info="declarationTaxInfo"></quick-fill-in>
      </template>
    </content-slider>

    <!-- 逾期弹框 -->
    <el-dialog
      modal-class="modal-class"
      class="custom-confirm dialogDrag"
      v-model="overdueDialog.visible"
      title="提示">
      <div
        v-dialogDrag
        class="dialog-main-has-table">
        <p>
          <el-icon><WarningFilled /></el-icon>
          <span style="vertical-align: middle">
            {{ `存在${overdueDialog.data.length}条逾期未申报记录，请尽快申报！` }}
          </span>
        </p>
        <LMTable
          row-key="id"
          :columns="overdueDialog.columns"
          :data="overdueDialog.data"></LMTable>
      </div>
      <template #footer>
        <a
          class="button mr-10"
          @click="overdueDialog.visible = false">
          取消
        </a>
        <a class="button solid-button">补申报</a>
      </template>
    </el-dialog>

    <!-- 申报弹框 -->
    <el-dialog
      modal-class="modal-class"
      class="custom-confirm dialogDrag"
      v-model="declarationDialog.visible"
      title="申报"
      width="900">
      <div
        v-dialogDrag
        class="dialog-main-has-table">
        <p style="font-size: 13px">
          应补（退）税（费）额合计：
          <span style="color: var(--orange)">{{ 12300 }}元</span>
          <span style="font-size: var(--h5); color: var(--grey); margin-left: 20px">
            如确认无误可点击【立即申报】；如有误可点击【编辑】进行编辑！
          </span>
        </p>
        <LMTable
          row-key="id"
          :columns="declarationDialog.columns"
          :data="declarationDialog.data">
          <template #operation="{ slotColumn }">
            <el-table-column v-bind="slotColumn">
              <el-button text>编辑</el-button>
            </el-table-column>
          </template>
        </LMTable>
      </div>
      <template #footer>
        <a
          class="button mr-10"
          @click="declarationDialog.visible = false">
          取消
        </a>
        <a class="button solid-button">立即申报</a>
      </template>
    </el-dialog>

    <!-- 判断财务准则弹框 -->
    <el-dialog
      modal-class="modal-class"
      class="custom-confirm dialogDrag"
      v-model="dialog1"
      title="提示"
      :width="540">
      <div
        class="dialog-main"
        v-dialogDrag>
        <el-icon><WarningFilled /></el-icon>
        <div>{{ dialog1Data.message }}</div>
      </div>
      <template #footer>
        <a
          v-if="dialog1Data.currentButton === 0"
          class="button mr-10">
          更改税局备案
        </a>
        <a
          v-else-if="dialog1Data.currentButton === 1"
          class="button mr-10">
          切换财务准则
        </a>
        <a
          v-else
          class="button mr-10"
          @click="dialog1 = false">
          关闭
        </a>
        <a
          class="button solid-button"
          @click="standardDialogConfirm">
          确定
        </a>
      </template>
    </el-dialog>
    <!-- 财务是否结账弹框 -->
    <el-dialog
      modal-class="modal-class"
      class="custom-confirm dialogDrag"
      v-model="dialog2"
      title="提示"
      :width="540">
      <div
        class="dialog-main"
        v-dialogDrag>
        <el-icon><WarningFilled /></el-icon>
        <div>
          <div>{{ dialog2Data.message }}</div>
          <div style="display: flex; color: var(--grey); padding-top: 35px">
            注：
            <div>
              <span v-if="dialog2Data.isFirstShow">
                申报属期与当前账期不符，可能造成财税不一致，请检查！
                <br />
              </span>
              您可以在 【
              <a @click="globalWindowOpenPage('/TaxReportSettings', '报税设置')">报税设置</a>
              】 中修改报表生成选项。
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <a
          class="button mr-10"
          @click="monthNotRemind">
          本月不在提醒
        </a>
        <a
          class="button solid-button"
          @click="isSettledDialogConfirm">
          确定
        </a>
      </template>
    </el-dialog>
    <!-- 提示信息弹框 -->
    <el-dialog
      modal-class="modal-class"
      class="custom-confirm dialogDrag"
      v-model="promptContent.visible"
      title="提示"
      width="500"
      @close="gotIt">
      <div
        class="dialog-main"
        v-dialogDrag>
        <el-icon v-if="promptContent.hasIcon"><WarningFilled /></el-icon>
        <div>{{ promptContent.message }}</div>
      </div>
      <template #footer>
        <a
          class="button solid-button"
          @click="gotIt">
          知道了
        </a>
      </template>
    </el-dialog>

    <!-- 报送财务报表弹窗 -->
    <el-dialog
      class="custom-confirm dialogDrag"
      v-model="dialogFinancialStatements"
      modal-class="modal-class"
      title="提示"
      :width="540">
      <div
        class="dialog-main"
        v-dialogDrag>
        <div>
          尚未报送本属期的财务报表，建议您先报送财务报表！申报企业所得税时，将根据财务报表数据自动预填“营业收入”“营业成本”“利润总额”等数据。
        </div>
      </div>
      <template #footer>
        <a
          class="button mr-10"
          @click="notRemind">
          忽略提示
        </a>
        <a
          class="button solid-button mr-10"
          @click="submitFinancialReport">
          报送财报
        </a>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
  import type { IColumnProps } from "@/components/Table/types"
  import ContentSlider from "@/components/ContentSlider/index.vue"
  import { useFullscreen } from "@vueuse/core"
  import { useTemplateRef } from "vue"
  import FinancialReport from "./FillIn/FinancialReport.vue"
  import AddTaxMonth from "./FillIn/AddTaxMonth.vue"
  import QuickFillIn from "./QuickFillIn/index.vue"
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { useTaxPeriodStore } from "@/store/modules/taxPeriod"
  import dayjs from "dayjs"
  import { globalWindowOpenPage, tryClearCustomUrlParams } from "@/utils/url"
  import {
    overdueList,
    getAddTaxMonthDeclarationDeadline,
    taxDeclarationList,
    getLatestUpdateTime,
    updateTaxDeclarationResult,
    getFinancialSettlementList,
  } from "@/api/taxDeclaration"
  import { LatestParamsType, checkUpdateStatus } from "@/api/taxManagement"
  import { EnumUpdateStatus } from "@/views/TaxManagement/constants"
  import { ElNotify } from "@/utils/notify"
  import { useRoute } from "vue-router"
  import screenshotPicUrl from "@/assets/TaxDeclaration/picture.png"
  import { TaxBureauLoginType, useTaxBureau } from "@/hooks/useTaxBureau"
  import { getDialogNotMindLocalStorage, setDialogNotMindLocalStorage } from "./utils"

  defineOptions({
    name: "TaxDeclaration",
  })

  import { useCompIncomeTaxQuarterStore } from "@/store/modules/compIncomeTaxQuarter"
  import CompIncomeTaxQuarterFill from "./FillIn/CompIncomeTaxQuarterFill.vue"
  const basicInfoStore = useBasicInfoStore()
  const { basicInfo } = storeToRefs(basicInfoStore)
  const taxPeriodStore = useTaxPeriodStore()
  const { currentPeriod, lastMonth } = storeToRefs(taxPeriodStore)

  // 遮罩内容
  const maskMessage = ref("正在税务初始化，获取税费种认定信息，请耐心等待……")
  const maskVisible = ref(false)
  watch(
    () => [basicInfo.value, basicInfo.value.initCompleted],
    (newVal) => {
      const [info, initCompleted] = newVal
      maskVisible.value = !!(Object.keys(info).length && !initCompleted)
    },
    { immediate: true },
  )

  const isCurrentPeriod = ref(true)
  // 税期切换，重新调用方法
  watch(
    () => currentPeriod.value,
    (newVal) => {
      init()

      isCurrentPeriod.value = newVal === lastMonth.value
      // 切换往期，往期没有更新申报结果的，自动更新申报结果
      if (!isCurrentPeriod.value && !isDeclarationResultUpdated.value) {
        updateDeclarationResult(true)
      }
    },
  )

  // 进入页面 要调用的接口
  function init() {
    getDeclarationStatusLastUpdateTime()
    getTaxDeclarationList()
  }

  // 逾期弹框数据
  const overdueDialog = ref({
    visible: false,
    columns: [
      { prop: "declareTypeName", label: "申报种类" },
      { prop: "periodStart", label: "所属期起" },
      { prop: "periodEnd", label: "所属期止" },
      { prop: "declarationPeriod", label: "申报期限" },
      { prop: "lateText", label: "申报状态" },
    ],
    data: [],
  })
  function getOverdueList() {
    overdueList().then((res) => {
      if (res.state === 1000) {
        // 当期有逾期记录且更新过申报结果
        if (res.data.length && isDeclarationResultUpdated.value) {
          overdueDialog.value.data = res.data
          overdueDialog.value.visible = true
        }
      }
    })
  }

  // 申报弹框
  const declarationDialog = ref<{ [key: string]: any }>({
    visible: false,
    columns: [
      { slot: "selection" },
      { prop: "", label: "申报种类" },
      { slot: "operation", label: "操作", width: 50 },
      { prop: "", label: "计税（费）依据" },
      { prop: "", label: "减免税（费）额" },
      { prop: "", label: "已缴税（费）额" },
      { prop: "", label: "应补（退）税（费）额" },
    ],
    data: [],
  })
  // 申报
  function handleDeclaration() {
    // 过滤出申报列表中已填写未申报的数据
    declarationDialog.value.data = data.value.filter((item) => item.status === 10)
    declarationDialog.value.visible = true
  }

  const addTaxPeriod = ref({
    startDate: "",
    endDate: "",
  })
  const isDuringPeriod = computed(() => {
    const { startDate, endDate } = toRefs(addTaxPeriod.value)
    return dayjs().format("YYYY-MM-DD") >= startDate.value.slice(0, 11) && dayjs().format("YYYY-MM-DD") <= endDate.value.slice(0, 11)
  })

  // 获取增值税月报的申报期限
  getAddTaxMonthDeclarationDeadline().then((res) => {
    if (res.state === 1000) {
      addTaxPeriod.value = res.data
    }
  })

  // 处理含未抄税的单元格样式
  function cellClassName({ row, columnIndex }: { row: any; columnIndex: number }) {
    if (columnIndex === 1 && "isCopyTax" in row && !row.isCopyTax) {
      return "has-copy-tax"
    }
  }

  const slots = ["main", "quickFillIn", "fillIn"]
  const currentSlot = ref("main")

  // 选中行税种名称
  const currentTaxCategory = ref("")
  // 选中行的申报税种信息
  const declarationTaxInfo = ref<{ [key: string]: any }>({})

  const bookContent = `
            <span>逾期申报可能存在以下风险：</span><br>
            <span>1、经济处罚：可能面临罚款（最高 1 万元）+ 每日 0.05% 滞纳金，增加直接成本。</span><br>
            <span>2、信用降级：纳税信用评分降低，可能被列为 D 级企业，受限发票领用、融资等。</span><br>
            <span>3、上市障碍：拟上市企业可能因财务合规问题被暂缓审核，已上市公司需披露风险。</span><br>
            <span>4、合作受阻：合作方通过纳税信用审查，低信用企业易被排除在招标、供应链外。</span><br>
            <span>5、人员追责：财务人员可能被企业追责（扣绩效、处分），严重时影响个人职业信用。</span><br>
       `

  // 报税总览表格列
  const overviewColumns: Array<IColumnProps> = [
    { type: "index", label: "序号" },
    { slot: "declareTypeName", prop: "declareTypeName", label: "申报种类" },
    { prop: "periodStart", label: "所属期起" },
    { prop: "periodEnd", label: "所属期止" },
    { prop: "declarationPeriod", label: "申报期" },
    { prop: "endDate", label: "申报期限" },
    { slot: "operation" },
    { slot: "status", prop: "status", label: "申报状态" },
    { prop: "declarationAmount", label: "应补（退）税额", align: "right" },
    { prop: "declarationTime", label: "申报日期" },
  ]

  let data = ref<{ [key: string]: any }[]>([])
  data.value.push({ declareTypeName: "增值税及附加税费申报（一般纳税人适用）", status: 0, isCopyTax: true })
  data.value.push({
    declareTypeName: "居民企业（查账征收）企业所得税月（季）度申报表",
    periodStart: "2023-01-01",
    periodEnd: "2023-03-31",
    declarationPeriod: "2023-04-01 -- 2023-04-30",
    status: 0,
  })
  data.value.push({
    declareTypeName: "财务报表",
    periodStart: "2023-01-01",
    periodEnd: "2023-03-31",
    declarationPeriod: "2023-04-01 -- 2023-04-30",
    status: 0,
  })

  enum status {
    "未填写" = 0,
    "已填写，未申报" = 10,
    "申报中" = 20,
    "申报成功，无需缴款" = 30,
    "申报成功，未缴款" = 40,
    "申报成功，缴款成功" = 41,
    "申报成功，部分缴款" = 42,
    "逾期" = 50,
    "申报失败" = 100,
  }
  function getTaxDeclarationList() {
    taxDeclarationList().then((res) => {
      if (res.state === 1000) {
        data.value = res.data
        data.value.push({ declareTypeName: "增值税及附加税费申报（一般纳税人适用）", status: 0 })
        data.value.push({
          declareTypeName: "居民企业（查账征收）企业所得税月（季）度申报表",
          periodStart: "2023-01-01",
          periodEnd: "2023-03-31",
          declarationPeriod: "2023-04-01 -- 2023-04-30",
          status: 0,
        })
      }
    })
  }

  // 账表核对
  function accountReconciliation() {
    console.log("父组件内click方法")
  }

  // 上次更新时间
  const lastUpdateTime = ref("")
  const isDeclarationResultUpdated = computed(() => {
    return !!lastUpdateTime.value
  })
  const updateDeclarationResultIntervalId = ref<any>(null)
  // 获取申报状态最后一次更新时间
  function getDeclarationStatusLastUpdateTime() {
    getLatestUpdateTime({ type: LatestParamsType.申报结果 }).then((res) => {
      if (res.state === 1000) {
        lastUpdateTime.value = res.data

        getOverdueList()
      }
    })
  }
  // 更新申报结果
  function updateDeclarationResult(isAuto?: boolean) {
    if (updateDeclarationResultIntervalId.value) {
      ElNotify({
        type: "info",
        message: "亲，本期正在更新申报结果，不用重复提交哦~",
      })
      return
    }
    let message = ""
    if (isAuto) {
      message = "正在获取本期申报结果……获取申报结果后自动刷新页面。"
    } else {
      message = "正在获取本期申报结果……"
    }
    ElNotify({
      type: "info",
      message,
    })
    updateTaxDeclarationResult().then((res) => {
      if (res.state === 1000) {
        pollingUpdateDeclarationResult(res.data)
        getDeclarationStatusLastUpdateTime()
      }
    })
  }
  // 轮询更新申报结果的状态
  function pollingUpdateDeclarationResult(taskId: string) {
    updateDeclarationResultIntervalId.value = setInterval(() => {
      checkUpdateStatus({ taskId }).then((res) => {
        switch (res.data) {
          case EnumUpdateStatus.成功:
            clearInterval(updateDeclarationResultIntervalId.value)
            updateDeclarationResultIntervalId.value = null
            ElNotify({
              type: "success",
              message: "更新申报结果成功",
            })
            getTaxDeclarationList()
            getDeclarationStatusLastUpdateTime()

            maskVisible.value = false
            // 往期更新申报结果成功
            if (!isCurrentPeriod.value) {
              const beforeClickTax = data.value.find((item) => item.declareTypeName === currentTaxCategory.value)
              const funMap: { [key: string]: any } = {
                "已填写，未申报": handleSupplementDeclare,
                逾期: handleSupplementDeclare,
                "申报成功，无需缴款": taxDeclared,
                "申报成功，未缴款": taxDeclared,
                "申报成功，缴款成功": taxDeclared,
                "申报成功，部分缴款": taxDeclared,
                申报失败: taxDeclared,
              }
              funMap[beforeClickTax!.status]()
            }
            break
          case EnumUpdateStatus.正在处理中:
            break
          case EnumUpdateStatus.失败:
            clearInterval(updateDeclarationResultIntervalId.value)
            updateDeclarationResultIntervalId.value = null
            ElNotify({
              type: "error",
              message: "更新申报结果失败，请稍后再试！",
            })
            maskVisible.value = false
            break
          default:
            break
        }
      })
    }, 5000)
  }

  // 税种已申报
  function taxDeclared() {
    promptContent.value = {
      visible: true,
      hasIcon: false,
      hasTable: false,
      message: `本税款所属期已申报${currentTaxCategory.value}，如需调整，请通过【作废与更正】处理`,
    }
  }

  const fillInMap: { [key: string]: any } = {
    财务报表: FinancialReport,
    "增值税及附加税费申报（一般纳税人适用）": AddTaxMonth,
    "居民企业（查账征收）企业所得税月（季）度申报表": CompIncomeTaxQuarterFill,
  }
  // 填表申报对应组件
  const fillInComponent = computed(() => {
    let temp = currentTaxCategory.value
    if (currentTaxCategory.value.includes("财务报表")) {
      temp = "财务报表"
    }
    return fillInMap[temp]
  })
  let dynamicFillInComponent = ref()

  // 系统支持填表申报的税种
  enum TaxSupportDeclaration {
    CB = "财务报表",
    QYSDS = "企业所得税",
    ZZS = "增值税及附加税费申报（一般纳税人适用）",
  }
  // 填写页面可切换税种列表
  const taxCategoryList = computed(() => {
    const taxCategoryList = data.value.filter((item) => {
      return [0, 10, 20, 100].includes(item.status)
    })
    const taxCategory = Object.values(TaxSupportDeclaration)
    const result = taxCategoryList.reduce((result, curr) => {
      const eligible = taxCategory.some((item) => curr.declareTypeName.includes(item))
      if (eligible) {
        result.push(curr)
      }
      return result
    }, [])
    return result
  })

  // 切换税种
  function changeTaxCategory(item: any) {
    declarationTaxInfo.value = item
    currentTaxCategory.value = item.declareTypeName
  }

  // 有快速填写页面的税种
  enum HasQuickFillTaxType {
    ZZSYB = "增值税及附加税费申报（一般纳税人适用）",
    QYSDSJB = "居民企业（查账征收）企业所得税月（季）度申报表",
  }
  console.log(Object.values(HasQuickFillTaxType), "HasQuickFillTaxType")
  const dialogFinancialStatements = ref(false)
  const currentRow = ref({})
  function handleFillInClick(row: any) {
    currentRow.value = row
    if (row.declareTypeName === HasQuickFillTaxType.QYSDSJB) {
      //TODO 判断是否报送同期财务报表（季报）
      const { isSubmitQuarterlyFinancialStatements = false } = row
      if (!isSubmitQuarterlyFinancialStatements) {
        dialogFinancialStatements.value = true
      } else {
        handleFillIn(row)
      }
    } else {
      handleFillIn(row)
    }
  }
  const notRemind = () => {
    dialogFinancialStatements.value = false
    handleFillIn(currentRow.value)
  }
  const submitFinancialReport = () => {
    dialogFinancialStatements.value = false
    // TODO 跳转报送财务报表页面
  }
  function handleFillIn(row: any) {
    declarationTaxInfo.value = row
    currentTaxCategory.value = row.declareTypeName // 未获取到本期申报结果

    // 往期申报结果未更新完成
    if (updateDeclarationResultIntervalId.value && !isCurrentPeriod.value) {
      maskMessage.value = "正在加载中"
      maskVisible.value = true

      return
    }

    if (row.declareTypeName.includes("财务报表")) {
      judgeAccountingStandard(row)
      return
    }

    // 根据当前行申报种类走下述逻辑，进入快捷填写页面时根据申报种类显示活跃的tab
    switch (row.declareTypeName) {
      case HasQuickFillTaxType.ZZSYB:
        if ("isCopyTax" in row && !row.isCopyTax) {
          ElNotify({
            type: "info",
            message: "请先完成抄报税再进行办理。",
          })
        } else {
          // globalWindowOpenPage("/InvoiceAccess?declare=true", "发票获取")
          currentSlot.value = "quickFillIn"
        }
        break
      case HasQuickFillTaxType.QYSDSJB:
        getCompIncomeTaxQuarter()
        currentSlot.value = "quickFillIn"
        break
      default:
        currentSlot.value = "fillIn"
        break
    }
  }

  const promptContent = ref({
    visible: false,
    hasIcon: true,
    hasTable: false,
    message: "",
  })
  enum ButtonType {
    ChangeTaxBureau = 0,
    SwitchAccountingStandard = 1,
    Close = 2,
  }
  const dialog1Data = ref({
    message: "",
    currentButton: 0,
  })
  const dialog2Data = ref({
    message: "",
    isFirstShow: false,
  })
  const dialog1 = ref(false)
  const dialog2 = ref(false)
  function judgeAccountingStandard(row: { [key: string]: any }) {
    const { financeStandard, taxAccountStandard, accountingStandard, subAccountingStandard } = toRefs(basicInfo.value)
    const { subAccountingStandard: subTaxAccountingStandard } = row

    // 1 ---》 小企业会计准则
    // 2 --->  企业会计准则
    // 先判断税务准则是不是本期支持做的税务准则，不是的话直接跳转税局
    // taxAccountStandard.value = 3
    if (![1, 2].includes(taxAccountStandard.value)) {
      // 跳转 税局申报页面：【财务报表报送及更正】页面
      checkLoginState(103, TaxBureauLoginType.Jump, "sbzx/view/sdsfsgjssb/#/yyzx/cwbbbs/ydy")
      return
    }

    // 判断财务准则和税务准则是否一致
    let flag = true
    switch (accountingStandard.value) {
      case 2: // 企业会计准则
        if (taxAccountStandard.value === 1) {
          // 小企业会计准则
          dialog1Data.value = {
            message: `财务系统使用【${financeStandard.value}】，与当前申报表会计准则不一致，系统将映射生成报表，请确认是否继续？`,
            currentButton: ButtonType.ChangeTaxBureau,
          }
          flag = false
        } else if (taxAccountStandard.value === 2 && subAccountingStandard.value !== subTaxAccountingStandard) {
          // 税务为企业会计准则，且财务和税务子会计准则不一致
          dialog1Data.value = {
            message: `财务系统使用【${financeStandard.value}】，与当前申报表会计准则不一致，系统将映射生成报表，请确认是否继续？`,
            currentButton: ButtonType.SwitchAccountingStandard,
          }
          flag = false
        }
        break
      case 1: // 小企业
        if (taxAccountStandard.value === 2) {
          // 企业会计准则
          dialog1Data.value = {
            message: "财务系统使用【小企业会计准则】，与当前申报表会计准则不一致，系统将映射生成报表，请确认是否继续？",
            currentButton: ButtonType.SwitchAccountingStandard,
          }
          flag = false
        }
        break
      default: // 非企业/小企业
        dialog1Data.value = {
          message: `财务系统使用【${financeStandard.value}】，与当前申报表会计准则不一致，系统将按0填写申报表，请确认是否继续？`,
          currentButton: ButtonType.Close,
        }
        flag = false
        break
    }
    financeReportPeriodEnd.value = row.periodEnd

    // 财务和税务会计准则不一致
    if (!flag) {
      // console.log(getDialogNotMindLocalStorage(StatementDialog[dialog1Data.value.currentButton]), "-------------")
      if (!getDialogNotMindLocalStorage(`${currentTaxCategory.value}_${StatementDialog[dialog1Data.value.currentButton]}`)) {
        dialog1.value = true
        return
      }
    }
    judgeFinanceIsSettled()
  }
  // 财报申报期止
  const financeReportPeriodEnd = ref("")
  async function judgeFinanceIsSettled() {
    const { dataSource } = toRefs(basicInfo.value)

    // 财务准则和税务准则一致，判断是否已结账
    if (await isSettled(financeReportPeriodEnd.value)) {
      //  已结账
      handleFinanceEnterFillIn(declarationTaxInfo.value)
    } else {
      const accountingPeriod = dayjs(financeReportPeriodEnd.value).format("YYYY-MM")
      // 未结账
      switch (dataSource.value) {
        case 0:
          dialog2Data.value = {
            message: `财务系统【${accountingPeriod}】未结账，根据【报税设置】是否获取最近一期已结账【${accountingPeriod}】的报表数据填写？`,
            isFirstShow: true,
          }
          break
        case 1:
          dialog2Data.value = {
            message: `财务系统【${accountingPeriod}】未结账，根据【报税设置】将生成0数据？`,
            isFirstShow: false,
          }
          break
        default:
          dialog2Data.value = {
            message: `财务系统【${accountingPeriod}】未结账，根据【报税设置】是否按未结账数据生成申报表？`,
            isFirstShow: false,
          }
          break
      }

      if (!getDialogNotMindLocalStorage(`${currentTaxCategory.value}_${isSettledDialog[dataSource.value]}`)) {
        dialog2.value = true
      } else {
        handleFinanceEnterFillIn(declarationTaxInfo.value)
      }
    }
  }
  // 获取当前财报申报所属期止对应月份是否结账
  async function isSettled(periodEnd: string) {
    const date = new Date(periodEnd)
    const year = date.getFullYear()
    const month = date.getMonth()
    // 获取财报是否结账
    const res = await getFinancialSettlementList()
    if (res.state === 1000 && res.data.length) {
      const periodSettlement = res.data.find((item: { [key: string]: any }) => {
        return item.year === year && item.sn === month + 1
      })
      if (periodSettlement) {
        return (periodSettlement as { [key: string]: any }).status === 3 // 3是已结账
      }
    }

    // 查不出来按未结账处理
    return false
  }
  // 判断准则对应弹框信息
  enum StatementDialog {
    "financeReportDialog0" = 0,
    "financeReportDialog1" = 1,
    "financeReportDialog2" = 2,
  }
  // 判断准则弹框确定按钮
  function standardDialogConfirm() {
    dialog1.value = false

    // 财务 非 企业/小企业会计准则，不进入是否结账判断
    if ([1, 2].includes(basicInfo.value.accountingStandard)) {
      judgeFinanceIsSettled()

      setDialogNotMindLocalStorage(`${currentTaxCategory.value}_${StatementDialog[dialog1Data.value.currentButton]}`)
      // setDialogNotMindLocalStorage(StatementDialog[dialog1Data.value.currentButton])
    } else {
      handleFinanceEnterFillIn(declarationTaxInfo.value)
    }
  }
  // 判断是否结账弹框确定按钮
  function isSettledDialogConfirm() {
    dialog2.value = false
    handleFinanceEnterFillIn(declarationTaxInfo.value)
  }
  // 弹框不在提示按人/月/账套存，目前缺人--->人指登录用户吗？
  function handleDialogNotPopup(dialog: string) {
    const obj = {
      user: "",
      date: dayjs().endOf("month").format("YYYY-MM-DD"),
    }
    localStorage.setItem(`${basicInfo.value.asId}-${dialog}`, JSON.stringify(obj))
  }
  // 判断是否展示弹框
  function judgeDisplayPopup(dialogNickname: string) {
    const info = JSON.parse(localStorage.getItem(`${basicInfo.value.asId}-${dialogNickname}`) as string)
    console.log(info, "info", dialogNickname)

    // 用户等于当前登录用户，弹框等于对应弹框
    if (info) {
      const now = dayjs().format("YYYY-MM-DD")

      return now > info.date
    } else {
      return true
    }
  }
  enum isSettledDialog {
    "financeReportDialog3" = 0,
    "financeReportDialog4" = 1,
    "financeReportDialog5" = 2,
  }
  function monthNotRemind() {
    setDialogNotMindLocalStorage(`${currentTaxCategory.value}_${isSettledDialog[basicInfo.value.dataSource]}`)
    dialog2.value = false

    handleFinanceEnterFillIn(declarationTaxInfo.value)
    // currentSlot.value = "fillIn"
  }
  // 财报进入填表申报页面时进行的提示
  function handleFinanceEnterFillIn(row: { [key: string]: any }) {
    const { accountingStandard, taxAccountStandard } = toRefs(basicInfo.value)
    if (accountingStandard.value === 2 && taxAccountStandard.value === 1 && row.status === 0) {
      promptContent.value = {
        visible: true,
        hasIcon: true,
        hasTable: false,
        message: "财报生成时明细项目未自动生成，请检查，如有符合的明细项目未填写，可手动填写。",
      }
    } else {
      currentSlot.value = "fillIn"
    }
  }
  // 我知道了
  function gotIt() {
    promptContent.value.visible = false
    if (!isCurrentPeriod.value) {
      return
    }
    currentSlot.value = "fillIn"
  }

  function handleEdit(row: any) {
    handleFillIn(row)
  }

  function handleDelete(row: any) {}

  function handleShow(row: any) {
    row.status = 30
    switch (row.status) {
      case status.申报中:
        // 进入填表式页面
        handleFillIn(row)
        break
      case status["申报成功，无需缴款"]:
      case status["申报成功，未缴款"]:
      case status["申报成功，缴款成功"]:
      case status["申报成功，部分缴款"]:
        // 申报成功跳转税局申报信息查询
        checkLoginState(103, TaxBureauLoginType.Jump, "szzh/zhcx/sbxx/sbxxcx")
        break
      default:
        break
    }
  }

  // 缴费
  function handleContributions() {
    // 跳转税局
    checkLoginState(103, TaxBureauLoginType.Jump, "skzx/view/skzs/jkkp")
  }

  const { checkLoginState } = useTaxBureau()
  // 补申报
  function handleSupplementDeclare() {
    checkLoginState(103, TaxBureauLoginType.Jump, "szc/szzh/sjswszzh/spHandler?cdlj=/szzh/zhcx/sbxx/wsbcx")
  }

  function handleDetails(row: any) {}

  const fullScreenEle = useTemplateRef("fullScreenEle")
  let { isFullscreen, toggle } = useFullscreen(fullScreenEle)

  function handleExit() {
    currentSlot.value = "main"
    declarationTaxInfo.value = {}
    currentTaxCategory.value = ""
  }

  function save() {
    dynamicFillInComponent.value.save()
  }

  // 申报总览
  function declarationOverview() {
    currentSlot.value = "quickFillIn"
  }

  // 填表设置
  function formFillSettings() {}

  init()

  const route = useRoute()
  // 从发票获取跳转至税种申报
  onActivated(() => {
    // 参数为税种名称
    if (route.query.declareTypeName) {
      currentSlot.value = "quickFillIn"
      currentTaxCategory.value = route.query.declareTypeName as string
    }
    tryClearCustomUrlParams(route)
  })

  const footerContent = ref<{ [key: string]: any }[][]>([
    [
      { label: "快捷入口：" },
      { label: `${toRef(basicInfo.value, "nationalName").value}电子税务局`, url: "mhzx/api/mh/mhsy/index" },
      { label: "税费种认定查询", url: `szc/szzh/sjswszzh/spHandler?cdlj=/szzh/zhcx/nsrxxcx` },
      {
        label: "征纳互动",
        url: "#/login?redirect_uri=https://znhd.hunan.chinatax.gov.cn:8443/znhdweb/nsrd/",
      },
      {
        label: "账户中心",
        url: `#/userCenter/baseInfoEE?client_id=`,
      },
    ],
    [
      { label: "常办业务：" },
      { label: "社保业务", url: "" },
      { label: "财产和行为税合并申报", url: "sbzx/view/cxssb/cchxwsnssb" },
      { label: "办税进度及结果查询", url: "sqsp/view/dksq/taxRelated" },
      { label: "出口退税申报", url: "ckts/view/ckts/#/yhscx/home?key=menu_0_0" },
    ],
    [
      { label: "数电票业务：" },
      { label: "开票业务", url: "kpfw/spHandler?cdlj=blue-invoice-makeout" },
      { label: "票种核定（发票用票需求申请）", url: "credit/application" },
      { label: "税务数字账户", url: "szc/szzh/sjswszzh/spHandler?cdlj=/szzh/szzh/" },
    ],
  ])
  function handleALinkClick(data: { [key: string]: any }) {
    if (!("url" in data)) return
    checkLoginState(103, TaxBureauLoginType.Jump, data.url)
  }

  // 当进入企业所得税快速填写页面时，如何进入到企业所得税的快速填写页面
  const { getCompIncomeTaxQuarter } = useCompIncomeTaxQuarterStore()
</script>
<style lang="scss" scoped>
  .content {
    .main-content {
      display: flex;
      flex-direction: column;

      .main-content-header {
        .left-content {
          font-size: var(--h5);

          :deep(.el-select) {
            width: 252px !important;
          }

          :deep(.el-card__body) {
            display: flex;
            padding: 6px 7px 3px 10px;
            line-height: 18px;
            color: var(--grey);

            .el-icon {
              padding-top: 2px;
              margin-right: 3px;
              font-size: var(--h4);
              color: var(--orange);
            }

            img {
              padding-top: 2px;
              margin-left: 5px;
              width: var(--h3);
              height: var(--h3);
              vertical-align: sub;
            }
          }

          .select-title {
            font-size: var(--h4);
            color: var(--dark-grey);
            vertical-align: middle;
          }
        }

        .right-content {
          img {
            width: 15px;
            height: 15px;
            vertical-align: text-bottom;
          }

          span {
            margin-right: 16px;
            font-size: var(--h5);
          }
        }
      }

      .main-content-body {
        height: 100%;
        overflow: hidden;

        .is-copy-tax::after {
          content: "未抄税";
          position: absolute;
          right: 10px;
          border: 1px solid var(--grey);
        }

        .picture {
          margin-left: 15px;
          width: var(--h4);
          height: var(--h4);
          vertical-align: sub;
          cursor: pointer;
        }

        :deep(.el-table) {
          // overflow: visible !important;
          // overflow-y: auto;

          .has-copy-tax > .el-tooltip {
            width: 108px !important;
          }

          .el-table__header-wrapper {
            z-index: 3;
            position: sticky;
            border-top: 1px var(--el-border-color) var(--el-border-style);
            top: 0;
          }
        }
        .declare-type {
          display: flex;
          span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          div {
            border: 1px solid var(--grey);
            height: 20px;
          }
        }

        .footer {
          display: flex;
          margin-top: 20px;
          padding: 16px 24px 28px;
          width: 60%;
          background-color: #fafafa;
          border-radius: 2px;

          .box {
            width: 28%;

            a {
              display: block;
              width: max-content;
              font-size: var(--h5);
              line-height: 22px;
            }
            a:not(:first-child) {
              color: var(--light-blue);
              cursor: pointer;
            }
          }
        }
      }
    }
  }
  :deep(.el-dialog) {
    .el-dialog__body {
      padding: 0 50px;
      min-height: 220px;

      .dialog-main {
        display: flex;
        padding-top: 50px;
        line-height: 24px;
        .el-icon {
          margin-top: 5px;
          margin-right: 10px;
          font-size: 15px;
          color: var(--orange);
        }

        a {
          text-decoration: none;
          color: var(--blue);
          cursor: pointer;
        }
      }

      .dialog-main-has-table {
        .el-icon {
          margin-right: 10px;
          font-size: 15px;
          color: var(--orange);
          vertical-align: middle;
        }

        .table {
          margin-bottom: 20px;
        }
      }
    }
  }
</style>
