<template>
    <div class="content">
        <div class="title">收票宝</div>
        <div class="main-content">
            <div id="intorContainer" class="intro-container" v-show="showContent === 'index' && !tableData.length">
                <div class="intor-title">员工收票交票，财务审批归档，企业电子档案管理好帮手</div>
                <div class="intor-sub-title">帮您智能收取票据，一键提交财务，解放双手之神器</div>
                <div class="button solid-button" @click="gotoStepOne">立即授权</div>
                <div class="intor-content-card">
                    <div class="card-left">
                        <div class="card-title">收票宝亮点：</div>
                        <div class="points-container">
                            <div class="point-content">
                                <img class="point-icon" src="@/assets/MiniProgramBinding/point-icon.png" />
                                <span>多方采集，自动核验</span>
                            </div>
                        </div>
                        <div class="points-container">
                            <div class="point-content">
                                <img class="point-icon" src="@/assets/MiniProgramBinding/point-icon.png" />
                                <span>一键交票，告别粘贴</span>
                            </div>
                        </div>
                        <div class="points-container">
                            <div class="point-content">
                                <img class="point-icon" src="@/assets/MiniProgramBinding/point-icon.png" />
                                <span>关联凭证，高效利用</span>
                            </div>
                        </div>
                        <div class="points-container">
                            <div class="point-content">
                                <img class="point-icon" src="@/assets/MiniProgramBinding/point-icon.png" />
                                <span>智能归档，无忧存储</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-right">
                        <img class="card-pic" src="@/assets/MiniProgramBinding/intor-card-pic.png" />
                    </div>
                </div>
            </div>
            <div v-if="!isErp" class="step-toolbar-container" v-show="showContent === 'step'">
                <div class="step-tab step-one">
                    <img
                        class="step-icon step-current-icon"
                        v-show="step === 1"
                        src="@/assets/MiniProgramBinding/step-one-icon-solid-erp.png"
                    />
                    <img
                        class="step-icon step-completed-icon"
                        v-show="step > 1"
                        src="@/assets/MiniProgramBinding/step-icon-completed-erp.png"
                    />
                    <span class="step-title">打开收票宝</span>
                </div>
                <div class="step-split-line"></div>
                <div class="step-tab step-two">
                    <img class="step-icon step-unselected-icon" v-show="step < 2" src="@/assets/MiniProgramBinding/step-two-icon.png" />
                    <img
                        class="step-icon step-current-icon"
                        v-show="step === 2"
                        src="@/assets/MiniProgramBinding/step-two-icon-solid-erp.png"
                    />
                    <img
                        class="step-icon step-completed-icon"
                        v-show="step > 2"
                        src="@/assets/MiniProgramBinding/step-icon-completed-erp.png"
                    />
                    <span class="step-title">分享给员工</span>
                </div>
                <div class="step-split-line"></div>
                <div class="step-tab step-three">
                    <img class="step-icon step-unselected-icon" v-show="step < 3" src="@/assets/MiniProgramBinding/step-three-icon.png" />
                    <img
                        class="step-icon step-current-icon"
                        v-show="step === 3"
                        src="@/assets/MiniProgramBinding/step-three-icon-solid-erp.png"
                    />
                    <span class="step-title">员工绑定</span>
                </div>
            </div>
            <div v-else class="step-toolbar-container" v-show="showContent === 'step'">
                <div class="step-tab step-one">
                    <img
                        class="step-icon step-current-icon"
                        v-show="step === 1"
                        src="@/assets/MiniProgramBinding/step-one-icon-solid.png"
                    />
                    <img
                        class="step-icon step-completed-icon"
                        v-show="step > 1"
                        src="@/assets/MiniProgramBinding/step-icon-completed.png"
                    />
                    <span class="step-title">打开收票宝</span>
                </div>
                <div class="step-split-line"></div>
                <div class="step-tab step-two">
                    <img class="step-icon step-unselected-icon" v-show="step < 2" src="@/assets/MiniProgramBinding/step-two-icon.png" />
                    <img
                        class="step-icon step-current-icon"
                        v-show="step === 2"
                        src="@/assets/MiniProgramBinding/step-two-icon-solid.png"
                    />
                    <img
                        class="step-icon step-completed-icon"
                        v-show="step > 2"
                        src="@/assets/MiniProgramBinding/step-icon-completed.png"
                    />
                    <span class="step-title">分享给员工</span>
                </div>
                <div class="step-split-line"></div>
                <div class="step-tab step-three">
                    <img class="step-icon step-unselected-icon" v-show="step < 3" src="@/assets/MiniProgramBinding/step-three-icon.png" />
                    <img
                        class="step-icon step-current-icon"
                        v-show="step === 3"
                        src="@/assets/MiniProgramBinding/step-three-icon-solid.png"
                    />
                    <span class="step-title">员工绑定</span>
                </div>
            </div>
            <div class="step-content-container">
                <div class="step-content step-one" v-show="step === 1 && !tableData.length">
                    <div class="step-one-card">
                        <div class="card-left">
                            <div class="card-title">微信扫码进入 [收票宝] 小程序</div>
                            <div class="card-sub-title">授权微信进行注册</div>
                            <img class="qr-code" src="@/assets/MiniProgramBinding/qr-code-1.png" />
                        </div>
                        <div class="card-right">
                            <img class="card-pic" src="@/assets/MiniProgramBinding/step-one-card-pic.png" />
                        </div>
                    </div>
                    <div class="step-one-btns">
                        <div class="button" @click="() => ((step = 0), (showContent = 'index'))">上一步</div>
                        <div class="button solid-button" @click="step = 2">已注册，下一步</div>
                    </div>
                </div>
                <div class="step-content step-two" v-show="step === 2 && !tableData.length">
                    <div class="step-two-card">
                        <div class="card-left">
                            <div class="card-title">[收票宝] 小程序分享或下载二维码</div>
                            <div class="card-sub-title">分享给员工 > 员工注册</div>
                            <img class="qr-code" src="@/assets/MiniProgramBinding/qr-code-2.png" />
                            <a
                                class="download-qr-code-btn"
                                :href="domain + '/Services/Doc/DownLoadImg.ashx?filePath=MiniProgramBinding/qr-code-2.png'"
                                download="qr-code-2.png"
                            >
                                <img class="btn-icon" src="@/assets/MiniProgramBinding/download-icon.png" />
                                <span>下载二维码</span>
                            </a>
                        </div>
                        <div class="card-right">
                            <img class="card-pic" src="@/assets/MiniProgramBinding/step-two-card-pic.png" />
                        </div>
                    </div>
                    <div class="step-two-btns">
                        <div class="button" @click="() => (step = 1)">上一步</div>
                        <div class="button solid-button" @click="step = 3">已分享，下一步</div>
                    </div>
                </div>
                <div class="step-content step-three" v-show="step === 3 || showContent === 'main' || tableData.length">
                    <div class="content-left">
                        <div class="main-tool-bar space-between table-tool-bar">
                            <div class="tool-bar-left">
                                当前账套：<span id="currentAsName">{{ asName }}</span>
                            </div>
                            <div class="tool-bar-right">
                                <div
                                    v-permission="['spb-canedit']"
                                    class="button solid-button mr-10"
                                    v-show="!isErp"
                                    @click="openAddEmployeeDialog()"
                                >
                                    新增
                                </div>
                                <div
                                    v-permission="['spb-canimport']"
                                    class="button solid-button width"
                                    @click="() => (importUserShow = true)"
                                >
                                    导入
                                </div>
                            </div>
                        </div>
                        <div
                            :class="['main-center', 'table-container', { 'hidden-table-body': !tableData.length && !noneDataTable.length }]"
                        >
                            <Table 
                                :data="tableData.length ? tableData : noneDataTable" 
                                :columns="columns" 
                                :scrollbarShow="true"
                                :max-height="isErp ? 'calc(100vh - 180px)' : 'calc(100vh - 280px)'"
                                :tableName="setModule"
                            >
                                <template #name>
                                    <el-table-column 
                                        label="用户" 
                                        min-width="83px" 
                                        align="left" 
                                        header-align="left"
                                        prop="name"
                                        :width="getColumnWidth(setModule, 'name')"
                                    >
                                        <template #default="scope">
                                            <span v-if="scope.row.eSn > -1" class="text">{{ scope.row.eName }}</span>

                                            <div class="input" v-if="isErp && scope.row.eSn === -1">
                                                <el-popover
                                                    placement="bottom-start"
                                                    :width="430"
                                                    :visible="employeeDialogVisible"
                                                    popper-class="spb-pover"
                                                >
                                                    <template #reference>
                                                        <el-input
                                                            v-model="scope.row.eName"
                                                            @input="emlpoyNameInput"
                                                            @focus="employeeDialogVisible = true"
                                                            @blur="employeeDialogVisible = false"
                                                        >
                                                            <template #suffix>
                                                                <el-icon
                                                                    class="el-input__icon"
                                                                    style="cursor: pointer"
                                                                    v-permission="['spb-canedit']"
                                                                    @click.stop="openSelectEmployeeDialog"
                                                                    ><MoreFilled
                                                                /></el-icon>
                                                            </template>
                                                        </el-input>
                                                    </template>
                                                    <el-table
                                                        :data="emlpoyeeSelectData"
                                                        @row-click="selecteEmployee"
                                                        style="max-height: 300px; overflow-y: auto"
                                                    >
                                                        <el-table-column prop="E_NAME" label="姓名" width="100">
                                                            <template v-slot="{ row }">
                                                                <div style="max-height: 50px;">
                                                                    {{ row.E_NAME }}
                                                                </div>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="MOBILE_PHONE" label="手机号" width="135">
                                                            <template v-slot="{ row }">
                                                                <div style="max-height: 50px;">
                                                                    {{ row.MOBILE_PHONE }}
                                                                </div>
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column prop="DEPARTMENT_NAME" label="部门" min-width="150">
                                                            <template v-slot="{ row }">
                                                                <ToolTip :content=row.DEPARTMENT_NAME placement="right" effect="light" :teleported="true">
                                                                    <div class="custom-cell" style="max-height: 50px;">
                                                                    {{ row.DEPARTMENT_NAME }}
                                                                </div>
                                                                 </ToolTip>
                                                            </template>
                                                        </el-table-column>
                                                    </el-table>
                                                    <div class="add_button" @click="openAddEmployeeDialog()">
                                                        <el-icon style="margin-right: 1px; font-size: 16px"><CirclePlus /></el-icon>
                                                        <div>新增用户</div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template #department>
                                    <template v-if="isErp">
                                        <el-table-column 
                                            label="部门" 
                                            min-width="96px" 
                                            align="left" 
                                            header-align="left"
                                            prop="department"
                                            :width="getColumnWidth(setModule, 'department')"
                                        >
                                            <template #default="scope">
                                                <span>{{ scope.row.departmentName }}</span>
                                            </template>
                                        </el-table-column>
                                    </template>
                                </template>
                                <template #operator>
                                    <el-table-column 
                                        label="操作" 
                                        min-width="120px" 
                                        align="left" 
                                        header-align="left"
                                        :resizable="false"
                                    >
                                        <template #default="scope">
                                            <span v-if="tableData.length">
                                                <a
                                                    v-permission="['spb-candelete']"
                                                    class="link"
                                                    @click="() => deleteItem(scope.row.eSn, scope.row.state)"
                                                    v-if="scope.row.state !== -1"
                                                >
                                                    删除
                                                </a>
                                                <a
                                                    v-permission="['spb-canedit']"
                                                    class="link invite-user"
                                                    @click="sendSPBInviteMsg(scope.row.mobilePhone)"
                                                    v-if="!scope.row.state"
                                                >
                                                    短信邀请
                                                </a>
                                            </span>
                                        </template>
                                    </el-table-column>
                                </template>
                            </Table>
                        </div>
                    </div>
                    <div class="content-right">
                        <img class="qr-code" src="@/assets/MiniProgramBinding/qr-code-3.png" />
                        <div class="qr-code-desc">
                            <div>[收票宝]</div>
                            <div>微信扫一扫，分享给同事</div>
                        </div>
                        <a
                            class="download-qr-code-btn"
                            :href="domain + '/Services/Doc/DownLoadImg.ashx?filePath=MiniProgramBinding/qr-code-3.png'"
                            download="qr-code-3.png"
                        >
                            <img class="btn-icon" src="@/assets/MiniProgramBinding/download-icon.png" />
                            <span>下载二维码</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <el-dialog center width="503px" top="200px" title="新增用户" v-model="addEmployeeDialogShow" class="custom-confirm dialogDrag">
        <div class="add-employee-container" v-dialogDrag>
            <div class="input-line">
                <div class="input-title"><span class="highlight-red">*</span>姓名：</div>
                <div class="input-field">
                    <input
                        v-model="addEmployeeInfo.eName" 
                        type="text" 
                        @input="handleAAInput(LimitCharacterSize.Name, $event, '姓名', 'eName', changeSearchInfo)"
                        @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                    />
                </div>
            </div>
            <div v-if="isErp" class="input-line erp-show">
                <div class="input-title"><span class="highlight-red">*</span>性别：</div>
                <div class="input-field">
                    <el-select v-model="addEmployeeInfo.gender" :teleported="false" placeholder=" ">
                        <el-option label="男" value="1" />
                        <el-option label="女" value="2" />
                    </el-select>
                </div>
            </div>
            <div class="input-line">
                <div class="input-title"><span class="highlight-red">*</span>手机号：</div>
                <div class="input-field">
                    <input 
                        v-model="addEmployeeInfo.mobile" 
                        type="text" 
                        @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机号', 'mobile', changeSearchInfo)"  
                        @paste="handleAAPaste(LimitCharacterSize.Phone, $event)" 
                    />
                </div>
            </div>

            <div v-if="isErp" class="input-line erp-show">
                <div class="input-title"><span class="highlight-red">*</span>部门：</div>
                <div class="input-field">
                    <Select 
                        v-model="addEmployeeInfo.department" 
                        :teleported="false" 
                        placeholder=" "
                        :filterable="true"
                        :filter-method="departFilterMethod"
                    >
                        <Option
                            v-for="item in showDepartmentList"
                            :key="item.DEPARTMENT"
                            :label="item.DEPARTMENT_NAME"
                            :value="item.DEPARTMENT"
                        ></Option>
                    </Select>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button width mr-10" @click="addEmployee">保存</a>
                <a class="button width" @click="cancelEmployee">取消</a>
            </div>
        </div>
    </el-dialog>
    <ImportSingleFileDialog
        class="spb-import-dialog"
        :importTitle="'导入'"
        v-model:import-show="importUserShow"
        :importUrl="importUrl"
        :uploadSuccess="uploadSuccess"
    >
        <template #download> 下载导入模板：<a class="link ml-20" @click="downloadTemplate">点击下载模板</a> </template>
        <template #import-content>
            <span>选择导入模板：</span>
        </template>
        <template #bottom-tips>
            <div class="import-tip">只能上传xls文件，且不超过10M</div>
        </template>
    </ImportSingleFileDialog>
    <el-dialog center width="800px" v-model="selectEmployeeDialogShow" align-center class="dialogDrag">
        <template v-slot:header>
            <h4 style="margin-top: 0">选择职员</h4>
        </template>
        <div class="select-employee-container" v-dialogDrag>
            <div class="select-employee-toolbar">
                <div class="search-controller">
                    <input
                        v-model="searchTxt"
                        type="text"
                        @keyup.enter="getEmployeeTableList(searchTxt)"
                        class="search-text"
                        placeholder="请输入工号/姓名/手机号"
                    />
                    <div class="search-submit" @click="getEmployeeTableList(searchTxt)"></div>
                </div>
            </div>
            <div class="select-employee-table" style="height: 400px">
                <Table
                    ref="employeeTableRef"
                    :data="emlpoyeeTableData"
                    :columns="employeeColumns"
                    :page-is-show="true"
                    :empty-text="emptyText"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :scrollbar-show="true"
                    @selection-change="handleSelectionChange"
                    @size-change="handleSizeChange2"
                    @current-change="handleCurrentChange2"
                >
                </Table>
            </div>
            <div class="buttons flex-right">
                <a class="button width" @click="() => (selectEmployeeDialogShow = false)">取消</a>
                <a class="button solid-button width ml-10" @click="batchAddSPBRelations">保存</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "MiniProgramBinding",
};
</script>
<script setup lang="ts">
import { ref, reactive, onMounted, watch, watchEffect } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElAlert } from "@/util/confirm";
import { globalExport } from "@/util/url";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { usePagination } from "@/hooks/usePagination";
import { getGlobalLodash } from "@/util/lodash";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableItem, IEmlpoyeeTableData, IGetEmployeeTableListParams } from "./types";
import { MoreFilled, CirclePlus } from "@element-plus/icons-vue";
import ToolTip from "@/components/Tooltip/index.vue";

import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { checkPermission } from "@/util/permission";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { ElConfirm } from "@/util/confirm";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const { accountSet } = useAccountSetStore();
const asName = accountSet?.asName || "";
const { paginationData, handleCurrentChange, handleSizeChange } = usePagination();
const domain = ref(window.jLmDiskHost);
const employeeTableRef = ref();
const _ = getGlobalLodash()
const isErp = ref(window.isErp);
const emptyText = ref(" ");
const setModule = "MiniProgam";
const columns = ref<Array<IColumnProps>>([
    { slot: "name" },
    { label: "手机号", prop: "mobilePhone", minWidth: 139, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'mobilePhone') },
    { slot: "department" },
    { label: "绑定日期", prop: "bindingDate", minWidth: 136, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'bindingDate') },
    {
        label: "状态",
        prop: "state",
        minWidth: 97,
        align: "left",
        headerAlign: "left",
        formatter: (row: any, column: any, value: number) => (value === 1 ? "已绑定" : value === 0 ? "未绑定" : ""),
        width: getColumnWidth(setModule, 'state')
    },
    { slot: "operator" },
]);
const employeeColumns = ref<Array<IColumnProps>>([
    { slot: "selection", width: 28, headerAlign: "center", align: "center" },
    { label: "工号", prop: "E_CODE", minWidth: 83, align: "left", headerAlign: "left", resizable: false },
    { label: "姓名", prop: "E_NAME", minWidth: 83, align: "left", headerAlign: "left", resizable: false },
    { label: "手机号", prop: "MOBILE_PHONE", minWidth: 139, align: "left", headerAlign: "left", resizable: false },
    { label: "部门", prop: "DEPARTMENT_NAME", minWidth: 268, align: "left", headerAlign: "left", resizable: false },
]);

const step = ref(0);
const tableData = ref<ITableItem[]>([]);
const addEmployeeDialogShow = ref(false);
const importUserShow = ref(false);
const selectEmployeeDialogShow = ref(false);
const emlpoyeeTableData = ref<IEmlpoyeeTableData[]>([]);
const emlpoyeeSelectData = ref<IEmlpoyeeTableData[]>([]);
const searchTxt = ref("");
const addEmployeeInfo = reactive<any>({
    eName: "",
    mobile: "",
    gender: "1",
    department: "",
});
const departmentList = ref([
    {
        DEPARTMENT_NAME: "1",
        DEPARTMENT: "0",
    },
]);
const changeSearchInfo = (key: string, value: string) => {
    addEmployeeInfo[key] = value;
}; 
const openAddEmployeeDialog = () => {
    addEmployeeDialogShow.value = true;
};
const deleteItem = (eSn: number, state: number) => {
    const message =
        state === 1
            ? "<span class='dialog-tip-left'>是否删除该用户？</span> <br/> <span class='dialog-tip-left'>注：已绑定用户删除后将不能再继续上传票据</span>"
            : "<span class='delete-notice'>是否删除该用户？</span>";
    const closeOnClickModal = true; //是否可以通过点击 modal 关闭 Dialog
    ElAlert({ message, closeOnClickModal }).then((r: any) => {
        if (r) {
            request({
                url: window.jLmDiskHost + "/Services/Doc/SPBDeleteRelation.ashx",
                method: "post",
                headers: { "Content-Type": "application/x-www-form-urlencoded" },
                data: { eSn },
            }).then((result: any) => {
                if (result.r) {
                    ElNotify({ type: "success", message: "删除成功" });
                    tableData.value = tableData.value.filter((item) => item.eSn !== eSn);
                    if (tableDataLength.value === 0) {
                        showContent.value = "step";
                        step.value = 3;
                    } else {
                        showContent.value = "main";
                    }
                    if (!tableData.value.length) {
                        noneDataTable.value = [];
                    }
                } else {
                    ElNotify({ type: "warning", message: result.msg || "删除用户绑定失败" });
                }
            });
        }
    });
};
let tableDataLength = ref(0);
let firstRequest = true;
const getSPBBindingUserList = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetSPBBindingUserList.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    }).then((res: any) => {
        tableData.value = res;
        showContent.value = tableData.value.length ? "main" : "index";
        if (firstRequest) {
            tableDataLength.value = tableData.value.length;
            firstRequest = false;
        } else {
            if (tableDataLength.value === 0) {
                showContent.value = "step";
                step.value = 3;
            }
        }
        if (window.isErp && tableData.value.length && checkPermission(["spb-canedit"])) {
            tableData.value.push({
                bindingDate: "",
                department: 0,
                departmentName: "",
                eName: "",
                eSn: -1,
                mobilePhone: "",
                state: -1,
            });
        }
    });
};
const noneDataTable = ref([{ bindingDate: "", department: 0, departmentName: "", eName: "", eSn: -1, mobilePhone: "", state: -1 }]);
if (!checkPermission(["spb-canedit"])) {
    noneDataTable.value = [];
}
const cancelEmployee = () => {
    addEmployeeDialogShow.value = false;
    (addEmployeeInfo.eName = ""), (addEmployeeInfo.mobile = "");
    addEmployeeInfo.department = String(departmentList.value[0].DEPARTMENT);
};
const addEmployee = () => {
    if (!addEmployeeInfo.eName.trim()) {
        ElNotify({ type: "warning", message: "请填写姓名" });
        return;
    }
    if (!addEmployeeInfo.gender && isErp.value) {
        ElNotify({ type: "warning", message: "请选择性别" });
        return;
    }
    if (!addEmployeeInfo.mobile) {
        ElNotify({ type: "warning", message: "请填写手机号" });
        return;
    }
    if (!/^1\d{10}$/.test(addEmployeeInfo.mobile)) {
        ElNotify({ type: "warning", message: "请填写正确的手机号" });
        return;
    }
    if (!addEmployeeInfo.department && isErp.value) {
        ElNotify({ type: "warning", message: "请选择部门" });
        return;
    }
    request({
        url: "/api/ErpAssistingAccounting/CheckSameNameEmployee",
        method: "post",
        params: {
            employeeName: addEmployeeInfo.eName
        }
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data) {
                let msg = `${addEmployeeInfo.eName}存在同名职员，是否继续保存？`;
                ElConfirm(msg).then((r) => {
                    if (r) {
                        addEmployeeSave();
                    }
                });
            } else {
                addEmployeeSave();
            }
        } else {
            ElNotify({ type: "warning", message: res.data.msg || "保存失败" });
        }
    })
};
function addEmployeeSave() {
    request({
        url: window.jLmDiskHost + "/Services/Doc/AddSPBBindingUser.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: {
            eName: addEmployeeInfo.eName,
            mobile: addEmployeeInfo.mobile,
            gender: addEmployeeInfo.gender,
            department: addEmployeeInfo.department,
        },
    }).then((data: any) => {
        if (data.r) {
            cancelEmployee();
            ElNotify({ type: "success", message: "保存成功" });
            getSPBBindingUserList();
            getEmployeeSelectList();
        } else {
            ElNotify({ type: "warning", message: data.msg || "保存失败" });
        }
    });
}
const sendSPBInviteMsg = (mobile: string) => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/SendSPBInviteMsg.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { mobile },
    }).then((result: any) => {
        if (result.r) {
            if (result.data === 1) getSPBBindingUserList();
            ElNotify({ type: "success", message: "发送成功" });
            getSPBBindingUserList();
        } else {
            ElNotify({ type: "warning", message: "发送失败" });
        }
    });
};
const downloadTemplate = () =>
    globalExport(window.jLmDiskHost + `/Services/Doc/DownLoadSPBBindingUserTemplate.ashx?appasid=${getGlobalToken()}`);

const importUrl = window.jLmDiskHost + "/Services/Doc/ImportSPBBindingUser.ashx";
const uploadSuccess = (res: any) => {
    if (res.r) {
        ElNotify({ type: "success", message: "导入成功" });
        importUserShow.value = false;
        getSPBBindingUserList();
    } else {
        if (res.msg == "repeat") {
            ElNotify({ type: "warning", message: "导入失败，系统已存在以下手机号用户：" + res.data.join("；") });
        } else {
            ElNotify({ type: "warning", message: res.msg || "导入失败" });
        }
    }
};

const showContent = ref("");
const gotoStepOne = () => {
    showContent.value = "step";
    step.value = 1;
};
const getEmployeeTableList = (txt?: string) => {
    const params: IGetEmployeeTableListParams = { page: paginationData.currentPage, rows: paginationData.pageSize };
    if (txt !== undefined && txt.trim() !== "") {
        params.searchInfo = txt;
    }
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetEmployees.ashx?ran=" + Math.random(),
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { ...params, needPageCount: 1 },
    }).then((res: any) => {
        paginationData.total = res.count;
        emlpoyeeTableData.value = res.employeeList || [];
        if (emlpoyeeTableData.value.length == 0) {
            emptyText.value = "暂无数据";
        }
    });
};
let emlpoyeeSelectDataOrigin: IEmlpoyeeTableData[] = [];
const getEmployeeSelectList = (txt?: string) => {
    let pageFlag = true;
    const params: IGetEmployeeTableListParams = { page: paginationData.currentPage };
    if (txt !== undefined && txt.trim() !== "") {
        params.searchInfo = txt;
        pageFlag = false;
    }
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetEmployees.ashx?ran=" + Math.random(),
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: params,
    }).then((res: any) => {
        emlpoyeeSelectData.value = res || [];
        emlpoyeeSelectDataOrigin = _.cloneDeep(emlpoyeeSelectData.value);

        departmentList.value = unique(res).map((v: any) => {
            return {
                DEPARTMENT_NAME: v.DEPARTMENT_NAME,
                DEPARTMENT: String(v.DEPARTMENT),
            };
        });
        useAssistingAccountingStore().departmentList.forEach((item) => {
            let obj = {
                DEPARTMENT_NAME: "",
                DEPARTMENT: "0",
            };
            obj.DEPARTMENT_NAME = item.aaname;
            obj.DEPARTMENT = String(item.aaeid);
            if (!departmentList.value.some((el) => el.DEPARTMENT_NAME === obj.DEPARTMENT_NAME && el.DEPARTMENT === obj.DEPARTMENT)) {
                departmentList.value.push(obj);
            }
        });
        addEmployeeInfo.department = String(departmentList.value[0].DEPARTMENT);
        //     for (let i = 0; i < res.length; i++) {
        //       const newItem = {
        //          DEPARTMENT_NAME: res[i].DEPARTMENT_NAME,
        //          DEPARTMENT: res[i].DEPARTMENT
        //     };
        //     const isDuplicate = departmentList.value.some(item => {
        //       return item.DEPARTMENT_NAME === newItem.DEPARTMENT_NAME && item.DEPARTMENT === newItem.DEPARTMENT;
        //     });
        //     if (!isDuplicate) {
        //       departmentList.value.push(newItem);
        //     }
        //   }
        if (pageFlag) {
            paginationData.total = res.length;
        }
    });
};
function unique(arr: any) {
    const res = new Map();
    return arr.filter((a: any) => !res.has(a.DEPARTMENT) && res.set(a.DEPARTMENT, 1));
}
// getEmployeeSelectList();
onMounted(() => {
    getSPBBindingUserList();
    getEmployeeSelectList();
});

const openSelectEmployeeDialog = () => {
    getEmployeeTableList();
    selectEmployeeDialogShow.value = true;
};
const selectedEmployee = ref<IEmlpoyeeTableData[]>([]);
const handleSelectionChange = (val: any) => (selectedEmployee.value = val);
const batchAddSPBRelations = () => {
    if (selectedEmployee.value.length === 0) {
        ElNotify({ type: "warning", message: "请选择职员" });
        return;
    }
    const eSns = selectedEmployee.value.map((item) => item.E_SN).join(",");
    const mobiles = selectedEmployee.value.map((item) => item.MOBILE_PHONE).join(",");
    request({
        url: window.jLmDiskHost + "/Services/Doc/SPBAddRelation.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { eSns, mobiles },
    }).then((result: any) => {
        if (result.r) {
            if (result.isNullMobile) {
                ElNotify({ type: "success", message: "保存成功！没有手机号的用户无法绑定收票宝，已跳过" });
            } else {
                ElNotify({ type: "success", message: "保存成功" });
            }
            getSPBBindingUserList();
            selectEmployeeDialogShow.value = false;
        } else {
            ElNotify({ type: "warning", message: result.msg || "新增用户绑定失败" });
        }
    });
};

let employeeDialogVisible = ref(false);
function emlpoyNameInput(eName: string) {
    emlpoyeeSelectData.value = commonFilterMethod(eName, emlpoyeeSelectDataOrigin, 'E_NAME');
}

function selecteEmployee(row: any) {
    let targetIndex = tableData.value.slice(0, tableData.value.length - 1).findIndex((v) => v.mobilePhone === row.MOBILE_PHONE);
    if (targetIndex >= 0) {
        ElNotify({
            type: "warning",
            message: "此用户已拥有该账套收票宝权限",
        });
    } else {
        selectedEmployee.value = [row];
        batchAddSPBRelations();
    }
}

const handleCurrentChange2 = (val: any) => {
    handleCurrentChange(val);
    getEmployeeTableList(searchTxt.value);
    employeeTableRef.value.setScrollTop(0);
};

const handleSizeChange2 = (val: any) => {
    handleSizeChange(val);
    getEmployeeTableList(searchTxt.value);
    employeeTableRef.value.setScrollTop(0);
};

const showDepartmentList = ref<any[]>([]);
watchEffect(() => {
    showDepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
});
function departFilterMethod(value: string) {
    showDepartmentList.value = commonFilterMethod(value, departmentList.value, 'DEPARTMENT_NAME');
}
</script>

<style lang="less" scoped>
@import "@/style/MiniProgramBinding/MiniProgramBinding.less";
@import "@/style/SelfAdaption.less";
body[erp] {
    .button {
        width: 172px !important;
    }
    .width {
        width: 76px !important;
    }
    .add_button {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        margin-top: 12px;
    }
    .add_button:hover {
        color: #3d7fff;
    }
}
:deep(.el-table__empty-text) {
    margin-top: 60px;
}
.flex-right {
    display: flex;
    justify-content: end;
}
:deep(.el-table__body-wrapper tbody tr:last-child td) {
    border-bottom: none;
}
.step-content-container {
    flex: 1;
    min-height: 300px;
    height: auto;
    .step-three {
        height: 100%;
    }
    .content-left .table-container {
        :deep(.table.paging-hide) {
            overflow: hidden;
            .el-table {
                border: none;
                border-top: 1px solid var(--table-border-color);
                flex: initial;
            }
            .el-table--border {
                .el-table__inner-wrapper {
                    &:before {
                        height: 0px;
                        background-color: var(--table-border-color);
                    }
                    &:after {
                        height: 0;
                    }
                }
            }
            .el-table__border-left-patch {
                width: 0;
            }
            .el-scrollbar__view {
                position: relative;
                border-bottom: 1px solid var(--table-border-color);
            }
        }
    }
}
body[erp] {
    .step-content-container {
        .step-three {
            .main-tool-bar {
                padding-bottom: 0;
            }
            :deep(.table) {
                .el-table {
                    border: none;
                    border-top: 1px solid var(--table-border-color);
                    flex: initial;
                    height: auto;
                }
                .el-table.el-table--border:before,
                .el-table.el-table--border:after {
                    width: 1px;
                    background-color: var(--table-border-color);
                }
            }
        }
        .content-left .table-container {
            :deep(.cell.el-tooltip) {
                .input {
                    width: 95%;
                }
                .el-input__wrapper .el-input__inner {
                    border: none;
                }
            }
        }
    }
    .select-employee-table {
        :deep(.table) {
            display: flex;
            flex-direction: column;
            height: 100%;
            .el-table {
                flex: 1;
                overflow: hidden;
            }
        }
    }
    .custom-cell{
        overflow: hidden; /* 超出部分隐藏 */
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 行数限制 */
        -webkit-box-orient: vertical;
        line-height: 1.6; /* 控制每行文本之间的间距 */
    }
}
</style>

<style lang="less">
body[erp] {
    .el-popper.spb-pover {
        .el-table__header {
            th:not(:first-child) {
                .cell {
                    border-left: 1px solid var(--el-table-border-color);
                }
            }
        }
        .el-scrollbar__bar.is-horizontal {
            display: none;
        }
    }
    .spb-import-dialog {
        .import-dialog {
            .import-content {
                flex-direction: row;
                align-items: center;
                .file-button {
                    height: 30px;
                    margin-top: 0;
                }
                .link {
                    display: inline-block;
                    width: 106px;
                    height: 30px;
                    line-height: 30px;
                    min-width: 76px;
                    padding: 0 12px;
                    box-sizing: border-box;
                    font-size: var(--font-size);
                    color: var(--white);
                    border-color: var(--main-color);
                    background-color: var(--main-color);
                    text-align: center;
                    border-radius: 2px;
                    text-decoration: none;
                }
            }
            .import-tip {
                margin-left: 152px;
            }
        }
    }
    &.el-popup-parent--hidden {
        width: 100% !important;
    }
}
</style>
