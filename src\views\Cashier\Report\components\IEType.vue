<template>
    <div class="tabs-pane-content">
        <div class="main-top main-tool-bar space-between">
            <div class="main-tool-left">
                <DatePicker
                    v-model:start-pid="searchInfo.startPid"
                    v-model:end-pid="searchInfo.endPid"
                    :clearable="true"
                    :disabled-date-start="disabledDateStart"
                    :disabled-date-end="disabledDateEnd"
                />
                <span class="ml-20">账户：</span>
                <CDAccountFilter
                    ref="CDAccountFilterRef"
                    v-model:accountOptions="accountOptions"
                    :options="options"
                    v-model:selectedList="selectedList"
                    :maxWidth="168"
                    @change-switch-status="changeSwitchStatus"
                />
                <span class="ml-20" v-if="fcList.length > 2">币别：</span>
                <CurrencyFilter
                    v-if="fcList.length > 2"
                    v-model:fcid="searchInfo.fcid" 
                    :fcList="fcList" 
                ></CurrencyFilter>
                <a class="solid-button ml-10" @click="getSearch">查询</a>
            </div>
            <div class="main-tool-right">
                <a class="button mr-10" v-permission="['report-canprint']" @click="handlePrintOrExport('print')">打印</a>
                <a class="button mr-10" v-permission="['report-canexport']" @click="handlePrintOrExport('export')">导出</a>
                <a class="button mr-10" v-if="subscribeShow" @click="scanShare">扫码分享</a>
                <a class="button" v-if="subscribeShow" @click="subscribe">报表订阅推送</a>
                <RefreshButton></RefreshButton>
            </div>
        </div>
        <div class="report-content" style="overflow-y: auto;">
            <div class="main-center" v-loading="loading" element-loading-text="正在加载数据...">
                <Table 
                    :columns="columns" 
                    :data="tableData10050" 
                    empty-text="暂无数据" 
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #code>
                        <el-table-column 
                            label="编码" 
                            min-width="106px" 
                            align="left" 
                            header-align="left"
                            prop="code"
                            :width="getColumnWidth(setModule, 'code')"
                        >
                            <template #default="scope">
                                <span :class="scope.row.parent_id > 0 ? 'pl-10' : ''">{{ scope.row.ie_code }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #name>
                        <el-table-column 
                            label="收入类别" 
                            min-width="178px" 
                            align="left" 
                            header-align="left"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span v-if="!scope.row.ie_name.startsWith('<a')">{{ scope.row.ie_name }}</span>
                                <span
                                    class="link"
                                    :class="scope.row.parent_id > 0 ? 'pl-10' : ''"
                                    @click="goToDetail(scope.row.ie_name)"
                                    v-else
                                    >{{ getText(scope.row.ie_name) }}</span
                                >
                            </template>
                        </el-table-column>
                    </template>
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.ie_id, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column 
                            label="收入总额" 
                            :min-width="216" 
                            align="right" 
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column 
                            label="收入总额" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column 
                            label="支出总额" 
                            :min-width="216" 
                            align="right" 
                            header-align="right"
                            prop="expenditure_standard"
                            :width="getColumnWidth(setModule, 'expenditure_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expenditure_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column 
                            label="支出总额" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expenditure) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expenditure_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </Table>
                <div class="mb-10"></div>
                <Table 
                    :columns="columns" 
                    :data="tableData10060" 
                    empty-text="暂无数据" 
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #code>
                        <el-table-column 
                            label="编码" 
                            min-width="106px" 
                            align="left" 
                            header-align="left"
                            prop="code"
                            :width="getColumnWidth(setModule, 'code')"
                        >
                            <template #default="scope">
                                <span :class="scope.row.parent_id > 0 ? 'pl-10' : ''">{{ scope.row.ie_code }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #name>
                        <el-table-column 
                            label="支出类别" 
                            min-width="178px" 
                            align="left" 
                            header-align="left"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span v-if="!scope.row.ie_name.startsWith('<a')">{{ scope.row.ie_name }}</span>
                                <span
                                    class="link"
                                    :class="scope.row.parent_id > 0 ? 'pl-10' : ''"
                                    @click="goToDetail(scope.row.ie_name)"
                                    v-else
                                    >{{ getText(scope.row.ie_name) }}</span
                                >
                            </template>
                        </el-table-column>
                    </template>
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.ie_id, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column 
                            label="收入总额" 
                            :min-width="216" 
                            align="right" 
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column 
                            label="收入总额" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column 
                            label="支出总额" 
                            :min-width="216" 
                            align="right" 
                            header-align="right"
                            prop="expenditure_standard"
                            :width="getColumnWidth(setModule, 'expenditure_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expenditure_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column 
                            label="支出总额" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expenditure) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expenditure_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </Table>
                <div class="mb-10"></div>
                <Table
                    empty-text="暂无数据"
                    v-show="tableDataTotal.length !== 0"
                    :columns="columns"
                    :data="tableDataTotal"
                    :show-header="false"
                    row-class-name="current-row"
                    :show-overflow-tooltip="true"
                    @save-header-dragend="headerDragend"
                    :tableName="setModule"
                >
                    <template #code>
                        <el-table-column 
                            min-width="106px" 
                            align="left" 
                            header-align="left"
                            prop="code"
                            :width="getColumnWidth(setModule, 'code')"
                        >
                            <template #default="scope">
                                <span :class="scope.row.parent_id > 0 ? 'pl-10' : ''">{{ scope.row.ie_code }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #name>
                        <el-table-column 
                            label="" 
                            min-width="178px" 
                            align="left" 
                            header-align="left"
                            prop="name"
                            :width="getColumnWidth(setModule, 'name')"
                        >
                            <template #default="scope">
                                <span>{{ scope.row.ie_name }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.ie_id, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column 
                            label="收入总额" 
                            :min-width="216" 
                            align="right" 
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column 
                            label="收入总额" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column 
                            label="支出总额" 
                            :min-width="216" 
                            align="right" 
                            header-align="right"
                            prop="expenditure_standard"
                            :width="getColumnWidth(setModule, 'expenditure_standard')"
                        >
                            <template #default="scope">
                                <span>{{ formatMoney(scope.row.expenditure_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column 
                            label="支出总额" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expenditure) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.expenditure_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                </Table>
            </div>
            <div class="main-bottom">
                <div style="color: var(--red)">提示：</div>
                <div class="mt-10">
                    <span>内部转账，不计入收支</span>
                    <a v-permission="['transfer-canview']" class="link ml-10" @click="redirectToTransfer">点击查看</a>
                </div>
            </div>
        </div>
    </div>
    <ScanShareDialog ref="ScanShareRef"></ScanShareDialog>
    <SubscribePushDialog ref="SubscribePushRef"></SubscribePushDialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from "vue";
import { getUrlSearchParams, globalExport, globalWindowOpenPage, globalPrint } from "@/util/url";
import { request } from "@/util/service";
import { formatMoney } from "@/util/format";
import { getText, getATagParams, getSearchInfoCD, getSearchParams, getFcCode } from "../utils";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

import type { IIETableData, IDateInfo, ISCurrcy } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { Option } from "@/components/SelectCheckbox/types";
import { dayjs } from "element-plus";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import ScanShareDialog from "./ScanShareDialog.vue";
import SubscribePushDialog from "./SubscribePushDialog.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import CDAccountFilter from "@/views/Cashier/components/CDAccountFilter.vue";
import CurrencyFilter from "@/views/Cashier/components/CurrencyFilter.vue";

const setModule = "CashReportIEType";
const accountSetStore = useAccountSetStore();

const props = defineProps<{ 
    options: IBankAccountItem[]; 
    dateInfo: IDateInfo; 
    fcList: ISCurrcy[];
}>();

const emit = defineEmits<{
    (event: "change-switch-status", args: boolean): void;
}>();
const changeSwitchStatus = (val: boolean) => {
    emit("change-switch-status", val);
}

const subscribeShow = computed(() => {
    return !window.isErp && !window.isAccountingAgent && !window.isBossSystem && !useThirdPartInfoStoreHook().isThirdPart
});
const ScanShareRef = ref();
const SubscribePushRef = ref();
function scanShare() {
    ScanShareRef.value.initShareDialog({tab:'IEType',startDate:searchInfo.startPid,endDate:searchInfo.endPid,accIds:selectedList.value.join(','),fcIds:searchInfo.fcid});
}
function subscribe() {
    SubscribePushRef.value.initSubscribe();
}

const dateInfo = computed(() => props.dateInfo);
const accountOptions = ref<Option[]>([]);

const tableData10050 = ref<IIETableData[]>([]);
const tableData10060 = ref<IIETableData[]>([]);
const tableDataTotal = ref<IIETableData[]>([]);
const selectedList = ref<number[]>([]);
const calcColumns = (): Array<IColumnProps> => {
    const list:IColumnProps[] = [
        { slot: "code" },
        { slot: "name" },
        { slot: "income" },
        { slot: "expenditure" },
        { 
            label: "收入笔数", 
            prop: "income_count", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 131,
            width: getColumnWidth(setModule, 'income_count')
        },
        { 
            label: "支出笔数", 
            prop: "expenditure_count", 
            align: "left", 
            headerAlign: "left", 
            minWidth: 131,
            resizable: false 
        },
    ];
    if (searchInfo.fcid > 1) {
        list.splice(2, 0, { slot: "fcName" });
        list.forEach((item) => {
            if (item.slot === "income" || item.slot === "expenditure") {
                item.slot += "Fc";
            }
        });
    }
    return list;
};

const searchInfo = reactive({
    startPid: "",
    endPid: "",
    CD_ACC_IDS: "ALL",
    fcid: -1,
});
watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});
const columns = ref<Array<IColumnProps>>();
columns.value = calcColumns();

const getSearch = () => {
    columns.value = calcColumns();
    getTableList();
}

const redirectToTransfer = () => globalWindowOpenPage("/Cashier/Transfer", "内部转账");
const loading = ref(false);
const currentFcid = ref(-1);
const getTableList = () => {
    loading.value = true;
    Promise.all([getTableListApi("Search", 10050), getTableListApi("Search", 10060), getTableListApi("Total")])
        .then((res: any) => {
            if (res[0].state == 1000) tableData10050.value = res[0].data.rows;
            if (res[1].state == 1000) tableData10060.value = res[1].data.rows;
            if (res[2].state == 1000) tableDataTotal.value = res[2].data.rows;
        })
        .finally(() => {
            loading.value = false;
            currentFcid.value = searchInfo.fcid;
        });
};
const handlePrintOrExport = (type: "print" | "export") => {
    if (searchInfo.startPid == "" || searchInfo.endPid == "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    const params = getSearchParams(searchInfo, "cashReport");
    const urlPath = type === "print" ? "PrintForIE?" : "ExportForIE?";
    const url = "/api/CashierReport/" + urlPath + getUrlSearchParams(params);
    type === "print" ? globalPrint(url) : globalExport(url);
};
const isErp = ref(window.isErp);
const goToDetail = (name: string) => {
    const params = { 
        ...getATagParams(name),
        fcid: searchInfo.fcid,
        showDisabled: getShowDisabledAccount("cashReport"),
        ran: Math.random(),
    };
    const basePath = isErp.value ? "Journal" : "IEJournal";
    globalWindowOpenPage("/Cashier/" + basePath + "?" + getUrlSearchParams(params), "收支类别日记账");
};
const CDAccountFilterRef = ref();
const handleInit = () => {
    const { start, end } = dateInfo.value;
    searchInfo.startPid = start;
    searchInfo.endPid = end;
    nextTick(() => {
        CDAccountFilterRef.value?.handleInit();
        selectedList.value = accountOptions.value.map((i) => i.id);
        searchInfo.CD_ACC_IDS = getSearchInfoCD(selectedList.value, props.options);
        getTableList();
    });
};
defineExpose({ handleInit });

watch(selectedList, () => {
    searchInfo.CD_ACC_IDS = getSearchInfoCD(selectedList.value, props.options);
});

const getTableListApi = (type: "Search" | "Total", ie_type?: number | string) => {
    const commonParams =  getSearchParams(searchInfo, "cashReport");
    const params = type === "Search" ? { ...commonParams, ie_type } : { ...commonParams};
    const urlPath = type === "Search" ? "ListForIE" : "ListForIETotal";
    return request({ url: "/api/CashierReport/" + urlPath + "?" + getUrlSearchParams(params), method: "post" });
};
function disabledDateStart(time: Date) {
    const endDate = dayjs(searchInfo.endPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    return time.getTime() > endDate || time.getTime() < asStartDate;
}
function disabledDateEnd(time: Date) {
    const startDate = dayjs(searchInfo.startPid).valueOf();
    const asStartDate = dayjs(accountSetStore.accountSet?.asStartDate).valueOf();
    const minTime = Math.max(startDate, asStartDate);
    return time.getTime() < minTime;
}
function headerDragend(width: number, prop: string) {
    columns.value = calcColumns();
}
</script>

<style scoped lang="less">
@import "@/style/Cashier/Report.less";
</style>
