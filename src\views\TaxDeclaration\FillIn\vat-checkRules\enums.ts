/**
 * 报税业务类型枚举
 */
export enum ReportTypeEnum {
  GeneralVatMonthly = "10", // 一般纳税人增值税月报
  SmallScaleVatQuarter = "20", // 小规模纳税人增值税季报
  IncomeQuarter = "30", // 查账征收企业所得税季报
  FinancialStatement = "40", // 财务报表
  IncomeAnnual = "50", // 查账征收企业所得税年报
  IncomeAnnualBaseInfo = "51", // 查账征收企业所得税年报-基础信息
  IncomeAnnualInitForm = "52", // 查账征收企业所得税年报-初始化表单信息
  IncomeAnnualAllReportInfo = "53", // 查账征收企业所得税年报-全部报表信息
  IncomeAnnualFormula = "54", //年度报税申报-报表规则
}
