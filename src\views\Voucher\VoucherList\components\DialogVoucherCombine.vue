<template>
    <el-dialog
        modal-class="modal-class"
        :destroy-on-close="true"
        v-model="display"
        title="凭证合并"
        center
        width="440px"
        class="dialogDrag">
        <div class="combine-content" v-dialogDrag>
            <div class="combine-main">
                <div class="err-tip highlight-red">*凭证合并不可逆，请谨慎操作！</div>
                <div class="mt-20 mb-10">
                    <div>科目合并：</div>
                    <div>
                        <el-checkbox v-model="combineInfo.debit">相同借方科目合并</el-checkbox>
                        <el-checkbox v-model="combineInfo.credit">相同贷方科目合并</el-checkbox>
                    </div>
                </div>
                <div class="warn-tip">
                    <div class="row">1、默认将所勾选的凭证依次罗列合并成一张凭证，如需合并科目请勾选科目合并规则</div>
                    <div class="row">2、原凭证系统会自动删除至回收站</div>
                    <div class="row">3、凭证合并，可能影响现金流量表取数，请谨慎操作</div>
                    <div class="row">
                        <span>4、可先到</span>
                        <span :class="{ link: hasPermission() }" @click="changeCurrentRoute">设置-备份恢复</span>
                        <span>中备份账套，再进行操作</span>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleCombine">确定</a>
                <a class="button ml-20" @click="display = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { checkPermission } from "@/util/permission";
import { globalWindowOpenPage } from "@/util/url";

import type { IVoucherModel, ICombineInfo } from "@/views/Voucher/VoucherList/types";

const emit = defineEmits<{ (event: "combine", combineInfo: ICombineInfo): void }>();

const display = ref(false);
const combineInfo = reactive<ICombineInfo>({
    debit: false,
    credit: false,
    selectList: [],
});

function handleOpenDialog(selectList: Array<IVoucherModel>) {
    combineInfo.selectList.length = 0;
    combineInfo.selectList = selectList;
    display.value = true;
}
defineExpose({ handleOpenDialog });

function hasPermission() {
    return checkPermission(["backup-canbackup"]);
}
function changeCurrentRoute() {
    if (!hasPermission()) return;
    globalWindowOpenPage(window.isErp ? "/backup" : "/Settings/Backup", "备份恢复");
}
function handleCombine() {
    display.value = false;
    emit("combine", combineInfo);
}
</script>

<style lang="less" scoped>
.combine-content {
    .combine-main {
        padding: 20px 40px;
        .err-tip {
            font-size: 16px;
            text-align: center;
        }
        .warn-tip {
            padding-left: 20px;
            .row {
                font-size: 12px;
                color: #666666;
                line-height: 22px;
                .link {
                    font-size: 12px;
                }
                &:first-child {
                    position: relative;
                    &::before {
                        content: "";
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        position: absolute;
                        top: 4px;
                        left: -20px;
                        background: url("@/assets/Icons/warn.png") no-repeat 100% 100%;
                    }
                }
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
