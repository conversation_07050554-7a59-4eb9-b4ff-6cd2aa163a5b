export const printTypeList = [
    { label: "发票版 14*24cm（推荐）", value: 4 },
    { label: "A4整版", value: 6 },
    { label: "A4两版（推荐）", value: 1 },
    { label: "A4三版", value: 2 },
    { label: "A4宽 12*21cm", value: 3 },
    { label: "A5", value: 7 },
    { label: "B5", value: 8 },
];

export const getMaxMargin = (printType: number,type:string='total') => {
    let margin = 0;
    let top = 0;
    switch (printType) {
        case 4:
            margin = 21;
            top = 21;
            break;
        case 1:
        case 6:
        case 8:
            top = 14;
            margin = 29;
            break;
        case 2:
            top = 5;
            margin = 13;
            break;
        case 3:
            top = 9;
            margin = 18;
            break;
        case 7:
            top = 14;
            margin = 29;
            break;
    }
    return type === 'total' ? margin :top ;
};
