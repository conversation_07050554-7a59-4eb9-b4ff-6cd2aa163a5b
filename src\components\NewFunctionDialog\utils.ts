import { request } from "@/util/service";
import { getCookie } from "@/util/cookie";

export let newFunctionHref = "";
export let newFunctionBody = "";
// 每次有了新功能发布，都需要改一下以下版本号
export const newFunctionVersion = "2025.04.10";
export const newFunctionFn = () => {
    return new Promise((resolve) => {
        request({
            url: window.apimHost + `/api/Message/JzPcUpdateNotice?version=${newFunctionVersion}`,
            method: "get",
            headers: {
                Authorization: `Bearer ${getCookie("ningmengcookie")}`,
            },
        }).then((res: any) => {
            if (res.code === 1000 && res.data) {
                newFunctionHref = res.data.link;
                newFunctionBody = res.data.body;
                resolve(true);
            } else {
                resolve(false);
            }
        });
    });
};
