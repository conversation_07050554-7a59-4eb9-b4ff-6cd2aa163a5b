<template>
    <div class="content">
        <ContentSlider :slots="slots" :currentSlot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">员工信息</div>
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <SearchInfoContainer ref="containerRef">
                                <template v-slot:title>所有员工</template>
                                <div class="line-item input">
                                    <div class="line-item-title">部门：</div>
                                    <div class="line-item-field">
                                        <div class="jqtransform float-l" style="width: 122px">
                                            <Select 
                                                :teleported="false" 
                                                v-model="searchInfo.department" 
                                                placeholder="请选择"
                                                :filterable="true"
                                                :filter-method="departFilterMethod"
                                            >
                                                <Option
                                                    v-for="item in showDepartmentList"
                                                    :value="item.value"
                                                    :label="item.label"
                                                    :key="item.value"
                                                ></Option>
                                            </Select>
                                        </div>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">员工状态：</div>
                                    <div class="line-item-field aatype-select">
                                        <el-select v-model="searchInfo.status" :teleported="false" style="width: 122px">
                                            <el-option
                                                :label="item.label"
                                                :value="item.value"
                                                v-for="item in statusList"
                                                :key="item.value"
                                            ></el-option>
                                        </el-select>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">姓名：</div>
                                    <div class="line-item-field">
                                        <Tooltip :content="searchInfo.e_name" :isInput='true' placement="right" :teleported="true">
                                            <el-input v-model="searchInfo.e_name" style="width: 132px" clearable></el-input>
                                        </Tooltip>
                                    </div>
                                </div>
                                <div class="line-item input">
                                    <div class="line-item-title">入职日期：</div>
                                    <div class="line-item-field">
                                        <!-- :disabled-date="disabledStartTime" -->
                                        <el-date-picker
                                            style="width: 132px; height: 32px"
                                            v-model="searchInfo.time_s"
                                            type="date"
                                            placeholder=" "
                                            value-format="YYYY-MM-DD"
                                            size="small"
                                            clearable
                                            :teleported="false"
                                            :disabled-date="disabledDateStart"
                                        />
                                        <span class="s-title item ml-10 mr-10">至</span>
                                        <!-- :disabled-date="disabledEndTime" -->
                                        <el-date-picker
                                            style="width: 132px; height: 32px"
                                            v-model="searchInfo.time_e"
                                            type="date"
                                            placeholder=" "
                                            value-format="YYYY-MM-DD"
                                            size="small"
                                            clearable
                                            :teleported="false"
                                            :disabled-date="disabledDateEnd"
                                        />
                                    </div>
                                </div>
                                <div class="buttons" style="text-align: left">
                                    <a class="button solid-button" @click="getTableData(true)">确定</a>
                                    <a class="button" @click="handleClose">取消</a>
                                    <a class="button" @click="handleReset">重置</a>
                                </div>
                            </SearchInfoContainer>
                        </div>
                        <div class="main-tool-right">
                            <div class="mr-20">
                                <el-checkbox
                                    v-model="searchInfo.showAllInfo"
                                    label="显示所有信息"
                                    @change="() => getTableData()"
                                ></el-checkbox>
                            </div>
                            <a
                                v-permission="['employinfo-canedit']"
                                class="button solid-button mr-10 aa-newemployee"
                                @click="addEmployee"
                                v-show="!isErp"
                                >增加员工</a
                            >
                            <a class="button mr-10" @click="currentSlot = 'import'" v-permission="['employinfo-canimport']"> 导入 </a>
                            <a class="button mr-10" @click="handleExport" v-permission="['employinfo-canexport']">导出</a>
                            <Dropdown
                                btnTxt="批量操作"
                                class="mr-10"
                                :width="126"
                                :downlistWidth="128"
                                v-if="checkPermission(['employinfo-canedit', 'employinfo-candelete', 'assistingaccount-canedit'])"
                            >
                                <li
                                    v-permission="['employinfo-candelete']"
                                    @click="BatchDelete"                                
                                >
                                    批量删除
                                </li>
                                <li
                                    v-permission="['employinfo-canedit']" 
                                    @click="BatchLeave"                             
                                >
                                    批量离职处理
                                </li>
                                <li
                                     v-permission="['employinfo-canedit']"
                                     @click="BatchAlterSubject"                                
                                >
                                    修改计提工资科目
                                </li>
                                <li
                                    v-permission="['employinfo-canedit']"
                                    @click="alterInsurance"
                                >
                                    批量修改五险一金
                                </li>
                                <li
                                    v-show="!isErp"
                                    v-permission="['assistingaccount-canedit']"
                                    @click="syncAccounting"
                                >
                                    同步至辅助核算
                                </li>
                            </Dropdown>
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <Table
                            ref="employTableRef"
                            :data="tableData"
                            :columns="columns"
                            :loading="loading"
                            :pageIsShow="true"
                            :page-sizes="paginationData.pageSizes"
                            :page-size="paginationData.pageSize"
                            :total="paginationData.total"
                            :current-page="paginationData.currentPage"
                            :scrollbarShow="true"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            @refresh="handleRerefresh"
                            @selection-change="tableSelectionChange"
                            :tableName="setModule"
                        >
                            <template #idcard>
                                <el-table-column min-width="150" align="left" header-align="left">
                                    <template #header>
                                        <div class="cell-header">
                                            <span>证件号码</span>
                                            <div class="ml-5 eyes-button" @click="changeIdStatus" v-if="['10001', '10005'].includes(userPermission)">
                                                <div :class="['eyes-icon', idHide ? 'eyes-hide' : 'eyes-open']"></div>
                                            </div>
                                        </div>
                                    </template>
                                    <template #default="scope">
                                        {{ scope.row.e_id }}
                                    </template>
                                </el-table-column>
                            </template>
                            <template #operator>
                                <el-table-column 
                                    label="操作" 
                                    min-width="80" 
                                    align="left" 
                                    header-align="left"
                                    :resizable="false"
                                >
                                    <template #default="scope">
                                        <a v-permission="['employinfo-canedit']" class="link" @click="EditHandle(scope.row.e_sn)"> 编辑 </a>
                                        <a v-permission="['employinfo-candelete']" class="link" @click="deleteHandle(scope.row.e_sn)"> 删除 </a>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
            </template>
            <template #import>
                <div>
                    <div class="slot-title">员工信息导入</div>
                    <div style="width: 1000px; margin: 0 auto">
                        <ImportSlot :asId="asId" @cancelImport="cancel"></ImportSlot>
                    </div>
                </div>
            </template>
            <template #edit>
                <div style="height: 100%;">
                    <div class="slot-title">{{title}}</div>
                    <div class="slot-content align-center">
                        <EditSlot
                            v-if="E_SN !== 0 && currentSlot == 'edit'"
                            :title="title"
                            :asId="asId"
                            :E_SN="E_SN"
                            :subject="subject"
                            :saveType="saveType"
                            :tableData="tableData"
                            @cancelEdit="cancel"
                            @successModify="successModify"
                            @saveSubject="saveSubject"
                        ></EditSlot>
                    </div>
                </div>
            </template>
        </ContentSlider>

        <el-dialog title="提示" center v-model="syncShow" :width="syncData.skipMsg ? '500px' : '350px'" class="dialogDrag">
            <div class="sync-tip-dialog" v-dialogDrag>
                <div class="sync-tip-box">
                    <div class="flex-center" :class="{'pd20': !syncData.skipMsg}">
                        本次同步成功：{{ syncData.succeeCount }}<span v-if="syncData.skipCount > 0">，跳过：{{ syncData.skipCount }}</span>
                    </div>
                    <div v-if="syncData.skipMsg">
                        <div class="mt-10 flex-center">被跳过的员工：<span class="flex-1">{{ syncData.skipMsg }}</span></div>
                        <div class="mt-20 center">注：与职员辅助核算中重复的员工，已被跳过！可前往<br><span  class="link" @click="goToAccounting">【设置-辅助核算-职员】</span>手动新增</div>
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="syncShow = false">确定</a>
                </div>
            </div>
        </el-dialog>
        <!-- 批量修改五险一金 -->
        <BatchInsurance 
            ref="BatchInsuranceRef" 
            :checkedTableData="checkedTableData"
            @batchSuccess="batchSuccess"
        ></BatchInsurance>
        <!-- 批量离职 -->
        <el-dialog title="批量离职处理" center v-model="leaveShow" width="500px" @close="closeLeave" class="dialogDrag">
            <div class="leave-dialog" v-dialogDrag>
                <div class="leave-box">
                   <div class="leave-item">
                        <span class="leave-label">员工状态</span>
                        <el-input 
                            v-model="inputStatus" 
                            style="width: 240px" 
                            placeholder="" 
                            :disabled="true"
                        ></el-input>
                   </div>
                   <div class="leave-item mt-20">
                        <span class="leave-label">离职日期</span>
                        <el-date-picker 
                            v-model="endDate" 
                            style="width: 240px"
                            :editable="false"
                            value-format="YYYY-MM-DD"
                        ></el-date-picker>
                   </div>
                   <div class="leave-item mt-20">
                        <span class="leave-label">工资停发日期</span>
                        <el-date-picker 
                            v-model="salaryEndDate" 
                            style="width: 240px"
                            :editable="false"
                            value-format="YYYY-MM-DD"
                        ></el-date-picker>
                   </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="confirmLeave">确定</a>
                    <a class="button ml-10" @click="closeLeave">取消</a>
                </div>
            </div>
        </el-dialog>
        <!-- 修改计提工资科目 -->
        <el-dialog title="修改计提工资科目" center v-model="alterSubjectShow" width="500px" @close="closeAlterSubject" class="dialogDrag">
            <div class="leave-dialog" v-dialogDrag>
                <div class="leave-box">
                   <div class="leave-item item-subject">
                        <span class="leave-label" style="width: 150px;">计提工资科目</span>
                        <SelectV2
                            ref="subjectRef"
                            :options="showSalaryAsubList"
                            :teleported="false"
                            :fit-input-width="true"
                            :filterable="true"
                            placeholder="请选择"
                            v-model="salaryAsub"
                            :props="salaryAsubProps"
                            :toolTipOptions="{ dynamicWidth: true }"
                            :isOuterTooltip="false"
                            @visible-change="clickChange"
                            :remote="true"
                            :filter-method="salaryAsubFilterMethod"
                            :isSuffixIcon="true"
                        ></SelectV2>
                   </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="confirmAlterSubject">确定</a>
                    <a class="button ml-10" @click="closeAlterSubject">取消</a>
                </div>
            </div>
        </el-dialog>
        <AuxiliaryDialog
            ref="AuxiliaryDialogRef"
            v-model="auxiliaryDialogShow"
            :title="'计提工资科目'"
            :asubName="auxiliaryAsubName"
            :aacode="auxiliaryCode"
            :aaReadonly="auxiliaryReadonly"
            :aaAllowNull="auxiliaryAllowNull"
            :departId="''"
            :employName="''"
            @setAsubAAE="setAsubAAE"
            :aaId="auxiliaryId"
        ></AuxiliaryDialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "EmployInfo",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import ImportSlot from "./components/ImportSlot.vue";
import EditSlot from "./components/EditEmployInfo.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { usePagination } from "@/hooks/usePagination";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISelectItem, IsearchInfo, IEmployData, IEmployItem, ISyncData } from "./types";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams, globalExport, globalWindowOpenPage } from "@/util/url";
import { setColumns } from "./utils";
import { request, type IResponseModel } from "@/util/service";
import { ref, reactive, onMounted, watch, watchEffect, nextTick, computed } from "vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import { checkPermission } from "@/util/permission";
import BatchInsurance from "./components/BatchInsurance.vue";
import { ElAlert } from "@/util/confirm";
import SelectV2 from "@/components/SelectV2/index.vue";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import AuxiliaryDialog from "./components/AuxiliaryDialog.vue";
import type { IAssistingAccount } from "@/api/assistingAccounting";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getCookie, setCookie } from "@/util/cookie";
import { getGlobalToken } from "@/util/baseInfo";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "EmployInfo";
const slots = ["main", "import", "edit"];
const currentSlot = ref("main");
const title = ref("");
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const saveType = ref("");
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const isErp = ref(window.isErp);

const searchInfo = reactive<IsearchInfo>({
    department: "0",
    status: 2,
    e_name: "",
    time_s: "",
    time_e: "",
    showAllInfo: false,
});

const statusList = [
    { value: 2, label: "全部" },
    { value: 0, label: "在职" },
    { value: 1, label: "离职" },
];

const loading = ref<boolean>(false);

const accountsetStore = useAccountSetStoreHook();
const asId = accountsetStore.accountSet?.asId;

const columns = ref<Array<IColumnProps>>();

function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.department = "0";
    searchInfo.status = 2;
    searchInfo.e_name = "";
    searchInfo.time_s = "";
    searchInfo.time_e = "";
}

function cancel(val: Boolean) {
    currentSlot.value = "main";
    if (val) {
        getTableData();
    }
}

function successModify() {
    currentSlot.value = "main";
    getTableData();
}

function handleExport() {
    const params = {
        department: searchInfo.department,
        status: searchInfo.status,
        name: searchInfo.e_name,
        timeStart: searchInfo.time_s,
        timeEnd: searchInfo.time_e,
        combineSearchInfo: "",
    };
    globalExport(`/api/Employee/Export?` + getUrlSearchParams(params));
}

const E_SN = ref();
// let flag = true;
function getDefaultSalaryAsub() {
    request({
        url: `/api/Employee/GetDefaultSalaryAsub`,
        method: "post",
    }).then((res: any) => {
        if (res.state == 1000) {
            subject.value = res.data + "";
            // flag = false;
            currentSlot.value = "edit";
        }
    });
}

function addEmployee() {
    request({
        url: `/api/InsuranceSettings/ExistsSettings`,
        method: "post",
    }).then((res: any) => {
        if (res.data) {
            saveType.value = "Add";
            title.value = "新增员工";
            E_SN.value = -1;
            getDefaultSalaryAsub()
            // flag ? getDefaultSalaryAsub() : (currentSlot.value = "edit");
        } else {
            ElConfirm("亲，当前账套未设置五险一金缴纳比例！");
        }
    });
}

const subject = ref("");
function saveSubject(val: string) {
    subject.value = val;
}
function EditHandle(SN: number) {
    saveType.value = "Edit";
    E_SN.value = SN;
    title.value = "编辑员工";
    currentSlot.value = "edit";
}

function deleteHandle(E_SN: number) {
    ElConfirm("确定删除当前选中的员工信息吗？").then((r: boolean) => {
        if (r) {
            request({
                url: `/api/Employee?sn=${E_SN}`,
                method: "delete",
            }).then((res: any) => {
                if (res.state === 1000) {
                    if (res.data) {
                        ElNotify({
                            type: "success",
                            message: "删除成功",
                        });
                        getTableData();
                        window.dispatchEvent(new CustomEvent('reloadAccountSubjectTree'));
                    }
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
}

const departmentList = ref<ISelectItem[]>([{value: "0", label: "全部"}]);
function getDepartmentApi() {
    request({
        url: `/api/AssistingAccounting/DepartmentList?showAll=true&onlyLeaf=true`,
        method: "get",
    }).then((res: any) => {
        if (res.state == 1000) {
            let list = res.data.reduce((prev: ISelectItem[], item: any) => {
                if (item.aaname !== "未录入辅助核算") {
                    prev.push({
                        value: item.aaeid + "",
                        label: item.aaname,
                    });
                }
                return prev;
            }, []);
            departmentList.value = [...departmentList.value, ...list];
        }
    });
}

const tableData = ref<IEmployItem[]>([]);
const getTableData = (searchFlag?: true) => {
    if (searchFlag) paginationData.currentPage = 1;
    const data = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        department: searchInfo.department,
        status: searchInfo.status,
        name: searchInfo.e_name,
        timeStart: searchInfo.time_s === null ? "" : searchInfo.time_s,
        timeEnd: searchInfo.time_e === null ? "" : searchInfo.time_e,
        combineSearchInfo: "",
    };
    initIdShow();
    let jointUrl = searchInfo.showAllInfo ? "PagingFullList?" : "PagingSimpleList?";
    loading.value = true;
    request({
        url: `/api/Employee/` + jointUrl + getUrlSearchParams(data),
        method: "get",
    })
        .then((res: IResponseModel<IEmployData>) => {
            columns.value = setColumns(searchInfo.showAllInfo, columns.value, res.data.data[0]);
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
        })
        .catch((error) => {
            console.log(error);
        })
        .finally(() => {
            loading.value = false;
        });
};

const initSearchInfo = () => {
    return Promise.all([getDepartmentApi()]).then(() => {
        getTableData();
    });
};

onMounted(() => {
    initSearchInfo();
    initIdShow();
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableData();
});

//筛选时间区间
const disabledDateStart = (date: Date) => {
    return date.getTime() > new Date(searchInfo.time_e).getTime();
};
const disabledDateEnd = (date: Date) => {
    return date.getTime() < new Date(searchInfo.time_s).getTime();
};

//同步至职员辅助核算
const employTableRef = ref<InstanceType<typeof Table>>();
let checkedTableData = ref<IEmployItem[]>([]);
const syncData = reactive<ISyncData>({
    succeeCount: 0,
    skipCount: 0,
    skipMsg: "",
});
const syncShow = ref(false);
function tableSelectionChange(value: any) {
    checkedTableData.value = value;
}
function clearSelect() {
    employTableRef.value?.getTable()?.clearSelection();
}
function getEmployNo() {
    let eSns: number[] = [];
    checkedTableData.value.forEach((v: any) => {
        eSns.push(v.e_sn);
    });
    return eSns;
}
const checkSelEmpty = () => {
    if (!checkedTableData.value.length) {
        ElNotify({
            type: "warning",
            message: "亲，您还没有选择员工哦~",
        });
        return false;
    }
    return true;
}
const syncAccounting = () => {
    if(!checkSelEmpty()) return;
    request({
        url: "/api/Employee/EmployeeSync",
        method: "post",
        data: getEmployNo()
    }).then((res: any) => {
        if (res.state === 1000) {
            syncShow.value = true;
            syncData.succeeCount = res.data.succeeCount;
            syncData.skipCount = res.data.skipCount;
            syncData.skipMsg = res.data.skipMsg;
        }
    }).finally(()=>{
        clearSelect();
    })
}
const goToAccounting = () => {
    if (checkPermission(["assistingaccount-canview"])) {
        globalWindowOpenPage(`/Settings/AssistingAccounting?aaType=10003`, "辅助核算");
        syncShow.value = false;
    } else {
        ElNotify({ 
            type: "warning",
            message: "请联系账套管理员设置！"     
        });
    }
}

//批量修改五险一金
const BatchInsuranceRef = ref();
const alterInsurance = () => {
    BatchInsuranceRef.value?.init();
}
const batchSuccess = () => {
    clearSelect();
    getTableData();
}

//证据号码显示隐藏(管理员和主管才有此按钮)
const userPermission = useAccountSetStore().accountSet?.permission as string;
const idHide = ref(false); //true隐藏，false不隐藏
const initIdShow = () => {
    request({
        url: "/api/Employee/GetIDCardIsHide",
        method: "get",
    }).then((res: IResponseModel<boolean>) => {
        idHide.value = res.data; 
    });
}
const changeIdStatus = () => {
    if (!getCookie(getGlobalToken() + "-employIdShowDialog")) {
        ElAlert({
            message: "证件号码隐藏/显示是对账套内所有员工生效。隐藏后，员工列表员工的证件号码只显示部分号码，其余号码将会用*代替显示",
            hideCancel: true,
            options: {
                confirmButtonText: "关闭",
                cancelButtonText: ""
            }
        });
    }
    setCookie(getGlobalToken() + "-employIdShowDialog", "1", "d365");
    request({
        url: `/api/Employee/SetIDCardDisplay?isHide=${!idHide.value}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        idHide.value = !idHide.value;
        getTableData();
    });
}

//批量删除
const BatchDelete = () => {
    if(!checkSelEmpty()) return;
    ElAlert({message: "亲，确定要删除员工信息吗？"}).then((r)=> {
        if (r) {
            request({
                url: "/api/Employee/BatchDelete",
                method: "post",
                data: getEmployNo()
            }).then((res: any) => {
                if (res.state === 1000) {
                    ElNotify({ 
                        type: "success",
                        message: `成功：${res.data.succeeCount}个，跳过：${res.data.skipCount}个，已有工资数据的员工将会跳过`    
                    });
                    getTableData();
                } else {
                    ElNotify({ 
                        type: "warning",
                        message: res.msg    
                    });
                }
            }).finally(()=>{
                clearSelect();
            })
        }
    })
}

//批量离职
const leaveShow = ref(false);
const inputStatus = ref("离职状态");
const endDate = ref("");
const salaryEndDate = ref("");
const BatchLeave = () => {
    if(!checkSelEmpty()) return;
    leaveShow.value = true;
}
const closeLeave = () => {
    leaveShow.value = false;
    endDate.value = "";
    salaryEndDate.value = "";
}
const confirmLeave = () => {
    if (!endDate.value) {
        ElNotify({ 
            type: "warning",
            message: "离职日期不能为空"    
        });
        return;
    }
    if (!salaryEndDate.value) {
        ElNotify({ 
            type: "warning",
            message: "工资停发日期不能为空"    
        });
        return;
    }
    request({
        url: "/api/Employee/BatchDepart",
        method: "post",
        data: {
            esns: getEmployNo(),
            endDate: endDate.value,
            salaryEndDate: salaryEndDate.value
        }
    }).then((res: any) => {
        if (res.state === 1000) {
            ElNotify({ 
                type: "success",
                message: `成功：${res.data.succeeCount}个，跳过：${res.data.skipCount}个，已离职的员工将会跳过`    
            });
            getTableData();
        } else {
            ElNotify({ 
                type: "warning",
                message: res.msg    
            });
        }
    }).finally(() => {
        clearSelect();
        closeLeave();
    })
}
//批量修改计提工资科目
const alterSubjectShow = ref(false);
const salaryAsub = ref(0);
const aaCode = ref("");
const BatchAlterSubject = () => {
    if(!checkSelEmpty()) return;
    alterSubjectShow.value = true;
}
const salaryAsubProps = {
    value: "value",
    label: "label",
};
const subjectData = ref<Array<IAccountSubjectModel>>();
const asubList = ref(new Array<{ asubType: number; asubList: Array<IAccountSubjectModel> }>());
const accountSubjectStore = useAccountSubjectStore();
const salaryAsubList = ref<any[]>([]);
const accountingStandard = useAccountSetStore().accountSet?.accountingStandard as number;
watchEffect(() => {
    asubList.value = [];
    subjectData.value = [];
    const accountSubjectList: IAccountSubjectModel[] = window.isErp
    ? JSON.parse(JSON.stringify(accountSubjectStore.accountSubjectList))
    : JSON.parse(JSON.stringify(accountSubjectStore.accountSubjectListWithoutDisabled));
    asubList.value.push({
        asubType: 0,
        asubList: accountSubjectList.filter((asub) => asub.status === 0 && asub.isLeafNode).map((asub) => ({ 
            ...asub 
        })),
    });
    let asubGroup: any = {};
    accountSubjectList.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = new Array<IAccountSubjectModel>();
            asubList.value.push({ asubType: asub.asubType, asubList: asubGroup[asub.asubType] });
        }
        if (asub.status === 0 && asub.isLeafNode) {
            asubGroup[asub.asubType].push({ ...asub });
        }
    });
    let asubType;
    switch (accountingStandard) {
        case AccountStandard.FolkComapnyStandard:
            asubType = [1, 9];
            break;
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.CompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            asubType = [1, 5, 6];
            break;
        case AccountStandard.UnionStandard:
            asubType = [1, 7, 10];
            break;
    }
    subjectData.value = asubType?.reduce((prev: IAccountSubjectModel[], item: number) => {
        const foundItem = asubList.value.find((i) => i.asubType === item);  
        const list = foundItem ? foundItem.asubList : [];
        list.forEach((item) => {
            salaryAsubList.value.push({ label: item.asubCode + " " + item.asubAAName, value: item.asubId.toString() });
        });
        prev.push(...list);
        return prev;
    }, []);
});
const AuxiliaryDialogRef = ref();
const auxiliaryDialogShow = ref(false);
const auxiliaryAsubName = ref("");
const clickAsubId = ref();
const auxiliaryCode = ref<string[]>([]); //科目辅助核算类型编码集合
const auxiliaryAllowNull = ref<string[]>([]); //科目辅助核算非必录
const auxiliaryReadonly = ref(false);
const auxiliaryId = ref<string[]>([]); //科目辅助核算设置的值
const clickChange = (visible: boolean) => {
    if (!visible) {
        clickAsubOption(Number(salaryAsub.value));
        showSalaryAsubList.value = JSON.parse(JSON.stringify(salaryAsubList.value));
    }
}
const clickAsubOption = (asubid: number|string) => {
    clickAsubId.value = Number(asubid);
    if (isHasAccount(Number(asubid))) {
        let asubAAName = subjectData.value?.filter((v)=>Number(v.asubId) === Number(asubid))[0].asubAAName || "";
        auxiliaryAsubName.value = asubAAName;
        auxiliaryId.value = aaCode.value ? aaCode.value.split(",") : [];
        nextTick(()=>{
            AuxiliaryDialogRef.value?.getInitAcconting();
        })
    }
}
//判断科目是否开启辅助核算
function isHasAccount(asubid: number) {
    auxiliaryCode.value = [];
    auxiliaryAllowNull.value = [];
    let item = subjectData.value?.filter((v)=>Number(v.asubId) === asubid);
    if(item && item[0] && item[0].aatypes) {
        auxiliaryCode.value = item[0].aatypes.split(",");
        let list = item[0].aatypesAllowNull.includes(",") ? item[0].aatypesAllowNull.split(",") : [item[0].aatypesAllowNull];
        auxiliaryAllowNull.value = auxiliaryCode.value.map((el) => {
            let index = list.indexOf(el);
            return index !==-1 ? el : "0";
        });
        return true;
    } else {
        return false;
    }
}
//判断所有辅助核算明细是否都已选择有值
const assistingAccountingListAll = computed(() => {
    return useAssistingAccountingStore().assistingAccountingListAll;
});
const salaryCodeList = ref();
function isHasDetail() {
    let detailList = aaCode.value.split(",");
    if (detailList.length !== auxiliaryCode.value.length) {  
        return false;  
    } 
    salaryCodeList.value = [];
    for(let i=0; i < auxiliaryCode.value.length; i++) {
        let list = assistingAccountingListAll.value.filter(v => v.aatype === Number(auxiliaryCode.value[i]));
        for (let j=0; j<list.length; j++) {
            salaryCodeList.value.push(list[j].aaeid);
        }
    }
    const hasAllDetails = detailList.every(el =>salaryCodeList.value.includes(Number(el)));
    return  hasAllDetails && detailList.every(detail => detail.trim()); 
}
const setAsubAAE = (data: any) => {
    salaryAsub.value = clickAsubId.value.toString(); //科目
    aaCode.value = data.map((item: any) => item.id).join(",");//辅助核算
}
const closeAlterSubject = () => {
    alterSubjectShow.value = false;
    salaryAsub.value = 0;
    aaCode.value = "";
}
const confirmAlterSubject = () => {
    if (!salaryAsub.value) {
        ElNotify({ 
            type: "warning",
            message: "计提工资科目不能为空"    
        });
        return;
    }
    if (isHasAccount(Number(salaryAsub.value)) && !isHasDetail()) {
        let item = subjectData.value?.filter(item => item.asubId === Number(salaryAsub.value))[0];
        ElNotify({ 
            type: "warning",
            message: `计提工资科目：${item?.asubCode} ${item?.asubAAName}启用了辅助核算，请重新选择计提工资科目，完善辅助核算明细后，再点击保存哦~`    
        });
        return;
    }
    let data = {
        esns: getEmployNo(),
        salaryAsub: salaryAsub.value,
        aaCode: isHasAccount(Number(salaryAsub.value)) ? aaCode.value : "",
    }
    request({
        url: "/api/Employee/BatchUpdateASub",
        method: "post",
        data
    }).then((res: any) => {
        if (res.state === 1000) {
            ElNotify({ 
                type: "success",
                message: `成功：${res.data.succeeCount}个，跳过：${res.data.skipCount}个`    
            });
            closeAlterSubject();
            getTableData();
        } else {
            ElNotify({ 
                type: "warning",
                message: res.msg    
            });
        }
    }).finally(() => {
        clearSelect();
    })
}

const showDepartmentList = ref<Array<ISelectItem>>([]);
const showSalaryAsubList = ref<Array<any>>([]);
watchEffect(() => { 
    showDepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));  
    showSalaryAsubList.value = JSON.parse(JSON.stringify(salaryAsubList.value)); 
});
function departFilterMethod(value: string) {
    showDepartmentList.value = commonFilterMethod(value, departmentList.value, 'label');
}
function salaryAsubFilterMethod(value: string) {
    showSalaryAsubList.value = commonFilterMethod(value, salaryAsubList.value , 'label');
}
</script>

<style scoped lang="less">
@import "@/style/SelfAdaption.less";

.line-item-field {
    :deep(.el-date-editor) {
        & .el-input__suffix-inner {
            position: absolute;
            right: 26px;
            top: 10px;
        }
    }
}
:deep(.el-table-fixed-column--left .el-table__cell) {
    border-right: 1px solid var(--el-table-border-color);
}
:deep(.el-table__empty-text){
    line-height: 563px;
}
:deep(.dropdown-button) {
    .button.dropdown {
        background-position-x: 92px;
    }
}
.sync-tip-box {
    padding: 20px 80px;
    .flex-center {
        display: flex;
        justify-content: center;
    }
    .flex-1 {
        flex: 1;
        min-width: 0;
    }
    .pd20 {
        padding: 20px;
    }
}
.sync-tip-dialog {
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
:deep(.el-table) {
    thead.is-group tr th div.cell-header {
        padding: 0;
    }
}
.cell-header {
    display: flex;
    align-items: center;
    .eyes-button {
        cursor: pointer;
        position: relative;
        padding: 0 !important;
    }
    .eyes-icon {
        width: 20px;
        height: 20px;
        padding: 0 !important;
    }
    .eyes-hide {
        background: url(@/assets/Salary/eyes-close.png)  center center no-repeat;
        background-size: 100% 100%;
    }
    .eyes-open {
        background: url(@/assets/Salary/eyes-open.png)  center center no-repeat;
        background-size: 100% 100%;
    }
}
.leave-dialog {
    .leave-box {
        padding: 30px 80px;
    }
    .leave-item {
        display: flex;
        align-items: center;
        &.item-subject {
            margin: 30px auto;
        }
    }
    .leave-label {
        margin-right: 10px;
        display: block;
        width: 100px;
        text-align: right;
        &::before {
            content: "*";
            color: var(--red);
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
    :deep(.el-date-editor) {
        .el-input__prefix {
            position: absolute;
            right: 0;
        }
        .el-input__suffix-inner {
            position: absolute;
            right: 26px;
            top: 9px;
        }
    }
}
.warning {
    :deep(.el-select) {
        .el-input .el-input__wrapper {
            box-shadow: 0 0 0 1px var(--red);
        }
    }
    :deep(.el-select-v2) {
        .el-select-v2__wrapper {
            border-color: var(--red);
        }
        .el-select-v2__placeholder {
            text-align: left;
        }
    }
}
</style>
