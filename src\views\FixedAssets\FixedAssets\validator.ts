import { ElNotify } from "@/util/notify";
import type { AccountingStandard } from "@/api/accountSet";
function checkQuoteForFA(str: string) {
    // if (str.match("[]")) {
    // let reg = /\\\\:'\"\\n;&/;
    const reg = /:|'|"|\n|;|&|\\/;
    if (reg.test(str)) {
        return true;
    }
    return false;
}

export function isInteger(str: string) {
    const regu = /^[-]{0,1}[0-9]{1,}$/;
    return regu.test(str);
}

export function isDecimal(str: string) {
    if (isInteger(str)) return true;
    const re = /^[-]{0,1}(\d+)[.]+(\d+)$/;
    if (re.test(str)) {
        if (!(re.exec(str) as any)[1] && !(re.exec(str) as any)[2]) return false;
        return true;
    } else {
        return false;
    }
}

/* * 用途：检查输入字符串是否是整数或者带2位小数以内小数的数字格式,不可以是负数*/
export function isDecimalPointLaterTwo(str: string) {
    if (isInteger(str)) return true;
    const re = /^\d+(\.\d{1,2})?$/;
    if (re.test(str)) {
        return true;
    } else {
        return false;
    }
}

export const checkAssetsInput: (data: any, standard: AccountingStandard, isAmortization: boolean) => boolean = (
    data: any,
    standard: AccountingStandard,
    isAmortization: boolean
) => {
    const faProperty = "faProperty" in data ? data.faProperty : data.fa_property;
    const depInfo = faProperty === 0 ? "折旧" : "摊销";
    if (data.faNum === "" || data.faNum.trim() === "") {
        ElNotify({
            type: "warning",
            message: "请录入资产编号！",
        });
        return false;
    }
    if (data.faNum.length > 20) {
        ElNotify({
            type: "warning",
            message: "资产编号不能超过20位哦~",
        });
        return false;
    }
    if (checkQuoteForFA(data.faNum)) {
        ElNotify({
            type: "warning",
            message: "资产编号不能带有特殊字符！",
        });
        return false;
    }
    if (data.faName === "" || data.faName.trim() === "") {
        ElNotify({
            type: "warning",
            message: "请录入资产名称！",
        });
        return false;
    }
    if (data.faName.length > 256) {
        ElNotify({
            type: "warning",
            message: "固定资产名称不能超过256位！",
        });
        return false;
    }
    //需要支持特殊字符
    //if (checkQuoteForFA($("#Text4").val())) {
    //    Notify("提示", "资产名称不能带有特殊字符！");
    //    return false;
    //}
    if (data.faType === "") {
        ElNotify({
            type: "warning",
            message: "请选择资产类别！",
        });
        return false;
    }
    //检查是否选择开始日期
    if (!data.startedDate) {
        ElNotify({
            type: "warning",
            message: "请录入开始使用日期！",
        });
        return false;
    }
    if (data.faModel.length > 256) {
        ElNotify({
            type: "warning",
            message: "固定资产规格型号不能超过256位！",
        });
        return false;
    }
    //if (checkQuoteForFA($("#Text6").val())) {
    //    Notify("提示", "资产规格型号不能带有特殊字符！");
    //    return false;
    //}
    if (data.faModel.StartedDate === "") {
        ElNotify({
            type: "warning",
            message: "请录入开始使用日期！",
        });
        return false;
    }
    // var text8value = $('#Text8').combobox('getValue');

    // if (text8value == "" || text8value == "0" || isNaN(text8value)) {
    //     Notify("提示", "请选择使用部门！");
    //     return false;
    // }
    if (!window.isErp) {
        if (data.vendor && data.vendor.length > 256) {
            ElNotify({
                type: "warning",
                message: "固定资产供应商不能超过256位！",
            });
            return false;
        }
        //if (checkQuoteForFA($("#Text9").val())) {
        //    Notify("提示", "资产供应商不能带有特殊字符！");
        //    return false;
        //}
    }
    if (!data.faAsub) {
        ElNotify({
            type: "warning",
            message: "请选择资产科目！",
        });
        return false;
    }
    if (!data.depreciationAsub && faProperty === 0) {
        ElNotify({
            type: "warning",
            message: "请选择累计折旧科目！",
        });
        return false;
    }
    if (!data.depreciationAsub && faProperty === 1 && [1, 2].includes(standard)) {
        ElNotify({
            type: "warning",
            message: "请选择累计摊销科目！",
        });
        return false;
    }
    if (!data.disposalAsub && faProperty !== 2) {
        ElNotify({
            type: "warning",
            message: "请选择资产处置科目！",
        });
        return false;
    }
    const withImpairmentProvision = (standard === 2 && [0, 1].includes(faProperty)) || (standard === 3 && faProperty === 0);
    if (!data.impairmentProvisionAsub && withImpairmentProvision) {
        ElNotify({
            type: "warning",
            message: "请选择资产减值准备科目！",
        });
        return false;
    }
    if (!data.costAsub && !isAmortization) {
        ElNotify({
            type: "warning",
            message: `请选择${depInfo}费用科目！` ,
        });
        return false;
    }

    // $("#Text16").val($("#Text16").val().replace(',', ''));
    if (!isDecimal(data.value)) {
        ElNotify({
            type: "warning",
            message: "请输入正确的资产原值！",
        });
        return false;
    } else if (Number(data.value) === 0) {
        ElNotify({
            type: "warning",
            message: "资产原值应当大于0哦！",
        });
        return false;
    }
    if (checkQuoteForFA(data.value)) {
        ElNotify({
            type: "warning",
            message: "资产原值不能带特殊字符！",
        });
        return false;
    } else if (data.value.split(".")[0].length > 9) {
        ElNotify({
            type: "warning",
            message: "资产原值不能超过亿位！",
        });
        return false;
    }
    if (checkQuoteForFA(data.netSalvageRate)) {
        ElNotify({
            type: "warning",
            message: "残值率不能带特殊字符！",
        });
        return false;
    }
    if (!isDecimalPointLaterTwo(data.netSalvageRate)) {
        ElNotify({
            type: "warning",
            message: "请输入正确的残值率！",
        });
        return false;
    }
    if (checkQuoteForFA(data.useFullLife)) {
        ElNotify({
            type: "warning",
            message: "预计使用月份不能带特殊字符！",
        });
        return false;
    }
    if (!isInteger(data.useFullLife)) {
        ElNotify({
            type: "warning",
            message: "请输入正确的预计使用月份！",
        });
        return false;
    }
    if (Number(data.useFullLife) < 0) {
        ElNotify({
            type: "warning",
            message: "预计使用月份不能为负数！",
        });
        return false;
    }
    if (checkQuoteForFA(data.usedLife)) {
        ElNotify({
            type: "warning",
            message: "已折旧月份不能带特殊字符！",
        });
        return false;
    }
    if (!isInteger(data.usedLife)) {
        ElNotify({
            type: "warning",
            message: `请输入正确的已${depInfo}月份！` ,
        });
        return false;
    }
    if (Number(data.usedLife) < 0) {
        ElNotify({
            type: "warning",
            message: "已折旧月份不能为负数！",
        });
        return false;
    }
    // $("#Text20").val($("#Text20").val().replace(',', ''));
    if (
        !isDecimal(data.accumlatedDepreciation) ||
        parseFloat(data.accumlatedDepreciation) > parseFloat(data.value) //累积折旧不能大于资产原值
    ) {
        ElNotify({
            type: "warning",
            message:  `请输入正确的累计${depInfo}！`,
        });
        return false;
    }
    // $("#Text21").val($("#Text21").val().replace(',', ''));
    if (checkQuoteForFA(data.accumlatedDepreciationTY)) {
        ElNotify({
            type: "warning",
            message: "本年累计折旧不能带特殊字符！",
        });
        return false;
    }
    if (!isDecimal(data.accumlatedDepreciationTY)) {
        ElNotify({
            type: "warning",
            message: `请输入正确的本年累计${depInfo}！`,
        });
        return false;
    }
    // $("#Text22").val($("#Text22").val().replace(',', ''));
    // if (!isDecimal($("#Text22").val())) {
    //     Notify("提示", "请输入正确的减值准备！");
    //     return false;
    // }
    if (Number(data.remainingMonth) < 0) {
        ElNotify({
            type: "warning",
            message: `已${depInfo}月份不能超过预计${depInfo}月份！`,
        });
        return false;
    }
    if ((data.accumlatedDepreciation - data.accumlatedDepreciationTY) < 0) {
        ElNotify({
            type: "warning",
            message: `本年累计${depInfo}不能大于累计${depInfo}！`,
        });
        return false;
    }
    if (Number(data.monthDepreciationValue) < 0) {
        ElNotify({
            type: "warning",
            message: "每月折旧额不能为负数！",
        });
        return false;
    }
    if (data.note.length > 256) {
        ElNotify({
            type: "warning",
            message: "备注不能超过256位！",
        });
        return false;
    }
    //if (checkQuoteForFA($("#Text24").val())) {
    //    Notify("提示", "备注不能带特殊字符！");
    //    return false;
    //}
    return true;
};
