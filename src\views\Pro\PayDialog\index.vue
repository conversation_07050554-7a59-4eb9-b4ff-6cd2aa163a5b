<template>
    <iframe :src="'/proPayDialog.html#' + decodeURIComponent(hash)"></iframe>
</template>
<style scoped lang="less">
iframe {
    width: 100%;
    height: 100%;
    border: none;
    margin: 0;
    padding: 0;
    vertical-align: top;
}
</style>
<script lang="ts">
export default {
    name: "PayDialog",
};
</script>
<script setup lang="ts">
import { nextTick } from "vue";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";

const hash = ref("");
onMounted(() => {
    const route = useRoute();
    setTimeout(() => {
        hash.value = route.query.hash?.toString() || "";
    });
});
</script>
