import { request } from "@/util/service";
import { getServiceId } from "@/util/proUtils";
export function getAccountList() {
    let url = "/api/AccountSetOnlyAuth/TopInfo";
    if (window.isProSystem) {
        //代帐由公司Id控制账套，不需要代理账套列表接口
        if (!window.isAccountingAgent) {
            //专业版记账
            url = window.eHost + "/wb/valveacc_vip_web" + url + "?serviceID=" + getServiceId();
        }
    }
    return request({
        url: url,
        method: "get",
    });
}

export interface IAccountList {
    asid: number;
    appAsId: string;
    asname: string;
    accountStandard: number;
    locked: boolean;
    needLockPassword: boolean;
    asStartMonth: number;
    asStartYear: number;
}
export interface ITopInfo {
    accountList: Array<IAccountList>;
    currentASID: number;
    currentEncriyAS: string;
    currentMonth: string;
    currentYear: string;
    endPeriod: number;
    startPeriod: number;
    userName: string;
}