import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios"
import { carryAppasidParam } from "./url"
import { ElNotify } from "./notify"
import { useBasicInfoStore } from "@/store/modules/basicInfo"
import { useTaxPeriodStore } from "@/store/modules/taxPeriod"

// 无需税务初始化完成就可以调用的接口
const NO_LIMIT_URL = ["/api/TaxBureau/LoginState", "/api/basic/info", "/api/basic/TaxSettings", "/api/basic/top-info"]

function createService() {
  const service = axios.create()
  service.interceptors.request.use(
    (config) => {
      config.withCredentials = true

      config.url = carryAppasidParam(config.url || "")

      // const basicInfoStore = useBasicInfoStore()
      // if (!NO_LIMIT_URL.some((item) => config.url?.includes(item)) && !basicInfoStore.basicInfo.initCompleted) {
      //   console.log("请求被中断")

      //   // 如果接口不在NO_LIMIT_URL列表中，并且初始化未完成
      //   const controller = new AbortController()
      //   config.signal = controller.signal
      //   controller.abort()
      // }

      return config
    },
    //发送失败
    (error) => Promise.reject(error),
  )
  //响应拦截
  service.interceptors.response.use(
    (response) => {
      return response.data
    },
    (error) => {
      if (error.response) {
        if (error.response.status === 401) {
          //获取账套信息出错，请您重新登录或询问账套管理员
          ElNotify({
            message: "获取账套信息出错，请您重新登录或询问账套管理员",
          })
        }
        //状态码判断
        return Promise.reject(error)
      } else {
        return Promise.reject(error)
      }
    },
  )
  return service
}

/**
 * 创建请求函数
 * @param service Axios实例
 * @param defaultConfig 默认配置，可以指定不同的baseURL
 * @returns 返回一个请求函数
 */
function createRequestFunction(service: AxiosInstance, defaultConfig: AxiosRequestConfig = {}) {
  return function (config: AxiosRequestConfig) {
    const configDefault = {
      // timeout: 60000,
      baseURL: window.jHost,
      data: {},
      ...defaultConfig, // 合并传入的默认配置
    }
    return service<IResponseModel<any>, IResponseModel<any>>(Object.assign(configDefault, config))
  }
}

export const service = createService()

// 创建默认请求函数
export const request = createRequestFunction(service)

// 创建使用 jAccApi 作为 baseURL 的请求函数
export const Jrequest = createRequestFunction(service, { baseURL: window.jAccApi })

// 创建带有periodId参数的请求函数 - 延迟获取currentPeriodId
export const requestWithPeriodId = (config: AxiosRequestConfig) => {
  // 在请求时获取store和currentPeriodId
  const texPeriodStore = useTaxPeriodStore()

  // 合并配置，添加periodId参数
  const mergedConfig = {
    ...config,
    params: {
      ...(config.params || {}),
      periodId: texPeriodStore.currentPeriodId,
    },
  }
  // 使用基本请求函数发送请求
  return request(mergedConfig)
}

export interface IResponseModel<T> {
  state: 1000 | 2000 | 9999
  subState: number
  data: T
  msg: string
}
