<template>
    <el-tooltip
        :visible="visible"
        popper-class="el-option-tool-tip"
        effect="light"
        :content="content"
        placement="right"
        :hide-after="0"
        :offset="-5"
    >
        <el-select-v2
            ref="selectRef"
            v-model="value"
            :filterable="filterable"
            :class="[props.class, 'select', 'custom-select-v2', visibleSelect ? 'visibleSelect' : '']"
            :popper-class="popperClass + ' select-down'"
            :options="options"
            :placeholder="placeholder"
            :style="style"
            :teleported="teleported"
            :scrollbar-always-on="scrollbarAlwaysOn"
            :item-height="28"
            :props="prop"
            :reserve-keyword="reserveKeyword"
            :allow-create="allowCreate"
            @change="handleChange"
            @visible-change="handleVisibleChange"
            @input="handleInput"
            @focus="handleFocus"
            @mouseenter="enterInput"
            @mouseleave="leaveInput"
            :remote="remote"
            :filter-method="filterMethod"
        >
            <template #prefix>
                <div
                    v-show="clearable && iconFlag"
                    class="icon_clear_v2"
                    @click="handleClearClick"
                    :style="{ right: iconClearRight + 'px' }"
                >
                    <el-icon color="#a8abb2"><CircleClose /></el-icon>
                </div>
                <div class="arrow-carte" v-if="isSuffixIcon && remote">
                    <i class="el-icon el-select-v2__caret el-input__icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                            <path fill="currentColor" d="m488.832 344.32-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872 319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0"></path>
                        </svg>
                    </i>
                </div>
            </template>
            <template #default="{ item }">
                <ToolTip
                    :content="item[prop.label]"
                    :font-size="fontSize"
                    :dynamicWidth="dynamicWidth"
                    :line-clamp="1"
                    :teleported="true"
                    placement="right"
                    :offset="-8"
                >
                    {{ item[prop.label] }}
                </ToolTip>
            </template>
        </el-select-v2>
    </el-tooltip>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, nextTick, onBeforeUnmount, watch } from "vue";
import ToolTip from "@/components/ToolTip/index.vue";

interface IProps {
    label?: string;
    value?: string;
    options?: string;
    disabled?: string;
}
interface IToolTipOptions {
    fontSize?: number;
    dynamicWidth?: boolean;
}

const defaultProps = {
    label: "label",
    value: "value",
    options: "options",
    disabled: "disabled",
};

const selectRef = ref();

const props = withDefaults(
    defineProps<{
        options: Array<any>;
        modelValue: string | number;
        teleported?: boolean;
        class?: string;
        filterable?: boolean;
        placeholder?: string;
        scrollbarAlwaysOn?: boolean;
        style?: any;
        popperClass?: string;
        clearable?: boolean;
        iconClearRight?: number;
        allowCreate?: boolean;
        props?: IProps;
        reserveKeyword?: boolean;
        toolTipOptions?: IToolTipOptions;
        emptyIsValue?: boolean;
        isOuterTooltip?: boolean;
        filterMethod?: Function;
        remote?: boolean;
        isSuffixIcon?: boolean;
    }>(),
    {
        teleported: true,
        filterable: false,
        class: "",
        placeholder: " ",
        scrollbarAlwaysOn: true,
        style: "",
        popperClass: "",
        clearable: false,
        iconClearRight: 18,
        allowCreate: false,
        props: () => ({}),
        reserveKeyword: true,
        toolTipOptions: () => ({}),
        emptyIsValue: false,
        isOuterTooltip: true,
        filterMethod: ()=>{},
        remote: false,
        isSuffixIcon: false,
    }
);
const fontSize = computed(() => props.toolTipOptions.fontSize || 14);
const dynamicWidth = computed(() => props.toolTipOptions.dynamicWidth || false);

const prop = Object.assign({}, defaultProps, props.props);

const emit = defineEmits<{
    (event: "update:modelValue", args: string | number): void;
    (event: "change", args: string): void;
    (event: "visible-change", args: boolean): void;
    (event: "input", args: string): void;
    (event: "focus", args: FocusEvent): void;
    (event: "keydownEnter", args: KeyboardEvent): void;
}>();

const value = computed<any>({
    get() {
        if (!props.emptyIsValue) return props.modelValue ? props.modelValue : undefined;
        return props.modelValue === undefined ? "" : props.modelValue;
    },
    set(value: string | number) {
        emit("update:modelValue", value === undefined ? "" : value);
    },
});

const handleChange = (value: string) => {
    emit("change", value);
};
const visibleSelect = ref(false);
const handleVisibleChange = (value: boolean) => {
    visibleSelect.value = value;
    if (props.remote) {
        const suffixEl = selectRef.value?.$el?.querySelector(".arrow-carte .el-icon");
        if (value) { 
            suffixEl && suffixEl.classList.add("is-reverse");
        } else {
            suffixEl && suffixEl.classList.remove("is-reverse");
        }
    }
    emit("visible-change", value);
};
const handleInput = (event: InputEvent) => {
    emit("input", (event.target as HTMLInputElement)?.value || "");
};
const handleFocus = (event: FocusEvent) => {
    emit("focus", event);
};
const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter") {
        // selectRef.value.popperRef.style.display = "none";
        emit("keydownEnter", event);
        nextTick().then(() => {
            // value.value = props.modelValue;
            // selectRef.value.popperRef.style.display = "none";
            iconFlag.value = value.value ? true : false;
            // selectRef.value.$refs.inputRef.value = props.options.find((item) => item.value === props.modelValue)?.label || "";
        });
    }
};

const handleClearClick = () => {
    value.value = "";
    iconFlag.value = false;
};
const enterInput = () => {
    iconFlag.value = value.value ? true : false;
    if (!props.isOuterTooltip) {
        checkOverflow();
    }
};
const leaveInput = () => {
    iconFlag.value = false;
    if (!props.isOuterTooltip) {
        hiddenOverflow();
    }
};
const iconFlag = ref(false);

const content = ref("");
const visible = ref(false);
function checkOverflow() {
    const wrapper = selectRef.value?.$el?.querySelector(".el-select-v2__wrapper");
    if (wrapper && wrapper.classList.contains("is-focused")) {
        visible.value = false;
        content.value = "";
        return;
    }
    const placeholder = selectRef.value?.$el?.querySelector(".el-select-v2__placeholder");
    content.value = placeholder ? placeholder.innerText || "" : "";
    visible.value = placeholder.scrollWidth > placeholder.clientWidth && content.value.trim() !== "";
}
function hiddenOverflow() {
    visible.value = false;
}
onMounted(() => {
    const input = selectRef.value?.$refs?.inputRef;
    const placeholder = selectRef.value?.$el?.querySelector(".el-select-v2__placeholder");
    if (input) {
        input.addEventListener("keydown", handleKeyDown);
    }
    if (!props.isOuterTooltip) return;
    if (placeholder) {
        placeholder.addEventListener("mouseenter", checkOverflow);
        placeholder.addEventListener("mouseleave", hiddenOverflow);
    }
});

onBeforeUnmount(() => {
    const input = selectRef.value?.$refs?.inputRef;
    const placeholder = selectRef.value?.$el?.querySelector(".el-select-v2__placeholder");
    if (input) {
        input.removeEventListener("keydown", handleKeyDown);
    }
    if (!props.isOuterTooltip) return;
    if (placeholder) {
        placeholder.removeEventListener("mouseenter", checkOverflow);
        placeholder.removeEventListener("mouseleave", hiddenOverflow);
    }
});

defineExpose({
    focus: () => {
        selectRef.value.focus();
    },
    blur: () => {
        selectRef.value.blur();
    },
    clear: () => {
        value.value = "";
    },
    toggleMenu: () => {
        selectRef.value.toggleMenu();
    },
});

</script>

<style lang="less">
.icon_clear_v2 {
    position: absolute;
    right: 0 !important;
    width: 20px;
    height: 20px;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.custom-select-v2 {
    .el-select-v2__wrapper {
        > div {
            &:not(.el-select-v2__input-wrapper) {
                width: 20px;
                height: 20px;
                position: absolute;
                right: 30px;
            }
        }
    }
}
.arrow-carte {
    position: absolute;
    right: -22px;
    height: 32px;
    top: 50%;
    transform: translateY(-50%);
}
</style>
