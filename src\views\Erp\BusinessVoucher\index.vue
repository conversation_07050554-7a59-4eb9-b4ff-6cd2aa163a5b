<template>
    <div class="content">
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <a class="button solid-button mr-20" v-permission="['businessvoucher-canedit']" @click="oneTouchGenerate">生成凭证</a>
                    <a class="button mr-20" v-show="showMethod === 'entry'" v-permission="['voucher-candelete']" @click="deleteVoucher">
                        删除凭证
                    </a>
                    <span v-permission="['asubrelationsettings-canview']" class="buttonContent mr-20">
                        <a class="button" @click="openAsubRelationSettings"> 科目关联设置 </a>
                        <el-popover
                            content="让业务基础资料和财务会计科目关联对应，生成业务凭证时自动带出"
                            placement="right"
                            :width="300"
                            trigger="hover"
                        >
                            <template #reference>
                                <span class="hover-icon"></span>
                            </template>
                        </el-popover>
                    </span>
                    <span v-permission="['businessvouchertemplate-canview']" class="buttonContent mr-20">
                        <a class="button" @click="openBusinessVoucherTemplate"> 业务凭证模板 </a>
                        <el-popover
                            content="针对所有单据类型或业务场景配置凭证模板，生成业务凭证时自动按模板生成"
                            placement="right"
                            :width="300"
                            trigger="hover"
                        >
                            <template #reference>
                                <span class="hover-icon"></span>
                            </template>
                        </el-popover>
                    </span>
                    <a class="button" @click="showGuidance">业务凭证操作指引</a>
                    <div class="help ml-10" @click="toHelp"><span class="vedio-icon"></span><span>帮助视频</span></div>
                    <ErpRefreshButton :reload="refreshMore"></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <template v-if="showMethod === 'module'">
                        <a class="button switch-button" @click="showByEntry">按业务单据列表展示</a>
                    </template>
                    <template v-else>
                        <div class="input-group">
                            <input
                                type="text"
                                v-model="txtSearch"
                                placeholder="请输入编码/名称等关键词进行搜索"
                                @keydown.enter="showByEntry"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                            />
                            <div class="icon" @click="showByEntry"></div>
                        </div>
                        <a class="button switch-button" @click="showByModule">按模块展示</a>
                    </template>
                </div>
            </div>
            <div class="main-center-options" :class="showMethod === 'entry' && showMore ? 'more' : ''">
                <div class="main-center-option">
                    <span>单据日期：</span>
                    <el-date-picker
                        v-model="startDate"
                        value-format="YYYY-MM-DD"
                        type="date"
                        placeholder="选择日期"
                        style="width: 132px; height: 32px"
                        :disabled-date="disabledDate"
                        @blur="forcedShutdown = false"
                        @focus="closeCombineSelect"
                    />
                    <span class="ml-10 mr-10">至</span>
                    <el-date-picker
                        v-model="endDate"
                        value-format="YYYY-MM-DD"
                        type="date"
                        placeholder="选择日期"
                        style="width: 132px; height: 32px"
                        :disabled-date="disabledDate"
                        @focus="closeCombineSelect"
                        @blur="forcedShutdown = false"
                    />
                </div>
                <template v-if="showMethod === 'entry'">
                    <div class="main-center-option ml-10">
                        <span>凭证合并：</span>
                        <SelectCheckbox
                            ref="voucherCombineSelectRef"
                            :class="{ empty: listVoucherCombineInfo.selectedList.length === 0 }"
                            width="160px"
                            place-holder="请选择凭证合并方式"
                            :useElIcon="true"
                            :options="listVoucherCombineInfo.options"
                            :select-all="false"
                            :show-all="false"
                            :z-index="101"
                            v-model:selectedList="listVoucherCombineInfo.selectedList"
                            @update:selected-list="handleChangeSelectedList"
                            @checkBoxShowChange="(display: boolean) => forcedShutdown = !display"
                        >
                        </SelectCheckbox>
                    </div>
                    <div class="main-center-option">
                        <span>科目合并：</span>
                        <SelectCheckbox
                            ref="asubCombineSelectRef"
                            :class="{ empty: listVoucherCombineInfo.selectedSubjectList.length === 0 }"
                            width="160px"
                            place-holder="请选择科目合并方式"
                            :useElIcon="true"
                            :options="subjectOption"
                            :select-all="false"
                            :show-all="false"
                            :z-index="101"
                            v-model:selectedList="listVoucherCombineInfo.selectedSubjectList"
                            @checkBoxShowChange="(display: boolean) => forcedShutdown = !display"
                        >
                        </SelectCheckbox>
                        <el-popover placement="top" :width="265" trigger="hover">
                            <span>
                                生成的凭证为<span class="highlight-red">暂存凭证</span>时<span class="highlight-red">不会合并科目</span>
                            </span>
                            <template #reference>
                                <span class="hover-icon"></span>
                            </template>
                        </el-popover>
                    </div>
                    <div class="main-center-option mr-10">
                        <span>凭证日期：</span>
                        <el-select
                            style="width: 100px"
                            v-model="listVoucherCombineInfo.date"
                            @blur="forcedShutdown = false"
                            @focus="closeCombineSelect"
                        >
                            <el-option label="单据日期" :value="3000"></el-option>
                            <el-option label="会计日期" :value="3001"></el-option>
                            <el-option label="电脑日期" :value="3002"></el-option>
                        </el-select>
                    </div>
                </template>
                <div class="search-button-container" :class="showMethod === 'entry' ? 'entry' : ''">
                    <div class="main-center-option" v-if="showMethod === 'entry'">
                        <span>摘要分隔符：</span>
                        <el-select
                            class="desc"
                            style="width: 80px"
                            :teleported="false"
                            v-model="listVoucherCombineInfo.separator"
                            @blur="forcedShutdown = false"
                            @focus="closeCombineSelect"
                        >
                            <el-option
                                v-for="(item, index) in erpSeparatorOption"
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                        <el-tooltip
                            effect="light"
                            content="配置摘要生成凭证时，不同字段或凭证合并的摘要均通过这里选择的符号进行连接"
                            placement="top"
                        >
                            <span class="hover-icon"></span>
                        </el-tooltip>
                    </div>
                    <div class="main-center-button ml-10" v-show="showMethod === 'module' || !showMore">
                        <a class="button solid-button" @click="searchPageData">查询</a>
                    </div>
                </div>
                <div class="warning-tip ml-20 mt-10" v-show="showMethod === 'module' && warnTip">
                    因单据生成凭证后不能修改，为保证单据及凭证数据准确性，请确保出入库单据完整不再插单/修改后，再生成库存类单据的凭证哦~
                    <span class="ml-10 delete" @click="warnTip = false"></span>
                </div>
                <div class="more-search" v-show="showMethod === 'entry' && moduleSearchList.length" @click="showMore = !showMore">
                    <a class="button solid-button">更多查询</a>
                    <el-icon v-show="!showMore" color="#fff"><ArrowDown /></el-icon>
                    <el-icon v-show="showMore" color="#fff"><ArrowUp /></el-icon>
                </div>
            </div>
            <div class="main-center-options-more" v-show="showMethod === 'entry' && showMore">
                <div class="more-search-option" v-for="searchOption in moduleSearchList" :key="searchOption">
                    <span class="search-label">{{ getSearchTypeText(searchOption) }}：</span>
                    <VirtualSelectCheckbox
                        v-if="checkUsePublicSelect(searchOption)"
                        :customProps="getSelectProps(searchOption)"
                        :options="getCircleOptions(searchOption)"
                        :use-el-icon="true"
                        :cacheLastSelect="true"
                        width="180px"
                        class="item"
                        v-model:forced-shutdown="forcedShutdown"
                        v-model:selectedList="moreSearchs[getCircleKey(searchOption)] as Array<any>"
                    />
                    <Select
                        :style="{ width: '100px' }"
                        v-if="searchOption === SearchType.HasGenerateVoucher"
                        v-model="moreSearchs.hasGenerateVoucher"
                        class="item"
                        :teleported="false"
                        :fit-input-width="true"
                        @blur="forcedShutdown = false"
                        @focus="closeCombineSelect"
                    >
                        <ElOption :value="-1" label="全部">全部</ElOption>
                        <ElOption :value="1" label="是">是</ElOption>
                        <ElOption :value="0" label="否">否</ElOption>
                    </Select>
                    <DatePicker
                        v-if="searchOption === SearchType.Vdate"
                        v-model:startPid="moreSearchs.vdate_s"
                        v-model:endPid="moreSearchs.vdate_e"
                        :disabledDateStart="disabledDate"
                        :disabledDateEnd="disabledDate"
                        @blur="forcedShutdown = false"
                        @focus="closeCombineSelect"
                    />
                    <template v-if="searchOption === SearchType.Vgid">
                        <el-select
                            v-model="moreSearchs.vgid"
                            :teleported="false"
                            style="width: 132px"
                            @change="handleVgIdChange"
                            @blur="forcedShutdown = false"
                            @focus="closeCombineSelect"
                        >
                            <el-option :value="0" label="请选择">请选择</el-option>
                            <el-option :value="1" label="全部">全部</el-option>
                            <el-option v-for="item in voucherGroup" :value="item.id" :key="item.id" :label="item.name"></el-option>
                        </el-select>
                        <el-input
                            clearable
                            style="width: 132px"
                            type="text"
                            class="ml-10"
                            v-model="moreSearchs.vnum_s"
                            :disabled="!moreSearchs.vgid"
                            @blur="forcedShutdown = false"
                            @focus="closeCombineSelect"
                        ></el-input>
                        <span style="padding: 0 8px; line-height: 30px">至</span>
                        <el-input
                            style="width: 132px"
                            clearable
                            type="text"
                            v-model="moreSearchs.vnum_e"
                            :disabled="!moreSearchs.vgid"
                            @blur="forcedShutdown = false"
                            @focus="closeCombineSelect"
                        ></el-input>
                    </template>
                </div>
                <a class="button solid-button search-2" @click="searchPageData">查询</a>
            </div>
            <div class="main-center">
                <ModuleVoucher
                    v-show="showMethod == 'module'"
                    ref="moduleVoucherRef"
                    :scm-asid="scmAsid"
                    :scm-product-type="scmProductType"
                    :get-search-params="getSearchParams"
                    :isProjectAccountingEnabled="isProjectAccountingEnabled"
                    @generate-voucher="generateVoucherByModule"
                    @check-detail-bill="checkDetailBill"
                >
                </ModuleVoucher>
                <EntryVoucher
                    v-show="showMethod === 'entry'"
                    ref="entryVoucherRef"
                    :scm-as-name="scmAsName"
                    :scm-asid="scmAsid"
                    :scm-product-type="scmProductType"
                    :more-searchs="moreSearchs"
                    :isProjectAccountingEnabled="isProjectAccountingEnabled"
                    :taxEnabled="taxEnabled"
                    :get-search-params="getSearchParams"
                    :syncHasGenerateVoucher="syncHasGenerateVoucher"
                    @set-module-search-list="setModuleSearchList"
                >
                </EntryVoucher>
            </div>
        </div>
        <el-dialog
            destroy-on-close
            v-model="voucherResult"
            title="凭证生成报告"
            center
            :width="dialogWidth"
            class="custom-confirm"
            draggable
        >
            <div id="divVoucherResult" title="" class="voucher-result panel-body panel-body-noborder window-body">
                <div class="result-info" id="resultInfo">
                    <div class="success-content">
                        <div class="t">{{ successInfo }}</div>
                        <div class="l" v-show="tempInfo">{{ tempInfo }}</div>
                    </div>
                    <div class="err-content" v-show="errorInfo">
                        <div class="t">
                            {{ errorInfo }}
                            <span class="link" @click="openCurrentEntry">查看未生成凭证单据</span>
                        </div>
                        <div class="l">由于凭证模板科目设置不当、单据金额为0、凭证行或合计金额超过亿位、凭证日期所在期间已结账等导致</div>
                    </div>
                </div>
                <div class="result-button">
                    <a class="button" @click="() => (voucherResult = false)">关闭</a>
                    <a
                        class="button solid-button ml-20"
                        v-show="checkPermission(['voucher-canview']) && showVoucher"
                        @click="checkCurrentVoucher(false)"
                    >
                        查看业务凭证
                    </a>
                    <a
                        v-permission="['voucher-canview']"
                        class="button solid-button ml-20"
                        v-show="checkPermission(['voucher-canview']) && showTempVoucher"
                        @click="checkCurrentVoucher(true)"
                    >
                        查看暂存凭证
                    </a>
                </div>
            </div>
        </el-dialog>
        <OperationGuidance current="BusinessVoucher" ref="operationGuidanceRef" />
        <GenerateVoucherIntercept ref="generateVoucherInterceptRef" @continue-generate="continueGenerate" />
        <el-dialog destroy-on-close v-model="monthErrTip" title="提示" center width="572px" class="custom-confirm">
            <div class="err-month-content">
                <div class="main">
                    <div class="e-l">{{ monthErrTipContent }}</div>
                    <div class="e-r warning">{{ monthErrTipContentDetail }}</div>
                    <div class="e-r">
                        2.到
                        <span :class="{ link: isManager }" @click="toAccounts">系统设置-系统参数-财税参数</span>
                        中修改为单据可以跨月生成凭证
                    </div>
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="monthErrTip = false">好的</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "BusinessVoucher",
};
</script>
<script setup lang="ts">
import { computed, nextTick, onActivated, onMounted, provide, reactive, ref } from "vue";
import { type IResponseModel, request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams, globalWindowOpen, globalWindowOpenPage, tryClearCustomUrlParams } from "@/util/url";
import { checkPermission } from "@/util/permission";
import { dayjs } from "element-plus";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { PeriodStatus } from "@/api/period";
import { getGlobalLodash } from "@/util/lodash";
import { getGlobalToken } from "@/util/baseInfo";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import {
    getSearchTypeText,
    SearchType,
    Searchs,
    accountSearchs,
    getCircleKey,
    checkUsePublicSelect,
    changeSelectedList,
    subjectOption,
    checkCanSearch,
    erpSeparatorOption,
    listCombineOptions,
    ERPVoucherSummaryWay,
} from "./utils";
import { customFilterLimitKey, customFilterOptionKey, customFilterPropsKey } from "@/components/TableHeaderFilter/utils";
import { useRoute } from "vue-router";
import { useScmInfoStore } from "@/store/modules/scm";
import { invoiceTypeListAll, invoiceTypeListAllPur } from "@/views/Invoice/utils";
import { useLoading } from "@/hooks/useLoading";
import { resetErpVoucherHistory } from "../utils";

import { ErpCheckBillType } from "./types";
import { Option } from "@/components/SelectCheckbox/types";
import type { IErpCashierData, IErpTransferData, IErpInvoiceData, IVoucherCombineInfo, IErpBillData, IBaseErpData } from "./types";
import type { ICDAccountItem, IIETypeItem } from "@/views/Cashier/type";
import type { IAssistingAccount } from "@/api/assistingAccounting";

import Select from "@/components/Select/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import ModuleVoucher from "./components/ModuleVoucher.vue";
import EntryVoucher from "./components/EntryVoucher.vue";
import OperationGuidance from "../components/OperationGuidance.vue";
import GenerateVoucherIntercept from "./components/GenerateVoucherIntercept.vue";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";

const moduleVoucherRef = ref<InstanceType<typeof ModuleVoucher>>();
const entryVoucherRef = ref<InstanceType<typeof EntryVoucher>>();
const operationGuidanceRef = ref<InstanceType<typeof OperationGuidance>>();
const generateVoucherInterceptRef = ref<InstanceType<typeof GenerateVoucherIntercept>>();
const voucherCombineSelectRef = ref<InstanceType<typeof SelectCheckbox>>();
const asubCombineSelectRef = ref<InstanceType<typeof SelectCheckbox>>();

const { intersection } = getGlobalLodash();
const showMethod = ref("module");
const txtSearch = ref("");
const scmAsid = ref(0);
const scmProductType = ref(0);
const scmAsName = ref("");
const warnTip = ref(true);

const voucherKey = "erp-voucher-combine-" + getGlobalToken();
const subjectKey = "erp-subject-combine-" + getGlobalToken();
const assistingAccountingStore = useAssistingAccountingStore();

// date
const periodStore = useAccountPeriodStore();
const uncheckoutDateInfo = periodStore.periodList.filter(
    (p) => p.status !== PeriodStatus.NoVoucher && p.status !== PeriodStatus.CheckOut
)[0];
const startDate = ref(dayjs(new Date(uncheckoutDateInfo?.startDate || "")).format("YYYY-MM-DD"));
const endDate = ref(dayjs(new Date(uncheckoutDateInfo?.endDate || "")).format("YYYY-MM-DD"));
const asStartDate = useAccountSetStore().accountSet!.asStartDate;
function disabledDate(current: Date) {
    return current.getTime() < dayjs(asStartDate).valueOf();
}
function closeCombineSelect() {
    voucherCombineSelectRef.value?.changeShow(false);
    asubCombineSelectRef.value?.changeShow(false);
    forcedShutdown.value = true;
}

// moreSearch
const forcedShutdown = ref(false);
const listVoucherCombineInfo = reactive<IVoucherCombineInfo>({
    selectedList: JSON.parse(localStorage.getItem(voucherKey) || "[]"),
    selectedSubjectList: JSON.parse(localStorage.getItem(subjectKey) || "[]"),
    selectZero: false,
    date: 3000,
    separator: "_",
    options: [...listCombineOptions],
});
function handleChangeSelectedList(val: Array<number>) {
    changeSelectedList(val, listVoucherCombineInfo);
}
function cacheLastCombineInfo() {
    localStorage.setItem(voucherKey, JSON.stringify(listVoucherCombineInfo.selectedList));
    localStorage.setItem(subjectKey, JSON.stringify(listVoucherCombineInfo.selectedSubjectList));
}
const moduleSearchList = ref<Array<SearchType>>([]);
function setModuleSearchList(searchs: Array<SearchType>, billTypes: Array<{ id: string; name: string }>) {
    editFilterOptions("billType", billTypes);
    const currentNode = entryVoucherRef.value?.getCurrentNode();
    const hasTypeNames = ["1050", "1060", "1130", "1100"];
    if (currentNode && ((currentNode.attributes && currentNode.attributes.BillRootType) || hasTypeNames.includes(currentNode.id))) {
        const type = currentNode.attributes?.BillRootType || ~~currentNode.id;
        // 1050 1060 其他出入库  业务类型
        // 1130 核销单  核销类型
        // 1100 其他支出单  费用类型
        const list =
            currentNode.children && currentNode.children.length
                ? currentNode.children.map((node) => ({ id: node.text, name: node.text }))
                : [{ id: currentNode.text, name: currentNode.text }];
        if (type === 1050 || type === 1060) {
            editFilterOptions("businessTypeName", list);
        } else if (type === 1130) {
            editFilterOptions("offsetTypeName", list);
        } else if (type === 1100) {
            const hasApportion = list.findIndex((item) => item.name === "采购费用已分摊");
            const noneApportion = list.findIndex((item) => item.name === "采购费用未分摊");
            if (hasApportion !== -1 && noneApportion !== -1) {
                const before = Math.max(hasApportion, noneApportion);
                const after = Math.min(hasApportion, noneApportion);
                list.splice(before, 1);
                list.splice(after, 1, { id: "采购费用", name: "采购费用" });
            } else if (hasApportion !== -1 || noneApportion !== -1) {
                list.splice(hasApportion !== -1 ? hasApportion : noneApportion, 1, { id: "采购费用", name: "采购费用" });
            }
            editFilterOptions("expenseTypeName", list);
        }
    }
    if (searchs.length === 0) {
        !isSearchByQuery && Object.assign(moreSearchs, new Searchs());
        moduleSearchList.value = searchs;
        showMore.value = false;
        setInvoiceType();
        setAccountType();
        return;
    }
    const sameSearchKeys: Array<SearchType> = intersection(moduleSearchList.value, searchs);
    const sameSearchs: any = {};
    for (let i = 0; i < sameSearchKeys.length; i++) {
        sameSearchs[getCircleKey(sameSearchKeys[i])] = moreSearchs[getCircleKey(sameSearchKeys[i])];
    }
    if (sameSearchKeys.includes(SearchType.Vgid)) {
        sameSearchs.vnum_s = moreSearchs.vnum_s;
        sameSearchs.vnum_e = moreSearchs.vnum_e;
    }
    if (sameSearchKeys.includes(SearchType.Vdate)) {
        sameSearchs.vdate_s = moreSearchs.vdate_s;
        sameSearchs.vdate_e = moreSearchs.vdate_e;
    }
    moduleSearchList.value.length = 0;
    moduleSearchList.value = searchs;
    setInvoiceType();
    setAccountType();
    if (!isSearchByQuery) {
        Object.assign(moreSearchs, new Searchs(), sameSearchs);
    }
}
const publicSelectProps = { id: "aaeid", name: "aaname" };
const accountProps = { id: "ac_id", name: "ac_name" };
const optionProps = { id: "id", name: "name" };
function getSelectProps(searchOption: SearchType) {
    if (accountSearchs.includes(searchOption)) return accountProps;
    if ([SearchType.InvoiceType, SearchType.InvoiceCategory].includes(searchOption)) return headerOptionProps;
    const optionsList = [SearchType.Ietype, SearchType.InWarehouse, SearchType.OutWarehouse, SearchType.InvoiceType];
    if (optionsList.includes(searchOption)) return optionProps;
    return publicSelectProps;
}
const moreSearchs = reactive(new Searchs());
const voucherGroup = computed(() => useVoucherGroupStore().voucherGroupList);
function handleVgIdChange(val: number) {
    if (val === 0) {
        moreSearchs.vnum_s = "";
        moreSearchs.vnum_e = "";
    }
}
const customers = ref<Array<IAssistingAccount>>([]);
const venders = ref<Array<IAssistingAccount>>([]);
const employees = ref<Array<IAssistingAccount>>([]);
const departments = ref<Array<IAssistingAccount>>([]);
const projects = ref<Array<IAssistingAccount>>([]);
const stocks = ref<Array<IAssistingAccount>>([]);
const salesInvoice = invoiceTypeListAll.map((item) => new Option(~~item.IN_ID, item.IN_NAME));
const purchaseInvoice = invoiceTypeListAllPur.map((item) => new Option(~~item.IN_ID, item.IN_NAME));
const oppositeParty = ref<Array<IAssistingAccount>>([]);
const invoiceTypeList = [
    { id: 1010, name: "增值税普通发票" },
    { id: 1020, name: "农产品发票" },
    { id: 1030, name: "其他发票" },
    { id: 1040, name: "增值税专用发票" },
];
function getCircleOptions(searchOption: SearchType) {
    if (accountSearchs.includes(searchOption)) return accountList.value;
    if (searchOption === SearchType.Ietype) {
        const billType = entryVoucherRef.value?.getBillType() || "0";
        if (billType === "3011" || billType === "3021") return incomeIETypeList.value;
        if (billType === "3012" || billType === "3022") return expenseIETypeList.value;
        return ietypeList.value;
    }
    if (searchOption === SearchType.InWarehouse || searchOption === SearchType.OutWarehouse) return whList.value;
    if (searchOption === SearchType.InvoiceType) return invoiceTypeList;
    if (searchOption === SearchType.InvoiceCategory) {
        const billType = entryVoucherRef.value?.getBillType() || "0";
        return billType === "2010" ? salesInvoice : purchaseInvoice;
    }
    let arr: Array<IAssistingAccount> = [];
    switch (searchOption) {
        case SearchType.Customer:
            arr = customers.value;
            break;
        case SearchType.Vender:
            arr = venders.value;
            break;
        case SearchType.Employee:
        case SearchType.Purchaser:
        case SearchType.Sales:
            arr = employees.value;
            break;
        case SearchType.Department:
            arr = departments.value;
            break;
        case SearchType.Project:
            arr = projects.value;
            break;
        case SearchType.AssemblyGoodsName:
        case SearchType.DisassemblyGoodsName:
            arr = stocks.value;
            break;
    }
    return arr;
}
function bindAAEList() {
    const assistingAccountingList = assistingAccountingStore.assistingAccountingList;
    const departmentList = assistingAccountingStore.departmentList;
    customers.value.length = 0;
    venders.value.length = 0;
    employees.value.length = 0;
    departments.value.length = 0;
    projects.value.length = 0;
    stocks.value.length = 0;
    oppositeParty.value.length = 0;
    for (let i = 0; i < assistingAccountingList.length; i++) {
        const item = assistingAccountingList[i];
        if (item.aaeid <= 0) continue;
        switch (item.aatype) {
            case 10001:
                customers.value.push(item);
                oppositeParty.value.push(item);
                break;
            case 10002:
                venders.value.push(item);
                oppositeParty.value.push(item);
                break;
            case 10003:
                employees.value.push(item);
                oppositeParty.value.push(item);
                break;
            case 10005:
                projects.value.push(item);
                break;
            case 10006:
                stocks.value.push(item);
                break;
        }
    }
    for (let i = 0; i < departmentList.length; i++) {
        const item = departmentList[i];
        departments.value.push(item);
    }
}
const accountList = ref<Array<ICDAccountItem>>([]);
const cashAccList = ref<Array<ICDAccountItem>>([]);
const bankAccList = ref<Array<ICDAccountItem>>([]);
const filterAccList = ref<Array<ICDAccountItem>>([]);
async function getCDAccount() {
    await Promise.all([getAccountList(1010), getAccountList(1020)]).then((res: Array<IResponseModel<Array<ICDAccountItem>>>) => {
        if (res[0].state !== 1000 || res[1].state !== 1000) return;
        // filter 后端那筛选条件依据ac_no，ac_name
        const formatAccountItem = (item: ICDAccountItem) => ({
            ...item,
            ac_name: `${item.ac_no}-${item.ac_name}`
        });
        const cashList = res[0].data.map(formatAccountItem)
        const bankList = res[1].data.map(formatAccountItem)
        accountList.value = [
            ...cashList,
            ...bankList
        ];
        cashAccList.value = cashList
        bankAccList.value = bankList
        editFilterOptions("cdaccount", accountList.value);
        editFilterOptions("cdaccountIn", accountList.value);
        editFilterOptions("cdaccountOut", accountList.value);
    });
}
const ietypeList = ref<Array<Option>>([]);
const incomeIETypeList = ref<Array<Option>>([]);
const expenseIETypeList = ref<Array<Option>>([]);
async function getIETypeList() {
    await request({ url: "/api/IEType/List" }).then((res: IResponseModel<{ rows: Array<IIETypeItem> }>) => {
        if (res.state !== 1000) return;
        const list: Array<Option> = [];
        for (let i = 0; i < res.data.rows.length; i++) {
            const item = res.data.rows[i];
            if (!item.haschild) {
                list.push(new Option(Number(item.subkey), item.value2));
                if (item.value2.startsWith("收-")) incomeIETypeList.value.push(new Option(Number(item.subkey), item.value2));
                if (item.value2.startsWith("支-")) expenseIETypeList.value.push(new Option(Number(item.subkey), item.value2));
            }
        }
        ietypeList.value = list;
        editFilterOptions("ietypeName", list);
        editFilterOptions("ietypeName_in", incomeIETypeList.value);
        editFilterOptions("ietypeName_out", expenseIETypeList.value);
    });
}
interface IRequestWhItem {
    whId: number;
    whNo: string;
    whName: string;
    catId: number;
    catName: string;
    address: string;
    status: number;
}
interface IWhItem extends IRequestWhItem {
    id: number;
    name: string;
}
const whList = ref<Array<IWhItem>>([]);
async function getWhList() {
    await request({ url: "/api/BaseDataForErp/GetWhList" }).then((res: IResponseModel<Array<IRequestWhItem>>) => {
        if (res.state !== 1000) return;
        const list = res.data.map((item) => ({ ...item, id: item.whId, name: item.whName }));
        whList.value = list;
        editFilterOptions("whName", list);
    });
}
const headerFilterOptions = reactive({
    customerName: customers.value,
    vendorName: venders.value,
    employeeName: employees.value,
    departmentName: departments.value,
    projectName: projects.value,
    assemblyName: stocks.value,
    invoiceTypeName: invoiceTypeList,
    ietypeName: ietypeList.value,
    ietypeName_in: incomeIETypeList.value,
    ietypeName_out: expenseIETypeList.value,
    cdaccount: filterAccList.value,
    cdaccountIn: accountList.value,
    cdaccountOut: accountList.value,
    billType: [],
    whName: whList.value,
    oppositeParty: oppositeParty.value,
    invoiceCategory: salesInvoice,
    businessTypeName: [],
    expenseTypeName: [],
    offsetTypeName: [],
});
const headerAAProps = { id: "aaname", name: "aaname" };
const headerWhProps = { id: "whName", name: "whName" };
const headerOptionProps = { id: "name", name: "name" };
const headerAccountProps = { id: "ac_name", name: "ac_name" };
const headerFilterProps = {
    customerName: headerAAProps,
    vendorName: headerAAProps,
    employeeName: headerAAProps,
    departmentName: headerAAProps,
    projectName: headerAAProps,
    assemblyName: headerAAProps,
    invoiceTypeName: headerOptionProps,
    ietypeName: headerOptionProps,
    ietypeName_in: headerOptionProps,
    ietypeName_out: headerOptionProps,
    cdaccount: headerAccountProps,
    cdaccountIn: headerAccountProps,
    cdaccountOut: headerAccountProps,
    billType: optionProps,
    whName: headerWhProps,
    oppositeParty: headerAAProps,
    invoiceCategory: headerOptionProps,
    businessTypeName: headerOptionProps,
    expenseTypeName: headerOptionProps,
    offsetTypeName: headerOptionProps,
};
function editFilterOptions(key: keyof typeof headerFilterOptions, value: any) {
    headerFilterOptions[key] = value;
}
provide(customFilterOptionKey, headerFilterOptions);
provide(customFilterPropsKey, headerFilterProps);
function setInvoiceType() {
    const billType = entryVoucherRef.value?.getBillType() || "0";
    if (billType !== "2010" && billType !== "2020") return;
    headerFilterOptions.invoiceCategory = billType === "2010" ? salesInvoice : purchaseInvoice;
}
function setAccountType() {
    const billType = entryVoucherRef.value?.getBillType() || "0";
    const journal = ["3010", "3011", "3012", "3020", "3021", "3022"];
    if (!journal.includes(billType)) return;
    headerFilterOptions.cdaccount = billType.startsWith("301") ? cashAccList.value : bankAccList.value;
}

// voucher
interface IBatchDelteResult {
    isOvertime: boolean;
    batchOperationResult: {
        success: number;
        jump: number;
        faild: number;
    };
}
let canDelete = true;
async function deleteVoucher() {
    if (showMethod.value === "module") return;
    if (!canDelete) return;
    canDelete = false;
    const selectData: Array<IBaseErpData> = entryVoucherRef.value?.getSelectData() || [];
    if (selectData.length === 0) {
        ElNotify({ type: "warning", message: "亲，请先勾选单据数据后再点击删除凭证" });
        canDelete = true;
        return;
    }
    const voucherInfo: Array<{ pid: number; vid: number }> = [];
    for (let i = 0; i < selectData.length; i++) {
        const billInfo = selectData[i];
        if (!billInfo.vid || !billInfo.pid) continue;
        voucherInfo.push({ pid: billInfo.pid, vid: billInfo.vid });
    }
    if (voucherInfo.length === 0) {
        ElNotify({ type: "success", message: `成功0个，跳过${selectData.length}个（已审核、已结账、未生成凭证的已跳过）` });
        canDelete = true;
        return;
    }
    const confirm = await ElConfirm("亲，确认要删除单据生成的凭证吗");
    if (!confirm) {
        canDelete = true;
        return;
    }
    useLoading().enterLoading("凭证删除中，请稍后...");
    request({ url: "/api/Voucher/Batch", method: "delete", data: voucherInfo })
        .then((res: IResponseModel<IBatchDelteResult>) => {
            canDelete = true;
            useLoading().quitLoading();
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: "亲，删除失败啦！请联系侧边栏客服！" });
                return;
            }
            if (res.data.isOvertime === true) {
                ElNotify({ type: "warning", message: "数据太多啦，服务器太忙了，请10分钟后分批删除哦~" });
                return;
            }
            const success = res.data.batchOperationResult.success;
            const jump = selectData.length - success;
            ElNotify({ type: "success", message: `成功${success}个，跳过${jump}个（已审核、已结账、未生成凭证的已跳过）` });
            entryVoucherRef.value?.setCacheTableScroll();
            showMethod.value === "entry" && entryVoucherRef.value?.debugResetHeaderSearch();
            searchPageData();
        })
        .catch(() => {
            canDelete = true;
            useLoading().quitLoading();
            ElNotify({ type: "warning", message: "亲，删除失败啦！请联系侧边栏客服！" });
        });
}
const storageKey = "generateVoucherIntercept-notTip-" + getGlobalToken();
async function oneTouchGenerate() {
    if (showMethod.value === "module") {
        const modules: Array<number> = [];
        const voucherModuleData = moduleVoucherRef.value?.getVoucherModuleData() || [];
        for (let i = 0; i < voucherModuleData.length; i++) {
            voucherModuleData[i].select && modules.push(voucherModuleData[i].type);
        }
        if (modules.length === 0) {
            ElNotify({ type: "warning", message: "亲，请先勾选模块后再点击生成凭证" });
            return;
        }
        if (modules.some((module) => module === 1060) && localStorage.getItem(storageKey) !== "1") {
            generateVoucherInterceptRef.value?.showDialog(modules);
            return;
        }
        generateVoucherByModules(modules);
        return;
    }
    useLoading().enterLoading("凭证生成中，请稍后...");
    const { hasGenerateVoucher, generateList, params, billDateInfo, skipLength } = getGenerateParamsByEntry();
    if (hasGenerateVoucher) {
        useLoading().quitLoading();
        const confirm = await ElConfirm(`亲，已经生成凭证的${skipLength}张单据将会被跳过，是否继续生成凭证？`);
        if (!confirm) return;
    }
    if (generateList.length === 0) {
        useLoading().quitLoading();
        if (hasGenerateVoucher) {
            const selectData: Array<any> = entryVoucherRef.value?.getSelectData() || [];
            ElNotify({ type: "success", message: `成功：0，跳过${selectData.length}（已经生成凭证的单据，将会被跳过！）` });
        } else {
            ElNotify({ type: "warning", message: "亲，请先勾选单据数据后再点击生成凭证" });
        }
        return;
    }
    if (generateList.some((bill: IBaseErpData) => !bill.vtId && !bill.vtName)) {
        useLoading().quitLoading();
        ElNotify({
            type: "warning",
            message: "选择的单据中，其他出库单/其他入库单中存在部分单据没有凭证模板，请检查并配置凭证模板后再生成凭证",
        });
        return;
    }
    if (params.invoiceIdList.length > 0 && localStorage.getItem(storageKey) !== "1") {
        useLoading().quitLoading();
        const _params = { params, billDateInfo };
        generateVoucherInterceptRef.value?.showDialog(_params);
        return;
    }
    generateVoucherByEntry(params, billDateInfo);
}
function continueGenerate(_params: any) {
    if (showMethod.value === "entry") {
        const { params, billDateInfo } = _params;
        generateVoucherByEntry(params, billDateInfo);
    } else {
        generateVoucherByModules(_params);
    }
}
const showVoucher = ref(false);
const showTempVoucher = ref(false);
const voucherResult = ref(false);
const successInfo = ref("");
const errorInfo = ref("");
const tempInfo = ref("");
class GenerateResult {
    successCount = 0; // 成功的单据数
    successVoucherCount = 0; // 生成的凭证数
    skipCount = 0;
    tempCount = 0;
    failedList: any = {};
    startPId = 0;
    endPId = 0;
    startCreateTime = "";
    endCreateTime = "";
    sumCount = 0;
    failedCount = 0;
}
class DisPlayGenerateResult extends GenerateResult {
    errorCount = 0;
    moduleList: Array<number> = [];
}
const generateResult = reactive(new DisPlayGenerateResult());
const dialogWidth = ref(572);
function showResult(data: GenerateResult | DisPlayGenerateResult) {
    dialogWidth.value = 572;
    successInfo.value = "";
    errorInfo.value = "";
    tempInfo.value = "";
    const error = data.skipCount + data.failedCount;
    const assignParams = { ...data, errorCount: error } as DisPlayGenerateResult;
    if (showMethod.value === "module" && data instanceof DisPlayGenerateResult && data.moduleList) {
        assignParams.moduleList = data.moduleList;
    }
    Object.assign(generateResult, new DisPlayGenerateResult(), assignParams);
    showVoucher.value = data.successVoucherCount > 0 && data.successVoucherCount != data.tempCount;
    showTempVoucher.value = data.tempCount > 0;
    voucherResult.value = true;
    if (data.successVoucherCount === 0) {
        // 全部失败
        errorInfo.value = `${assignParams.sumCount}张单据生成凭证全部失败`;
        return;
    }
    dispatchReloadAsubAmountEvent();
    if (data.tempCount === 0) {
        // 全部成功
        if (error === 0) {
            dialogWidth.value = 440;
            successInfo.value = `${assignParams.sumCount}张单据已全部生成凭证，共生成${assignParams.successVoucherCount}张业务凭证`;
        } else {
            // 成功 + 失败
            successInfo.value = `${assignParams.successCount}张单据已生成凭证，共生成${assignParams.successVoucherCount}张业务凭证`;
            errorInfo.value = `${error}张单据未生成凭证`;
        }
    } else {
        if (error === 0) {
            if (data.successVoucherCount === data.tempCount) {
                // 全部暂存
                successInfo.value = `${assignParams.sumCount}张单据已全部生成凭证，共生成${assignParams.tempCount}张暂存凭证，请到查看凭证中补充科目或核算项目`;
            } else {
                // 成功 + 暂存
                successInfo.value = `${assignParams.sumCount}张单据已全部生成凭证，共生成${assignParams.successVoucherCount}张业务凭证`;
                tempInfo.value = `其中，暂存凭证${assignParams.tempCount}张，请到查看凭证中补充科目或核算项目`;
            }
        } else {
            errorInfo.value = `${error}张单据未生成凭证`;
            if (data.successVoucherCount === data.tempCount) {
                // 暂存 + 失败
                successInfo.value = `${assignParams.successCount}张单据已全部生成凭证，共生成${assignParams.tempCount}张暂存凭证，请到查看凭证中补充科目或核算项目`;
            } else {
                // 成功 + 暂存 + 失败
                successInfo.value = `${assignParams.successCount}张单据已生成凭证，共生成${assignParams.successVoucherCount}张业务凭证`;
                tempInfo.value = `其中，暂存凭证${assignParams.tempCount}张，请到查看凭证中补充科目或核算项目`;
            }
        }
    }
}
function generateVoucherByModule(module: number, count: number) {
    if (!count || !checkPermission(["businessvoucher-canedit"])) return;
    const modules: Array<number> = new Array();
    modules.push(module);
    if (module === 1060 && localStorage.getItem(storageKey) !== "1") {
        generateVoucherInterceptRef.value?.showDialog(modules);
        return;
    }
    generateVoucherByModules(modules);
}
const monthErrTip = ref(false);
const monthErrTipContent = ref("");
const monthErrTipContentDetail = ref("");
const isManager = useAccountSetStore()!.accountSet!.permission === "*********";
function toAccounts() {
    if (!isManager) return;
    globalWindowOpenPage("/ArgsSetting?taxmanage=1", "系统参数");
}
function generateVoucherByModules(modules: number[]) {
    if (!canGenerate) return;
    canGenerate = false;
    const checkParams = getSearchParams();
    if (!checkCanSearch(checkParams)) return;
    const data = {
        startDate: startDate.value,
        endDate: endDate.value,
        voucherModuleList: modules,
        scmProductType: scmProductType.value,
        scmAsid: scmAsid.value,
    };
    useLoading().enterLoading("努力加载中，请稍后...");
    request({
        url: `/api/VoucherForErp/GenerateByModules?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
        method: "post",
        data,
    })
        .then((res: IResponseModel<GenerateResult>) => {
            if (res.state === 1000) {
                const result = res.data as DisPlayGenerateResult;
                Object.setPrototypeOf(result, DisPlayGenerateResult.prototype);
                result.moduleList = modules;
                showResult(result);
                moduleVoucherRef.value?.getBusinessVoucherCount();
                useAccountPeriodStore().getPeriods();
            } else if (res.state === 2000) {
                ElNotify({ type: "warning", message: res.msg });
            } else {
                if (res.state === 9999 && res.msg === "month-span error") {
                    monthErrTipContent.value = "存在单据日期和凭证期间不一致的情况，不能一起生成凭证哦~";
                    monthErrTipContentDetail.value = "1.检查选择的单据日期和各模块凭证日期设置，调整为不跨月";
                    monthErrTip.value = true;
                    return;
                }
                ElNotify({ type: "warning", message: "生成失败，请刷新页面重试或联系客服" });
            }
        })
        .finally(() => {
            useLoading().quitLoading();
            canGenerate = true;
            moduleVoucherRef.value?.clearVoucherModuleDataChecked();
        });
}
let canGenerate = true;
class EntryVoucherGenerateModel {
    lineSnDict: { [key: number]: Array<{ date: string; line_sn: string }> } = {};
    invoiceIdList: Array<number> = [];
    billList: { [key: number]: Array<number> } = {};
    cdDates = "";
    createdDates = "";
    cdAccounts = "";
    summaryWays: Array<number> = [];
    asubDirectionWays: Array<number> = [];
    dateWay: number = 0;
}
async function generateVoucherByEntry(param?: any, billDateInfoLs?: Array<{ year: number; month: number }>) {
    if (!canGenerate) return;
    if (!useLoading().isLoading) useLoading().enterLoading("凭证生成中，请稍后...");
    canGenerate = false;
    const params = param || getGenerateParamsByEntry().params;
    const billDateInfo = billDateInfoLs || getGenerateParamsByEntry().billDateInfo;
    const data = new EntryVoucherGenerateModel();
    Object.assign(data, params);
    const combineInfo = {
        summaryWays: listVoucherCombineInfo.selectedList,
        asubDirectionWays: listVoucherCombineInfo.selectedSubjectList,
        dateWay: listVoucherCombineInfo.date,
        separator: listVoucherCombineInfo.separator,
    };
    Object.assign(data, combineInfo);
    request({
        url: `/api/VoucherForErp/GenerateVoucherByEntry?scmProductType=${scmProductType.value}&scmAsid=${scmAsid.value}`,
        method: "post",
        data,
    })
        .then((res: IResponseModel<GenerateResult>) => {
            canGenerate = true;
            useLoading().quitLoading();
            if (res.state === 1000) {
                showResult(res.data);
                entryVoucherRef.value?.clearSelection();
                entryVoucherRef.value?.setCacheTableScroll();
                entryVoucherRef.value?.debugResetHeaderSearch();
                useAccountPeriodStore().getPeriods();
                showByEntry();
            } else if (res.state === 2000) {
                ElNotify({ type: "warning", message: res.msg });
            } else {
                if (res.state === 9999 && res.msg === "month-span error") {
                    const info: Array<{ year: number; months: Array<number> }> = [];
                    for (let i = 0; i < billDateInfo.length; i++) {
                        const item = billDateInfo[i];
                        const index = info.findIndex((d) => d.year === item.year);
                        if (index === -1) {
                            info.push({ year: item.year, months: [item.month] });
                        } else {
                            info[index].months.push(item.month);
                        }
                    }
                    const dateStr = info.map((d) => `${d.year}年${d.months.join("、")}月`).join("；");
                    monthErrTipContent.value = `选择的单据为${dateStr}，按照当前选择的凭证合并及凭证日期规则，会导致单据日期所在期间和凭证期间不一致，不能一起生成凭证哦~`;
                    monthErrTipContentDetail.value = "1.可重新选择单据，或修改凭证合并及凭证日期规则";
                    monthErrTip.value = true;
                    return;
                }
                ElNotify({ type: "warning", message: "生成失败，请刷新页面重试或联系客服" });
            }
        })
        .catch(() => {
            useLoading().quitLoading();
            canGenerate = true;
            ElNotify({ type: "warning", message: "生成失败，请刷新页面重试或联系客服" });
        });
}
function getGenerateParamsByEntry() {
    const selectData: Array<any> = entryVoucherRef.value?.getSelectData() || [];
    let hasGenerateVoucher = false;
    const cashierParams: { [key: number]: Array<{ date: string; line_sn: string }> } = {};
    const cdDates: Array<string> = [];
    const createdDates: Array<string> = [];
    const cdAccounts: Array<number> = [];
    const invoiceParams: Array<number> = [];
    const billParams: { [key: number]: Array<number> } = {};
    const generateList: Array<any> = [];
    const billDateInfo: Array<{ year: number; month: number }> = [];
    let skipLength = 0;
    for (let i = 0; i < selectData.length; i++) {
        const item = selectData[i];
        if (item.pid !== 0 || item.vid !== 0) {
            hasGenerateVoucher = true;
            skipLength++;
        } else {
            generateList.push(item);
            let type = "bill";
            if (item.billType >= 1000) type = "bill";
            if (item.billType >= 2000) type = "invoice";
            if (item.billType >= 3000) type = "cashier";
            if (item.billType >= 3030) type = "transfer";
            if (item.billType >= 10000) type = "bill";
            if (type === "bill") {
                const select: IErpBillData = item;
                !billParams[select.billType] && (billParams[select.billType] = []);
                billParams[select.billType].push(select.billId);
            } else if (type === "invoice") {
                const select: IErpInvoiceData = item;
                invoiceParams.push(select.invoiceId);
            } else if (type === "cashier") {
                const select: IErpCashierData = item;
                const cdaccount = select.billId;
                const lineSnName = select.billNo.split("-");
                const lineSn = lineSnName.length > 0 ? lineSnName[lineSnName.length - 1] : "";
                !cashierParams[cdaccount] && (cashierParams[cdaccount] = []);
                cashierParams[cdaccount].push({ date: select.billDateText, line_sn: lineSn });
            } else if (type === "transfer") {
                const select: IErpTransferData = item;
                cdDates.push(select.billDateText);
                createdDates.push(select.billCreateDate);
                cdAccounts.push(select.billId);
            }
            const bill: IBaseErpData = item;
            const year = new Date(bill.billDateText).getFullYear();
            const month = new Date(bill.billDateText).getMonth() + 1;
            if (billDateInfo.findIndex((d) => d.year === year && d.month === month) === -1) {
                billDateInfo.push({ year, month });
            }
        }
    }
    billDateInfo.sort((a, b) => a.year - b.year || a.month - b.month);
    const params = {
        billList: billParams,
        invoiceIdList: invoiceParams,
        lineSnDict: cashierParams,
        cdDates: cdDates.join(","),
        createdDates: createdDates.join(","),
        cdAccounts: cdAccounts.join(","),
    };
    return { hasGenerateVoucher, generateList, params, billDateInfo, skipLength };
}

// redirect
function openCurrentEntry() {
    voucherResult.value = false;
    if (showMethod.value === "entry") {
        searchPageData();
        return;
    }
    const modules = generateResult.moduleList;
    if (modules.length === 1) {
        checkDetailBill(ErpCheckBillType.All, modules[0]);
        return;
    }
    showMethod.value = "entry";
    entryVoucherRef.value?.backFirstPage();
    entryVoucherRef.value?.syncTreeStatus(false);
    entryVoucherRef.value?.virtualScrollTreeNode();
    entryVoucherRef.value?.searchDataByVirtualClick("1000");
}
function checkCurrentVoucher(onlyTempVoucher: boolean) {
    voucherResult.value = false;
    const params: any = {
        startP: generateResult.startPId,
        endP: generateResult.endPId,
        startCreateTime: generateResult.startCreateTime,
        endCreateTime: generateResult.endCreateTime,
        r: Math.random(),
    };
    if (onlyTempVoucher) params.onlyTempVoucher = 1;
    globalWindowOpenPage("/Voucher/VoucherList?" + getUrlSearchParams(params), "查看凭证");
}
function openAsubRelationSettings() {
    globalWindowOpenPage("/Erp/AsubRelationSettings1", "科目关联设置");
}
function openBusinessVoucherTemplate() {
    globalWindowOpenPage("/Erp/BusinessVoucherTemplate", "业务凭证模板");
}

// search
function syncHasGenerateVoucher(unGenerated: boolean) {
    moreSearchs.hasGenerateVoucher = unGenerated ? 0 : -1;
}
function getSearchParams() {
    return {
        startDate: startDate.value,
        endDate: endDate.value,
        txtSearch: txtSearch.value,
    };
}
function showByEntry() {
    showMethod.value = "entry";
    searchPageData();
}
function showByModule() {
    showMethod.value = "module";
    searchPageData();
}
const lastSearchDate = reactive({ startDate: "", endDate: "" });
function disabledHeaderDate(current: any) {
    return current.getTime() < dayjs(lastSearchDate.startDate).valueOf() || current.getTime() > dayjs(lastSearchDate.endDate).valueOf();
}
provide(customFilterLimitKey, { disabledStartDate: disabledHeaderDate, disabledEndDate: disabledHeaderDate });
function searchPageData() {
    if (moreSearchs.vdate_s > moreSearchs.vdate_e) {
        ElNotify({ type: "warning", message: "凭证开始日期不能大于结束日期" });
        return;
    }
    const _s = new Date(startDate.value);
    _s.setFullYear(_s.getFullYear() + 1);
    if (new Date(endDate.value).getTime() > _s.getTime()) {
        ElNotify({ type: "warning", message: "亲，单据日期跨度不能超过1年，系统已自动为您调整为1年内数据，您也可以重新查询哦~" });
        endDate.value = dayjs(new Date(startDate.value)).add(1, "year").subtract(1, "day").format("YYYY-MM-DD");
    }
    lastSearchDate.startDate = startDate.value;
    lastSearchDate.endDate = endDate.value;
    cacheLastCombineInfo();
    if (showMethod.value === "module") {
        moduleVoucherRef.value?.getBusinessVoucherCount();
    } else {
        entryVoucherRef.value?.syncTreeStatus(moreSearchs.hasGenerateVoucher === 0);
        entryVoucherRef.value?.searchDataByVirtualClick();
    }
}
async function refreshMore() {
    showMethod.value === "module" && searchPageData();
    await entryVoucherRef.value?.getBusinessModuleTree();
    await entryVoucherRef.value?.getVoucherTemplate();
    await getCDAccount().then(() => {
        setAccountType();
    });
    await getIETypeList();
    await getWhList();
    await assistingAccountingStore.getAssistingAccounting().then(() => {
        assistingAccountingStore.getDepartment().then(() => {
            bindAAEList();
        });
    });
    showMethod.value === "entry" && entryVoucherRef.value?.batchSetCustomFilter();
}
const showMore = ref(false);

function showGuidance() {
    operationGuidanceRef.value?.showGuidance();
}
function toHelp() {
    globalWindowOpen("https://help.ningmengyun.com/#/yyc/videoPlayerForYYC?qType=*********");
}
function checkShowGuidance() {
    const key = "businessVoucherGuidance-" + getGlobalToken();
    if (!localStorage.getItem(key)) {
        operationGuidanceRef.value?.checkShowGuidance();
        const timer = setTimeout(() => {
            localStorage.setItem(key, "1");
            clearTimeout(timer);
        }, 1000);
    }
}
const treeIdMap = {
    1010: "1010",
    1020: "1020",
    1030: "1140",
    1040: "1030",
    1050: "1040",
    1060: "1000",
};
function checkDetailBill(generateType: ErpCheckBillType, moduleType: number) {
    const treeId = treeIdMap[moduleType as keyof typeof treeIdMap];
    let generateVoucherType = -1;
    entryVoucherRef.value?.syncTreeStatus(false);
    switch (generateType) {
        case ErpCheckBillType.All:
            generateVoucherType = -1;
            break;
        case ErpCheckBillType.Generated:
            generateVoucherType = 1;
            break;
        case ErpCheckBillType.UnGenerated:
            generateVoucherType = 0;
            entryVoucherRef.value?.syncTreeStatus(true);
            break;
    }
    Object.assign(moreSearchs, new Searchs());
    moreSearchs.hasGenerateVoucher = generateVoucherType;
    showMethod.value = "entry";
    if (moduleType === 1060) {
        entryVoucherRef.value?.setCustomFilter("billType", "multiSelect", ["2010", "2020"]);
    } else if (moduleType === 1010) {
        entryVoucherRef.value?.setCustomFilter("billType", "multiSelect", ["1011", "1012"]);
    } else if (moduleType === 1020) {
        entryVoucherRef.value?.setCustomFilter("billType", "multiSelect", ["1021", "1022", "1081", "1082"]);
    }
    entryVoucherRef.value?.backFirstPage();
    entryVoucherRef.value?.virtualScrollTreeNode();
    entryVoucherRef.value?.searchDataByVirtualClick(treeId);
}
function getVoucherDateWay() {
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    request({ url: "/api/VoucherForErp/VoucherDateWay?" + getUrlSearchParams(params) }).then((res: IResponseModel<number>) => {
        if (res.state === 1000) listVoucherCombineInfo.date = res.data;
    });
}
let hasMounted = false;
const scmInfoStore = useScmInfoStore();
const isProjectAccountingEnabled = ref(false);
async function getIsProjectAccountingEnabled() {
    await request({
        url: "/api/AsubRelation/IsProjectAccountingEnabled",
        method: "post",
        params: { scmAsid: scmAsid.value, scmProductType: scmProductType.value },
    }).then((res: IResponseModel<boolean>) => {
        isProjectAccountingEnabled.value = res.state === 1000 && res.data;
        const projectIndex = listVoucherCombineInfo.options.findIndex((item) => item.id === ERPVoucherSummaryWay.Project);
        if (projectIndex !== -1 && !isProjectAccountingEnabled.value) {
            listVoucherCombineInfo.options.splice(projectIndex, 1);
        }
    });
}
const taxEnabled = ref(false);
async function getTaxEnabled() {
    const params = { scmAsid: scmAsid.value, scmProductType: scmProductType.value };
    await request({ url: "/api/VoucherForErp/TaxEnabled?" + getUrlSearchParams(params) }).then((res: IResponseModel<boolean>) => {
        taxEnabled.value = res.state === 1000 && res.data;
    });
}
onMounted(async () => {
    await scmInfoStore.handleGetScmRelationInfo().then(() => {
        scmAsid.value = scmInfoStore.scmAsid;
        scmProductType.value = scmInfoStore.scmProductType;
        scmAsName.value = scmInfoStore.scmAsName;
    });
    await resetErpVoucherHistory();
    await getIsProjectAccountingEnabled();
    await getTaxEnabled();
    checkShowGuidance();
    moduleVoucherRef.value?.getBusinessVoucherCount();
    entryVoucherRef.value?.getBusinessModuleTree().then(() => {
        tryCheckDetaillBill();
    });
    entryVoucherRef.value?.getVoucherTemplate();
    bindAAEList();
    getVoucherDateWay();
    getCDAccount();
    getIETypeList();
    getWhList();
    hasMounted = true;
});
const route = useRoute();
let isSearchByQuery = false;
async function tryCheckDetaillBill() {
    if (!canGenerate) {
        ElNotify({ type: "warning", message: "亲，当前正在生成凭证，请稍后重新联查数据哦~" });
        return;
    }
    const { vdate, vnum, vgname, searchStartDate, searchEndDate, from, P_ID, billType } = route.query;
    if (from === "voucherList") {
        showMethod.value = "entry";
        showMore.value = true;
        isSearchByQuery = true;
        startDate.value = searchStartDate as string;
        endDate.value = searchEndDate as string;
        Object.assign(moreSearchs, new Searchs());
        if (vdate) {
            moreSearchs.vdate_s = vdate as string;
            moreSearchs.vdate_e = vdate as string;
        }
        if (vgname && vnum) {
            const vgid = voucherGroup.value.find((item) => item.name === vgname)?.id;
            moreSearchs.vgid = vgid ?? 0;
            moreSearchs.vnum_s = vnum as string;
            moreSearchs.vnum_e = vnum as string;
        }
        entryVoucherRef.value?.backFirstPage();
        entryVoucherRef.value?.syncTreeStatus(false);
        entryVoucherRef.value?.virtualScrollTreeNode();
        await entryVoucherRef.value?.searchDataByVirtualClick("1000");
        isSearchByQuery = false;
    } else if (from === "voucherPage") {
        showMethod.value = "entry";
        entryVoucherRef.value?.setCacheTableScroll();
        entryVoucherRef.value?.virtualScrollTreeNode();
        searchPageData();
    } else if (from === "checkout") {
        showMethod.value = "module";
        const period = periodStore.periodList.find((item) => item.pid === ~~(P_ID || 0));
        if (period) {
            startDate.value = dayjs(period.startDate).format("YYYY-MM-DD");
            endDate.value = dayjs(period.endDate).format("YYYY-MM-DD");
        }
        searchPageData();
    } else {
        // 其他单据点击生成凭证跳转
        if (!searchStartDate || !searchEndDate || !billType) return;
        showMethod.value = "entry";
        isSearchByQuery = true;
        startDate.value = searchStartDate as string;
        endDate.value = searchEndDate as string;
        entryVoucherRef.value?.backFirstPage();
        entryVoucherRef.value?.virtualScrollTreeNode();
        await entryVoucherRef.value?.searchDataByVirtualClick((billType as string) || "1000");
        isSearchByQuery = false;
    }
    tryClearCustomUrlParams(route);
}
onActivated(() => {
    if (!hasMounted) return;
    tryCheckDetaillBill();
});
</script>

<style scoped lang="less">
@import "@/style/Erp/BusinessVoucher.less";
</style>
