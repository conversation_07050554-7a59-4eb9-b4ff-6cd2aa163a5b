<template>
    <div class="content narrow-content">
        <div class="title">利润表</div>
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef"></PaginationPeriodPicker>
                            <CheckOutTooltip v-if="periodIsCheckOut"></CheckOutTooltip>
                            <ErpRefreshButton></ErpRefreshButton>
                            <div v-show="causeOfUnevenness" class="uneven-prompt">
                                <Tooltip
                                    :content="causeOfUnevenness"
                                    :max-width="500"
                                    :line-clamp="1"
                                    placement="bottom"
                                    :teleported="true">
                                    <img v-show="isErp" src="@/assets/Erp/tip-erp.png" alt="" class="uneven-prompt-icon ml-10" />
                                    <span :class="['highlight-red uneven-prompt-text', { 'pl-10': !isErp }]">{{ causeOfUnevenness }}</span>
                                </Tooltip>
                            </div>
                        </div>
                        <div class="main-tool-right">
                            <div class="mr-10">
                                <el-checkbox
                                    v-model="searchInfo.classification"
                                    :label="accountStandard === 1 ? '显示上年累计金额' : '显示上年同期累计金额'"></el-checkbox>
                            </div>
                            <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['incomestatement-canprint']">
                                <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                <li @click="handlePrint(1)">批量打印</li>
                                <li @click="handlePrint(2)">打印设置</li>
                            </Dropdown>
                            <Dropdown :btnTxt="'导出'" class="mr-10" :downlistWidth="102" v-permission="['incomestatement-canexport']">
                                <li @click="handleExport(0)">当前报表数据</li>
                                <li @click="handleExport(1)">批量导出</li>
                            </Dropdown>
                            <a
                                class="button solid-button mr-10"
                                v-if="checkPermission(['incomestatement-canedit']) && resetButton"
                                @click="resetFormula"
                                >重置公式</a
                            >
                            <a class="button" v-if="!isHideBarcode && checkPermission(['incomestatement-canshare'])" @click="handleShare"
                                >微信分享</a
                            >
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div v-if="isErp" class="divider-line"></div>
                    <div class="main-center">
                        <IncomeTable
                            v-loading="loading"
                            element-loading-text="正在加载数据..."
                            :empty-text="emptyText"
                            :tableData="tableData"
                            :periodRef="periodRef"
                            :searchInfo="searchInfo"
                            :editData="editData"
                            :accountStandard="accountStandard"
                            @changeSlot="changeSlot"
                            :lineIDList="lineIDList"
                            @colunmOrderChange="handleColunmOrderChange"></IncomeTable>
                    </div>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <EditEquation
                        ref="editRef"
                        :class="[{ 'edit-content': !isErp }, 'slot-mini-content']"
                        :statement-id="editData.statementId"
                        :line-id="editData.lineId"
                        :pid="editData.pid"
                        :title="editData.title"
                        :month-title="
                            accountStandard === 2 && (subAccountStandard === 1 || subAccountStandard === 2) ? '期末数' : '本月累计'
                        "
                        :year-title="
                            accountStandard === 2 && (subAccountStandard === 1 || subAccountStandard === 2) ? '年初数' : '本年累计'
                        "
                        :value-type-options="valueTypeOptions"
                        @handle-submit-success="handleEditSubmitSuccess"
                        @handle-cancel="handleEditCancel"></EditEquation>
                </div>
            </template>
        </ContentSlider>
    </div>
    <!-- 打印导出弹窗 -->
    <PrintOrExportDialog :show-dialog="showDialog" :type="dialogType" :pid="searchInfo.pid" :fromStatement="2" @close="handleDialogClose" />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="利润表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "IncomeStatement",
};
</script>
<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import ContentSlider from "@/components/ContentSlider/index.vue";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import IncomeTable from "./components/IncomeTable.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { globalExport, globalPrint,getUrlSearchParams } from "@/util/url";
import { isInWxWork } from "@/util/wxwork";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { nextTick, reactive, ref, watch, computed } from "vue";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import usePrint from "@/hooks/usePrint";
import { useRoute } from "vue-router";
import type { IIncomeSheet, IIncomeEditData, ISearchInfo } from "./types";
import type { IValueTypeOption } from "../types";
import { share } from "@/views/Statements/utils";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import Tooltip from "@/components/Tooltip/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import { checkPermission } from "@/util/permission";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import { PeriodStatus } from "@/api/period";

const isErp = ref(window.isErp);
const route = useRoute();
const colunmOrderChange = ref("0");
const periodStore = useAccountPeriodStore();
const slots = ["main", "edit"];
const currentSlot = ref("main");
const searchInfo = reactive<ISearchInfo>({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    classification: false,
});
const accountStandard = useAccountSetStore().accountSet!.accountingStandard;
const subAccountStandard = useAccountSetStore().accountSet?.subAccountingStandard || 0;
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const isWxwork = ref(isInWxWork());
const isAccountingAgent = ref(window.isAccountingAgent);
const isProSystem = ref(window.isProSystem);
const tableData = ref<IIncomeSheet[]>([]);
const reportType: { [key: number]: string } = {
    0: "IncomeSheet",
    1: "NewIncomeSheetUnExecuted",
    2: "NewIncomeSheetExecuted",
};
let subAccountingStandardReport: string = reportType[useAccountSetStore().accountSet?.subAccountingStandard || 0];
const reportStatementId =
    (useAccountSetStore().accountSet?.accountingStandard as number) * 1000 + Number(ReportTypeEnum[subAccountingStandardReport as any]);
//改变滑块
const recordScrollTop = ref(0);
function changeSlot(data: IIncomeEditData) {
    if (data.lineType === 1) {
        editData.statementId = data.statementId;
        editData.lineId = data.lineId;
        editData.pid = searchInfo.pid;
        editData.title = data.title;
        recordScrollTop.value = document.body.scrollTop;
        nextTick().then(() => {
            editRef.value?.init();
            currentSlot.value = "edit";
        });
    }
}
const loading = ref(false);
const emptyText = ref("");
function handleSearch() {
    loading.value = true;
    emptyText.value = " ";
    // 获取利润表
    request({
        url: `/api/IncomeSheet`,
        params: { Pid: searchInfo.pid, isTax: searchInfo.classification },
        method: "get",
    })
        .then((res: IResponseModel<IIncomeSheet[]>) => {
            loading.value = false;
            if (res.state === 1000) {
                tableData.value = [];
                let parent = null;
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children!.push(element);
                        continue;
                    }
                    if (index !== 0 && res.data[index - 1].proName.includes("其中：")) {
                        element.indentation = true;
                    }
                    // element.indentation= element.proName.length - element.proName.trimLeft().length;
                    tableData.value.push(element);
                }
                checkAsubNotInEquations();
            } else {
                console.log(res.msg);
            }
            if (!tableData.value.length) {
                emptyText.value = "暂无数据";
            }
        })
        .catch((error) => {
            console.log(error);
        });
}
const lineIDList = ref();
function searchLineId() {
    request({
        url: `/api/IncomeSheet/Formulas?PId=${searchInfo.pid}&isTax=${searchInfo.classification}`,
        method: "get",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            lineIDList.value = res.data;
        }
    });
}
const handleColunmOrderChange = (data: string) => {
    colunmOrderChange.value = data;
};

let causeOfUnevenness = ref("");
function checkAsubNotInEquations() {
    // 检查是否有科目未添加进报表公式
    request({
        url: `/api/IncomeSheet/CheckAsubNotInEquations?pId=${searchInfo.pid}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                causeOfUnevenness.value = res.data;
            } else {
                causeOfUnevenness.value = "";
            }
        })
        .catch((error) => {
            console.log(error);
        });
}
const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const periodIsCheckOut = computed(() => {
    return periodRef.value?.periodStatus === PeriodStatus.CheckOut;
});

// 打印导出
// const showDialog = ref(false);
// const dialogType = ref<"print" | "export">("print");

const handleDialogClose = () => {
    showDialog.value = false;
};

function getDefaultParams() {
    return {
        isTax: searchInfo.classification,
        pId: searchInfo.pid,
        colunmOrderChange: colunmOrderChange.value,
    };
}
const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "incomeSheet",
    `/api/IncomeSheet/Print`,
    {},
    false,
    false,
);
// 导出
function handleExport(exportType: number) {
    if (exportType === 0) {
        globalExport(`/api/IncomeSheet/Export?` + getUrlSearchParams(getDefaultParams()));
    } else {
        // 批量导出
        dialogType.value = "export";
        showDialog.value = true;
    }
}

//跳转编辑
const editRef = ref<InstanceType<typeof EditEquation>>();
const editData = reactive<IIncomeEditData>({
    statementId: reportStatementId,
    lineId: 0,
    pid: 0,
    title: "",
});

//重置公式
const resetButton = ref(false);
resetButtonHandle();

// 重置公式
function resetFormula() {
    ElConfirm(
        "确认删除此报表所有自定义公式？<div style='margin-top: 10px;'>重置公式仅影响未结账期间的报表数据</div>",
        false,
        () => {},
        "重置此报表公式"
    ).then((r: boolean) => {
        if (r) {
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${reportStatementId}`,
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    ElNotify({
                        type: "success",
                        message: "已经成功重置",
                    });
                    resetButton.value = res.data;
                    handleSearch();
                    resetButtonHandle();
                    checkAccPeriodStatus();
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
}
// 检查是否有自定义公式
function resetButtonHandle() {
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${reportStatementId}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            resetButton.value = res.data;
        }
    });
}

// 获取当前账套指定期间的状态,结账状态不允许编辑公式
const status = ref("1");
const checkAccPeriodStatus = () => {
    // 获取期间详情
    request({
        url: `/api/Period?pid=${searchInfo.pid}`,
        method: "get",
    }).then((res: IResponseModel<string>) => {
        status.value = res.data;
    });
};

//微信分享
const shareReportHost = ref("");
function handleShare() {
    request({
        url: `/api/IncomeSheet/Share?IsTax=${searchInfo.classification}&PId=${searchInfo.pid}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                shareReportHost.value =
                    window.accountSrvHost +
                    "/api/WxPay/MakeQRCode.ashx?data=" +
                    window.shareReportHost +
                    "/ShareReport/" +
                    res.data +
                    "&CurrentSystemType=1";
                share(shareReportHost.value);
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg,
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "warning",
                message: "分享失败，请联系客服或管理员～",
            });
        });
}

//编辑
const valueTypeOptions: IValueTypeOption[] = [
    { value: "4", label: "发生额" },
    { value: "3", label: "贷方发生额" },
];
function handleEditSubmitSuccess() {
    handleSearch();
    searchLineId();
    currentSlot.value = "main";
    resetButton.value = true;
    resetButtonHandle();
}
function handleEditCancel() {
    currentSlot.value = "main";
    setTimeout(() => {
        document.body.scrollTop = recordScrollTop.value;
    }, 0);
}

watch(
    searchInfo,
    () => {
        handleSearch();
        searchLineId();
    },
    { immediate: true }
);
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.edit-content {
    width: 1100px !important;
}

.main-top {
    padding: 20px 20px 16px;
}

:deep(.main-center) {
    tr {
        td {
            .cell {
                //这里样式已经控制好了，禁用掉element的样式
                .el-table__indent {
                    display: none !important;
                }

                .el-table__placeholder {
                    display: none;
                }

                .el-table__expand-icon {
                    position: absolute;
                }

                .el-icon {
                    margin-top: 2px;
                }

                .level2 {
                    span {
                        // max-width: 200px;
                        white-space: normal;
                    }
                }

                // .level3.pl-60 {
                //     padding-left: 60px;
                // }
            }
        }
    }
}

.new-message-box {
    .box-title {
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        height: 42px;
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        line-height: 42px;
    }

    .box-footer {
        text-align: center;
        padding: 10px 0;
    }
}

:deep(.el-table) {
    .el-table__expand-icon {
        transform: rotate(90deg);
        margin-top: 3px;
        color: #fff;

        .el-icon {
            width: 1em;
            height: 1em;
            margin: 0 !important;
            border-radius: 50%;
            background-color: #2abe2a;
        }

        &.el-table__expand-icon--expanded {
            transform: rotate(-90deg) !important;
        }
    }
}
</style>
