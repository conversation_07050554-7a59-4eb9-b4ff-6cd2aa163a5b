import type { IAssistingAccountWithChecked } from "@/api/assistingAccounting";
import type { IAccountSubjectModelWithChecked } from "@/api/accountSubject";
import { useAccountPeriodStoreHook } from "@/store/modules/accountPeriod";
const periodStore = useAccountPeriodStoreHook();

export interface IStatementEquationModel {
    equationId?: number;
    operator?: number;
    source?: string;
    valueType?: number;
    value?: number;
    rate?: number;
    evaluated?: boolean;
}
export interface ICustomStatementListItem {
    cells: null;
    columnNumber: number;
    columnWidths: null;
    createdBy: string;
    createdDate: string;
    modifiedBy: string;
    modifiedDate: string;
    note: string;
    periodStr: string;
    pid: number;
    rowHeights: null | number;
    rowNumber: number;
    statementCode: string;
    statementId: number;
    statementName: string;
    customStatementType: number;
}
export interface ICustomStatementDetail {
    columnNumber: number;
    columnWidths: { [key: number]: number };
    createdBy: string;
    createdDate: string;
    modifiedBy: string;
    modifiedDate: string;
    note: string;
    periodStr: string;
    pid: number;
    rowHeights: { [key: number]: number };
    rowNumber: number;
    statementCode: string;
    statementId: number;
    statementName: string;
    cells: { [key: string]: IStatementCell };
}
export interface IStatementCell {
    cellContent: string;
    cellId: number;
    columnSpan: number;
    contentType: number;
    equations: IStatementCellEquations[] | null;
    evaluated: boolean;
    evaluating: boolean;
    fontFamily: string;
    fontSize: number;
    horizontalAlign: number;
    rowSpan: number;
    verticalAlign: number;
}
export interface IStatementCellEquations {
    equationId: number;
    evaluated: boolean;
    operator: number;
    rate: number;
    source: string;
    value: number;
    valueType: number;
}
export interface ICreateTemplateTable {
    aatypeNames: string;
    aatypes: string;
    aatypesAllowNull: string;
    acronym: string;
    assistingAccounting: number;
    asubAAName: string;
    asubCode: string;
    asubId: number;
    asubLevel: number;
    asubName: string;
    asubType: number;
    direction: number;
    fcAdjust: number;
    fcIds: string;
    foreigncurrency: number;
    index?: number;
    isLeafNode: boolean;
    measureUnit: string;
    measureUnitId: string;
    note: string;
    operation: number;
    parentId: number;
    quantityAccounting: number;
    restricted: number;
    status: number;
    uscc: string;
}
export interface ICreateTemplateColumnsRight {
    label: string;
    property: string;
    width: number;
    tableData: Array<{ index: string }>;
    index: number;
    dateType: number;
    dataType: number;
    prop?: string;
}
export interface IStatementLines {
    statementId: number;
    statementName: string;
    dataTypes: { [key: number]: string };
    statementItems: IStatementLinesItems[];
}
export interface IStatementLinesItems {
    amount: number;
    amount1: string;
    asid: number;
    coumType: number;
    createdManually: number;
    entryType: number;
    expand: number;
    initalAmount: number;
    initalAmount1: string;
    lineID: number;
    lineName: string;
    lineNumber: number;
    lineType: number;
    note: string;
    parentID: number;
    pid: number;
    priority: number;
    statementID: number;
}
export interface IStatementModel {
    statementId: number;
    statementCode: string;
    statementName: string;
    rowNumber: number;
    columnNumber: number;
    rowHeights: { [key: number]: number };
    columnWidths: { [key: number]: number };
    pId: number;
    note: string;
    createdBy: string;
    createdDate: string;
    modifiedBy: string;
    modifiedDate: string;
    cells: { [key: string]: IStatementModelCellValue };
}
export interface IStatementModelCellValue {
    cellContent: string;
    cellId: number;
    columnSpan: number;
    contentType: number;
    equations: IStatementEquationModel[] | [];
    evaluated: boolean;
    evaluating: boolean;
    fontFamily: string;
    fontSize: number;
    horizontalAlign: number;
    rowSpan: number;
    verticalAlign: number;
}
export interface IAccountSetListItem {
    accountStandard: string;
    accountStartDate: string;
    activeCashJournal: boolean;
    activeFixedModel: boolean;
    asid: number;
    asname: string;
    createdDate: string;
    decimalPlace: number;
    deleteTime: any | null;
    lockState: boolean;
    permission: string;
    showScm: boolean;
    subAccountStandard: string;
    taxType: string;
    usedApprove: boolean;
}
export interface IAACodeTableItem {
    aaeid: string;
    aatype: string;
    code: number;
    name: string;
    aaname: string;
    list: IAssistEntriesItem[];
}
export interface IAssistEntries {
    code: number;
    name: string;
    list: IAssistEntriesItem[];
}
export interface IAssistEntriesItem {
    aaeid: number;
    aaname: string;
    aanum: string;
    aatype: number;
    asid: number;
    createdBy: number;
    createdDate: string;
    preName: string;
    status: number;
    uscc: string;
    value01: string;
}
// 数据透视表
export interface IDimensionItem {
    id: number;
    type: string;
    name: string;
    value: number | string;
    dropDownOPtions: IDropDownOPtions;
}
export interface IDropDownOPtions {
    selectList: {
        value: boolean;
        label: string;
        list: IAssistingAccountWithChecked[] | IAccountSubjectModelWithChecked[] | [];
    };
    fields: {
        label: string;
        children: Array<{
            value: string;
            label: string;
            required: boolean;
        }>;
        checked: string[];
    };
    enable: {
        value: boolean;
        label: string;
    };
}
export interface IValueListItem {
    id: number;
    type: string;
    name: string;
    value: number | string;
    dropDownOPtions: IValuePeriod;
}

export interface IYQMItem {
    label: string;
    children: Array<{ value: number; label: string }>;
    checked: number[];
}
export interface IPivotModel {
    statementId: number;
    statementCode: string;
    statementName: string;
    note?: string;
}
export interface IPivotTableDetailRowColumn {
    aaeids?: number[];
    aatypeId?: number;
    isQuery: boolean;
    optionId: number;
    optionPlace: number;
    optionType: number;
    showFields: number[];
    asubIds?: number[];
}
export interface IPivotTableDetailValues {
    optionId: number;
    optionPlace: number;
    optionType: number;
    valueType: number;
    valuePeriodType: number;
    startValuePeriod: number;
    endValuePeriod: number;
}
export interface IPivotTableDetail {
    asId: number;
    columns: IPivotTableDetailRowColumn[];
    createdBy: number;
    createdDate: string;
    modifiedBy: number;
    modifiedDate: string;
    note: string;
    rows: IPivotTableDetailRowColumn[];
    statementCode: string;
    statementId: number;
    statementName: string;
    valueShow: number;
    values: IPivotTableDetailValues[];
    isCustomPeriod: boolean;
    valuePeriodType: number;
    endValuePeriod: number;
    startValuePeriod: number;
}

export interface IViewPivotTableResData {
    columnCount: number;
    columnOptions: Array<{
        name: string;
        optionId: number;
        optionPlace: number;
        optionType: number;
        typeId: number;
    }>;
    count: number;
    datas: string;
    titles: Array<IViewPivotTableTitleItem>;
}

export interface IViewPivotTableTitleItem {
    childrens: null | IViewPivotTableTitleItem[];
    name: string;
    prop: string;
}
export interface IAAFilterListItem {
    aaType: number;
    aaTypeName: string;
    aaList: Array<{ id: number; name: string }>;
    selectAAeids: number[];
    defaultSelectAAeids: number[];
}
export interface IPivotQueryParameter {
    endValuePeriod: number;
    isCustomPeriod: boolean;
    items: IPivotQueryParameterItems[] | [];
    startValuePeriod: number;
    valuePeriodType: number;
}
export interface IPivotQueryParameterItems {
    optionType: number;
    type: number;
    selectedIds: number[];
}
export interface ITabListItem {
    label: string;
    name: string;
    asubType: number;
}
export class AasObj {
    optionId: number = 0;
    optionType: number = 2;
    optionPlace: number = 1;
    isQuery: boolean = false;
    showFields: number[] = [];
    AAEIds: number[] = [];
    AATypeId: number = 0;
}
export class ValueObj {
    optionId: number = 0;
    optionType: number = 3;
    optionPlace: number = 3;
    valueType: number = 0;
    valuePeriodType: number = 1;
    startValuePeriod: number = periodStore.period.endPid;
    endValuePeriod: number = periodStore.period.endPid;
}
export class IValueDropDownOPtionsClass {
    valuePeriodType: number;
    startValuePeriod: number;
    endValuePeriod: number;
    constructor(value: { valuePeriodType?: number; startValuePeriod?: number; endValuePeriod?: number } = {}) {
        this.valuePeriodType = value.valuePeriodType || 1;
        this.startValuePeriod = value.startValuePeriod || 0;
        this.endValuePeriod = value.endValuePeriod || 0;
    }
}
export class IDimensionDropDownOPtionsClass {
    constructor(value: IDimensionItem) {
        this.selectList = value.dropDownOPtions.selectList;
        this.fields = value.dropDownOPtions.fields;
        this.enable = value.dropDownOPtions.enable;
        this.selectList.list = [];
        this.selectList.value = false;
        this.fields.checked = value.type === "asub" ? ["asubName"] : ["name"];
        this.enable.value = false;
    }
    selectList: {
        value: boolean;
        label: string;
        list: IAssistingAccountWithChecked[] | IAccountSubjectModelWithChecked[] | [];
    } = {
        value: false,
        label: "",
        list: [],
    };
    fields: {
        label: string;
        children: Array<{
            value: string;
            label: string;
            required: boolean;
        }>;
        checked: string[];
    } = {
        label: "",
        children: [],
        checked: ["name"],
    };
    enable: {
        value: boolean;
        label: string;
    } = {
        value: false,
        label: "启用筛选",
    };
}
export interface IValuePeriod {
    valuePeriodType: number;
    startValuePeriod: number;
    endValuePeriod: number;
}
export class IPivotTableSearchInfo {
    startValuePeriod = 0;
    endValuePeriod = 0;
    subjectCodeStart = "";
    subjectCodeEnd = "";
    fcid = -1;
    subTotal = false;
    total = true;
    selectAAEIDs = "";
    statementId? = 0;
}
