<template>
    <div class="content view-pivot-table">
        <div class="title">数据透视表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line" style="box-sizing: border-box">
                <div class="main-tool-left">
                    <SearchInfoContainer v-if="toolLeftShow" ref="containerRef">
                        <template v-slot:title>{{ periodSelectable ? currentPeriodInfo : "筛选" }}</template>
                        <div v-if="periodSelectable" class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field" style="width: 304px; padding-left: 6px">
                                <Select v-model="searchInfo.startValuePeriod" :teleported="false" class="period-select-list">
                                    <el-option v-for="item in accountantList" :label="item.label" :value="item.pid" :key="item.pid" />
                                </Select>
                                <span class="pl-10 pr-10">至</span>
                                <Select v-model="searchInfo.endValuePeriod" :teleported="false" class="period-select-list">
                                    <el-option v-for="item in accountantList" :label="item.label" :value="item.pid" :key="item.pid" />
                                </Select>
                            </div>
                        </div>

                        <div class="line-item input" v-if="subjectsFilterable">
                            <div class="line-item-title">起始科目：</div>
                            <div class="line-item-field">
                                <FilterCustomSelect
                                    v-model="searchInfo.subjectCodeStart"
                                    :teleported="false"
                                    :clearable="true"
                                    :filterable="true"
                                    :filter-method="handleAsubNameFilter"
                                    IconClearRight="26px"
                                    placeholder=" "
                                >
                                    <ElOption v-for="item in subjectsFilterList" :key="item.value" :label="item.label" :value="item.value">
                                    </ElOption>
                                </FilterCustomSelect>
                            </div>
                        </div>
                        <div class="line-item input" v-if="subjectsFilterable">
                            <div class="line-item-title">结束科目：</div>
                            <div class="line-item-field">
                                <FilterCustomSelect
                                    v-model="searchInfo.subjectCodeEnd"
                                    :teleported="false"
                                    :filterable="true"
                                    :clearable="true"
                                    :filter-method="handleAsubNameFilter"
                                    placeholder=" "
                                    IconClearRight="26px"
                                >
                                    <ElOption v-for="item in subjectsFilterList" :key="item.value" :label="item.label" :value="item.value">
                                    </ElOption>
                                </FilterCustomSelect>
                            </div>
                        </div>
                        <div class="line-item input" v-for="item in aaFilterList" :key="item.aaTypeName">
                            <div class="line-item-title">{{ item.aaTypeName }}：</div>
                            <div class="line-item-field">
                                <SelectCheckbox
                                    :options="item.aaList"
                                    :useClick="true"
                                    :use-el-icon="true"
                                    width="304px"
                                    v-model:selectedList="item.selectAAeids"
                                    class="item"
                                />
                            </div>
                        </div>

                        <div class="line-item input" v-show="cFilterShow">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field fcid-select">
                                <el-select 
                                    v-model="searchInfo.fcid" 
                                    :teleported="false" 
                                    placeholder=" "
                                    :filterable="true"
                                    :filter-method="fcListFilterMethod"
                                >
                                    <el-option 
                                        :label="item.fcname" 
                                        :value="item.fcid" 
                                        v-for="item in showfcList" 
                                        :key="item.fcid"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.subTotal" label="显示小计"></el-checkbox>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.total" label="显示合计"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleConfirm">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                </div>
                <div class="main-tool-right">
                    <el-checkbox v-if="!toolLeftShow" v-model="searchInfo.subTotal" label="显示小计" @change="handleConfirm"></el-checkbox>
                    <el-checkbox v-if="!toolLeftShow" v-model="searchInfo.total" label="显示合计" @change="handleConfirm"></el-checkbox>

                    <a v-permission="['customstatement-canexport']" class="button ml-10" @click="exportTemplate()">导出</a>
                    <!-- <a v-permission="['customstatement-canexport']" class="button ml-10" @click="exportModel()">打印</a> -->
                    <a class="button ml-10" @click="goBack()">返回</a>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :class="['view-table custom-table', { 'overflow-hidden': showDataMask }]"
                    ref="previewPivotTableRef"
                    :data="tableData"
                    :columns="Columns"
                    :fit="true"
                    height="100%"
                    :empty-text="emptyText"
                    v-loading="loading"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :currentPage="paginationData.currentPage"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :object-span-method="objectSpanMethod"
                    :scrollbar-show="true"
                    :highlight-current-row="false"
                    :show-overflow-tooltip="true"
                    :cell-class-name="handleCellClassName"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                </Table>
                <DataMask v-show="showDataMask" ref="dataMaskRef" :immediate="false" :show-lines="5" :hasPage="true" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, toRef, type Ref, watch, computed, defineAsyncComponent, onActivated, watchEffect } from "vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import Select from "@/components/Select/index.vue";
import type { IPeriod } from "@/api/period";
import { request, type IResponseModel } from "@/util/service";
import Table from "@/components/Table/index.vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import $bus from "@/bus";
import { ElNotify } from "@/util/notify";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import { globalExport, getUrlSearchParams, globalFormPost } from "@/util/url";
import type { ICurrency } from "@/views/AccountBooks/AASubsidiaryLedger/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type {
    IViewPivotTableResData,
    IViewPivotTableTitleItem,
    IAAFilterListItem,
    IPivotQueryParameter,
    IPivotQueryParameterItems,
    IPeriodList,
} from "../types";
import { IPivotTableSearchInfo } from "../types";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { usePagination } from "@/hooks/usePagination";
import { getPeriodList } from "../utils";
import { ElConfirm } from "@/util/confirm";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import { formatMoney } from "@/util/format";
import FilterCustomSelect from "@/views/Statements/components/EditEquation/FilterCustomSelect.vue";
import ElOption from "@/components/Option/index.vue";
import { cloneDeep } from "lodash";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "CustomViewPivotTable";
const DataMask = defineAsyncComponent(() => import("@/components/DataMask/index.vue"));

const { monPeriodList, quarterPeriodList, yearPeriodList } = getPeriodList();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination("viewPivotTable", true);
const accountSubjectStore = useAccountSubjectStore();
const subjectDataAll: Ref<IAccountSubjectModel[]> = toRef(accountSubjectStore, "accountSubjectList");
const aaTypeList = toRef(useAssistingAccountingStore(), "assistingAccountingTypeList");
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingListAll");
const emits = defineEmits(["goMain"]);
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const subjectsFilterable = ref<IPivotQueryParameterItems | []>();
const aaFilterList = ref<IAAFilterListItem[]>([]);
const subjectsFilterList = ref<Array<{ value: string; label: string; acronym: string }>>([]);
// 是否显示遮罩
const trialStatusStore = useTrialStatusStore();
const isExpired = computed(() => trialStatusStore.isExpired);
let tableData = ref<{ [key: string]: string }[]>([]);
const showDataMask = computed(() => isExpired.value && !window.isErp && !window.isProSystem && tableData.value.length > 5);
const dataMaskRef = ref<InstanceType<typeof DataMask>>();

let accountantList = ref<IPeriodList[] | Array<{ pid: number; label: string }>>(monPeriodList);
const toolLeftShow = ref(true);
const periodSelectable = ref(true);
const currentPeriodInfo = ref(accountantList.value[accountantList.value.length - 1]?.label + "—" + accountantList.value[0]?.label);
const searchInfo = reactive(new IPivotTableSearchInfo());
let exportParams = ref(new IPivotTableSearchInfo());

const fcList = ref<{ fcid: number; fcname: string }[]>([]);
const previewPivotTableRef = ref<InstanceType<typeof Table>>();

let cFilterShow = ref(false);

let Columns = ref<IColumnProps[]>([]);
const InitCurrencyApi = async () => {
    await useCurrencyStore().getCurrencyList();
    fcList.value = useCurrencyStore().fcList.map((v: ICurrency) => {
        return { fcid: v.id, fcname: v.name };
    });
}
const initial = () => {
    request({
        url: "/api/AccountSubject/ExistsFc",
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        cFilterShow.value = res.data;
        if (res.data) {
            InitCurrencyApi();
        }
    });
};
function convertData(data: IViewPivotTableTitleItem[]): IColumnProps[] {
    return data.map((item: IViewPivotTableTitleItem) => {
        const { name, prop, childrens } = item;
        const newItem: IColumnProps = {
            label: name,
            prop: prop,
            minWidth: window.isErp ? 120 : 100,
            align: prop.includes("key") ? "left" : "right",
            headerAlign: "center",
            className: "set-min-col-width",
            formatter: (row, column, value) => {
                return value
                    ? prop.includes("value")
                        ? Number(value) === 0
                            ? 0
                            : formatMoney(value)
                        : value.includes("^")
                        ? customSubstring(value)
                        : value
                    : "";
            }, 
            width: getColumnWidth(setModule, prop)
        };
        if (childrens && childrens.length > 0) {
            newItem.children = convertData(childrens);
        }
        return newItem;
    });
}
function customSubstring(value: string) {
    return value.substring(0, value.lastIndexOf("^"));
}
const emptyText = ref(" ");
const loading = ref(false);
const subjectsFilterListAll = ref<Array<{ value: string; label: string; acronym: string }>>([]);
let queryParamsInit: any = null;
$bus.on("enterPivotView", (statementId: number) => {
    loading.value = true;
    searchInfo.statementId = statementId;
    request({
        url: `/api/CustomStatement/PivotTable/QueryParameter?statementId=${statementId}`,
        method: "get",
    })
        .then((res: IResponseModel<IPivotQueryParameter>) => {
            if (res.state === 1000) {
                let data = res.data;
                queryParamsInit = data;
                searchInfo.startValuePeriod = data.startValuePeriod;
                searchInfo.endValuePeriod = data.endValuePeriod;
                toolLeftShow.value = !data.isCustomPeriod || data.items.length > 0;
                periodSelectable.value = !data.isCustomPeriod;
                getAccountantList(data.valuePeriodType);
                subjectsFilterable.value = data.items.find((v) => v.optionType === 1) as IPivotQueryParameterItems;
                const selectedIdsMap = new Map(subjectsFilterable.value?.selectedIds.map((value) => [value, true]));
                if (subjectsFilterable.value) {
                    subjectsFilterList.value = subjectDataAll.value.reduce(
                        (acc: Array<{ value: string; label: string; acronym: string }>, v) => {
                            if (selectedIdsMap.has(v.asubId)) {
                                acc.push({ value: String(v.asubCode), label: v.asubAAName, acronym: v.acronym });
                            }
                            return acc;
                        },
                        []
                    );
                    subjectsFilterListAll.value = cloneDeep(subjectsFilterList.value);
                }
                data.items.length &&
                    data.items.forEach((v) => {
                        if (v.optionType === 2) {
                            const op = v;
                            const i = aaFilterList.value.length;
                            const targetAAType = aaTypeList.value.find((type) => type.aaType === op.type);
                            aaFilterList.value[i] = {
                                aaType: op.type,
                                aaTypeName: targetAAType?.aaTypeName || "",
                                aaList: [],
                                selectAAeids: [],
                                defaultSelectAAeids: [],
                            };

                            assistingAccountingList.value.forEach((item) => {
                                if (item.aatype === op.type && op.selectedIds.includes(item.aaeid)) {
                                    aaFilterList.value[i].selectAAeids.push(item.aaeid);
                                    aaFilterList.value[i].defaultSelectAAeids.push(item.aaeid);
                                    aaFilterList.value[i].aaList.push({ id: item.aaeid, name: item.aaname });
                                }
                            });
                        }
                    });
            } else {
                ElNotify({
                    type: "warning",
                    message: res.msg || "获取失败",
                });
            }
        })
        .catch(() => {})
        .finally(() => {
            if (paginationData.currentPage !== 1) {
                paginationData.currentPage = 1;
            } else {
                handleSearchSwitch();
            }
        });
});
watch(
    () => containerRef.value?.popoverShow,
    (val) => {
        if (val) {
            aaFilterList.value.forEach((item) => {
                if (!item.selectAAeids.length) {
                    item.selectAAeids = item.defaultSelectAAeids;
                }
            });
        }
    }
);
const handleAsubNameFilter = (v: string) => {
    subjectsFilterList.value = subjectsFilterListAll.value.filter(
        (item: any, index: number) => item.label.includes(v.trim()) || item.acronym.includes(v.trim())
    );
};
function handleConfirm() {
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    if (searchInfo.startValuePeriod > searchInfo.endValuePeriod) {
        ElNotify({
            type: "warning",
            message: "亲，开始期间不能大于结束期间哦！",
        });
        searchInfo.endValuePeriod = searchInfo.startValuePeriod;
    }
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
    } else {
        handleSearchSwitch();
    }
    containerRef.value?.handleClose();
}
function getAccountantList(valuePeriodType: number) {
    let selectedList: IPeriodList[] | Array<{ pid: number; label: string }> = [];
    switch (valuePeriodType) {
        case 1:
            selectedList = monPeriodList;
            break;
        case 2:
            selectedList = quarterPeriodList;
            break;
        case 3:
            selectedList = yearPeriodList;
            break;
        default:
            selectedList = [];
            break;
    }

    accountantList.value = selectedList;
}
let colCount = 0;
function handleSearchSwitch() {
    loading.value = true;
    currentPeriodInfo.value = ((accountantList.value.find((item: IPeriod) => item.pid === searchInfo.startValuePeriod)?.label as string) +
        "—" +
        accountantList.value.find((item: IPeriod) => item.pid === searchInfo.endValuePeriod)?.label) as string;
    let selectAAEIDs = "";
    for (const obj of aaFilterList.value) {
        if (obj.selectAAeids && obj.selectAAeids.length > 0) {
            selectAAEIDs += `${obj.aaType}:${obj.selectAAeids.join(",")}|`;
        }
    }
    searchInfo.selectAAEIDs = selectAAEIDs.slice(0, -1);
    let queryData: any = { ...searchInfo };
    delete queryData.statementId;
    exportParams.value = queryData;
    let params: any = { statementId: searchInfo.statementId, pageIndex: paginationData.currentPage, pageSize: paginationData.pageSize };
    new Promise<IResponseModel<IViewPivotTableResData>>((resolve, reject) => {
        request({
            url: `/api/CustomStatement/PivotTable/GetData`,
            method: "post",
            params: params,
            data: queryData,
        })
            .then((res: IResponseModel<IViewPivotTableResData>) => {
                if (res.state === 1000) {
                    let data = res.data;
                    if (data.columnCount > 200 && data.columnOptions.length) {
                        request({
                            url: `/api/CustomStatement/PivotTable/Conversion`,
                            method: "put",
                            params: { statementId: searchInfo.statementId },
                        })
                            .then((resC: IResponseModel<boolean>) => {
                                loading.value = false;
                                if (resC.state === 1000 && resC.data) {
                                    ElConfirm(
                                        `您设置的报表列设置的数据已超限制，已自动将您列设置的${data.columnOptions
                                            .map((item) => item.name)
                                            .join("、")}转换至行，如您仍需按列设置查看数据，请重新编辑修改报表设置。`,
                                        true
                                    );
                                    Columns.value = [];
                                    tableData.value = [];
                                    request({
                                        url: `/api/CustomStatement/PivotTable/GetData`,
                                        method: "post",
                                        params: params,
                                        data: queryData,
                                    })
                                        .then((resAgain: IResponseModel<IViewPivotTableResData>) => {
                                            if (res.state === 1000) {
                                                resolve(resAgain);
                                            }
                                        })
                                        .catch(() => {
                                            resolve(res);
                                        });
                                } else {
                                    resolve(res);
                                }
                            })
                            .catch(() => {
                                loading.value = false;
                            });
                    } else {
                        loading.value = false;

                        resolve(res);
                    }
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg || "获取失败",
                    });
                }
            })
            .catch(() => {
                loading.value = false;
            });
    })
        .then((res: IResponseModel<IViewPivotTableResData>) => {
            let data = res.data;
            colCount = data.columnCount;
            Columns.value = data.titles ? convertData(data.titles) : [];
            if (Columns.value.length > 0) {
                let lastCol = Columns.value[Columns.value.length - 1];
                if (lastCol.children && lastCol.children.length > 0) {
                    lastCol.children[lastCol.children.length - 1].resizable = false;
                } else {
                    lastCol.resizable = false;
                }
            }
            tableData.value = data.datas ? JSON.parse(data.datas) : [];
            emptyText.value = data.datas ? " " : "暂无数据";
            paginationData.total = data.count;
            precomputeSpans();
            if (showDataMask.value) {
                dataMaskRef.value?.getTableHeight(previewPivotTableRef.value?.getTable());
            }
        })
        .catch(() => {
            loading.value = false;
        });
}
let cachedSpans = {} as any;
function precomputeSpans() {
    let flattenChildrenColumns = flattenChildren(Columns.value);

    tableData.value.forEach((row, rowIndex) => {
        flattenChildrenColumns.forEach((column, columnIndex) => {
            let key = "row" + rowIndex + "column" + columnIndex;
            if (column.prop?.includes("key")) {
                colPropsData.value[columnIndex] = column.prop || "";
                const currentValue = row[column.prop];
                const preRow = tableData.value[rowIndex - 1] as any;
                const preValue = preRow ? preRow[column.prop] : null;
                if (
                    (currentValue === preValue && checkAllValuesEqual(row, rowIndex, columnIndex)) ||
                    (customSubstring(row["key1"]) === "合计" && columnIndex !== 0 && column.label !== "取值类型" && column.label !== "日期")
                ) {
                    cachedSpans[key] = { rowspan: 0, colspan: 0 };
                } else {
                    let rowspan = 1;
                    for (let i = rowIndex + 1; i < tableData.value.length; i++) {
                        const nextRow = tableData.value[i] as any;
                        const nextValue = nextRow[column.prop];
                        if (nextValue === currentValue && checkAllValuesEqual(nextRow, rowIndex + 1, columnIndex)) {
                            rowspan++;
                        } else {
                            break;
                        }
                    }
                    rowSpanData.value[columnIndex] = rowspan;
                    let needSpan = columnIndex === 0 ? rowspan : Math.min(rowspan, rowSpanData.value[columnIndex - 1]);
                    if (needSpan > 1 && !mergeColArr.value.includes(column.prop)) {
                        mergeColArr.value.push(column.prop);
                    }
                    // 合计
                    cachedSpans[key] = {
                        rowspan: needSpan,
                        colspan:
                            customSubstring(row["key1"]) === "合计" && columnIndex === 0
                                ? countKeysWithSubstring(row, flattenChildrenColumns, "key")
                                : 1,
                    };
                }
            }
        });
    });
}
function countKeysWithSubstring(obj: any, flattenChildrenColumns: IColumnProps[], substring: string) {
    let count = 0;

    for (let key in obj) {
        if (
            key.includes(substring) &&
            findLabel(flattenChildrenColumns, key) !== "取值类型" &&
            findLabel(flattenChildrenColumns, key) !== "日期"
        ) {
            count++;
        }
    }
    return count;
}
function findLabel(arr: IColumnProps[], key: string) {
    return arr.find((item: IColumnProps) => item.prop === key)?.label;
}
function flattenChildren(arr: IColumnProps[]) {
    let result: IColumnProps[] = [];
    arr.forEach((item) => {
        if (item.children && item.children.length > 0) {
            result.push(...flattenChildren(item.children));
        } else {
            result.push(item);
        }
    });

    return result;
}
watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    handleSearchSwitch();
});
onActivated(() => {
    previewPivotTableRef.value?.setScrollTop(previewPivotTableRef.value?.scrollTop);
});
initial();

function exportTemplate() {
    if (loading.value) {
        return false;
    }
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    if (colCount > 256) {
        ElNotify({
            type: "warning",
            message: "导出数据量超过256列，无法导出，请重新筛选条件！",
        });
        return false;
    }
    globalFormPost(`/api/CustomStatement/PivotTable/Export?StatementId=${searchInfo.statementId}`, exportParams.value, "export");
}
let rowSpanData = ref<{ [key: number]: number }>({ 0: 0 });
let colPropsData = ref<{ [key: number]: string }>({ 0: "" });
let mergeColArr = ref<string[]>([]);
const objectSpanMethod = (data: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    let { column, rowIndex, columnIndex } = data;
    let key = "row" + rowIndex + "column" + columnIndex;
    if (column.property?.includes("key")) {
        return cachedSpans[key];
    }
    // 如果缓存中没有对应的结果，返回默认值或进行实际计算
    return { rowspan: 1, colspan: 1 };
};
// 判断相同行前面所有列的值是否都相等
const checkAllValuesEqual = (row: any, rowIndex: number, columnIndex: number) => {
    if (columnIndex === 0) {
        return true;
    }
    for (let i = 0; i < columnIndex; i++) {
        const prop = colPropsData.value[i];
        if (row[prop] !== (tableData.value[rowIndex - 1] as any)[prop]) {
            return false;
        }
    }
    return true;
};

function handleCellClassName(data: any) {
    if (Object.values(mergeColArr.value).includes(data.column.property)) {
        return "merge-col";
    }

    if (data.column.property.includes("value") && Number(data.row[data.column.property]) < 0) {
        return "highlight-red";
    }
}
function handleReset() {
    searchInfo.startValuePeriod = queryParamsInit?.startValuePeriod;
    searchInfo.endValuePeriod = queryParamsInit?.endValuePeriod;
    searchInfo.subjectCodeStart = "";
    searchInfo.subjectCodeEnd = "";
    searchInfo.fcid = -1;
    searchInfo.subTotal = false;
    searchInfo.total = true;
    aaFilterList.value.forEach((item) => {
        item.selectAAeids = item.defaultSelectAAeids;
    });
}
function goBack() {
    tableData.value = [];
    Columns.value = [];
    emptyText.value = " ";
    aaFilterList.value = [];
    handleReset();
    emits("goMain");
}
function handleClose() {
    containerRef.value?.handleClose();
}

const fcListAll = ref<Array<any>>([]);
const showfcList = ref<Array<any>>([]);
watchEffect(() => {
    fcListAll.value = JSON.parse(JSON.stringify(fcList.value));
    fcListAll.value.unshift({ fcid: -1, fcname: "综合本位币" });
    showfcList.value = JSON.parse(JSON.stringify(fcListAll.value));
});
function fcListFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcListAll.value, 'fcname');
}
</script>

<style scoped lang="less">
@import "@/style/Statements/Statements.less";
.view-pivot-table {
    width: 100%;
    height: 100%;
    .main-content {
        width: 100%;
        height: 100%;
        .main-top {
            .main-tool-left {
                :deep(.search-info-container .search-info-container-popover) {
                    .downlist .line-item .line-item-field {
                        .el-select {
                            width: 304px;
                            &.select-mini {
                                width: 132px;
                            }
                            .el-input__suffix-inner > :first-child {
                                margin-left: 0;
                            }
                        }
                    }
                }
            }
        }
        .main-center {
            flex: 1;
            .view-table {
                height: 100%;
                display: flex;
                flex-direction: column;
                .el-table {
                    flex: 1;
                }
                &.overflow-hidden {
                    :deep(.el-scrollbar__wrap) {
                        overflow: hidden;
                    }
                    :deep(.el-scrollbar__bar.is-vertical) {
                        display: none;
                    }
                }
            }

            :deep(.el-table__empty-block) {
                width: 500px !important;
                height: 100% !important;
                position: absolute;
                left: 50%;
                top: 0;
                transform: translateX(-50%);
                min-height: 0;
            }
            :deep(.el-table) {
                .el-table__body tr:hover,
                .el-table__body tr:hover > td.merge-col.el-table__cell {
                    background-color: unset !important;
                }
                .first_row,
                .second_row,
                .third_row {
                    td:nth-child(1) {
                        background: #f5f7fa !important;
                    }
                }
                // 第一列和第二列是同一行的情况   第二列和第三列是同一行的情况
                .first_row.second_row,
                .second_row.third_row {
                    td:nth-child(2) {
                        background: #f5f7fa !important;
                    }
                }
                // 第一列、第二列和第三列是同一行的情况
                .first_row.second_row.third_row {
                    td:nth-child(3) {
                        background: #f5f7fa !important;
                    }
                }
                .set-min-col-width.highlight-red .cell {
                    color: var(--red);
                }
            }
        }
    }
}
</style>
