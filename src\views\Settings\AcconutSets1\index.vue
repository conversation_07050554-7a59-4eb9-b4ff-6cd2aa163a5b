<template>
    <div class="slot-content">
        <div id="proNewAccountSetDialog" class="pro-newaccountset-dialog" v-if="proNewAccountSetDialog">
            <img src="@/assets/Settings/noaccountset-pic1.png" />
            <div class="dialog-title">您是需要导入免费版账套，还是新建账套？</div>
            <div class="buttons">
                <a class="button opacity" @click="createAccount">新建账套</a>
                <a class="button solid-button" style="margin-left: 35px" @click="gotoTransferPro">导入免费版账套</a>
            </div>
        </div>
        <div id="divEditInfo" class="slot-content-mini create-content" v-if="divEditInfo">
            <div style="border: 1px solid var(--border-color); border-radius: 0 0 10px 12px">
                <div class="edit-content-title pro-show">
                    {{ isProSystem ? "创建账套，即可使用全部功能" : "创建账套，即可体验全部功能" }}
                </div>
                <!-- v-show="!isHideBarcode" -->
                <div class="edit-content-sub-title pro-show" style="">
                    {{ isProSystem ? "云财务专业版；支持随时修改账套信息" : "柠檬云不限账套，支持随时修改账套信息；创建后还可领取惊喜福利"
                    }}<img class="icon" src="@/assets/Settings/gift-small.png" />
                </div>
                <!-- 新版 -->
                <div class="create-content-form">
                    <div class="line-item1">
                        <div class="line-item-title"><span class="highlight-red">*</span>单位名称：</div>
                        <div class="line-item-field">
                            <el-autocomplete
                                ref="asNameRef"
                                v-model="createData.asName"
                                :prop="[{ required: true, message: '亲，单位名称不能为空', trigger: ['blur', 'change'] }]"
                                :fetch-suggestions="querySearch"
                                :trigger-on-focus="false"
                                class="inline-input w-50"
                                placeholder="请输入完整的单位名称"
                                style="width: 238px"
                                @select="selectName"
                            />
                        </div>
                    </div>
                    <div class="line-item1">
                        <div class="line-item-title"><span class="highlight-red">*</span>启用年月：</div>
                        <div class="line-item-field" id="startYear">
                            <div class="jqtransform">
                                <el-select
                                    v-model="createData.asStartYear"
                                    style="width: 115px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                >
                                    <el-option
                                        v-for="item in yearList"
                                        :value="item"
                                        :label="(item as number)+'年'"
                                        :key="(item as number)"
                                    ></el-option>
                                </el-select>
                                <el-select
                                    v-model="createData.asStartMonth"
                                    style="width: 115px; padding: 0; margin-left: 10px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                >
                                    <el-option
                                        v-for="item in monthList"
                                        :value="item"
                                        :label="(item as number)+'月'"
                                        :key="(item as number)"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div class="line-item1">
                        <div class="line-item-title"><span class="highlight-red">*</span>会计准则：</div>
                        <div class="line-item-field">
                            <div class="jqtransform">
                                <el-select
                                    v-model="createData.accountingStandard"
                                    style="width: 238px"
                                    :teleported="false"
                                    :fit-input-width="true"
                                    placement="bottom"
                                >
                                    <el-option label="小企业会计准则" value="1" />
                                    <el-option label="企业会计准则" value="2" />
                                    <el-option label="民间非营利组织会计制度" value="3" />
                                    <el-option label="农民专业合作社财务会计制度（2023年）" value="5" />
                                    <el-option value="6" label="工会会计制度" />
                                    <el-option value="7" label="农村集体经济组织会计制度" />
                                </el-select>
                                <el-popover
                                    placement="right"
                                    :width="210"
                                    trigger="hover"
                                    content="创建后年月无法修改，企业会计准则可点击账套管理-设置-切换准则调整报表样式。"
                                >
                                    <template #reference>
                                        <div class="image-container"></div>
                                    </template>
                                </el-popover>
                            </div>
                        </div>
                    </div>
                    <div class="line-item1">
                        <div class="line-item-title"><span class="highlight-red">*</span>增值税种类：</div>
                        <div class="line-item-field">
                            <el-radio-group v-model="createData.taxType">
                                <el-radio label="1" size="large">小规模纳税人</el-radio>
                                <el-radio label="2" size="large">一般纳税人</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="buttons" style="display: flex; justify-content: center">
                        <a class="button solid-button large-1" id="saveAS" @click="SubmitAccountSet">创建账套</a>
                    </div>
                    <a class="link" v-if="isProSystem && !isThirdPart" @click="gotoTransferPro">导入免费版账套</a>
                </div>
                <el-popover v-if="isAccountingAgent" placement="top-end" :width="180" trigger="hover" :show-arrow="false">
                    <template #reference>
                        <div class="report-container" id="dzReport"></div>
                    </template>
                    <div class="qrcode-box">
                        <div>
                            <img v-if="isProSystem" src="@/assets/Settings/import-pro.png" alt="" style="height: 160px" />
                            <img v-else src="@/assets/Settings/import-other.png" alt="" style="height: 160px" />
                        </div>
                        <div class="qrcode-tip">遇到问题扫码添加客服<br />免费为你迁移账套</div>
                    </div>
                </el-popover>
                <div v-if="!isAccountingAgent && !isThirdPart" class="report-container" id="dzReport" @click="gotoImportFromOther"></div>
            </div>
        </div>
        <ProOverFlowDialog v-model:proOverFlow="proOverFlowShow" :proOverFlowText="proOverFlowText" />
    </div>
    <div class="bg_shandow"></div>
</template>

<script lang="ts">
export default {
    name: "AccountSets1",
};
</script>
<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { setTopLocationhref } from "@/util/url";
import { onMounted, ref, reactive, nextTick, inject,computed } from "vue";
import { useRoute } from "vue-router";
import type { ICreateAccountSetResult } from "../AccountSets/types";
import ProOverFlowDialog from "@/components/Dialog/ProOverFlowDialog/index.vue";
import { isCurrentWxworkService, isInWxWork, wxworkSuperAdminUserSn } from "@/util/wxwork";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getAACompanyId, getAACustomerId, getAACustomerName } from "@/util/baseInfo";
import { useUserStoreHook } from "@/store/modules/user";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import type { ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import { getCompanyDetailApi } from "@/api/getCompanyList";
import { globalWindowOpenPage } from "@/util/url";
import { getServiceId } from "@/util/proUtils";

const props = defineProps({
    asId: {
        type: String,
        default: "",
    },
});
const emits = defineEmits(["cancelCreate"]);
const route = useRoute();
const isAccountingAgent = window.isAccountingAgent;
const thirdPartInfoStore = useThirdPartInfoStoreHook();
const isThirdPart = computed({
    get: () => {
        return thirdPartInfoStore.isThirdPart || window.location.href.toLowerCase().includes("isthird");
    },
    set: (val) => val,
});

const proNewAccountSetDialog = ref(true);
const divEditInfo = ref(false);
if (window.isProSystem && !isInWxWork()) {
    request({
        url: window.eHost + "/wb/plan/is-administrator?serviceID=" + getServiceId(),
        method: "get",
    }).then((res: any) => {
        if (!res.statusCode) {
            request({
                url: window.eHost + "/wb/redirect/acc",
                method: "get",
            }).then((res: any) => {
                if (res.statusCode) {
                    setTopLocationhref(res.data);
                } else {
                    ElNotify({ message: "出现异常，请刷新页面重试或联系客服", type: "warning" });
                }
            });
        }
    });
}

const asNameRef = ref();
let isProSystem = window.isProSystem;
const proOverFlowShow = ref(false);
const proOverFlowText = ref("");

const queryParams = reactive({
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
});

const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};

function selectName(item: any) {
    createData.asName = item.value;
    createData.unifiedNumber = item.creditCode;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}

async function createAccount() {
    if (isInWxWork() && (await isCurrentWxworkService()) && accountSetStore.userInfo?.userSn !== (await wxworkSuperAdminUserSn())) {
        ElNotify({ message: "请联系系统管理员创建专业版账套", type: "warning" });
    } else {
        proNewAccountSetDialog.value = false;
        divEditInfo.value = true;
    }
}
if (window.isAccountingAgent) {
    createAccount();
}
const accountSetStore = useAccountSetStore();
async function gotoTransferPro() {
    if (isInWxWork() && (await isCurrentWxworkService()) && accountSetStore.userInfo?.userSn !== (await wxworkSuperAdminUserSn())) {
        ElNotify({ message: "请联系系统管理员创建专业版账套", type: "warning" });
    } else {
        globalWindowOpenPage("/Settings/TransferPro", "账套升级");
    }
}
const yearList = ref<Number[]>([]);
const monthList = ref<Number[]>([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);

function getYearList() {
    let year = new Date().getFullYear();
    for (let i = 2000; i < year + 6; i++) {
        yearList.value.push(i);
    }
}

const createData = reactive({
    asId: props.asId,
    asName: useUserStoreHook().userName || "",
    asStartYear: new Date().getFullYear(),
    asStartMonth: new Date().getMonth() + 1,
    unifiedNumber: "",
    accountingStandard: "1",
    asIndustry: "1010",
    fixdAsset: "1",
    taxType: "1",
    cashJournal: "1",
    showScm: window.isAccountingAgent ? "0" : "1",
    isChangedStartDate: false,
    creditCode: "",
    aaCompanyId: 0,
    customerId: 0,
    scmAsId: 0,
});

function ValidateSelectedDateForEdit() {
    return ValidateSelectedDateInner(createData.asStartYear, createData.asStartMonth);
}

function ValidateSelectedDateInner(yearSelector: number, monthSelector: number) {
    let _msg = "";
    let _succeed = true;
    if (!yearSelector) {
        _msg = "亲，请选择账套启用年月";
        _succeed = false;
    } else {
        if (!monthSelector) {
            _msg = "亲，请选择账套启用年月";
            _succeed = false;
        }
    }
    return { result: _succeed, msg: _msg };
}

// 按钮状态
let submitFlag = true;
function SubmitAccountSet() {
    if (!submitFlag) {
        ElNotify({
            type: "warning",
            message: "账套正在添加中，请稍候",
        });
        return;
    }
    if (createData.asName === "") {
        ElNotify({
            type: "warning",
            message: "亲，单位名称不能为空！",
        });
        return;
    }
    if (createData.asName.match('[\\\\/*?:<>|"]')) {
        ElNotify({
            type: "warning",
            message: '亲，单位名称不能包含\\/*?:<>|"等特殊符号！',
        });
        return;
    }
    if (createData.asName.length < 5) {
        ElNotify({
            type: "warning",
            message: "亲，请输入完整的单位名称哦！至少5个字符~",
        });
        return;
    }
    if (createData.asName.length > 40) {
        ElNotify({
            type: "warning",
            message: "亲，单位名称最多40个字哦！",
        });
        return;
    }
    let _r = ValidateSelectedDateForEdit();
    if (!_r.result) {
        ElNotify({
            type: "warning",
            message: _r.msg,
        });
        return;
    }

    submitFlag = false;
    const data = {
        accountingStandard: createData.accountingStandard,
        asId: createData.asId,
        asName: createData.asName,
        asStartMonth: createData.asStartMonth,
        asStartYear: createData.asStartYear,
        creditCode: createData.unifiedNumber, //重复字段
        isChangedStartDate: createData.isChangedStartDate,
        SubmitType: "New",
        AACompanyId: getAACompanyId(),
        CustomerId: getAACustomerId(),
        // 增值税种类 默认小规模
        taxType: createData.taxType,
        // 凭证审核 默认不审核
        checkNeeded: 0,
        // 资金模块  默认启用
        cashJournal: "1",
        // 关联进销存 默认启用
        showScm: "1",
        // 固定资产模块 默认启用
        fixedAsset: "1",
        // 统一社会信用代码 默认为空
        unifiedNumber: "",
        // 行业 默认空
        asIndustry: "",
    };
    SubmitAccountSetInner(data);
}

function SubmitAccountSetInner(data: any) {
    submitFlag = false;
    let submitUrl = `/api/AccountSetOnlyAuth/V2?serviceid=${getServiceId()}`;

    request({
        url: submitUrl,
        method: "post",
        data,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    })
        .then((res: IResponseModel<ICreateAccountSetResult>) => {
            if (res.state == 1000) {
                ElNotify({
                    type: "success",
                    message: "亲，保存成功啦！",
                });
                if (isThirdPart.value) {
                    thirdPartNotify(thirtPartNotifyTypeEnum.accountSet1CreateAccountSet, { asId: res.data.newAsid }).then(() => {
                        setTopLocationhref(res.data.urlTarget);
                    });
                    return;
                }
                localStorage.setItem("showCreateSuccessDialog", "true");
                if (isLemonClient()) {
                    getLemonClient().setSelectedAccount(res.data.newAsid);
                    return;
                }
                //"/Default/Default?appasid=200006112I1l1"
                setTopLocationhref(res.data.urlTarget);
                // 创建完返回账套列表页
                cancelCreate();
                return;
            } else if (res.state === 2000 && res.subState === 1 && res.msg === "Overflow") {
                ElNotify({
                    type: "warning",
                    message: "亲，您的应用创建账套的数量已经超过限额，请联系管理员!x！",
                });
            } else if (res.state === 2000 && res.subState === 8) {
                ElConfirm(res.msg, true, () => {}, "提示", { confirmButtonText: "知道了", cancelButtonText: "" });
            } else {
                if (res.msg !== null && res.msg !== undefined && res.msg !== "" && res.msg !== "Failed") {
                    if (res.msg === "亲，期初的借方累计或贷方累计存在数据，需要先删除才能修改为1月哦~") {
                        ElConfirm(res.msg);
                    } else {
                        ElNotify({
                            type: "warning",
                            message: res.msg == "请使用管理员操作" ? "系统管理员才可以创建账套" : res.msg,
                        });
                    }
                } else {
                    ElNotify({
                        type: "error",
                        message: "亲，保存失败啦，请刷新重试！",
                    });
                }
            }
        })
        .catch((err: any) => {
            if (err.response?.status === 400) {
                proOverFlowText.value = err.response.data;
                thirdPartNotify(thirtPartNotifyTypeEnum.accountSet1AccountSetOverflow).then(() => {
                    proOverFlowShow.value = true;
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "创建账套失败，请稍后重试！",
                });
            }
        })
        .finally(() => {
            submitFlag = true;
        });
}

// 跳转到旧账导入
const gotoImportFromOther = () => {
    // 触发返回账套
    localStorage.setItem("isFromCreateAccountSet", "true");
    globalWindowOpenPage("/Settings/ImportFromOther", "旧账导入");
};
// function ShowCompanyModal($modal, width) {
//     let topPosition;
//     if (isLemonClient()) {
//         topPosition = (document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop) + 100;
//     } else {
//         topPosition = (top.document.documentElement.scrollTop || top.window.pageYOffset || top.document.body.scrollTop) + 100;
//     }
//     $modal.window({
//         minimizable: false,
//         maximizable: false,
//         closable: true,
//         _draggable: false,
//         _resizable: false,
//         modal: true,
//         width: width
//     });
//     $modal.window("open").window('resize', { top: topPosition });
//     $(".window-mask").css("height", document.body.scrollHeight);
//     $modal.show();
// }

function showStartYearTip() {
    ElConfirm("有凭证、资产、资金、工资数据或存在结账期间，无法再修改账套启用年月。", true, () => {}, "账套启用年月说明");
}

function cancelCreate() {
    emits("cancelCreate");
}

const reloadTopAccountSetList = inject("reloadTopAccountSetList") as () => void;
onMounted(() => {
    console.log("isAccountingAgent", reloadTopAccountSetList);
    !window.isAccountingAgent && reloadTopAccountSetList && reloadTopAccountSetList();
    getYearList();
    nextTick(() => {
        // 单位名称默认带出用户名
        if (!createData.asName) {
            if (getAACustomerName()) {
                createData.asName = decodeURIComponent(getAACustomerName());
            } else {
                createData.asName = useUserStoreHook().userName;
            }
        }
    });
    //目前是针对杭州渠道云
    if (window.isProSystem && !isThirdPart.value) {
        proNewAccountSetDialog.value = true;
        divEditInfo.value = false;
    } else {
        proNewAccountSetDialog.value = false;
        divEditInfo.value = true;
    }
});
</script>

<style scoped lang="less">
@import "@/style/Settings/CreatAccountSet.less";
.line-item1 + .line-item1 {
    margin-top: 24px;
}
.bg_shandow {
    background-color: #c0c0c0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -999;
}
.pro-newaccountset-dialog {
    position: fixed;
    top: 0;
    left: 116px;
    right: 0;
    margin: 150px auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--white);
    border-radius: 10px;
    width: 1001px;
    height: 552px;

    & img {
        width: 237px;
        height: 164px;
        margin-top: 66px;
    }
    & .dialog-title {
        color: var(--font-color);
        line-height: 45px;
        font-size: 32px;
        font-weight: 600;
        margin-top: 47px;
    }
    & .buttons {
        margin-top: 54px;
        & .button {
            border-radius: 6px;
            width: 160px;
            height: 48px;
            box-sizing: border-box;
            line-height: 46px;
            font-size: 18px;
        }
        & .opacity {
            background-color: rgba(68, 180, 73, 0.08);
            border-color: var(--main-color);
            color: var(--main-color);
        }
    }
}
.create-content {
    position: fixed;
    top: 0;
    left: 116px;
    right: 0;
    margin: 150px auto;
    display: block;
    border-radius: 0 0 12px 12px;
    padding: 0px;
    border: none;
    & .edit-content-title {
        color: var(--font-color);
        font-size: var(--h1);
        line-height: 28px;
        text-align: center;
        margin-top: 34px;
        font-weight: bold;
    }
    & .edit-content-sub-title {
        margin-top: 5px;
        color: #999999;
        font-size: 13px;
        line-height: 19px;
        text-align: center;
        & .icon {
            vertical-align: top;
            margin-left: 7px;
            margin-top: -2px;
            width: 17px;
            height: 19px;
        }
    }

    // & .report-container {
    //     background: url(@/assets/Settings/account-set-import-bg2.png) no-repeat;
    //     background-size: contain;
    // }
}
:deep(.el-select-dropdown__list) {
    max-height: 200px;
    // overflow-y: auto;
    overflow-x: hidden;
}
:deep(.el-select-dropdown__item) {
    width: 50x;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    &.selected {
        background: #f6f6f6;
        color: var(--el-text-color-regular);
        // background: #44b449;
        // color: #fff;
    }
    &:hover {
        background: #44b449;
        color: #fff;
    }
}
.new-message-box {
    & .box-body {
        border-bottom: 1px solid var(--border-color);
        padding: 40px 70px;
        text-align: center;
        min-height: 42px;
        & .body-message {
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            width: 100%;
            & .body-message-title {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                white-space: normal;
                word-break: break-all;
            }
            & .body-message-content {
                color: var(--font-color);
                font-size: var(--h5);
                line-height: 17px;
                margin-top: 10px;
                text-align: left;
                width: 100%;
                padding: 0;
                white-space: normal;
            }
        }
    }
}
</style>
