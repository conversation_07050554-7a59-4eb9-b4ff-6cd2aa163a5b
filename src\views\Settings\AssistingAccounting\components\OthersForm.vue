<template>
    <div class="add-content">
        <el-form :model="formList" label-width="222px" class="formRef" ref="formRef">
            <el-form-item :label="props.rows?.aaTypeName + '编码：'" class="code">
                <div class="max">
                    <input
                        type="text"
                        class="more-long"
                        @input="
                            handleAAInput(LimitCharacterSize.Code, $event, props.rows?.aaTypeName + '编码', 'aaNum', changeFormListData)
                        "
                        @paste="
                            handleAAPaste(LimitCharacterSize.Code, $event)
                        "
                        v-model="formList.aaNum"
                    />
                </div>
            </el-form-item>
            <el-form-item :label="props.rows?.aaTypeName + '名称：'" class="name">
                <div class="max">
                    <el-input
                        class="more-long input-ellipsis"
                        v-model="formList.aaName"
                        @blur="inputTypeBlur('aaNameInputRef')"
                        v-if="aaNameTextareaShow"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        type="textarea"
                        maxlength="256"
                        @input="limitInputLength(formList.aaName,props.rows?.aaTypeName + '名称','name')"
                        @focus="inputTypeFocus()"
                        resize="none"
                        ref="aaNameInputRef"
                        style="z-index:1001"
                    />
                    <Tooltip :content="formList.aaName" :isInput="true" placement="right" :teleported="true" v-else>
                        <input
                            @focus="inputTypeFocus(1)"
                            ref="aaNameInputRef"
                            type="text"
                            class="more-long input-ellipsis"
                            v-model="formList.aaName"
                        />
                    </Tooltip>
                </div>
            </el-form-item>
            <div class="isRow" v-if="props.rows?.column01 !== ''">
                <div class="isRowColumn" v-if="props.rows?.column01 !== ''">
                    <label for="inputValue01" class="lable" @click.prevent>
                        <Tooltip :content="props.rows?.column01" placement="right" :maxWidth="270">{{ props.rows?.column01 }}：</Tooltip>
                    </label>
                <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value01" :is-input="true" placement="right">
                        <input
                            id="inputValue01"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column01, 'Value01', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value01"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                </div>
             <div class="isRowColumn" v-if="props.rows?.column02 !== ''">
                <label for="inputValue02" class="lable" @click.prevent>
                    <Tooltip :content="props.rows?.column02" placement="right" :maxWidth="270">{{ props.rows?.column02 }}：</Tooltip>
                </label>
                <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value02" :is-input="true" placement="right">
                        <input
                           id="inputValue02"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column02, 'Value02', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value02"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
             </div>
            </div>
            <div class="isRow" v-if="props.rows?.column03 !== ''">
                <div class="isRowColumn" v-if="props.rows?.column03 !== ''">
                    <label for="inputValue03" class="lable" @click.prevent>
                        <Tooltip :content="props.rows?.column03" placement="right" :maxWidth="270">{{ props.rows?.column03 }}：</Tooltip>
                    </label>
                    <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value03" :is-input="true" placement="right">
                        <input
                            id="inputValue03"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column03, 'Value03', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value03"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                </div>
               <div class="isRowColumn" v-if="props.rows?.column04 !== ''">
                <label for="inputValue04" class="lable" @click.prevent>
                    <Tooltip :content="props.rows?.column04" placement="right" :maxWidth="270">{{ props.rows?.column04 }}：</Tooltip>
                </label>
                <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value04" :is-input="true" placement="right">
                        <input
                            id="inputValue04"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column04, 'Value04', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value04"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
               </div>
            </div>
            <div class="isRow" v-if="props.rows?.column05 !== ''">
                <div class="isRowColumn" v-if="props.rows?.column05 !== ''">
                    <label for="inputValue05" class="lable" @click.prevent>
                        <Tooltip :content="props.rows?.column05" placement="right" :maxWidth="270">{{ props.rows?.column05 }}：</Tooltip>
                    </label>
                    <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value05" :is-input="true" placement="right">
                        <input
                            id="inputValue05"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column05, 'Value05', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value05"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                </div>
               <div class="isRowColumn" v-if="props.rows?.column06 !== ''">
                <label for="inputValue06" class="lable" @click.prevent>
                    <Tooltip :content="props.rows?.column06" placement="right" :maxWidth="270">{{ props.rows?.column06 }}：</Tooltip>
                </label>
                <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value06" :is-input="true" placement="right">
                        <input
                            id="inputValue06"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column06, 'Value06', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value06"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
               </div>
            </div>
            <div class="isRow" v-if="props.rows?.column07 !== ''">
                <div class="isRowColumn" v-if="props.rows?.column07 !== ''">
                    <label for="inputValue07" class="lable" @click.prevent>
                        <Tooltip :content="props.rows?.column07" placement="right" :maxWidth="270">{{ props.rows?.column07 }}：</Tooltip>
                    </label>
                    <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value07" :is-input="true" placement="right">
                        <input
                            id="inputValue07"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column07, 'Value07', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value07"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                </div>
                <div class="isRowColumn" v-if="props.rows?.column08 !== ''">
                    <label for="inputValue08" class="lable" @click.prevent>
                        <Tooltip :content="props.rows?.column08" placement="right" :maxWidth="270">{{ props.rows?.column08 }}：</Tooltip>
                    </label>
                    <el-form-item>
                    <div class="row">
                        <Tooltip :content="formList.aaEntity.Value08" :is-input="true" placement="right">
                        <input
                            id="inputValue08"
                            type="text"
                            class="input-ellipsis"
                            @input="
                                handleAAInput(LimitCharacterSize.Default, $event, props.rows?.column08, 'Value08', changeFormListData)
                            "
                            @paste="
                                handleAAPaste(LimitCharacterSize.Default, $event)
                            "
                            v-model="formList.aaEntity.Value08"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                </div>
            </div>
            <el-form-item label="备注：">
                <div class="max">
                    <el-input
                        class="more-long input-ellipsis"
                        v-model="formList.aaEntity.note"
                        @blur="inputTypeBlur('noteInputRef')"
                        v-if="noteTextareaShow"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        type="textarea"
                        maxlength="1024"
                        @input="limitInputLength(formList.aaEntity.note,'备注')"
                        @focus="inputTypeFocus()"
                        resize="none"
                        ref="aaNameInputRef"
                        style="z-index:1000"
                    />
                    <div v-else class='note-tooltip-width'>
                        <Tooltip :content="formList.aaEntity.note" :isInput="true" placement="bottom">
                            <input
                                @focus="inputTypeFocus(2)"
                                ref="aaNameInputRef"
                                type="text"
                                class="more-long input-ellipsis"
                                v-model="formList.aaEntity.note"
                            />
                        </Tooltip>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="是否启用：" class="status">
                <el-checkbox label="启用" v-model="formList.aaEntity.status" @change="handleStatusChange" />
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">返回</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, nextTick, watch } from "vue";
import { createCheck, LimitCharacterSize, getNextAaNum, handleAAInput, handleAAPaste, textareaBottom } from "../utils";
import Tooltip from "@/components/Tooltip/index.vue";
import { ValidataCustom } from "../validator";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => (EditType = val);

const props = defineProps<{
    rows: any;
    aaType: number;
}>();

const aaType = computed(() => props.aaType);

const formList = reactive<any>({
    aaNum: "",
    aaName: "",
    aaeID: "",
    USCC: "",
    hasEntity: true,
    aaEntity: {
        Value01: "",
        Value02: "",
        Value03: "",
        Value04: "",
        Value05: "",
        Value06: "",
        Value07: "",
        Value08: "",
        note: "",
        status: true,
    },
});
const changeFormListData = (key: string, val: string) => {
    const keys = Object.keys(formList);
    const aaEntityKeys = Object.keys(formList.aaEntity);
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            formList[key] = val;
            return;
        }
    }
    for (let i = 0; i < aaEntityKeys.length; i++) {
        if (aaEntityKeys[i] === key) {
            formList.aaEntity[key] = val;
            break;
        }
    }
};
const formRef = ref<any>(null);
const emit = defineEmits(["formCancel", "formChanged", "formCancelEdit"]);

let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    const aaeID = EditType === "Edit" ? formList.aaeID : 0;
    const aaNum = formList.aaNum;
    const aaName = formList.aaName;
    createCheck(props.aaType, aaeID, aaNum, aaName, Save);
};
const aaNameTextareaShow=ref(false)
const noteTextareaShow=ref(false)
const aaNameInputRef=ref()
const inputTypeBlur = (value:string) => {
    switch (value) {
        case 'aaNameInputRef':
            aaNameTextareaShow.value = false;
            break;
        case 'noteInputRef':
            noteTextareaShow.value = false;
            break;
    }
};
const inputTypeFocus = (num?:number) => {
    textareaBottom(formRef)
    switch (num) {
        case 1:
            aaNameTextareaShow.value = true;
            break;
        case 2:
            noteTextareaShow.value = true;
            break;
    }
    nextTick(()=>{
        if(num){
            getTextareaFocus(num)
        }
    })
};
const getTextareaFocus = (num:number) => {
    switch (num) {
        case 1:
        case 2:
            aaNameInputRef.value.focus();
            break;
    }
};
function limitInputLength(val: string,label: string,type?:string) {
    if(label==='备注'){
        if(val.length===1024){
            ElNotify({ type: "warning", message: `亲，${label}不能超过1024个字哦~` });
        }
    }else if(type==='name'){
        if(val.length===256){
            ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
        }
    }
}
const Save = () => {
    const entityParams = {
        value01: formList.aaEntity.Value01,
        value02: formList.aaEntity.Value02,
        value03: formList.aaEntity.Value03,
        value04: formList.aaEntity.Value04,
        value05: formList.aaEntity.Value05,
        value06: formList.aaEntity.Value06,
        value07: formList.aaEntity.Value07,
        value08: formList.aaEntity.Value08,
        note: formList.aaEntity.note,
    };
    const params = {
        entity: entityParams,
        aaNum: formList.aaNum,
        aaName: formList.aaName,
        uscc: formList.USCC,
        status: formList.aaEntity.status ? 0 : 1,
        hasEntity: formList.hasEntity,
        ifvoucher: true,
    };
    const urlPath = EditType === "Edit" ? "Custom?aaType=" + aaType.value + "&aaeid=" + formList.aaeID : "Custom?aaType=" + aaType.value;
    if (ValidataCustom(entityParams, params.aaNum, params.aaName, props.rows?.aaTypeName || "")) {
        isSaving = true;
        request({
            url: "/api/AssistingAccounting/" + urlPath,
            method: EditType === "New" ? "post" : "put",
            headers: { "Content-Type": "application/json" },
            data: JSON.stringify(params),
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000 || "Failed" === res.data) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    isSaving = false;
                    return;
                }
                ElNotify({ type: "success", message: "保存成功" });
                useAssistingAccountingStore().getAssistingAccounting();
                if (EditType === "New") {
                    getNextAaNum(props.aaType)
                        .then((res: IResponseModel<string>) => {
                            resetForm();
                            formList.aaNum = res.data;
                            emit("formCancelEdit");
                        })
                        .finally(() => {
                            isSaving = false;
                        });
                } else {
                    handleCancel();
                    isSaving = false;
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
                isSaving = false;
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};
const resetForm = () => {
    const initParams = {
        aaNum: "",
        aaName: "",
        aaeID: "",
        USCC: "",
        hasEntity: true,
        aaEntity: {
            Value01: "",
            Value02: "",
            Value03: "",
            Value04: "",
            Value05: "",
            Value06: "",
            Value07: "",
            Value08: "",
            note: "",
            status: true,
        },
    };
    editForm(initParams);
};
const editForm = (data: any) => {
    Object.keys(data).forEach((key) => {
        if (Object.keys(formList).includes(key)) {
            formList[key] = data[key];
        } else if (Object.keys(formList.aaEntity).includes(key)) {
            formList.aaEntity[key] = data[key];
        }
    });
};
watch(
    formList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
defineExpose({ resetForm, editForm, changeType });
const handleStatusChange = (check: any) => {
    if (!check) {
        ElConfirm("亲，辅助核算项目停用后不能再在凭证中使用哦，是否确认停用？").then((r: boolean) => {
            formList.aaEntity.status = !r;
        });
    }
};
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
@import "@/style/Functions.less";
.add-content {
    :deep(.el-form) {
        .el-form-item {
            &.code,
            &.name {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
}
input.note {
    .detail-original-input(598px, 32px);
}
.formRef {
    input {
        .detail-original-input(188px, 32px);
        &.middle {
            .detail-original-input(288px, 32px);
        }
        &.more-long {
            .detail-original-input(598px, 32px);
        }
        &.big {
            .detail-original-input(698px, 32px);
        }
    }

    .isRow {
        display: flex;
        align-items: center;
        .isRowColumn{
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-left: 42px;
            .lable{
            overflow: hidden;
            width: 167px;
            height: 40px;
            margin-right:13px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            -webkit-box-orient: vertical;
            margin-bottom: 18px;
            word-wrap: break-word;
            font-size: 14px;
            }
            :deep(.el-form-item) {
                .el-form-item__content{
                    margin: 0 !important;
                }
            
            }
        }
        
    }
}
:deep(.span_wrap){
text-align: left;
}
</style>
