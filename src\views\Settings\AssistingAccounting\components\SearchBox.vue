<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <div v-if="!isErp" class="label">输入编码或名称：</div>
            <el-input v-if="!isErp" v-model="searchStr" clearable/>
            <a v-if="!isErp" class="button solid-button ml-10" @click="searchHandle()">查询</a>
            <SearchInfo
                v-if="isErp"
                :width="320"
                :height="30"
                :placeholder="'输入编码/名称/助记码/备注等关键字搜索'"
                @search="searchHandle"
            ></SearchInfo>
            <ErpRefreshButton></ErpRefreshButton>
        </div>
        <div class="main-tool-right">
            <a v-permission="['assistingaccount-canedit']" class="button solid-button ml-10 edited" @click="newHandle">新增</a>
            <a v-permission="['assistingaccount-canexport']" class="button ml-10" @click="exportHandle">导出</a>
            <a v-permission="['assistingaccount-canimport']" class="button ml-10 edited" @click="importHandle">导入</a>
            <a v-permission="['assistingaccount-candelete']" class="button ml-10 edited" @click="clearHandle">清空</a>
            <RefreshButton></RefreshButton>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import SearchInfo from "@/components/SearchInfo/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";

const searchStr = ref("");
const isErp = ref(window.isErp);
const emit = defineEmits(["handleNew", "handleSearch", "handleClear", "handleExport", "handleImport"]);
const newHandle = () => emit("handleNew");
const searchHandle = (search?: string) => {
    if (search !== undefined) {
        searchStr.value = search;
    }
    emit("handleSearch", searchStr.value.trim());
};
const clearHandle = () => emit("handleClear");
const exportHandle = () => emit("handleExport");
const importHandle = () => emit("handleImport");
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
</style>
