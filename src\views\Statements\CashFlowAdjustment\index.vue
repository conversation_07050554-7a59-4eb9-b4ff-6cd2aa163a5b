<template>
    <div class="content">
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :isPeriodList="true"
                                    @getActPid="getActPid"
                                />
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    asubImgRight="14px"
                                    v-model="searchInfo.asubId"
                                    :showDisabled="true"
                                    :is-by-id="true"
                                    :defaultMaxWidth="280"
                                />
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">主表项目：</div>
                            <div class="line-item-field">
                                <el-select 
                                    v-model="searchInfo.mainItemId" 
                                    :teleported="false"
                                    :filterable="true"
                                    :filter-method="SeMCItemFilterMethod"
                                >
                                    <el-option
                                        v-for="item in showSeMCItemList"
                                        :key="item.itemId"
                                        :label="item.itemName"
                                        :value="item.itemId"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item" v-if="accountStandard === AccountStandard.CompanyStandard">
                            <div class="line-item-title">附表项目：</div>
                            <div class="line-item-field">
                                <el-select 
                                    v-model="searchInfo.subItemId" 
                                    :teleported="false"
                                    :filterable="true"
                                    :filter-method="SeSCItemFilterMethod"
                                >
                                    <el-option
                                        v-for="item in showSeSCItemList"
                                        :key="item.itemId"
                                        :label="item.itemName"
                                        :value="item.itemId"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleQuickSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <div class="search-content ml-10">
                        <el-input
                            clearable
                            v-model="searchInfo.searchText"
                            placeholder="输入科目编码/名称搜索"
                            @keydown.enter="handleQuickSearch"
                        />
                        <div class="search-icon" @click.stop.self="handleQuickSearch"></div>
                    </div>
                </div>
                <div class="main-tool-right">
                    <el-checkbox
                        v-model="searchInfo.onlyShowEmptyItemVoucher"
                        label="只显示未指定现金流量的凭证"
                        @change="handleQuickSearch"
                    ></el-checkbox>
                    <el-checkbox v-model="searchInfo.showWholeVoucher" label="显示完整凭证" @change="handleQuickSearch"></el-checkbox>
                    <!-- 当期该功能先不做 -->
                    <!-- <a class="button solid-button large-2 ml-10" @click="splitVoucher">多借多贷凭证拆分</a> -->
                    <a class="button mr-10 ml-10" v-permission="['cashflowstatement-canedit']" @click="handleUpdateByDefault">按预设更新</a>
                    <a class="button mr-10" v-permission="['cashflowstatement-canedit']" @click="changeCurrentRoute">流量预设</a>
                    <a class="button mr-10" v-permission="['cashflowstatement-canexport']" @click="handleExport">导出</a>
                    <!-- <a class="button ml-10" v-permission="['cashflowstatement-canprint']" @click="handlePrint">打印</a> -->
                    <Dropdown :btnTxt="'打印'" :downlistWidth="102" v-permission="['cashflowstatement-canprint']">
                        <li @click="handlePrint(0,getSearchParams())">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                </div>
            </div>
            <div class="main-center">
                <Table
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :emptyText="emptyText"
                    :data="tableData"
                    :columns="columns"
                    :page-is-show="true"
                    :showOverflowTooltip="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :scrollbar-show="true"
                    :row-class-name="rowClassName"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    @row-click="handleRowClick"
                    :tableName="setModule"
                >
                    <template #vnum>
                        <el-table-column 
                            label="凭证字号" 
                            min-width="100px" 
                            align="left" 
                            header-align="left"
                            prop="vnum"
                            :width="getColumnWidth(setModule, 'vnum')"
                        >
                            <template #default="{ row }: { row: ITableDataMap }">
                                <a class="link" @click="handleCheckVoucher(row.pid, row.vid)">{{ row.vgName }}-{{ row.vnum }}</a>
                            </template>
                        </el-table-column>
                    </template>
                    <template #mainEntry>
                        <el-table-column 
                            label="主表项目" 
                            min-width="240px" 
                            align="left" 
                            header-align="left"
                            prop="mainEntry"
                            :width="getColumnWidth(setModule, 'mainEntry')"
                            :resizable="accountStandard === AccountStandard.CompanyStandard"
                        >
                            <template #default="{ row }: { row: ITableDataMap }">
                                <template v-if="!row.isCashAsub && row.isOtherCashAsub && currentEditIndex === row.index">
                                    <Select
                                        ref="mainSelectRef"
                                        v-model="editInfo.mainItemId"
                                        placeholder=" "
                                        IconClearRight="26px"
                                        :filterable="true"
                                        @focus="handleInEdit"
                                        @blur="handleBlur"
                                        @change="handleMainItemChange"
                                        :filter-method="mcItemFilterMethod"
                                    >
                                        <Option
                                            v-for="item in showMCItemList"
                                            :key="item.itemId"
                                            :value="item.itemId"
                                            :label="item.itemName"
                                        ></Option>
                                    </Select>
                                </template>
                                <template v-else>
                                    <div class="default-cell">
                                        <span>{{ row.mainItemName || "" }}</span>
                                        <el-icon
                                            v-if="!row.isCashAsub && row.isOtherCashAsub && checkPermission(['cashflowstatement-canedit'])"
                                            ><ArrowDown
                                        /></el-icon>
                                    </div>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                    <template #subEntry>
                        <el-table-column 
                            label="附表项目" 
                            min-width="240px" 
                            align="left" 
                            header-align="left"
                            prop="mainEntry"
                            :width="getColumnWidth(setModule, 'subEntry')"
                            :resizable="false"
                        >
                            <template #default="{ row }: { row: ITableDataMap }">
                                <template v-if="!row.isCashAsub && row.showSubDropdown && currentEditIndex === row.index">
                                    <Select
                                        ref="subSelectRef"
                                        v-model="editInfo.subItemId"
                                        placeholder=" "
                                        IconClearRight="26px"
                                        :filterable="true"
                                        :clearable="subItemCanClearable"
                                        @focus="handleInEdit"
                                        @blur="handleBlur"
                                        :filter-method="scItemFilterMethod"
                                    >
                                        <Option
                                            v-for="item in showSCItemList"
                                            :key="item.itemId"
                                            :value="item.itemId"
                                            :label="item.itemName"
                                        ></Option>
                                    </Select>
                                </template>
                                <template v-else>
                                    <div class="default-cell">
                                        <span>{{ row.subItemName || "" }}</span>
                                        <el-icon
                                            v-if="!row.isCashAsub && row.showSubDropdown && checkPermission(['cashflowstatement-canedit'])"
                                            ><ArrowDown
                                        /></el-icon>
                                    </div>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <el-dialog
        center
        width="440px"
        title="按流量预设更新凭证项目"
        class="custom-confirm dialogDrag"
        v-model="updateTipDisplay"
        @closed="dateInfo = ''"
    >
        <div class="dialog-content" v-dialogDrag>
            <div class="dialog-content-body">
                <div class="dialog-content-message">
                    <div style="text-align: left">
                        按流量预设更新后，{{ dateInfo }}中所有未结账期间相关凭证的主表和附表项目将更新，并重算标准现金流量表数据哦~
                    </div>
                    <div class="mt-10" style="text-align: left">是否继续更新？</div>
                </div>
            </div>
            <div class="buttons" style="border-top: 1px solid var(--border-color)">
                <a class="button solid-button" @click="confirmUpdate">继续更新</a>
                <a class="button ml-20" @click="updateTipDisplay = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="现金流量调整打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getSearchParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    // 现金流量调整
    name: "CashFlowAdjustment",
};
</script>
<script setup lang="ts">
import { ref, reactive, nextTick, provide, computed, watch, watchEffect } from "vue";
import { usePagination } from "@/hooks/usePagination";
import usePrint from "@/hooks/usePrint";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { componentFinishKey } from "@/symbols";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { formatMoney } from "@/util/format";
import { checkPermission } from "@/util/permission";
import { PeriodStatus } from "@/api/period";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IVoucherSplitItem, IVoucherSplitBase } from "./types";
import type { IAsubItem, IAsubItemString } from "@/views/Statements/CashflowAssumption/types";
import type { IPeriod } from "@/api/period";

import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import PeriodPicker from "@/components/Picker/PeriodPicker/index.vue";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import Dropdown from "@/components/Dropdown/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";

const setModule = "CashFlowAdjust";
interface ITableDataMap extends IVoucherSplitBase {
    mainItemId: string;
    subItemId: string;
    index: number;
    showSubDropdown: boolean;
}
interface ISearchBack {
    data: Array<IVoucherSplitItem>;
    count: number;
}

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const mainSelectRef = ref<InstanceType<typeof Select>>();
const subSelectRef = ref<InstanceType<typeof Select>>();

const periodStore = useAccountPeriodStore();
const accountStandard = useAccountSetStore().accountSet!.accountingStandard as unknown as AccountStandard;
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const currentPeriodInfo = ref("");
const searchInfo = reactive({
    startPId: Number(periodStore.getPeriodRange().start),
    endPId: Number(periodStore.getPeriodRange().end),
    asubId: "",
    mainItemId: "",
    subItemId: "",
    searchText: "",
    onlyShowEmptyItemVoucher: false,
    showWholeVoucher: true,
    startMonth: "",
    endMonth: "",
});

const { periodData } = usePeriodData(searchInfo, searchInfo.startPId, searchInfo.endPId);  
const getActPid = (start: number, end: number) => {
    searchInfo.startPId = start;
    searchInfo.endPId = end;
}

const tableData = ref<Array<ITableDataMap>>([]);
const loading = ref(false);
const emptyText = ref("暂无数据");
const columns = computed<Array<IColumnProps>>(() => {
    const column: Array<IColumnProps> = [
        { label: "日期", prop: "vdate", minWidth: 100, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "vdate") },
        { slot: "vnum" },
        { label: "摘要", prop: "description", minWidth: 150, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "description") },
        {
            label: "科目名称",
            prop: "asubName",
            minWidth: 200,
            align: "left",
            headerAlign: "left",
            formatter: function (row: ITableDataMap) {
                return row.asubCode + " " + row.asubName;
            }, 
            width: getColumnWidth(setModule, "asubName")
        },
        {
            label: "借方金额",
            prop: "debit",
            minWidth: 100,
            align: "right",
            headerAlign: "right",
            formatter: function (row: ITableDataMap, column: any, value: number) {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, "debit")
        },
        {
            label: "贷方金额",
            prop: "credit",
            minWidth: 100,
            align: "right",
            headerAlign: "right",
            formatter: function (row: ITableDataMap, column: any, value: number) {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, "credit")
        },
        { slot: "mainEntry" },
    ];
    if (accountStandard === AccountStandard.CompanyStandard) {
        column.push({ slot: "subEntry" });
    }
    return column;
});

async function handleLoadTableData() {
    periodStore.changePeriods(String(searchInfo.startPId), String(searchInfo.endPId));
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPId, searchInfo.endPId);
    const queryParams = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    const params = getSearchParams();
    loading.value = true;
    await request({
        url: "/api/StandardCashFlowStatement/GetSplitedVoucherPagingList?" + getUrlSearchParams(queryParams),
        method: "post",
        data: params,
    })
        .then((res: IResponseModel<ISearchBack>) => {
            loading.value = false;
            if (res.state !== 1000) {
                tableData.value = [];
                emptyText.value = "暂无数据";
                ElNotify({ type: "warning", message: res.msg || "获取数据失败" });
                return;
            }
            tableData.value = res.data.data.map((item, index) => {
                const mainItemId = !item.mainItemId ? "" : item.mainItemId.toString();
                const subItemId = !item.subItemId ? "" : item.subItemId.toString();
                return {
                    ...item,
                    mainItemId,
                    subItemId,
                    index,
                    showSubDropdown: checkShowSubAllowSelect(item.isOtherCashAsub, item.isProfitandloss, mainItemId),
                };
            });
            paginationData.total = res.data.count;
        })
        .catch(() => {
            loading.value = false;
            tableData.value = [];
            emptyText.value = "暂无数据";
        });
}
function getSearchParams() {
    return {
        searchText: searchInfo.searchText || "",
        startPId: searchInfo.startPId,
        endPId: searchInfo.endPId,
        asubId: searchInfo.asubId ? Number(searchInfo.asubId) : 0,
        mainItemId: searchInfo.mainItemId ? Number(searchInfo.mainItemId) : 0,
        subItemId: searchInfo.subItemId ? Number(searchInfo.subItemId) : 0,
        onlyShowEmptyItemVoucher: searchInfo.onlyShowEmptyItemVoucher,
        showWholeVoucher: searchInfo.showWholeVoucher,
    };
}
function checkCanSearch() {
    if (searchInfo.startPId > searchInfo.endPId) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哟！" });
        return false;
    }
    return true;
}
function handleSearch() {
    if (!checkCanSearch()) return;
    handleClose();
    handleResetEditInfo();
    handleLoadTableData();
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "cashFlowAdjust",
    "/api/StandardCashFlowStatement/PrintSplitedVoucherList",
    {},
    false,
    false,
);
function handleExport() {
    const params = getSearchParams();
    globalExport("/api/StandardCashFlowStatement/ExportSplitedVoucherList?" + getUrlSearchParams(params));
}
function changeCurrentRoute() {
    globalWindowOpenPage("/Statements/CashflowAssumption", "现金流量预设");
}
const updateTipDisplay = ref(false);
const dateInfo = ref("");
function handleUpdateByDefault() {
    const date = currentPeriodInfo.value.replace("—", "-");
    const startPeriod = periodStore.periodList.find((item) => item.pid === searchInfo.startPId) as IPeriod;
    const endPeriod = periodStore.periodList.find((item) => item.pid === searchInfo.endPId) as IPeriod;
    const stareHasCheckout = startPeriod.status === PeriodStatus.CheckOut;
    const endHasCheckout = endPeriod.status === PeriodStatus.CheckOut;
    if (stareHasCheckout && endHasCheckout) {
        ElConfirm(`${date}已结账，不能进行更新操作哦~`, true);
        return;
    }
    dateInfo.value = date;
    updateTipDisplay.value = true;
}
function confirmUpdate() {
    updateTipDisplay.value = false;
    const params = { startPId: searchInfo.startPId, endPId: searchInfo.endPId };
    request({
        url: "/api/StandardCashFlowStatement/ResetVoucherItems?" + getUrlSearchParams(params),
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "按预设更新失败" });
                return;
            }
            ElNotify({ type: "success", message: "按预设更新成功" });
            handleSearch();
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "按预设更新失败" });
        });
}

// function handleShowWholeVoucherChange(type: any) {
//     const showWholeVoucher = !!type;
//     window.localStorage.setItem("showWholeVoucher", String(showWholeVoucher));
//     handleQuickSearch();
// }
function handleQuickSearch() {
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
}
function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.asubId = "";
    searchInfo.mainItemId = "";
    searchInfo.subItemId = "";
}
const asubItemList = ref<Array<IAsubItemString>>([]);
const mainCashFlowItemList = ref<Array<IAsubItemString>>([]);
const subCashFlowItemList = ref<Array<IAsubItemString>>([]);
async function handleGetStandardCashFlowStatementItemList() {
    await request({
        url: "/api/StandardCashFlowStatement/GetStandardCashFlowStatementItemList",
        method: "post",
    })
        .then((res: IResponseModel<Array<IAsubItem>>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "获取数据失败" });
                return;
            }
            const formatData = res.data.map((item, index) => ({ ...item, itemId: item.itemId.toString(), isBusinessAsub: index < 7 }));
            asubItemList.value = formatData;
            mainCashFlowItemList.value = formatData.filter((item) => item.itemType === 1);
            subCashFlowItemList.value = formatData.filter((item) => item.itemType === 2);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "获取数据失败" });
        });
}
async function handleInit() {
    currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.startPId, searchInfo.endPId);
    await handleGetStandardCashFlowStatementItemList();
    // 业财暂时无法调试 强刷一下期间
    await periodStore.getPeriods();
    if (periodStore.getPeriodRange().start && periodStore.getPeriodRange().end) {
        searchInfo.startPId = Number(periodStore.getPeriodRange().start) || 0;
        searchInfo.endPId = Number(periodStore.getPeriodRange().end) || 0;
        nextTick().then(() => {
            handleSearch();
        });
        return;
    }
    handleSearch();
}

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        handleInit();
    }
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag], () => {
    handleSearch();
});
watch(
    () => paginationData.pageSize,
    (pageSize) => {
        if (pageSize * paginationData.currentPage > paginationData.total) {
            paginationData.currentPage = Math.ceil(paginationData.total / pageSize);
        } else {
            handleSearch();
        }
    }
);

const currentEditIndex = ref(-1);
let isEditing = false;
let allowBlur = true;
const editInfo = reactive({
    subItemId: "",
    mainItemId: "",
});
function handleRowClick(row: ITableDataMap, column: any) {
    if (!checkPermission(["cashflowstatement-canedit"])) return;
    if (row.index !== currentEditIndex.value && currentEditIndex.value !== -1) {
        allowBlur = false;
        handleSubmit();
        return;
    }
    if (row.isCashAsub) return;
    if (!row.isOtherCashAsub && accountStandard !== AccountStandard.CompanyStandard) return;
    if (row.index === currentEditIndex.value || (!row.isOtherCashAsub && !row.showSubDropdown)) return;
    const { label } = column;
    if (label !== "主表项目" && label !== "附表项目") return;
    const periodItem = periodStore.periodList.find((item) => item.pid === row.pid) as IPeriod;
    if (periodItem.status === PeriodStatus.CheckOut) {
        ElNotify({ type: "warning", message: "亲，该凭证期间已结账，不能修改主附表项目哦~" });
        return;
    }
    editInfo.mainItemId = row.mainItemId;
    editInfo.subItemId = row.subItemId;
    currentEditIndex.value = row.index;
    changeSubSelectState(row.mainItemId);
    nextTick().then(() => {
        if (label === "主表项目" && row.isOtherCashAsub) {
            mainSelectRef.value?.focus();
        } else if (label === "附表项目" && checkShowSubAllowSelect(row.isOtherCashAsub, row.isProfitandloss, row.mainItemId)) {
            subSelectRef.value?.focus();
        }
    });
}
function handleResetEditInfo() {
    editInfo.subItemId = "";
    editInfo.mainItemId = "";
    currentEditIndex.value = -1;
    isEditing = false;
    allowBlur = true;
    subItemCanClearable.value = true;
}
function handleInEdit() {
    const timer = setTimeout(() => {
        isEditing = true;
        clearTimeout(timer);
    });
}
const subItemCanClearable = ref(true);
function handleMainItemChange(mainItemId: string) {
    const cashItem = mainCashFlowItemList.value.find((item) => item.itemId === mainItemId);
    if (!cashItem) {
        editInfo.mainItemId = "";
        return;
    }
    changeSubSelectState(mainItemId);
}
function changeSubSelectState(mainItemId: string) {
    // 所有凭证均默认为一借一贷凭证
    const cashItem = mainCashFlowItemList.value.find((item) => item.itemId === mainItemId);
    if (accountStandard !== AccountStandard.CompanyStandard) return;
    const row = tableData.value[currentEditIndex.value];
    row.showSubDropdown = checkShowSubAllowSelect(row.isOtherCashAsub, row.isProfitandloss, mainItemId);
    if (!row.showSubDropdown) {
        editInfo.subItemId = "";
        row.subItemId = "";
        row.subItemName = "";
        subItemCanClearable.value = true;
        return;
    }
    // 恢复默认值
    subItemCanClearable.value = true;
    // 对方现金科目
    if (row.isOtherCashAsub) {
        // 对方科目为现金时的附表项目只有两种情况  ===>  必须指定  /  不能指定
        // 不能指定和必须指定应该均不允许展示下拉清空按钮
        subItemCanClearable.value = false;
        // 不能指定  ===>  己方损益科目 + 主表现金流量  /  己方非损益科目 + 主表非现金流量  ===> 需要清空附表项目
        const needClearSubItem = row.isProfitandloss ? !!cashItem?.isBusinessAsub : !cashItem?.isBusinessAsub;
        if (needClearSubItem) {
            editInfo.subItemId = "";
            row.subItemId = "";
            row.subItemName = "";
        }
    } else if (row.isProfitandloss) {
        // 对方非现金科目 + 己方损益科目  ===>  必须指定附表项目
        subItemCanClearable.value = false;
    }
}
function checkIsBusinessAsub(mainItemId: string) {
    const cashItem = mainCashFlowItemList.value.find((item) => item.itemId === mainItemId);
    return cashItem && cashItem.isBusinessAsub;
}
function checkShowSubAllowSelect(isOtherCashAsub: boolean, isProfitandloss: boolean, mainItemId: string) {
    if (!isOtherCashAsub) return true;
    if (isProfitandloss && !checkIsBusinessAsub(mainItemId)) return true;
    if (!isProfitandloss && checkIsBusinessAsub(mainItemId)) return true;
    return false;
}
function handleBlur() {
    isEditing = false;
    const timer = setTimeout(() => {
        clearTimeout(timer);
        if (isEditing || !allowBlur) return;
        handleSubmit();
    }, 100);
}
let isRequesting = false;
function handleSubmit() {
    if (isRequesting) return;
    if (!handleCheckSubmit()) return;
    const row = tableData.value[currentEditIndex.value];
    const mainItemId = !editInfo.mainItemId ? 0 : Number(editInfo.mainItemId);
    const subItemId = !editInfo.subItemId ? 0 : Number(editInfo.subItemId);
    const { pid, vid, veId, splitNum } = row;
    const params = { pid, vid, veId, splitNum, mainItemId, subItemId };
    isRequesting = true;
    request({ url: "/api/StandardCashFlowStatement/UpdateSplitVoucherItemId?" + getUrlSearchParams(params), method: "post" })
        .then((res: IResponseModel<boolean>) => {
            isRequesting = false;
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "操作失败" });
                return;
            }
            ElNotify({ type: "success", message: "操作成功" });
            handleSearch();
        })
        .catch(() => {
            isRequesting = false;
            ElNotify({ type: "warning", message: "操作失败" });
        });
}
function handleCheckSubmit() {
    const row = tableData.value[currentEditIndex.value];
    const cashItem = mainCashFlowItemList.value.find((item) => item.itemId === editInfo.mainItemId);
    const isCompanyStandard = accountStandard === AccountStandard.CompanyStandard;
    if (row.isOtherCashAsub) {
        if (!cashItem && row.mainItemId) {
            ElNotify({ type: "warning", message: "请选择主表项目" });
            return false;
        }
        if (!isCompanyStandard || editInfo.subItemId) return true;
        if (row.isProfitandloss && !cashItem?.isBusinessAsub) {
            ElNotify({ type: "warning", message: "请选择附表项目" });
            return false;
        }
        if (!row.isProfitandloss && cashItem?.isBusinessAsub) {
            ElNotify({ type: "warning", message: "请选择附表项目" });
            return false;
        }
    } else {
        if (!isCompanyStandard || editInfo.subItemId) return true;
        if (row.isProfitandloss) {
            ElNotify({ type: "warning", message: "请选择附表项目" });
            return false;
        }
    }
    return true;
}

function handleCheckVoucher(pid: number, vid: number) {
    if (!checkPermission(["voucher-canview"])) {
        ElNotify({ type: "warning", message: "亲，您没有查看凭证权限哦~" });
        return;
    }
    globalWindowOpenPage(`/Voucher/VoucherPage?pid=${pid}&vid=${vid}&from=cashFlow`, "查看凭证");
}

function rowClassName({ row }: { row: ITableDataMap }) {
    return row.isCashAsub ? "cash-asub-row" : "";
}

const SeMCItemListAll = ref<Array<any>>([]);
const showSeMCItemList = ref<Array<any>>([]);
const SeSCItemListAll = ref<Array<any>>([]);
const showSeSCItemList = ref<Array<any>>([]);
watchEffect(() => {
    SeMCItemListAll.value = JSON.parse(JSON.stringify(mainCashFlowItemList.value));
    SeMCItemListAll.value.unshift({ itemId: "", itemName: "全部" });
    showSeMCItemList.value = JSON.parse(JSON.stringify(SeMCItemListAll.value));

    SeSCItemListAll.value = JSON.parse(JSON.stringify(subCashFlowItemList.value));
    SeSCItemListAll.value.unshift({ itemId: "", itemName: "全部" });
    showSeSCItemList.value = JSON.parse(JSON.stringify(SeSCItemListAll.value));
});
function SeMCItemFilterMethod(value: string) {
    showSeMCItemList.value = commonFilterMethod(value, SeMCItemListAll.value, 'itemName');
}
function SeSCItemFilterMethod(value: string) {
    showSeSCItemList.value = commonFilterMethod(value, SeSCItemListAll.value, 'itemName');
}

const showMCItemList = ref<Array<any>>([]);
const showSCItemList = ref<Array<any>>([]);
watchEffect(() => {
    showMCItemList.value = JSON.parse(JSON.stringify(mainCashFlowItemList.value));
    showSCItemList.value = JSON.parse(JSON.stringify(subCashFlowItemList.value));
});
function mcItemFilterMethod(value: string) {
    showMCItemList.value = commonFilterMethod(value, mainCashFlowItemList.value, 'itemName');
}
function scItemFilterMethod(value: string) {
    showSCItemList.value = commonFilterMethod(value, subCashFlowItemList.value, 'itemName');
}
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.main-tool-right {
    a.button.solid-button.large-2 {
        padding: 0 10px;
    }
}
.line-item-field {
    & > :deep(.el-select) {
        width: 298px;
    }
}
.main-center {
    flex: 1 !important;
    :deep(.custom-table) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table {
            input[type="text"] {
                border: none;
            }
        }
        .el-table__cell {
            .el-select {
                width: 100%;
            }
        }
        .default-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            & > span {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .el-table__row.cash-asub-row {
            .el-table__cell {
                .cell {
                    color: #aaa;
                }
            }
        }
    }
}
.search-content {
    position: relative;
    :deep(.el-input) {
        .el-input__suffix {
            position: absolute;
            right: 26px;
        }
    }
}
.search-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    height: 16px;
    width: 15px;
    background: url("@/assets/Icons/search-icon.png") no-repeat;
    background-size: 15px 16px;
    cursor: pointer;
}
</style>
