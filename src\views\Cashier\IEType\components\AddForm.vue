<template>
    <div class="title">{{ props.tabName === 10050 ? "收入类别" : "支出类别" }}</div>
    <div class="form-content">
        <div class="form-component">
            <el-form :model="searchInfo" label-width="150px" ref="formRef" :rules="rules" :show-message="false">
                <el-form-item label="编码：" prop="ie_no">
                    <Tooltip :content="searchInfo.ie_no" :is-input="true" placement="right">
                        <el-input
                            v-model="searchInfo.ie_no"
                            placeholder=" "
                            maxlength="4"
                            @input="handleCodeInput(searchInfo.ie_no, '编码')"
                        />
                    </Tooltip>
                </el-form-item>
                <el-form-item label="名称：" prop="ie_name">
                    <Tooltip :content="searchInfo.ie_name" :is-input="true" placement="right">
                        <el-input v-model="searchInfo.ie_name" @input="handleInput" />
                    </Tooltip>
                </el-form-item>
                <el-form-item label="上级类别：" prop="ie_parentid">
                    <Tooltip :content="parentName" :is-input="true" placement="right">
                        <el-input v-model="parentName" disabled />
                    </Tooltip>
                </el-form-item>
                <el-form-item label="关联现金流：" v-if="cashFlowDisplay" class="cashflow">
                    <Select
                        v-model="searchInfo.cashFlow"
                        placeholder="请选择"
                        :fit-input-width="true"
                        :clearable="true"
                        IconClearRight="26px"
                        :filterable="true"
                        :filter-method="cashFlowFilterMethod"
                    >
                        <Option 
                            v-for="item in showCashFlowSelectList" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value" 
                        />
                    </Select>
                </el-form-item>
                <el-form-item label="智能匹配摘要关键字：" prop="ie_keyword">
                    <el-input
                        class="filed-textarea"
                        v-model="searchInfo.ie_keyword"
                        placeholder=" "
                        type="textarea"
                        :rows="3"
                        style="margin-right: 60px"
                        resize="none"
                    />
                    <div class="keyword-info">
                        <div class="keyword-info-img">
                            <img src="@/assets/Cashier/ietype_edit_note.png" />
                        </div>
                        <div class="keyword-info-note">
                            <div>1.当日记账的摘要中有对应关键字时，可以自动匹配带出收支类别</div>
                            <div>2.不同关键字之间用逗号隔开</div>
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <div class="buttons">
            <a class="solid-button mr-10" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">取消</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, computed, toRef, watch, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { request, type IResponseModel } from "@/util/service";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { useRoute } from "vue-router";

import type { FormInstance, FormRules } from "element-plus";

import Tooltip from "@/components/Tooltip/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { commonFilterMethod } from "@/components/Select/utils";

const props = defineProps<{
    tabName: 10050 | 10060;
    cashFlowDisplay: boolean;
    getEditType: () => "" | "New" | "Edit";
}>();

const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingList");
const accountingStandard = useAccountSetStore().accountSet!.accountingStandard as unknown as AccountStandard;
const initialCashAAEID =
    accountingStandard === AccountStandard.CompanyStandard || accountingStandard === AccountStandard.FolkComapnyStandard ? 31 : 24;
const cashFlowSelectList = computed(() => {
    if (props.cashFlowDisplay) {
        return assistingAccountingList.value
            .filter((item) => item.aatype === 10007 && item.aaeid !== initialCashAAEID)
            .map((item) => ({ label: item.aaname, value: item.aaeid.toString() }));
    } else {
        return [];
    }
});

const emit = defineEmits(["cancel", "save-success"]);

const rules = reactive<FormRules>({
    ie_no: [{ required: true }],
    ie_name: [{ required: true }],
});

const formRef = ref<FormInstance>();
const parentName = ref("全部类别");
const searchInfo = reactive({
    ie_no: "",
    ie_name: "",
    ie_id: "",
    ie_keyword: "",
    ie_parentid: 0,
    cashFlow: "",
    state: 0,
});

let isSaving = false;
function checkCanSave() {
    const reg = /^[0-9a-zA-Z]{1,4}$/;
    if ((searchInfo.ie_no + "").trim() === "") {
        ElNotify({ type: "warning", message: "编码不能为空" });
        return false;
    }
    if (!reg.test((searchInfo.ie_no + "").trim())) {
        ElNotify({ type: "warning", message: "亲，编码只能输入四位及以下的数字字母组合" });
        return false;
    }
    if (searchInfo.ie_name.trim() === "") {
        ElNotify({ type: "warning", message: "名称不能为空" });
        return false;
    }
    return true;
}
function handleSave() {
    if (isSaving) return;
    isSaving = true;
    if (!checkCanSave()) {
        isSaving = false;
        return;
    }
    searchInfo.ie_name = searchInfo.ie_name.slice(0, 64);
    const params = {
        ie_type: props.tabName,
        ie_no: searchInfo.ie_no.trim(),
        ie_name: searchInfo.ie_name.trim(),
        ie_id: searchInfo.ie_id,
        ie_keyword: searchInfo.ie_keyword.trim(),
        parentID: searchInfo.ie_parentid,
        cashAAEID: ~~searchInfo.cashFlow,
        state: searchInfo.state,
    };
    request({
        url: "/api/IEType",
        method: props.getEditType() === "New" ? "post" : "put",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: params,
    })
        .then((res: IResponseModel<string>) => {
            if (res.data == "Success") {
                ElNotify({ type: "success", message: "保存成功！" });
                emit("save-success");
            } else if (res.data == "NO") {
                ElNotify({ type: "warning", message: "亲，编码重复了" });
            } else if (res.data == "Name") {
                ElNotify({ type: "warning", message: "亲，类别名称重复了" });
            } else {
                ElNotify({ type: "warning", message: "亲，保存失败了，请联系侧边栏客服" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "亲，保存失败了，请联系侧边栏客服" });
        })
        .finally(() => {
            isSaving = false;
        });
}
function handleCancel() {
    emit("cancel");
}

function handleInput(val: string) {
    if (val.length > 64) {
        nextTick().then(() => {
            searchInfo.ie_name = val.slice(0, 64);
            ElNotify({ type: "warning", message: "亲，名称只能输入64个字符" });
        });
    }
}
function handleCodeInput(description: string, name: string) {
    if (description.length === 4) {
        ElNotify({ type: "warning", message: "亲，" + name + "不能超过4个字符,请修改后再试哦~" });
    }
}
function editSearchInfo(data: any, name?: string) {
    Object.assign(searchInfo, data);
    parentName.value = name ? name : "全部类别";
    nextTick(() => {
        init = true;
    });
}
const resetForm = () => {
    init = false;
    isEditting.value = false;
    formRef.value?.resetFields();
    searchInfo.ie_no = "";
    searchInfo.ie_name = "";
    searchInfo.ie_id = "";
    searchInfo.ie_keyword = "";
    searchInfo.ie_parentid = 0;
    searchInfo.cashFlow = "";
    searchInfo.state = 0;
    parentName.value = "全部类别";
};
defineExpose({ editSearchInfo, resetForm });

let init = false;
const route = useRoute();
const isEditting = ref(false);
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
watch(
    searchInfo,
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});

const showCashFlowSelectList = ref<Array<any>>([]);
watchEffect(() => {
    showCashFlowSelectList.value = JSON.parse(JSON.stringify(cashFlowSelectList.value));
});
function cashFlowFilterMethod(value: string) {
    showCashFlowSelectList.value = commonFilterMethod(value, cashFlowSelectList.value, 'label');
}
</script>

<style lang="less" scoped>
@import url("@/style/Functions.less");
.form-content {
    margin: 0 auto;
    padding: 40px 0;
    width: 1000px;
    background-color: var(--white);
    .buttons {
        margin-top: 24px;
    }
    .form-component {
        width: 532px;
        margin: 0 auto;
    }
    .detail-el-form(232px, 32px);

    .keyword-info {
        display: flex;
        margin-top: 6px;
        margin-bottom: 16px;
        line-height: var(--line-height);

        .keyword-info-img {
            margin-top: 3px;
            img {
                height: 14px;
            }
        }

        .keyword-info-note {
            font-size: 12px;
            color: var(--text-color);
            text-align: start;
            margin-left: 4px;
            color: #666;
        }
    }

    :deep(.el-input__inner) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    :deep(.el-textarea) {
        &.filed-textarea {
            .detail-el-textarea-scroll-thumb();
        }
    }

    :deep(.el-form-item) {
        &.cashflow {
            .el-input__suffix {
                .el-icon {
                    margin-left: 14px !important;
                }
            }
        }
     }
}
</style>
