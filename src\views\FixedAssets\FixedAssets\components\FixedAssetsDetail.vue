<template>
  <div class="content">
    <div class="title">资产明细</div>
    <div class="tab-title">资产明细</div>
    <div class="divEdit">
      <div class="block-title">
        基本信息
        <span class="float-r" id="txtceatetime">录入日期：{{ created_date?.substring(0, 10) }}</span>
      </div>
      <div class="block-main">
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">资产类别：</span>
            <Tooltip :content="fa_type_name" :max-width="150" :font-size="12" :line-clamp="2">{{ fa_type_name }}</Tooltip>
          </el-col>
          <el-col :span="8">
            <span class="label">资产编号：</span>
            <Tooltip :content="fa_num" :max-width="150" :font-size="12" :line-clamp="2">{{ fa_num }}</Tooltip>
          </el-col>
          <el-col :span="8">
            <span class="label">资产名称：</span>
            <Tooltip :content="fa_name" :max-width="150" :font-size="12" :line-clamp="2">{{ fa_name }}</Tooltip>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">使用部门：</span>
            <Tooltip :content="department_name" :max-width="150" :font-size="12" :line-clamp="2">
              {{ faAmortizations.map((item: any) => `${item.departmentName}`).join("，") }}
            </Tooltip>
          </el-col>
          <el-col :span="8">
            <span class="label">规格型号：</span>
            <Tooltip :content="fa_model" :max-width="150" :font-size="12" :line-clamp="2">{{ fa_model }}</Tooltip>
          </el-col>
          <el-col :span="8">
            <span class="label">录入期间：</span>
            <span class="text">{{ created_period }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">开始使用日期：</span>
            <span class="text">{{ started_date.substring(0, 10) }}</span>
          </el-col>
          <el-col :span="8" v-if="fa_property !== 2">
            <span class="label">增加方式：</span>
            <span class="text">{{ createdWayList[created_way] || "" }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">供应商：</span>
            <Tooltip :content="vendor" :max-width="150" :font-size="12" :line-clamp="2">{{ vendor }}</Tooltip>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">减少方式：</span>
            <span class="text">{{ decreased_way === "0" ? "" : decreased_way }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">减少期间：</span>
            <span class="text">{{ decreased_period }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">使用人：</span>
            <Tooltip :content="use_people" :max-width="150" :font-size="12" :line-clamp="2">{{ use_people }}</Tooltip>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">存放位置：</span>
            <Tooltip :content="location" :max-width="150" :font-size="12" :line-clamp="2">{{ location }}</Tooltip>
          </el-col>
        </el-row>
      </div>
      <div class="line"></div>
      <div class="block-title">折旧/摊销方式</div>
      <div class="block-main" :class="impairmentProvisionBlock ? 'lableImpairment' : ''">
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">{{ fa_property === 0 ? "折旧方法：" : "摊销方法：" }}</span>
            <span class="text">{{ depreciationTypeList[depreciation_type] || "" }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">{{ fa_property === 0 ? "录入当期是否折旧：" : "录入当期是否摊销：" }}</span>
            <span class="text">{{ depreciation_now ? "是" : "否" }}</span>
          </el-col>
          <el-col :span="8"></el-col>
        </el-row>
        <template v-if="fa_property === 2 || (fa_property === 1 && [3, 4, 5].includes(accountingStandard))">
          <el-row :gutter="20">
            <el-col :span="8">
              <span class="label">资产科目：</span>
              <Tooltip :content="formatName(fa_asub_name, fa_asub_aae)" :max-width="150" :font-size="12" :line-clamp="2">
                {{ formatName(fa_asub_name, fa_asub_aae) }}
              </Tooltip>
            </el-col>
            <el-col :span="8" v-if="fa_property === 1">
              <span class="label">资产处置科目：</span>
              <Tooltip :content="formatName(disposal_asub_name, disposal_asub_aae)" :max-width="300" :font-size="12" :line-clamp="2">
                {{ formatName(disposal_asub_name, disposal_asub_aae) }}
              </Tooltip>
            </el-col>
            <el-col :span="8">
              <span class="label">{{ fa_property === 0 ? "折旧费用科目：" : "摊销费用科目：" }}</span>
              <Tooltip
                :content="faAmortizations
                                    .map((item: any) => `${formatName(item.subjectName,item.asub_aae_name)}（${item.departmentName} ${Math.floor(item.ratio * 100)}%）`)
                                    .join(' ')
                                "
                :max-width="150"
                :font-size="12"
                :line-clamp="2">
                {{
                  faAmortizations
                    .map(
                      (item: any) =>
                        `${formatName(item.subjectName, item.asub_aae_name)}（${item.departmentName} ${Math.floor(item.ratio * 100)}%）`
                    )
                    .join(" ")
                }}
              </Tooltip>
            </el-col>
          </el-row>
        </template>
        <template v-else>
          <el-row :gutter="20">
            <el-col :span="8">
              <span class="label">资产科目：</span>
              <Tooltip :content="formatName(fa_asub_name, fa_asub_aae)" :max-width="150" :font-size="12" :line-clamp="2">
                {{ formatName(fa_asub_name, fa_asub_aae) }}
              </Tooltip>
            </el-col>
            <el-col :span="8">
              <span class="label">{{ fa_property === 0 ? "累计折旧科目：" : "累计摊销科目:" }}</span>
              <Tooltip
                :content="formatName(depreciation_asub_name, depreciation_asub_aae)"
                :max-width="150"
                :font-size="12"
                :line-clamp="2">
                {{ formatName(depreciation_asub_name, depreciation_asub_aae) }}
              </Tooltip>
            </el-col>
            <el-col :span="8">
              <span class="label">资产处置科目：</span>
              <Tooltip :content="formatName(disposal_asub_name, disposal_asub_aae)" :max-width="300" :font-size="12" :line-clamp="2">
                {{ formatName(disposal_asub_name, disposal_asub_aae) }}
              </Tooltip>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8" v-if="impairmentProvisionBlock">
              <span class="label">资产减值准备科目：</span>
              <Tooltip
                :content="formatName(impairment_provision_asub_name, impairment_provision_asub_aae)"
                :max-width="150"
                :font-size="12"
                :line-clamp="2">
                {{ formatName(impairment_provision_asub_name, impairment_provision_asub_aae) }}
              </Tooltip>
            </el-col>
            <el-col :span="8">
              <span class="label">{{ fa_property === 0 ? "折旧费用科目：" : "摊销费用科目：" }}</span>
              <Tooltip
                :content="faAmortizations
                                .map((item: any) => `${formatName(item.subjectName,item.asub_aae_name)}（${item.departmentName} ${Math.floor(item.ratio * 100)}%）`)
                                .join(' ')"
                :max-width="150"
                :font-size="12"
                :line-clamp="2">
                {{
                  faAmortizations
                    .map(
                      (item: any) =>
                        `${formatName(item.subjectName, item.asub_aae_name)}（${item.departmentName} ${Math.floor(item.ratio * 100)}%）`
                    )
                    .join(" ")
                }}
              </Tooltip>
            </el-col>
            <el-col :span="8"></el-col>
          </el-row>
        </template>
      </div>
      <div class="line"></div>
      <div class="block-title">资产数据</div>
      <div class="block-main">
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">资产原值：</span>
            <span class="text">{{ value.toFixed(2) }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">残值率：</span>
            <span class="text">{{ netsalvage_rate.toFixed(2) }}%</span>
          </el-col>
          <el-col :span="8">
            <span class="label">预计残值：</span>
            <span class="text">{{ ((value * netsalvage_rate) / 100).toFixed(2) }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <span class="label">预计使用月份：</span>
            <span class="text">{{ usefullife }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">{{ fa_property === 0 ? "已折旧月份：" : "已摊销月份：" }}</span>
            <span class="text">{{ provisionmonth }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">剩余使用月份：</span>
            <span class="text">{{ usefullife - provisionmonth }}</span>
          </el-col>
        </el-row>
        <template v-if="fa_property === 0 || (fa_property === 1 && [1, 2].includes(accountingStandard))">
          <el-row :gutter="20">
            <el-col :span="8">
              <span class="label">{{ fa_property === 0 ? "累计折旧：" : "累计摊销：" }}</span>
              <span class="text">{{ totaldeprecition.toFixed(2) }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">{{ fa_property === 0 ? "本年累计折旧：" : "本年累计摊销：" }}</span>
              <span class="text">{{ yeardeprecition.toFixed(2) }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">{{ fa_property === 0 ? "以前年度累计折旧：" : "以前年度累计摊销：" }}</span>
              <span class="text">{{ preyeardeprecition.toFixed(2) }}</span>
            </el-col>
          </el-row>
        </template>
        <el-row :gutter="20">
          <el-col :span="8" v-if="impairmentProvisionBlock">
            <span class="label">减值准备：</span>
            <span class="text">{{ impairment.toFixed(2) }}</span>
          </el-col>
          <el-col :span="8">
            <span class="label">{{ fa_property === 0 ? "月折旧额：" : "月摊销额：" }}</span>
            <span class="text">
              {{
                monthdeprecition === 0
                  ? calMonthDepAmount(value, netsalvage_rate, totaldeprecition, impairment, usefullife, provisionmonth)
                  : monthdeprecition.toFixed(2)
              }}
            </span>
          </el-col>
          <el-col :span="8" v-if="!(fa_property === 0 || (fa_property === 1 && [1, 2].includes(accountingStandard)))">
            <span class="label">累计摊销：</span>
            <span class="text">{{ totaldeprecition.toFixed(2) }}</span>
          </el-col>
          <el-col :span="8" v-if="noteShow() < 3">
            <span class="label">净值：</span>
            <span class="text">{{ zcjz.toFixed(2) }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" v-if="noteShow() >= 3">
            <span class="label">净值：</span>
            <span class="text">{{ zcjz.toFixed(2) }}</span>
          </el-col>
        </el-row>
      </div>
      <div class="line"></div>
      <div class="block-title">附件</div>
      <div class="block-main">
        <el-row :gutter="20">
          <el-col :span="4">
            <a class="link" style="margin-left: -35px" @click="handleGetAttachFileList">
              {{ attachsCount ? `附件(${~~attachsCount})` : "附件" }}
            </a>
          </el-col>
        </el-row>
      </div>
      <div class="buttons">
        <a class="button" @click="divCancel">返回</a>
      </div>
    </div>
    <div class="fixedImg"></div>
    <UploadFileDialog :readonly="true" ref="uploadFileDialogRef" />
  </div>
</template>
<script setup lang="ts">
import { toRefs, computed, ref } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import Tooltip from "@/components/Tooltip/index.vue";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import type { IGetAttachFileListBack } from "@/components/UploadFileDialog/types";

//用不上了，有机会可以去掉
function calMonthDepAmount(
  value: number,
  netsalvage_rate: number,
  totaldeprecition: number,
  impairment: number,
  usefullife: number,
  provisionmonth: number
) {
  let remainDepreciation = value * (1 - netsalvage_rate / 100) - totaldeprecition - impairment;
  let remainMounth = usefullife - provisionmonth;
  if (remainMounth === 0) {
    return "0.00";
  }
  let MonthDepAmount = remainDepreciation / remainMounth;
  if (isNaN(MonthDepAmount)) {
    return "0.00";
  } else {
    return MonthDepAmount.toFixed(2);
  }
}

const emits = defineEmits(["divCancel"]);
const props = defineProps({
  detailData: {
    type: Object,
    default: () => {},
  },
});

const accountsetStore = useAccountSetStore();
const accountingStandard = accountsetStore.accountSet!.accountingStandard as number;
const taxType = useAccountSetStore().accountSet?.taxType;
const impairmentProvisionBlock = computed(() => {
  return (accountingStandard === 3 && fa_property.value === 0) || (accountingStandard === 2 && [0, 1].includes(fa_property.value));
});
function noteShow() {
  let prevContent = 0;
  if ((accountingStandard === 3 && fa_property.value === 0) || (accountingStandard === 2 && [0, 1].includes(fa_property.value))) {
    prevContent++;
  }
  if (taxType === 2 && [1, 2, 3].includes(accountingStandard)) {
    prevContent++;
  }
  if (!(fa_property.value === 0 || (fa_property.value === 1 && [1, 2].includes(accountingStandard)))) {
    prevContent++;
  }
  return prevContent;
}
const {
  created_date,
  fa_num,
  created_period,
  created_way,
  fa_name,
  fa_type_name,
  fa_model,
  started_date,
  department_name,
  vendor,
  decreased_way,
  decreased_period,
  depreciation_type,
  fa_asub_name,
  fa_asub_aae,
  depreciation_asub_name,
  depreciation_asub_aae,
  disposal_asub_name,
  disposal_asub_aae,
  impairment_provision_asub_name,
  impairment_provision_asub_aae,
  cost_asub_name,
  value,
  netsalvage_rate,
  usefullife,
  provisionmonth,
  monthdeprecition,
  yeardeprecition,
  preyeardeprecition,
  depreciation_now,
  totaldeprecition,
  impairment,
  faAmortizations,
  fa_property,
  zcjz,
  use_people,
  location,
  attachsCount,
  fa_id,
} = toRefs(props.detailData);

// 增加方式列表
const createdWayList: Record<number, string> = {
  1: "购入",
  2: fa_property.value === 0 ? "在建工程转入" : "内部研发",
  3: "其他",
};

const depreciationTypeList: Record<number, string> = {
  1: "平均年限法",
  0: fa_property.value === 0 ? "不折旧" : "不摊销",
};

function formatName(name: string, aaeName: string) {
  return aaeName ? `${name}_${aaeName}` : `${name}`;
}

const divCancel = () => {
  emits("divCancel");
};

const { debounce } = getGlobalLodash();
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
function handleGetAttachFileListFn() {
  if (!attachsCount.value) {
    ElNotify({ type: "warning", message: "暂无附件" });
    return;
  }
  const params = { faId: fa_id.value };
  request({
    url: "/api/FixedAssets/GetAttachFileList",
    method: "post",
    params,
  }).then((res: IResponseModel<IGetAttachFileListBack>) => {
    if (res.state === 1000 && res.data.result) {
      const list = res.data.data.map((item: any) => {
        item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
        return item;
      });
      uploadFileDialogRef.value?.open(params, list, res.data.parentId);
    } else {
      ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
    }
  });
}
const handleGetAttachFileList = debounce(handleGetAttachFileListFn, 500);
</script>
<style scoped lang="less">
.content {
  position: relative;
  margin: 0 auto;
  width: 100%;

  & .tab-title {
    padding: 20px 0;
    text-align: center;
    color: var(--font-color);
    font-size: var(--h2);
    line-height: 25px;
    font-weight: bold;
  }

  & .divEdit {
    width: 1000px;
    background-color: var(--white);
    margin: 0 auto 40px;
    overflow: hidden;

    & .block-title {
      padding: 20px 20px 10px 20px;
      color: var(--font-color);
      font-size: var(--h3);
      line-height: 22px;
      font-weight: bold;
      text-align: left;
    }

    & .block-main {
      padding: 0px 20px 10px;

      &.lableImpairment {
        .is-guttered:nth-child(3n + 1) {
          & .label {
            width: 128px;
          }
        }
      }

      .is-guttered {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 10px 20px !important;

        & span {
          display: inline-block;
          text-align: left;
          font-size: 14px;
          line-height: 18px;
          color: var(--font-color);
        }

        &:nth-child(3n + 1) {
          & .label {
            width: 105px;
          }
        }

        &:nth-child(3n + 2) {
          & .label {
            width: 133px;
          }
        }

        &:nth-child(3n + 3) {
          & .label {
            width: 131px;
          }
        }

        & .text {
          width: 150px;
          max-height: 42px;
          font-size: 12px;
          line-height: 14px;
          color: #808080;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
        }
      }
    }

    & .buttons {
      // margin-top: 20px;
      margin-bottom: 20px;
      text-align: center;
      border: 0;
    }
  }

  & .fixedImg {
    position: absolute;
    top: 80px;
    left: 500px;
    width: 120px;
    height: 60px;
    background: url(@/assets/FixedAssets/020FixedAssets.png) no-repeat 0px -280px;
  }
}

:deep(.span_wrap) {
  width: 150px;
  max-height: 42px;
  font-size: 12px;
  line-height: 15px;
  color: #808080;
  text-align: left;
  word-break: break-all;
  overflow: hidden;
}
</style>
