export interface ICustomWage {
    as_id: number;
    item_id: number;
    item_type: number;
    item_name: string;
    m_id: number;
}

export interface ICheckSalaryVoucherItem {
    assistingaccounting: number | null;
    asub_code: string;
    asub_id: number;
    asub_name: string;
    credit: number;
    debit: number;
    description: string;
    index: number;
    v_date: string;
    v_num: number;
    vg_name: string;
}

export interface ISelectItem {
    value: string;
    label: string;
}

export interface IPeriodItem {
    value: string;
    label: string;
    time: string;
}

export interface IItemData {
    VET_ID: string;
    ASUB_ID: string;
    Direction: number;
    ValueId: string;
}

export interface IDetailColumn {
    insurance: Array<IInsuranceCol[]>;
    salary: Array<ISalaryCol[]>;
}
interface IInsuranceCol {
    as_id: number;
    companypercent: number;
    index: number;
    item_id: number;
    item_name: string;
    personpercent: number;
}
interface ISalaryCol {
    item_type: number;
    item_id: number;
    item_name: string;
}

interface ISalaryADJ {
    item_id: number;
    item_type: number;
    item_name: string;
    item_amount: number;
}
interface ISalarySS {
    ss_type: number;
    ss_name: string;
    com_amount: number;
    com_percent: number;
    per_amount: number;
    per_percent: number;
}
export interface IItemDetailData {
    tax_show: number;
    have_before: boolean;
    salary_year_before: number;
    ss_p_year_before: number;
    deduction_other_year_before: number;
    gross_pay_1: number;
    gross_pay_2: number;
    gross_pay_3: number;
    gross_pay_4: number;
    gross_pay_5: number;
    gross_pay_6: number;
    gross_pay_7: number;
    gross_pay_8: number;
    gross_pay_9: number;
    gross_pay_10: number;
    gross_pay_11: number;
    gross_pay_12: number;
    gross_pay_13: number;
    gross_pay_14: number;
    gross_pay_15: number;
    gross_pay_16: number;
    gross_pay_17: number;
    gross_pay_18: number;
    gross_pay_19: number;
    gross_pay_20: number;
    deduct_wage_1: number;
    deduct_wage_2: number;
    deduct_wage_3: number;
    deduct_wage_4: number;
    deduct_wage_5: number;
    deduct_wage_6: number;
    deduct_wage_7: number;
    deduct_wage_8: number;
    deduct_wage_9: number;
    deduct_wage_10: number;
    deduct_wage_11: number;
    deduct_wage_12: number;
    deduct_wage_13: number;
    deduct_wage_14: number;
    deduct_wage_15: number;
    deduct_wage_16: number;
    deduct_wage_17: number;
    deduct_wage_18: number;
    deduct_wage_19: number;
    deduct_wage_20: number;
    after_tax_1: number;
    after_tax_2: number;
    after_tax_3: number;
    after_tax_4: number;
    after_tax_5: number;
    after_tax_6: number;
    after_tax_7: number;
    after_tax_8: number;
    after_tax_9: number;
    after_tax_10: number;
    after_tax_11: number;
    after_tax_12: number;
    after_tax_13: number;
    after_tax_14: number;
    after_tax_15: number;
    after_tax_16: number;
    after_tax_17: number;
    after_tax_18: number;
    after_tax_19: number;
    after_tax_20: number;
    insurance_p_1: number;
    insurance_p_2: number;
    insurance_p_3: number;
    insurance_p_4: number;
    insurance_p_5: number;
    insurance_p_6: number;
    insurance_p_7: number;
    insurance_p_8: number;
    insurance_p_9: number;
    insurance_p_10: number;
    insurance_p_11: number;
    insurance_p_12: number;
    insurance_p_13: number;
    insurance_p_14: number;
    insurance_p_15: number;
    insurance_p_16: number;
    insurance_p_17: number;
    insurance_p_18: number;
    insurance_p_19: number;
    insurance_p_20: number;
    insurance_c_1: number;
    insurance_c_2: number;
    insurance_c_3: number;
    insurance_c_4: number;
    insurance_c_5: number;
    insurance_c_6: number;
    insurance_c_7: number;
    insurance_c_8: number;
    insurance_c_9: number;
    insurance_c_10: number;
    insurance_c_11: number;
    insurance_c_12: number;
    insurance_c_13: number;
    insurance_c_14: number;
    insurance_c_15: number;
    insurance_c_16: number;
    insurance_c_17: number;
    insurance_c_18: number;
    insurance_c_19: number;
    insurance_c_20: number;
    salaryADJ: ISalaryADJ[];
    salary_sum: number;
    deduct_wage: number;
    gross_pay: number;
    after_tax: number;
    salarySS: ISalarySS[];
    ss_p: number;
    ss_c: number;
    deduction_other: number;
    salary_year: number;
    tax_base_amount_year: number;
    ss_p_year: number;
    deduction_child_year: number;
    deduction_parent_year: number;
    deduction_house_year: number;
    deduction_rent_year: number;
    deduction_education_year: number;
    deduction_baby_year: number;
    tax_deduction_special_year: number;
    deduction_other_year: number;
    tax_year: number;
    tax_payed_year: number;
    tax_adj_diff: number;
    as_id: number;
    m_id: number;
    e_sn: number;
    e_code: string;
    e_name: string;
    e_id: string;
    department: number;
    aa_name: string;
    email: string;
    mobile_phone: string;
    tax: number;
    net_salary: number;
    total_cost: number;
    tax_base_amount: number;
    index?: string | number;
    net_salary_first: number;
    net_salary_second: number;
}
export interface ISalaryData {
    count: number;
    data: IItemDetailData[];
}

export interface ICreateVoucher {
    assistingaccounting: number;
    assistsetting: string;
    asub_code: string;
    asub_id: number;
    asub_name: string;
    credit: number;
    debit: number;
    description: string;
    index: number;
    index2: number;
    is_farow: number;
    measureupnit: number;
    price: number;
    quantity: number;
    quantityaccounting: number;
    fcamount: number;
    fccode: string;
    fcid: number;
    fcrate: number;
    foreigncurrency: number;
    isjtsub: number;
}

export interface IIofo {
    checkImgClass: String;
    checkBg: Boolean;
    checkBgClass: String;
    checkTxt: String;
    checkTxtClass: string;
}

export interface ITaxDeclarationWaitParams {
    waitingFrom: string;
    spanEmpCount: string;
    employeeCount: number;
    waitingText: string;
}