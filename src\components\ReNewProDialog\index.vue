<template>
    <el-dialog v-model="reNewProDialogShow" title="提示" width="400px">
        <div class="renew-dialog-container">
            <div class="renew-dialog-txt mt-20">
                <div style="margin-top: 18px; color: #333333">
                    您好，您的服务还有 <span class="point-value">{{ remainingDays }}天到期</span
                    ><span style="color: #ff7500">（{{ expiredTime }}）</span>
                </div>
                <div style="margin-left: 14px; margin-top: 5px; margin-bottom: 40px; color: #333333">
                    为不影响您正常使用，<span style="color: #ff7500">请尽快续费</span>，谢谢!
                </div>
            </div>
            <div class="button solid-button renew-dialog-btn" @click="toRenew()">立即续费</div>
        </div>
    </el-dialog>
</template>
<style lang="less" scoped>
.renew-dialog-container {
    width: 400px;
    background-color: var(--white);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    border-radius: 2px;

    .renew-dialog-title {
        justify-content: center;
        display: flex;
        align-items: center;
        margin-top: 16px;
        color: #333333;
        font-size: 16px;
        font-weight: 600;
    }

    .renew-dialog-close {
        height: 23px;
        width: 23px;
        cursor: pointer;
        position: absolute;
        top: 12px;
        right: 12px;
    }

    .renew-dialog-txt {
        font-size: 14px;
        font-weight: 500;
        color: #563e17;
        line-height: 24px;
        padding-left: 43px;
        border-bottom: 1px solid #e9e9e9;
        margin-top: 16px;
    }

    .point-value {
        font-size: 16px;
        font-weight: 600;
        color: #ff7500;
    }

    .renew-dialog-btn {
        width: 130px;
        height: 28px;
        line-height: 28px;
        align-self: center;
        margin-top: 20px;
        margin-bottom: 20px;
        font-size: 13px;
        font-weight: 300;
        border-radius: 2px;
    }
}
</style>
<script setup lang="ts">
import { gotoBuy } from "@/util/proUtils";
import { ref } from "vue";

const props = defineProps<{ remainingDays: number; expiredTime: string }>();

const reNewProDialogShow = ref(true);
const toRenew = () => {
    reNewProDialogShow.value = false;
    gotoBuy();
};
</script>
