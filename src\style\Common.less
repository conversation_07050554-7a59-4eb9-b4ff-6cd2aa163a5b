@import "functions.less";
* {
    :focus {
        outline: none;
    }
}
html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
}

//背景色
body {
    background: var(--background-color);
}

//disabled输入框
input[type="text"][disabled="disabled"],
input[type="text"][disabled] {
    // background-color: var(--input-disabled-color);
    // background-color: var(--input-disabled-border-color);
    color: var(--input-disabled-font-color) !important;
    border-color: var(--input-disabled-border-color);
}

//disabled下拉框
select[disabled="disabled"],
select[disabled] {
    background-color: var(--input-disabled-color);
    border-color: var(--input-disabled-border-color);
}
//suffix-icon
// :deep(.el-select__caret::before) {
//     content: "";
//     background: url(@/assets/Icons/down-black.png) center center no-repeat;
//     position: absolute;
//     width: 100%;
//     height: 100%;
//     top: 50%;
//     left: 50%;
//     transform: translate(-50%, -50%);
// }

input[type="text"],
input[type="password"],
input[type="number"],
input[type="text"],
textarea {
    &::-webkit-input-placeholder {
        color: var(--input-placeholder-color);
        font-weight: 400;
    }
}

// 科目选择器与筛选下拉框公共样式
.el-select__popper,
.el-select-v2__popper {
    box-sizing: content-box;
    margin-top: -10px;
    margin-bottom: -10px;
}

.subject-picker,
.period-container,
.jqtransform {
    .el-scrollbar {
        .el-select-dropdown__list {
            max-height: 200px;
        }
        .el-select-dropdown__item {
            height: auto;
            white-space: normal;
            text-overflow: initial;
            color: var(--el-text-color-regular);
            // &.hover {
            //     background-color: #fff;
            //     background-color: #44b449;
            // }
            &.hover,
            &:hover,
            // &.selected,
            &.selected.hover {
                background-color: #44b449;
                color: #fff;
            }
            span {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
            }
        }
    }
}

// :deep(.el-select-dropdown__item.hover, .el-select-dropdown__item:hover) {
//     background-color: var(--el-color-primary);
//     color: white;
// }

.button {
    border-radius: 2px;
    .base-button(26px, 84px);
    .hollow(var(--font-color), var(--button-border-color));
    .hover(var(--main-color));
    .active(var(--dark-main-color));

    &.dropdown {
        background-image: url("@/assets/Icons/down-black.png");
        background-position-x: 86px - 18px;
        background-position-y: center;
        background-repeat: no-repeat;
        position: relative;

        .downlist {
            display: block;
            position: absolute;
            top: 28px;
            left: -1px;
            visibility: hidden;
            opacity: 0;
            .set-transition;
            z-index: 100;
            .downlist-buttons {
                color: var(--font-color);
                background-color: var(--white);
                box-shadow: 0 0 4px var(--button-border-color);
                border-radius: 2px;
                min-width: 84px;
                div {
                    height: 27px;
                    line-height: 27px;
                    cursor: pointer;
                    width: 100%;
                    padding: 0 12px;
                    box-sizing: border-box;
                    text-align: left;
                    white-space: nowrap;
                    &:hover {
                        // .solid(var(--table-hover-color));
                        // color: var(--font-color);
                        background-color: var(--main-color);
                        color: #fff;
                    }
                    .active(var(--dark-main-color));
                }
                &.large {
                    min-width: 96px;
                }
            }
        }

        &:hover {
            background-image: url("@/assets/Icons/down-white.png");

            .downlist {
                visibility: visible;
                opacity: 1;
            }
        }
    }

    &.warn-button {
        border-color: #d9d9d9;
        background-color: rgba(0, 0, 0, 0.04);
        color: var(--button-red);
        .hover(var(--button-red));
        .active(var(--button-dark-red));
    }

    &.large-1 {
        width: 94px;
        background-position-x: 96px - 18px;
    }

    &.large-2 {
        width: 104px;
        background-position-x: 106px - 18px;
    }
    &.disabled {
        color: var(--border-color);
        border-color: var(--border-color);
        background-color: var(--white);
    }
}

.solid-button {
    border: none;
    border-radius: 2px;
    .base-button(28px, 86px);
    .solid(var(--main-color));
    .hover(var(--light-main-color));
    .active(var(--dark-main-color));

    &.dropdown {
        background-image: url("@/assets/Icons/down-white.png");

        .downlist {
            left: 0;
        }
    }

    &.large-1 {
        width: 96px;
    }

    &.large-2 {
        width: 106px;
    }

    &.disabled {
        background-color: var(--border-color);
        border-color: var(--border-color);
        color: var(--white);
    }
}

//单选按钮
.radio-button {
    height: 20px;
    display: inline-block;

    input[type="radio"]:nth-child(1) {
        display: none;

        &:checked + .radio-box {
            background-image: url("/Image/Settings/radio-checked.png");
        }

        &:checked[disabled="disabled"] + .radio-box,
        &:checked[disabled] + .radio-box {
            background-image: url("/Image/Settings/radio-checked-disabled.png");
        }
    }

    .radio-box {
        .set-font;
        position: relative;
        display: inline-block;
    }

    .radio-box:last-child {
        padding-left: 28px;
        background-repeat: no-repeat;
        background-position: top 1px left;
        background-image: url("/Image/Settings/radio.png");
    }
}

.radio-button + .radio-button {
    margin-left: 30px;
}

//复选按钮
.checkbox-button {
    height: 20px;
    display: inline-block;

    input[type="checkbox"]:nth-child(1) {
        display: none;

        &:checked + .checkbox-box {
            background-image: url(../assets/Settings/checkbox-checked.png);
        }

        &[disabled="disabled"] + .checkbox-box,
        &[disabled] + .checkbox-box {
            background-image: url(../assets/Settings/checkbox-disabled.png);
        }

        &:checked[disabled="disabled"] + .checkbox-box,
        &:checked[disabled] + .checkbox-box {
            background-image: url(../assets/Settings/checkbox-checked-disabled.png);
        }
    }

    .checkbox-box {
        .set-font;
        position: relative;
        display: inline-block;
    }

    .checkbox-box:last-child {
        padding-left: 22px;
        background-repeat: no-repeat;
        background-position: top 3px left;
        background-image: url(../assets/Settings/checkbox.png);
    }
}

.checkbox-button + .checkbox-button {
    margin-left: 30px;
}

//文件选择框
.file-button {
    height: 20px;
    position: relative;
    display: inline-block;
    overflow: hidden;

    input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        filter: alpha(opacity=0);
        cursor: pointer;
    }

    .file-box {
        .set-font;
        margin-left: 20px;
        display: none;
        max-width: 160px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: top;
    }

    &:hover {
        .link {
            text-decoration: underline;
        }
    }

    &:active {
        .link {
            color: var(--dark-link-color);
        }
    }
}

// 大一点的按钮
.solid-button-large {
    .button;
    .solid-button;
    border-radius: 4px;
    height: 32px;
    width: 102px;
    line-height: 32px;
}

//链接
.link {
    outline: none;
    cursor: pointer;
    text-decoration: none;
    .set-font(var(--link-color));

    &:hover {
        text-decoration: underline;
    }

    .active-font(var(--dark-link-color));

    &.disabled {
        text-decoration: none;
        color: var(--border-color);
        cursor: default;
    }

    &.red {
        color: var(--red);
        .active-font(var(--dark-red));
    }

    &.yellow {
        color: var(--yellow);
        .active-font(var(--dark-yellow));
    }
}

.line {
    .line;
}

//高亮字体
.highlight-red {
    color: var(--red) !important;
}

.highlight-green {
    color: var(--main-color);
}

.highlight-blue {
    color: var(--link-color);
}

.highlight-orange {
    color: var(--orange);
}

.weaker-font {
    color: var(--weaker-font-color);
}

.refresh-icon {
    display: inline-block;
    height: 20px;
    width: 32px;
    background: url("@/assets/Erp/page-refresh.png") no-repeat;
    background-size: 20px 20px;
    cursor: pointer;
}

.mr-5 {
    margin-right: 5px;
}

.mr-10 {
    margin-right: 10px !important;
}

.mr-20 {
    margin-right: 20px !important;
}
.mr-30 {
    margin-right: 30px;
}
.mt-5 {
    margin-top: 5px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-40 {
    margin-top: 40px;
}

.mt-100 {
    margin-top: 100px;
}

.mb-5 {
    margin-bottom: 5px;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-8 {
    margin-top: 8px;
}

.mb-8 {
    margin-bottom: 8px;
}

.ml-5 {
    margin-left: 5px;
}

.ml-10 {
    margin-left: 10px;
}
.ml-16 {
    margin-left: 16px;
}
.ml-20 {
    margin-left: 20px;
}
.pr-5 {
    padding-right: 5px;
}
.pr-10 {
    padding-right: 10px;
}

.pr-20 {
    padding-right: 20px;
}

.pt-10 {
    padding-top: 10px;
}

.pt-20 {
    padding-top: 20px;
}

.pb-10 {
    padding-bottom: 10px;
}

.pb-20 {
    padding-bottom: 20px;
}
.pl-5 {
    padding-left: 5px;
}

.pl-10 {
    padding-left: 10px;
}
.pl-15 {
    padding-left: 15px;
}
.pl-16 {
    padding-left: 16px;
}
.pl-20 {
    padding-left: 20px;
}
.pl-30 {
    padding-left: 30px;
}
.pl-40 {
    padding-left: 40px;
}
.pl-50 {
    padding-left: 50px;
}
.pl-60 {
    padding-left: 60px;
}
.pl-70 {
    padding-left: 70px;
}
.pl-80 {
    padding-left: 80px;
}
.pl-90 {
    padding-left: 90px;
}
.pl-56 {
    padding-left: 56px !important;
}
.flex-space {
    flex-shrink: 5;
}

.flex-space-10 {
    .flex-space;
    width: 10px;
}

.float-l {
    float: left;
}

.float-r {
    float: right;
}

.font-size0 {
    font-size: 0;
}

.bold{
    font-weight: bold;
}

.buttons {
    padding: 10px 0;
    text-align: center;
}
.borderTop {
    border-top: 1px solid var(--border-color);
}

//公用标题样式
.title {
    height: var(--title-height);
    padding-top: 30px;
    box-sizing: border-box;
    .set-font(var(--font-color), var(--h2), 25px);
    font-weight: bold;
    background-color: var(--background-color);
    display: none;
    align-items: flex-start;
    justify-content: center;
}

.slot-title {
    padding: 16px 0;
    text-align: center;
    .set-font(var(--font-color), var(--h4), 22px);
    font-weight: bold;
    font-family: PingFangSC, PingFang SC;
    width: 100%;
    border-bottom: 1px solid var(--slot-title-color);

    &.no-border {
        border-bottom: none;
    }
}

.cursor-default {
    cursor: default;
}

.warning-tip {
    padding: 5px 10px;
    color: #ff7500;
    background-color: #fff7f1;
    border-radius: 2px;
}

//公用内容样式
.content {
    width: 1000px;
    width: var(--content-width);
    //overflow: hidden;
    margin: 0 auto;
    position: relative;
    padding-bottom: var(--content-padding-bottom);

    .main-content,
    .edit-content,
    .slot-content,
    .slot-mini-content {
        width: 1000px;
        width: var(--content-width);
        background-color: var(--white);
        display: flex;
        flex-direction: column;

        .main-top,
        .main-tool-bar {
            padding: 10px;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .main-tool-left {
                display: flex;
                align-items: center;
                .set-font;
            }

            .main-tool-right {
                display: flex;
                align-items: center;
            }

            &.space-between {
                justify-content: space-between;
            }
        }

        .main-center {
            padding: 10px;
            padding-top: 0;
        }
    }

    .main-content.align-center,
    .slot-content.align-center {
        background-color: transparent;
        align-items: center;
    }
}
.content {
    height: 100%;
    .main-content {
        height: 100%;
        .main-center {
            flex: 1;
            overflow: hidden;
        }
        .table {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .el-table {
            flex: 1;
        }
    }
    .el-table__empty-block {
        position: absolute;
        top: 0;
        bottom: 0;
        width: calc(100% - 2px) !important;
    }
}

//日期组件图标放右
:deep(.el-date-editor) {
    & .el-input__prefix {
        position: absolute;
        right: 0;
    }
    & .el-input__suffix-inner {
        position: absolute;
        right: 30px;
        top: 9px;
    }
}

//dialog
.dialog-content-body {
    padding: 40px 40px;
    text-align: center;
    min-height: 42px;
    .set-font;
}

.commmon-table {
    //.set-border;
    thead {
        tr {
            th {
                height: var(--table-title-height);
                text-align: left;

                div.cell {
                    padding: 0 8px;
                    .set-font(var(--font-color), var(--table-title-font-size), var(--table-title-line-height));
                    font-weight: bold;
                }

                &.money {
                    text-align: right;
                }

                &.center {
                    text-align: center;
                }
            }
            &.green-header {
                th {
                    div {
                        color: var(--white);
                    }
                    background-color: var(--main-color);
                }
            }
        }

        &.is-group {
            tr {
                th {
                    height: var(--table-title-height);
                    text-align: left;

                    div {
                        padding: 0 8px;
                        .set-font(var(--font-color), var(--table-title-font-size), var(--table-title-line-height));
                        font-weight: bold;
                    }

                    &.money {
                        text-align: right;
                    }

                    &.center {
                        text-align: center;
                    }
                }
                &.green-header {
                    th {
                        div {
                            color: var(--white);
                        }
                        background-color: var(--main-color);
                    }
                }
            }
        }
    }

    tbody {
        tr {
            td {
                &.el-table__cell {
                    padding: 0;
                }
                height: var(--table-body-height);

                .cell {
                    .set-font(var(--font-color), var(--table-body-font-size), var(--table-body-line-height));
                    .link {
                        font-size: var(--table-body-font-size);
                        line-height: var(--table-body-line-height);
                    }

                    .link + .link {
                        margin-left: 10px;
                    }

                    input[type="text"] {
                        width: 98% !important;
                        border: 1px solid var(--border-color);
                        outline: none;
                        font-size: var(--table-body-font-size);
                    }

                    &.money {
                        text-align: right;
                    }
                    padding: 0 8px;
                }
            }
        }
    }
}
//自定义表格
.custom-table {
    .commmon-table;
    .el-scrollbar__bar.is-horizontal {
        position: absolute;
        // bottom: -11px;
        height: 10px;
        border-radius: 10px;
        &:hover {
            height: 10px;
            border-radius: 10px;
        }
    }
    :deep(.el-table--enable-row-hover) {
        .el-popper {
            max-width: 300px;
            text-align: left;
        }
    }
    :deep(.table-column-header-popper) {
        max-width: 300px;
        text-align: left;
    }
}

//自定义表格
.custom-table-normal {
    .commmon-table;
    .el-scrollbar__bar.is-horizontal {
        height: 10px;
        border-radius: 10px;
        display: block;
        position: absolute;
        // bottom: -11px;
        z-index: 30;
        &:hover {
            height: 10px;
            border-radius: 10px;
        }
    }
}

//自定义标签
.switch-tabs {
    height: var(--tab-height);
    list-style: none;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);

    li {
        float: left;
        position: relative;
        width: var(--tab-head-width);
        height: var(--tab-height);
        .set-font(var(--font-color), var(--font-size), var(--tab-height));
        text-align: center;
        background-repeat: no-repeat;
        background-image: url("/Image/Settings/tab-head.png");
        cursor: pointer;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;

        &.active {
            z-index: 99 !important;
            background-image: url("/Image/Settings/tab-head-active.png");
            color: var(--main-color);
            font-weight: bold;

            &.border {
                background-image: url("/Image/Settings/tab-border-head-active.png");
            }
        }

        .active-font(var(--dark-main-color));

        &.long {
            width: calc(~"var(--tab-head-long-width) - 20px");
            padding-right: 20px;
            background-image: url("/Image/Settings/tab-long-head.png");

            &.active {
                background-image: url("/Image/Settings/tab-long-head-active.png");

                &.border {
                    background-image: url("/Image/Settings/tab-border-long-head-active.png");
                }
            }
        }
    }

    li + li {
        margin-left: -16px;
        width: var(--tab-width);
        background-image: url("/Image/Settings/tab.png");

        &.active {
            background-image: url("/Image/Settings/tab-active.png");

            &.border {
                background-image: url("/Image/Settings/tab-border-active.png");
            }
        }

        &.long {
            width: calc(~"var(--tab-long-width) - 30px");
            padding-right: 15px;
            padding-left: 15px;
            background-image: url("/Image/Settings/tab-long.png");

            &.active {
                background-image: url("/Image/Settings/tab-long-active.png");

                &.border {
                    background-image: url("/Image/Settings/tab-border-long-active.png");
                }
            }
        }
    }

    li:nth-child(2) {
        margin-left: -20px;
    }
}

.check-box-selector {
    .set-border;
    box-sizing: border-box;
    height: 30px;
    padding-left: 8px;
    padding-right: 20px;
    display: flex;
    align-items: center;
    background-repeat: no-repeat;
    background-position: right 5px top 10px;
    background-image: url(/Image/Settings/down-black.png);
    position: relative;
    cursor: pointer;

    .check-box-content {
        .set-font;
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        border: none !important;
        outline: none !important;
        height: 28px !important;
        line-height: 28px !important;
        padding: 0 !important;
    }

    .check-box-downlist {
        position: absolute;
        left: -1px;
        right: -1px;
        top: 28px;
        .set-border;
        box-sizing: border-box;
        background-color: var(--white);
        display: none;
        z-index: 1;
        max-height: 200px;
        overflow: auto;

        .check-box-item {
            display: flex;
            align-items: center;
            height: 28px;
            cursor: pointer;

            .checkbox-button {
                padding-left: 8px;
                height: 28px;
                flex: 1;
                box-sizing: border-box;

                .checkbox-box {
                    margin-top: 4px;
                    margin-bottom: 4px;
                }
            }

            &:hover {
                background-color: var(--table-hover-color);
            }
        }
    }

    &.down {
        background-image: url(/Image/Settings/up-black.png);

        .check-box-downlist {
            display: block;
        }
    }
}

.search-input-box {
    position: relative;

    .search-btn {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        background-size: 15px 16px;
        width: 25px;
        cursor: pointer;
        background-position: center;
        background-repeat: no-repeat;
        background-image: url(/Image/Settings/AccountSet/sousuo-blue.png);
    }
}

// 处理scrollbar中的tree没有横向滚动条问题
.el-scrollbar {
    .el-tree > .el-tree-node {
        min-width: 100%;
        display: inline-block;
    }
}

// 账簿右侧树添加滚动条样式
.tree {
    .el-scrollbar {
        box-sizing: border-box;
        padding-right: 8px;
        padding-bottom: 5px;
        // .el-scrollbar__thumb {
        //     background: #636363;
        // }
    }
}

.el-dialog {
    .el-dialog__header {
        padding-bottom: 0;
        margin-right: 0;
        font-weight: bold;
        border-bottom: 1px solid var(--border-color);
        text-align: center;
        .el-dialog__title {
            text-align: center;
        }
        .el-dialog__headerbtn {
            top: 0;
            width: 52px;
            height: 52px;
        }
    }
}
.el-notification {
    text-align: left;
    word-break: break-all;
}

.el-popper {
    &.max-width-300 {
        max-width: 300px;
    }
}
body[erp] {
    //背景色
    background: var(--white);
    // 处理进入erp的视觉效果
    animation: toshowerp 0.6s linear;
    -webkit-animation: toshowerp 0.6s linear;

    // input交互设置
    input[type="text"],
    input[type="password"],
    input[type="number"] {
        transition: all 0.2s linear;

        &:hover {
            border-color: var(--input-border-hover-color);
        }

        &:focus {
            border-color: var(--input-border-hover-color);
            // -webkit-box-shadow: 0 0 0 1px rgba(140, 197, 255, 0.35);
            // box-shadow: 0 0 0 1px rgba(140, 197, 255, 0.35);
        }

        &::placeholder {
            color: #c0c4cc;
        }
    }

    // select交互兼容erp
    select {
        transition: all 0.2s linear;
        background-color: #fff;
        outline: none;

        &:hover {
            border-color: var(--input-border-hover-color) !important;
        }

        option {
            background-color: #fff;

            &:hover {
                background-color: var(--table-hover-color);
            }
        }
    }

    // 科目选择器与筛选下拉框公共样式
    .el-select__popper {
        margin-top: -12px;
        margin-bottom: -12px;
    }
    .no-dialog-header {
        .el-dialog__header {
            display: none;
        }
    }
    //confirm
    .el-dialog__header {
        text-align: left;
        padding: 0 0 0 32px;
        line-height: 64px;
        height: 64px;
        font-weight: bold;
        border-bottom: 1px solid var(--border-color);
        color: var(--font-color);
        .el-dialog__title {
            font-size: var(--h2);
            line-height: 18px;
            font-weight: 500;
        }
    }

    .custom-confirm {
        .el-dialog__header {
            text-align: left;
            padding: 0 0 0 32px;
            line-height: 64px;
            height: 64px;
            font-weight: bold;
            border-bottom: 1px solid var(--border-color);
            color: var(--font-color);
            font-size: var(--h3);
            .el-dialog__title {
                font-size: var(--h3);
                line-height: 18px;
                font-weight: bold;
            }
        }
        .el-dialog__body {
            & .buttons {
                height: 30px;
                display: block;
                box-sizing: content-box;
                border-top: 1px solid var(--border-color);
                text-align: right;
                padding: 16px 20px 16px 0;
                & a {
                    text-align: center;
                    display: block;
                    float: right;
                    width: auto;
                    min-width: 76px;
                    height: 30px;
                    border-radius: 2px;
                    margin-left: 20px;
                    line-height: 30px;
                }
                & button {
                    box-sizing: border-box;
                    padding: 0 12px;
                    font-size: var(--font-size);
                }
            }
            & .borderTop {
                border-top: none;
            }
        }
        &.no-split-line {
            .el-dialog__header {
                border-bottom: none;
            }
            .el-dialog__body {
                & .buttons {
                    border-top: none;
                }
            }
        }
        &.no-split-line-buttons {
            .el-dialog__body {
                & .buttons {
                    border-top: none;
                }
            }
        }
    }

    .dialog-content-body {
        border: none;
        padding: 30px 78px 34px 60px;
        text-align: left;
        font-size: 14px;
        .dialog-content-message {
            word-wrap: break-word;
        }
        //buttomn
    }

    .el-select__popper {
        .el-select-dropdown {
            .el-select-dropdown__empty {
                height: 50px;
            }
        }
    }

    .button {
        width: auto;
        min-width: 76px;
        height: 28px;
        box-sizing: border-box;
        padding: 0 12px;
        font-size: var(--font-size);
        white-space: nowrap;
        &:hover {
            border-color: var(--main-color);
            color: var(--main-color);
            background-color: var(--white);
        }

        &:active {
            border-color: var(--main-color);
            color: var(--main-color);
            background-color: #f6faff;
        }

        &.dropdown {
            background-position: right 14px center;
            padding-right: 28px;
            white-space: nowrap;

            &:hover {
                background-image: url("@/assets/Icons/down-black.png");
            }
        }

        &.disabled {
            color: #c0c4cc;
            cursor: not-allowed;
            background-image: none;
            background-color: #f5f5f5;
            border: 1px solid #e9e9e9;
        }

        &.solid-button {
            width: auto;
            min-width: 76px;
            padding: 0 12px;
            box-sizing: border-box;
            font-size: var(--font-size);
            .hover(var(--light-main-color));
            .active(var(--dark-main-color));

            &.disabled {
                color: #c0c4cc;
                cursor: not-allowed;
                background-image: none;
                background-color: #f5f5f5;
                border: 1px solid #e9e9e9;
            }
        }

        .downlist {
            left: -1px !important;
            top: 29px;

            .downlist-buttons {
                // border-top: 1px solid var(--button-border-color);
                div {
                    border-radius: 0 !important;
                    margin-top: -1px;
                    z-index: 1;
                    width: 100%;
                    // padding: 0 12px;
                    box-sizing: border-box;
                    // text-align: left;
                    padding: 0;
                    text-align: center;
                    white-space: nowrap;

                    &:hover {
                        z-index: 9;
                        position: relative;
                        background-color: var(--main-color);
                        color: #fff;
                    }
                }
            }
        }
    }

    //自定义表格
    .custom-table,
    .custom-table-normal {
        //针对业财所有表格都会多边框
        // .set-border(var(--table-border-color));
        border-radius: 4px 4px 0 0;
        //业财圆角需要搭配hidden，否则会出现圆角不生效的问题
        // overflow: hidden;
        border-collapse: separate !important; // 去掉默认样式影响
        thead {
            tr {
                td,
                th {
                    border-right: 1px solid transparent;
                    border-bottom: 1px solid var(--table-border-color);

                    div.cell {
                        position: relative;
                        padding: 0 8px;

                        &:after {
                            content: "";
                            height: 16px;
                            width: 1px;
                            background-color: rgba(0, 0, 0, 0.2);
                            // background-color: #d0dae8;
                            display: block;
                            position: absolute;
                            right: 0;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                    }

                    &:last-child {
                        div {
                            &:after {
                                display: none;
                            }
                        }
                    }
                }
            }
        }

        tbody {
            tr {
                // background-color: transparent;
                td {
                    border-right: 1px solid transparent;
                    border-bottom: 1px solid var(--table-border-color);
                    .cell {
                        padding: 0 8px;
                    }
                }

                &:hover {
                    background-color: var(--table-hover-color);
                }

                &.selected {
                    background-color: var(--table-selected-color);

                    &:hover {
                        background-color: var(--table-selected-hover-color);
                    }
                }
            }
        }

        tfoot {
            tr {
                // background-color: transparent;

                td {
                    border-right: 1px solid transparent;
                    border-bottom: 1px solid var(--table-border-color);
                    padding: 0;
                    .cell {
                        padding: 0 8px;
                        height: var(--table-body-height);
                        line-height: var(--table-body-height);
                    }
                }
            }
        }
    }

    //自定义标签
    .switch-tabs {
        background-color: var(--white) !important;
        padding-top: 30px;
        height: auto;
        border-bottom: 1px solid var(--title-split-line);
        display: flex;
        justify-content: center;

        li {
            float: none;
            width: auto;
            background-image: none;
            height: var(--tab-height);
            line-height: 22px;
            padding: 0 2px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;

            &:after {
                content: " ";
                max-width: 60px;
                width: 100%;
                height: 2px;
                background-color: var(--white);
            }

            &.active {
                background-image: none;

                &.border {
                    background-image: none;
                }

                &:after {
                    background-color: var(--main-color);
                }
            }

            &.long {
                width: auto;
                padding-right: 0;
                background-image: none;

                &.active {
                    background-image: none;

                    &.border {
                        background-image: none;
                    }
                }
            }
        }

        li + li {
            width: auto;
            background-image: none;
            margin-left: 63px;

            &.active {
                background-image: none;

                &.border {
                    background-image: none;
                }
            }

            &.long {
                width: auto;
                padding-right: 0;
                padding-left: 0;
                background-image: none;

                &.active {
                    background-image: none;

                    &.border {
                        background-image: none;
                    }
                }
            }
        }

        li:nth-child(2) {
            margin-left: 63px;
        }
    }

    // 内容区域
    .main-content {
        // box-shadow: 0px 4px 8px 0px rgba(17, 31, 65, 0.1);
        border-radius: 0px 0 6px 6px;
    }

    // 标题
    .title {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 22px;
        padding: 20px 0;
        text-align: center;
        background-color: #ffffff;
        border-bottom: 1px solid var(--title-split-line);
        width: var(--content-width);
        max-width: 100%;
        margin: 24px auto 0;
        border-radius: 6px 6px 0px 0;
        display: none;
    }

    // 图标-check
    .checkbox-button {
        input[type="checkbox"]:nth-child(1) {
            &:checked + .checkbox-box {
                background-image: url(/Image/Settings/checkbox-checked-erp.png);
                background-size: 14px 14px;
            }

            &[disabled="disabled"] + .checkbox-box,
            &[disabled] + .checkbox-box {
                background-image: url("/Image/Settings/checkbox-disabled.png");
            }

            &:checked[disabled="disabled"] + .checkbox-box,
            &:checked[disabled] + .checkbox-box {
                background-image: url("/Image/Settings/checkbox-checked-disabled.png");
            }
        }
    }

    // 图标-radio
    .radio-button {
        input[type="radio"]:nth-child(1) {
            &:checked + .radio-box {
                background-image: url(/Image/Settings/radio-checked-erp.png);
                background-size: 18px 19px;
            }

            &:checked[disabled="disabled"] + .radio-box,
            &:checked[disabled] + .radio-box {
                background-image: url("/Image/Settings/radio-checked-disabled.png");
                background-size: 18px 18px;
            }
        }

        .radio-box:last-child {
            background-image: url("/Image/Settings/radio.png");
        }
    }

    // 编辑框去掉阴影层
    .content {
        padding-bottom: 0;

        .main-content,
        .edit-content,
        .slot-content,
        .slot-mini-content {
            height: 100vh;
            overflow: hidden;

            .main-top,
            .main-tool-bar {
                padding: 20px;

                &.split-line {
                    border-bottom: 1px solid var(--title-split-line);
                    margin-bottom: 20px;
                }
            }

            .main-center {
                padding: 20px;
                padding-top: 0;
                flex: 1;
                overflow-x: hidden;
                overflow-y: auto;

                // 原生el-table
                .el-table {
                    height: 100%;
                }

                // Table组件
                .table {
                    height: 100%;
                    &.paging-show {
                        display: flex;
                        flex-direction: column;
                        .el-table {
                            flex: 1;
                        }
                    }
                }
            }
        }

        .el-tabs {
            .el-tabs__nav-scroll {
                display: flex;
                align-items: center;
                justify-content: center;
                .el-tabs__nav {
                    padding-left: 0;
                    .el-tabs__item {
                        font-weight: 400;
                        padding: 24px 30px;
                        &.is-active {
                            font-weight: 600;
                        }
                    }
                }
            }
        }
    }

    .pagination {
        tbody {
            tr {
                &:hover {
                    background-color: transparent;
                }
                td {
                    border: none;
                }
            }
        }
    }

    .table {
        &.paging-show {
            .el-table {
                .el-table__body-wrapper {
                    padding-bottom: 0;
                }
                .el-scrollbar {
                    position: relative;
                }
            }
            .pagination {
                margin-top: 10px;
                border: none;
            }
        }
    }

    .el-table {
        thead {
            tr {
                td,
                th {
                    &.el-table-column--selection {
                        div.cell {
                            &:after {
                                display: none;
                            }
                        }
                    }
                }
            }
        }

        .el-table__border-left-patch {
            width: 0;
        }

        &.el-table--border {
            &::before,
            &::after {
                width: 0;
            }
        }

        .el-table__inner-wrapper {
            &::before,
            &::after {
                height: 0;
            }
        }

        border: 1px solid var(--border-color);
        border-radius: 4px;

        .el-table__header-wrapper {
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        &:not(.no-header) {
            .el-table__body-wrapper {
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }

        &.no-header {
            .el-table__body-wrapper {
                border-radius: 4px;
            }
        }

        &.has-footer {
            .el-table__body-wrapper {
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
            .el-table__footer-wrapper {
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }
    }

    // 期初气泡框
    .initial-balance-popover {
        .el-popover__title {
            color: #333;
        }
        .el-popper__arrow {
            &::before {
                background-color: #fff !important;
            }
        }
    }
    // 加载样式
    /*加载框跳动问题，因为自动添加了样式is-fUliscreen，使position交成了fixed. */
    .el-loading-mask {
        position: absolute;
        background-color: rgba(255, 255, 255, 0.0001);
        .el-loading-spinner {
            border-radius: 6px;
            left: 50%;
            top: 40%;
            height: auto;
            line-height: 30px;
            padding: 5px 0;
            text-align: left;
            width: auto;
            margin-left: -100px;
            background-color: white;
            box-shadow: 0px 10px 50px 0px rgba(0, 0, 0, 0.2);
            .el-loading-text {
                color: rgba(0, 0, 0, 0.85);
                /* display: inline-block;*/
                margin: 0 0 0 30px;
            }
            i {
                color: #d8d8d8;
            }
            .circular {
                position: absolute;
                top: 10px;
                left: 20px;
                height: 20px;
                width: 20px;
            }
            .path {
                stroke: #d8d8d8;
                stroke-width: 6px;
            }
            .el-loading-text {
                padding: 0 25px;
            }
        }
        &.is-fullscreen .el-loading-spinner .circular {
            height: 20px;
            width: 20px;
        }
    }

    .el-select:hover:not(.el-select--disabled) .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--main-color) inset;
    }
    // 业财复选框悬停全局样式
    .el-checkbox__inner {
        &:hover {
            color: #3882f8;
        }
    }
    .el-select__popper {
        .el-select-dropdown {
            .el-select-dropdown__list {
                .el-tree {
                    .el-tree-node__content {
                        height: auto;
                        .el-tree-node__expand-icon {
                            margin-left: 0;
                        }
                        .el-select-dropdown__item {
                            padding-left: 10px;
                            margin-left: -24px;
                            &:active {
                                background-color: var(--white) !important;
                                color: var(--dark-main-color);
                            }
                            &.selected {
                                background-color: var(--white) !important;
                                color: var(--main-color);
                            }
                            &.hover {
                                background-color: var(--main-color) !important;
                                color: var(--white);
                            }
                        }
                    }
                }
            }
        }
    }
    // 弹框
    .el-message {
        min-width: 240px;
        padding-top: 12px;
        padding-bottom: 12px;
        box-sizing: border-box;
        border-radius: 4px;
        position: fixed;
        left: 50%;
        top: 20px;
        transform: translateX(-50%);
    }
    .header-operate {
        .header-caret.ascending {
            background: url(@/assets/ERecord/sortIconUp-erp.svg) center center no-repeat;
            background-size: 100% 100%;
        }
        .header-caret.descending {
            background: url(@/assets/ERecord/sortIconDown-erp.svg) center center no-repeat;
            background-size: 100% 100%;
        }
    }
}

// 判断erp后先透明再显示，避免页面布局变动，影响视觉效果
@-webkit-keyframes toshowerp {
    0% {
        opacity: 0;
    }

    95% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes toshowerp {
    0% {
        opacity: 0;
    }

    95% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

//el-button样式
.el-button {
    border-radius: 2px;
    height: 26px;
    width: 84px;
    line-height: 26px;
    font-size: 13px;
    display: inline-block;
    cursor: pointer;
    text-align: center;
    padding: 0;
    outline: none;
    transition: var(--transition-time);
    color: var(--font-color);
    border: 1px solid var(--button-border-color);
    background-color: var(--white);
    &:hover {
        color: white;
        background-color: #44b449;
    }
}

//el-table
:deep(.el-table) {
    .cell {
        white-space: nowrap;
    }
}

.el-table {
    :deep(.cell) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        word-break: unset;
        color: var(--font-color);
        font-size: var(--table-body-font-size);
        line-height: var(--table-body-line-height);
        padding: 0 8px;
        & .handle-item {
            width: 24px;
            height: 15.5px;
            margin-right: 10px;
            font-size: 12px;
            color: #3385ff;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    :deep(.table-right) {
        text-align: right;
    }
    .el-table__cell.customize-hidden-columns {
        .cell .el-tooltip__trigger {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
.customize-hidden-column-popper {
    color: #333;
}
:deep(.el-tree) {
    .el-tree__empty-block {
        & .el-tree__empty-text {
            width: 100%;
            text-align: center;
            line-height: 60px;
        }
    }
}

.el-year-table {
    td {
        &.today {
            .cell {
                font-weight: 400 !important;
                color: var(--el-datepicker-text-color) !important;

                &:hover {
                    color: var(--el-color-primary) !important;
                }
            }
        }
    }
}

.el-month-table {
    td {
        &.today {
            .cell {
                font-weight: 400 !important;
                color: var(--el-datepicker-text-color) !important;

                &:hover {
                    color: var(--el-color-primary) !important;
                }
            }
        }
    }
}

.el-date-table {
    td {
        &.today {
            .el-date-table-cell__text {
                font-weight: 600 !important;
                &:hover {
                    background-color: #ccc;
                    color: var(--el-color-primary) !important;
                }
            }
            .el-date-table-cell {
                &::after {
                    content: "";
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    background: var(--el-color-primary);
                    border-radius: 50%;
                    bottom: 0px;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
    }
}

// 图片预览插件viewjs样式
.viewer-container {
    z-index: 9999 !important;
}

// 设置期初自定义类名+气泡框防止覆盖
.initial-balance-popover {
    .el-popover__title {
        color: #fff;
        font-size: 14px;
        margin-bottom: 8px;
    }
    .el-popper__arrow {
        &::before {
            background-color: #44b449 !important;
        }
    }
}

span.dialog-tip-left {
    text-align: left;
    display: inline-block;
    width: 100%;
}

.el-radio {
    .el-radio__input {
        .el-radio__inner {
            border-color: #d9d9d9;
            &:hover {
                border-color: var(--main-color);
            }
            &::after {
                width: 8px;
                height: 8px;
                border-radius: 100%;
            }
        }
    }
    &.is-disabled {
        cursor: default;
        .el-radio__input {
            .el-radio__inner {
                cursor: default;
                border-color: #d9d9d9;
                background-color: #f7f7f7;
            }
            &.is-disabled + span.el-radio__label {
                cursor: default;
                color: #b7b7b7;
            }
        }
    }
    &.is-checked {
        .el-radio__input {
            .el-radio__inner {
                border-color: var(--main-color);
                background-color: transparent;
                &::after {
                    background-color: var(--main-color);
                }
            }
            &.is-checked + span.el-radio__label {
                color: var(--font-color);
            }
        }
        &.is-disabled {
            cursor: default;
            .el-radio__input {
                .el-radio__inner {
                    cursor: default;
                    border-color: #d9d9d9;
                    background-color: #f7f7f7;
                    &::after {
                        cursor: default;
                        background-color: #d9d9d9;
                    }
                }
                &.is-disabled + span.el-radio__label {
                    cursor: default;
                    color: #b7b7b7;
                }
            }
        }
    }
}
.el-overlay {
    background-color: var(--shadow-color) !important;
}

.el-select:focus-visible,
.el-popper:focus-visible,
.el-select-dropdown:focus-visible,
.el-input:focus {
    outline: none;
}
.el-checkbox {
    --el-checkbox-input-border-color-hover: none !important;
}
.el-picker__popper :focus-visible {
    outline: none;
}
// 兼容360极速浏览器，搜狗浏览器

.el-select:focus,
.select-trigger:focus,
.el-popper:focus,
.el-select-dropdown:focus {
    outline: none;
}
.el-picker__popper :focus {
    outline: none;
}
.el-checkbox {
    .el-checkbox__input.is-checked + .el-checkbox__label {
        color: var(--font-color);
    }
}
.el-select-v2 {
    &.visibleSelect {
        .el-select-v2__wrapper.is-focused {
            .el-select-v2__placeholder {
                color: var(--border-color);
            }
        }
    }
}
// 下拉框公共样式
.el-select__popper {
    &.el-popper.is-light,
    &.el-popper.is-dark {
        border-color: #dcdfe6;
    }
    .el-popper__arrow {
        display: none;
    }
    .el-select-dropdown {
        .el-select-dropdown__wrap {
            max-height: 200px;
            overflow-y: auto;
        }
        .el-select-dropdown__list {
            margin: 0 !important;
            .el-select-dropdown__item {
                height: auto;
                padding: 6px 10px 6px 8px;
                line-height: 16px;
                white-space: normal;
                background-color: var(--white);
                color: var(--font-color);
                width: 100%;
                & span {
                    display: -webkit-box;
                    -webkit-line-clamp: 2; /* 设置最多显示2行 */
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    white-space: normal;
                    word-break: break-all;
                    text-align: left;
                }

                &.selected {
                    background-color: var(--white);
                    color: var(--main-color);
                    font-weight: normal;
                    &:hover {
                        background-color: var(--main-color);
                        color: var(--white);
                    }
                }

                &.hover {
                    background-color: var(--main-color);
                    color: var(--white);
                }

                &:active {
                    background-color: var(--white);
                    color: var(--dark-main-color);
                }
            }
        }

        .el-scrollbar__bar.is-vertical {
            display: block !important;
            width: 8px;
            .el-scrollbar__thumb {
                // 加 important 是因为他们想让有滚动条的时候滚动条常显
                background-color: #cccc !important;
                &:hover {
                    background-color: #999 !important;
                }
            }
        }

        .el-select-dropdown__empty {
            height: 50px;
        }

        .el-select-group__wrap {
            &:not(:last-of-type) {
                padding-bottom: 0;
                &::after {
                    display: none;
                }
            }
            .el-select-group__title {
                padding-left: 8px;
                font-size: 13px;
                color: #333333;
                background: #f7f8fb;
                font-weight: 600;
            }
            .el-select-dropdown__item {
                &:hover {
                    background-color: var(--main-color);
                    color: var(--white);
                }
            }
        }
    }
}
.el-select-v2__popper {
    &.el-popper.is-light,
    &.el-popper.is-dark {
        border-color: #dcdfe6;
    }
    .el-popper__arrow {
        display: none;
    }
    .el-select-dropdown {
        .el-select-dropdown__wrap {
            max-height: 200px;
            overflow-y: auto;
        }
        .el-select-dropdown__list {
            margin: 0 !important;
            .el-select-dropdown__option-item {
                height: auto !important;
                padding: 6px 10px 6px 8px;
                line-height: 16px;
                white-space: normal;
                background-color: var(--white);
                color: var(--font-color);
                width: 100%;
                & span {
                    display: -webkit-box;
                    -webkit-line-clamp: 1; /* 设置最多显示2行 */
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    white-space: normal;
                    word-break: break-all;
                    text-align: left;
                }

                &.is-selected {
                    background-color: var(--white);
                    color: var(--main-color);
                    font-weight: normal;
                    &::after {
                        display: none;
                    }
                    &:hover {
                        background-color: var(--main-color) !important;
                        color: var(--white);
                    }
                }

                &.hover {
                    background-color: var(--main-color) !important;
                    color: var(--white);
                }

                &:active {
                    background-color: var(--dark-main-color) !important;
                    color: var(--white);
                }
            }
        }

        .el-select-v2__empty {
            height: 170px;
        }
    }
}
// 给Option用
.el-option-tool-tip {
    max-width: 300px;
    text-align: left;

    &.wider {
        max-width: 320px;
    }
}

.invoice-add-taxRate-select-popper {
    &.large {
        width: 138px;
    }
    width: 70px;

    .el-select-dropdown__list {
        padding: 0 20px;
        height: calc(26 * 6px);
        .el-select-dropdown__item {
            height: 26px;
            line-height: 26px;
        }
    }
}
// tab栏公共样式
.el-tabs {
    .el-tabs__nav {
        padding-left: 20px;
    }
    .el-tabs__header {
        margin: 0;
        // 边线高度1px
        .el-tabs__nav-wrap {
            &::after {
                height: 1px;
            }
        }
    }
    .el-tabs__item {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        padding: 24px 20px;
        // &.is-active {
        font-weight: 600;
        // }
    }
}

.text-overflow-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

// 表格多选去除半选状态
:deep(.el-checkbox__input.is-indeterminate) {
    .el-checkbox__inner {
        background-color: #fff;
        border-color: #dcdfe6;
        &::before {
            display: none;
        }
    }
}

// 兼容旧版本360el-dialog样式
.el-overlay,
.el-overlay-dialog {
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
}
// 业财复选框样式
:deep(.el-checkbox__inner) {
    color: var(--font-color) !important;
    cursor: default !important;
    &:hover {
        color: #3882f8 !important;
    }
}
// 输入框省略号样式
:deep(.el-input__inner) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.main-container .right-container .router-container {
    .el-overlay.modal-class {
        position: absolute;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
    }
    // .el-overlay.no-position-modal-class {
    //     position: absolute;
    //     // top: 0 !important;
    //     left: 0 !important;
    //     // right: 0 !important;
    //     // bottom: 0 !important;
    // }
}
//列设置
.cellMoveStyle {
    cursor: move;
}
.header-operate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 !important;
    // width: calc(100% + 4px);
    width: 100%;
    > span {
        flex: 1;
        min-width: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    div {
        padding: 0 !important;
    }
    .header-operate-lt {
        flex: 1;
        display: flex;
        align-items: center;
    }
    .header-operate-rt {
        display: flex;
        align-items: center;
    }
    .set-icon {
        display: block;
        width: 20px;
        height: 20px;
        background: url(@/assets/Common/col-set-icon.png) center center no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
    }
    .filter-icon {
        position: relative;
        width: 18px;
        height: 18px;
        cursor: pointer;
    }
    .table-filter-fill {
        fill: #999;
    }
    .header-caret {
        margin-left: 5px;
        width: 15px;
        height: 15px;
        cursor: pointer;
        background: url(@/assets/ERecord/sortIcon.svg) center center no-repeat;
        background-size: 100% 100%;
        &.ascending {
            background: url(@/assets/ERecord/sortIconUp.svg) center center no-repeat;
            background-size: 100% 100%;
        }
        &.descending {
            background: url(@/assets/ERecord/sortIconDown.svg) center center no-repeat;
            background-size: 100% 100%;
        }
    }
}   

.select-checkbox {
    &.placeTop {
        .check-box {
            top: -1px;
            border-top: 1px solid var(--border-color);
            transform: translateY(-100%);
        } 
    }
}
.el-table.el-table--scrollable-x {
    .el-table-fixed-column--left.is-last-column:before {
        box-shadow: inset 8px 0 10px -10px rgba(0, 0, 0, 0.3) !important;
    }
    .el-table-fixed-column--right.is-first-column:before {
        box-shadow: inset -8px 0 10px -10px rgba(0, 0, 0, 0.3) !important;
    }
    &.is-scrolling-none {
        .el-table-fixed-column--left.is-last-column:before,
        .el-table-fixed-column--right.is-first-column:before {
            box-shadow: none !important;
        }
    }
}

.date-picker-class {
    &.el-date-editor {
        .el-input__wrapper {
            padding: 1px 8px;
        }
        .el-input__prefix {
            position: absolute;
            right: 0;
        }
        .el-input__prefix-inner>:first-child.el-input__icon {
            margin-right: 4px;
        }
        .el-input__suffix-inner {
            position: absolute;
            right: 20px;
            top: 9px;
        }
    }
}
.dialogDrag {
    height: fit-content;
}
.vedio-icon {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
    background: url("@/assets/Icons/video-blue.png") 100% 100% no-repeat;
}
.noWrap {
    white-space: nowrap;
}