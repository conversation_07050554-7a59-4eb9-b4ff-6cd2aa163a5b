<template>
  <div
    class="checkable-select"
    :class="{ 'is-invalid': !isValid && showError }">
    <el-select
      v-model="innerValue"
      v-bind="$attrs"
      :class="borderClass"
      @change="handleChange"
      @blur="handleBlur"
      @focus="handleFocus">
      <!-- 错误图标 -->
      <template
        v-if="!isValid && showError"
        #prefix>
        <el-tooltip
          effect="light"
          :content="errorMessage">
          <el-icon><WarningFilled /></el-icon>
        </el-tooltip>
      </template>

      <!-- 透传选项插槽 -->
      <slot></slot>
    </el-select>
  </div>
</template>

<script setup lang="ts">
  import { WarningFilled } from "@element-plus/icons-vue"
  import { Rule, useValidate } from "./useValidate"

  // 定义props
  const props = withDefaults(
    defineProps<{
      // 校验相关
      rule?: Rule // 规则对象
      validateTrigger?: string // 全局默认的校验触发时机
    }>(),
    {
      validateTrigger: "blur",
    },
  )

  // 定义事件
  const emits = defineEmits<{
    (e: "update:modelValue", value: any): void
    (e: "change", value: any): void
    (e: "blur", value: any): void
    (e: "focus", value: any): void
  }>()

  // 使用v-model
  const modelValue = defineModel<any>()

  // 使用验证钩子
  const { innerValue, isValid, errorMessage, showError, borderClass, handleChange, handleBlur, handleFocus, validate } = useValidate(
    props,
    emits,
    modelValue,
  )
  // 暴露方法
  defineExpose({
    validate,
    isValid,
    errorMessage,
  })

  // 挂载时校验
  onMounted(() => {
    validate()
  })
</script>

<style lang="scss" scoped>
  .checkable-select {
    width: 100%;
    position: relative;

    .common-border {
      :deep(.el-select__wrapper) {
        box-shadow: 0 0 0 1px var(--el-border-color) inset;

        &:hover {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px var(--el-color-primary) inset;
        }
      }
    }

    .prompt-border {
      :deep(.el-select__wrapper) {
        box-shadow: 0 0 0 1px var(--el-color-warning) inset;

        .el-select__prefix {
          color: var(--el-color-warning);
        }
      }
    }

    .force-border {
      :deep(.el-select__wrapper) {
        box-shadow: 0 0 0 1px var(--el-color-danger) inset;

        .el-select__prefix {
          color: var(--el-color-danger);
        }
      }
    }

    :deep(.el-select) {
      width: 100%;
    }

    :deep(.el-select__prefix) {
      font-size: 16px;
      display: flex;
      align-items: center;
    }
  }
</style>
