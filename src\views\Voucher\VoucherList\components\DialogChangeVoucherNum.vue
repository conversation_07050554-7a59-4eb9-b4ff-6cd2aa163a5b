<template>
    <el-dialog center width="500px" class="custom-confirm dialogDrag" v-model="display" title="凭证号调整" @closed="resetDialogInfo">
        <div class="vnum-change-content" v-dialogDrag>
            <div class="vnum-change-main">
                <div class="vnum-title mb-20 highlight-red">将按照调整后的凭证顺序，重新整理凭证号</div>
                <div class="select-vnum">
                    <span>将选中的凭证：</span>
                    <span v-for="(vnum, index) in vNumList" :key="vnum">
                        <span>{{ vgName + "-" + vnum }}</span>
                        <span v-show="index !== vNumList.length - 1">、</span>
                    </span>
                </div>
                <div class="change-vnum mt-10 mb-10">
                    <span>{{ selectParams.length === 1 ? "调整为" : "插入至" }}</span>
                    <el-input @input="handleInput" @keydown="handleKeyDown" v-model="newVnum" />
                    <span>{{ selectParams.length === 1 ? "号凭证" : "号凭证之前" }}</span>
                </div>
                <div class="warn-tip">
                    <span>此操作不可逆！可先到</span>
                    <span :class="{ link: hasPermission() }" @click="changeCurrentRoute">{{ backupPathName }}</span>
                    <span>中备份账套，再进行操作</span>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleSave">确定</a>
                <a class="button ml-20" @click="display = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { checkPermission } from "@/util/permission";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { handleKeyDown, useNumberInput } from "@/util/format";

import type { IVoucherModel } from "@/views/Voucher/VoucherList/types";

const voucherGroupList = computed(() => useVoucherGroupStore().voucherGroupList);

const newVnum = ref("");
function hasPermission() {
    if (window.isAccountingAgent) return false;
    return checkPermission(["backup-canview"]);
}
const backupPathName = computed(() => {
    if (window.isErp) return "系统设置-备份与恢复";
    if (window.isAccountingAgent) return "代账-记账平台";
    return "设置-备份恢复";
});
function changeCurrentRoute() {
    if (!hasPermission()) return;
    globalWindowOpenPage(window.isErp ? "/backup" : "/Settings/Backup", "备份恢复");
}

interface IRequestParams {
    pid: number;
    vid: number;
    vnum: number;
    vgName: string;
}
const selectParams = ref<Array<IRequestParams>>([]);
const vgName = ref("");
const vNumList = ref<Array<number>>([]);
const display = ref(false);
let vgid = 0;
function resetDialogInfo() {
    newVnum.value = "";
    selectParams.value.length = 0;
    vgName.value = "";
    vNumList.value.length = 0;
    toastDate = "";
    vgid = 0;
}

const emit = defineEmits<{
    (e: "load-data"): void;
}>();
let isSaving = false;
async function handlePreCheck() {
    const params = {
        vgId: vgid,
        prepareBy: "",
        approvalStatus: "-1",
        description: "",
        note: "",
        asubCode: "",
        startAmount: "",
        endAmount: "",
        startVNum: newVnum.value.trim(),
        endVNum: newVnum.value.trim(),
        sortColumn: "0",
        sortType: "0",
        onlyTempVoucher: false,
        searchInfo: "",
        selectorType: "0",
        startPId: selectParams.value[0].pid,
        endPId: selectParams.value[0].pid,
        pageIndex: 1,
        pageSize: 20,
    };
    return request({
        url: "/api/Voucher/PagingList?" + getUrlSearchParams(params),
        method: "get",
    })
        .then((res: IResponseModel<{ data: Array<any>; count: number }>) => {
            if (res.state !== 1000 || res.data.count === 0) return false;
            return res.data.data[0];
        })
        .catch(() => {
            return false;
        });
}
async function handleSave() {
    if (isSaving) return;
    isSaving = true;
    const item = await handlePreCheck();
    const name = newVnum.value.trim();
    if (!item) {
        isSaving = false;
        ElNotify({ type: "warning", message: `${toastDate}不存在${vgName.value}-${name}号凭证，请重新输入哦~` });
        return;
    }
    const params = {
        vgid: vgid,
        pid: selectParams.value[0].pid,
        fromNumIDList: selectParams.value.map((item) => ({ vnum: item.vnum, vid: item.vid })),
        toVNumID: { vnum: item.vnum, vid: item.vid },
    };
    request({ url: "/api/Voucher/VoucherAdjust", method: "post", data: params })
        .then((res: IResponseModel<boolean>) => {
            isSaving = false;
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "调整凭证号失败" });
                return;
            }
            ElNotify({ type: "success", message: "调整凭证号成功" });
            display.value = false;
            emit("load-data");
        })
        .catch(() => {
            isSaving = false;
            ElNotify({ type: "warning", message: "调整凭证号失败" });
        });
}
const handleInput = useNumberInput(newVnum, "ref");

let toastDate = "";
function handleOpenDialog(list: Array<IVoucherModel>, year: number, month: number) {
    toastDate = `${year}年${month}月`;
    const params: Array<IRequestParams> = [];
    const vnums: Array<number> = [];
    for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (i === 0) {
            vgName.value = item.vgName;
            vgid = voucherGroupList.value.find((item) => item.name === vgName.value)?.id || 0;
        }
        vnums.push(item.vnum);
        params.push({ pid: item.pid, vid: item.vid, vnum: item.vnum, vgName: item.vgName });
    }
    vNumList.value = vnums;
    selectParams.value = params;
    display.value = true;
}
defineExpose({ handleOpenDialog });
</script>

<style lang="less" scoped>
.vnum-change-content {
    .vnum-change-main {
        padding: 20px 40px;
        .select-vnum {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            max-width: 400px;
        }
        .change-vnum {
            display: flex;
            align-items: center;
            :deep(.el-input) {
                width: 100px;
                height: 24px;
                margin-left: 5px;
                margin-right: 5px;
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
