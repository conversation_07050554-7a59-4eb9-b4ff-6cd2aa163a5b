<template>
    <div class="main-ecord-top">
        <div class="e-cord-top">
            <div :class="['main-tool-left', { 'buttons-contract': buttonContract }]">
                <SearchInfoContainer ref="containerRef">
                    <template v-slot:title>{{ currentPeriodInfo }}</template>
                    <div class="line-item input first-item">
                        <div class="line-item-title">上传日期：</div>
                        <div class="line-item-field">
                            <datePicker
                                v-model:startPid="startPid"
                                v-model:endPid="endPid"
                                v-model:periodInfo="periodInfo"
                                :disabled-date-start="disabledDate"
                                v-bind:disabled-date-end="disabledEndDate"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">上传人：</div>
                        <div class="line-item-field">
                            <VirtualSelectCheckbox
                                :options="uploadPersonListOptions"
                                :use-el-icon="true"
                                width="298px"
                                v-model:selectedList="uploadPersonSelected"
                                class="item"
                                v-model:forced-shutdown="forcedShutdown"
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">事项：</div>
                        <div class="line-item-field">
                            <el-input
                                v-model="searchInfo.matters"
                                placeholder="请输入"
                                style="width: 298px"
                                @keyup.enter="executeFilter(false)"
                                clearable
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">上传方式：</div>
                        <div class="line-item-field">
                            <VirtualSelectCheckbox
                                :options="uploadTypeOptions"
                                :use-el-icon="true"
                                width="298px"
                                v-model:selectedList="uploadTypeSelected"
                                v-model:forced-shutdown="forcedShutdown"
                                class="item"
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">文件状态：</div>
                        <div class="line-item-field">
                            <el-select 
                                v-model="searchInfo.fileState" 
                                :teleported="false"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                            >
                                <el-option value="-1" label="全部" />
                                <el-option value="0" label="未审核" />
                                <el-option value="1" label="已审核" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input">
                        <div class="line-item-title">单据类型：</div>
                        <div class="line-item-field">
                            <VirtualSelectCheckbox
                                :options="documentTypeOptions"
                                :use-el-icon="true"
                                width="298px"
                                v-model:selectedList="documentTypeSelected"
                                v-model:forced-shutdown="forcedShutdown"
                                class="item"
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">生成凭证：</div>
                        <div class="line-item-field">
                            <el-select 
                                v-model="searchInfo.voucherState" 
                                :teleported="false"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                            >
                                <el-option value="-1" label="全部" />
                                <el-option value="1" label="是" />
                                <el-option value="0" label="否" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">纸质档案：</div>
                        <div class="line-item-field">
                            <el-select 
                                v-model="searchInfo.paperState" 
                                :teleported="false"
                                @blur="forcedShutdown = false"
                                @focus="forcedShutdown = true"
                            >
                                <el-option value="-1" label="全部" />
                                <el-option value="1" label="是" />
                                <el-option value="0" label="否" />
                            </el-select>
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">查验状态：</div>
                        <div class="line-item-field">
                            <VirtualSelectCheckbox
                                :options="checkStatesOptions"
                                :use-el-icon="true"
                                width="298px"
                                v-model:selectedList="checkStatesSelected"
                                v-model:forced-shutdown="forcedShutdown"
                                class="item placeTop"
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">文件名称：</div>
                        <div class="line-item-field">
                            <el-input
                                v-model="searchInfo.fileName"
                                placeholder="请输入"
                                style="width: 298px"
                                clearable
                            />
                        </div>
                    </div>
                    <div class="line-item input long-input">
                        <div class="line-item-title">文件类别：</div>
                        <div class="line-item-field">
                            <VirtualSelectCheckbox
                                :options="fileCategoryOptions"
                                :use-el-icon="true"
                                width="298px"
                                v-model:selectedList="fileCategorySelected"
                                v-model:forced-shutdown="forcedShutdown"
                                class="item placeTop"
                            />
                        </div>
                    </div>
                    <div class="buttons">
                        <a class="button solid-button" @click="executeFilter(false)">确定</a>
                        <a class="button" @click="cancel">取消</a>
                        <a class="button" @click="resetSearch">重置</a>
                    </div>
                </SearchInfoContainer>
                <a v-permission="['spb-canview']" class="button solid-button ml-10 mr-10" @click="spbUpload">收票宝上传</a>
                <Dropdown
                    :btnTxt="'电脑上传'"
                    class="mr-10"
                    :downlistWidth="isErp ? 98 : 86"
                    v-permission="['lemondisk-canimport']"
                    @click="showUploadWin"
                >
                    <li @click="showUploadMobile($event)">手机上传</li>
                </Dropdown>
                <Dropdown
                    v-if="checkPermission(['lemondisk-cancheck']) && checkPermission(['lemondisk-canreject'])"
                    :btnTxt="'审核'"
                    class="mr-10"
                    :downlistWidth="isErp ? 76 : 86"
                    @click="handleCheck"
                >
                    <li @click="handleReject($event)">驳回</li>
                </Dropdown>
                <a v-else-if="checkPermission(['lemondisk-cancheck'])" class="button mr-10 file-pass" @click="handleCheck">审核</a>
                <a v-else-if="checkPermission(['lemondisk-canreject'])" class="button mr-10 file-reject" @click="handleReject">驳回</a>
                <Dropdown
                    :btnTxt="'关联凭证'"
                    class="mr-10"
                    :downlistWidth="isErp ? 98 : 86"
                    v-permission="['lemondisk-cancreatevoucher']"
                    @click="relateVoucher"
                >
                    <li @click="unrelateVoucher($event)">取消关联</li>
                </Dropdown>
                <Dropdown
                    class="mr-10"
                    :btnTxt="'生成凭证'"
                    :downlistWidth="104"
                    v-permission="['lemondisk-cancreatevoucher']"
                    @click="attachVoucher"
                >
                    <li @click="attachVoucherTemplate($event)">选择模板生成</li>
                </Dropdown>
                <Dropdown
                    v-if="checkPermission(['lemondisk-canedit'])"
                    :btnTxt="'标记为纸质档案'"
                    class="mr-10 mark"
                    :width="isErp ? 128 : 120"
                    :downlistWidth="isErp ? 128 : 120"
                    @click="handleMark"
                >
                    <li @click="handleUnMark">取消标记</li>
                </Dropdown>
                <Dropdown
                    :btnTxt="'更多'"
                    :downlistWidth="isErp ? 98 : 86"
                    v-permission="['lemondisk-canedit', 'lemondisk-candownload', 'lemondisk-candelete']"
                >
                    <li v-permission="['lemondisk-canedit']" @click="handleMove">移动</li>
                    <li v-permission="['lemondisk-canedit']" @click="() => (newDirDialogShow = true)">新建文件夹</li>
                    <li v-permission="['lemondisk-candownload']" @click="downloadDoc">下载</li>
                    <li v-permission="['lemondisk-candelete']" @click="handleDelete">删除</li>
                </Dropdown>
                <el-checkbox class="ml-10" v-model="isNewName" label="文件名增加凭证号"></el-checkbox>
                <ErpRefreshButton marginLeft="ml-10"></ErpRefreshButton>
            </div>
            <div class="main-tool-right">
                <div class="search-controller">
                    <el-input
                        type="text"
                        class="search-text"
                        placeholder="请输入文件名称/发票号码搜索"
                        v-model="searchText"
                        @input="handleEmptySearch"
                        @keyup.enter="handleSearch"
                        clearable
                    />
                    <div class="search-submit" @click="handleSearch"></div>
                </div>
            </div>
        </div>
        <div class="main-top-refresh">
            <RefreshButton></RefreshButton>
        </div>
    </div>
    <el-dialog
        v-model="uploadFileShow"
        title="上传新文件"
        center
        width="688px"
        class="custom-confirm no-split-line-buttons dialogDrag"
        @close="cancelFileUpload"
    >
        <div class="upload-content" v-dialogDrag>
            <div class="content-body">
                <div class="upload-path-section">
                    <span class="float-l">文件路径：</span>
                    <span style="display: inline-block">
                        <Tooltip :content="filePath" :maxWidth="480" :lineClamp="1" :fontSize="12">
                            <span class="upload-path" :style="{ width: isErp ? '484px' : '' }">{{ filePath }}</span>
                        </Tooltip>
                    </span>
                    <div class="file-button float-r ml-5">
                        <a class="button solid-button" @click="addFiles">新增文件</a>
                    </div>
                </div>
                <div class="upload-files">
                    <div class="upload-files-item" v-for="item in fileNameList" :key="item">
                        <span class="file-icon" :class="'icon-' + addFileTypeImgPath(item.fileType)"></span>
                        <span class="upload-file-name">{{ item.name }}</span>
                        <span class="upload-file-delete" title="删除" @click="deleteFileItem(item.name)"></span>
                    </div>
                </div>
                <div class="bottom">
                    <el-popover
                        placement="right"
                        :width="300"
                        trigger="hover"
                        content="标记为纸档后，可筛选纸质档案查看对应凭证号，便于在整理纸质档案时在纸档上标注对应的凭证号或快速找到审计所需纸质文件"
                    >
                        <template #reference>
                            <div style="display: inline-block">
                                <el-checkbox v-model="paperState" label="标记为纸质档案"></el-checkbox>
                                <img class="tip" src="@/assets/Scm/question.png" />
                            </div>
                        </template>
                    </el-popover>
                </div>
            </div>
            <div class="buttons">
                <span>
                    <a class="button solid-button ml-5" @click="uploadConfirm">确定</a>
                    <a class="button ml-5" @click="cancelFileUpload">取消</a>
                </span>
                <span class="float-r mr-20" v-show="uploadBaseImageShow">
                    <el-checkbox v-model="uploadBaseImage" label="上传原图" />
                </span>
            </div>
        </div>
    </el-dialog>
    <MobileUpload v-model="mobileShow" :fid="fid" ref="mobileUploadRef" @success-upload="handleMobileSucessUpload" />
    <el-dialog v-model="rejectDialogShow" title="驳回" center width="440px" class="custom-confirm dialogDrag">
        <div class="reject-dialog-content" v-dialogDrag>
            <div class="newdir-main">
                <div class="d-text">驳回文件后将退回到对应员工的收票宝，且系统档案中将不再保存，请谨慎操作！</div>
                <div class="d-notice" v-show="dNoticeShow"><span>存在已审核的票据</span></div>
            </div>
            <div v-if="isErp" class="buttons no-border">
                <a class="button solid-button ml-5" @click="rejectFileConfirm">驳回</a>
                <a class="button ml-5" @click="() => (rejectDialogShow = false)">取消</a>
            </div>
            <div v-else class="buttons no-border">
                <a class="button ml-5" @click="() => (rejectDialogShow = false)">取消</a>
                <a class="button solid-button ml-5" @click="rejectFileConfirm">驳回</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="relateVoucherShow" title="关联凭证" center width="440px" class="custom-confirm dialogDrag">
        <div class="relate-voucher-content" v-dialogDrag>
            <div class="form-main">
                <div class="form-item">
                    <span class="form-label">会计期间：</span>
                    <span class="form-content date-picker">
                        <!-- <el-select v-model="accountPeriod" :teleported="false" placeholder=" ">
                            <el-option v-for="item in accountPeriodList" :key="item.value" :value="item.value" :label="item.label" />
                        </el-select> -->
                        <el-date-picker
                            v-model="startMonth"
                            type="month"
                            :disabled-date="disabledDateMonth"
                            :clearable="false"
                            :editable="false"
                            :teleported="false"
                            :value-format="'YYYYMM'"
                            :format="'YYYY年MM月'"
                            style="width: 180px"
                            @change="changeMonth"
                        />
                    </span>
                </div>
                <div class="form-item">
                    <span class="form-label">凭证字号：</span>
                    <span class="form-content">
                        <el-select 
                            v-model="voucherNumber" 
                            :filterable="true" 
                            :teleported="false" 
                            placeholder=" "
                            :filter-method="voucherListFilterMethod"
                        >
                            <template #empty>
                                <span class="pl-10" style="font-size: 14px; padding: 6px 10px 6px 8px; display: inline-block"
                                    >找不到查询结果</span
                                >
                            </template>
                            <el-option 
                                v-for="item in showVoucherList" 
                                :key="item.value" 
                                :value="item.value" 
                                :label="item.label" 
                            />
                        </el-select>
                    </span>
                </div>
            </div>
            <div v-if="isErp" class="buttons">
                <a class="button solid-button" @click="relateVoucherConfirm">关联</a>
                <a class="button" @click="() => (relateVoucherShow = false)">取消</a>
            </div>
            <div v-else class="buttons">
                <a class="button mr-10" @click="() => (relateVoucherShow = false)">取消</a>
                <a class="button solid-button" @click="relateVoucherConfirm">关联</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="attachVoucherTemplateShow" title="生成凭证" center width="440px" class="custom-confirm dialogDrag">
        <div class="relate-voucher-content" v-dialogDrag>
            <div class="form-main">
                <div class="form-item">
                    <span class="form-label">模板名称：</span>
                    <span class="form-content">
                        <Select 
                            v-model="voucherTemplateNumber" 
                            filterable 
                            :teleported="false" 
                            placeholder=" "
                            :remote="true"
                            :remoteShowSuffix="true"
                            :filter-method="voucherTemplateFilterMethod"
                        >
                            <el-option
                                v-for="(item, index) in showVoucherTemplateList"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                                :class="{ hover: index === hoverIndex }"
                                @mouseenter="handleMouseEnter(index)"
                            />
                        </Select>
                    </span>
                </div>
            </div>
            <div v-if="isErp" class="buttons">
                <a class="button solid-button" @click="attachVoucherTemplateConfirm">生成</a>
                <a class="button" @click="() => (attachVoucherTemplateShow = false)">取消</a>
            </div>
            <div v-else class="buttons">
                <a class="button mr-10" @click="() => (attachVoucherTemplateShow = false)">取消</a>
                <a class="button solid-button" @click="attachVoucherTemplateConfirm">生成</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="moveShow" title="移动" center width="440px" class="custom-confirm dialogDrag">
        <div class="movefile-tcontent" v-dialogDrag>
            <div class="movefile-main">
                <div class="movefile-title">文件目录：</div>
                <div class="movefile-content">
                    <div class="tree">
                        <el-tree
                            :data="treeList"
                            ref="treeRef"
                            :indent="21"
                            node-key="id"
                            :expand-on-click-node="false"
                            :default-expanded-keys="[0]"
                            :props="{ label: 'text' }"
                            :highlight-current="true"
                            :current-node-key="treeFid"
                            @node-click="handleNodeClick"
                        >
                        </el-tree>
                    </div>
                </div>
            </div>
            <div v-if="isErp" class="buttons">
                <a @click="moveFileConfirm" class="button solid-button ml-5">确认</a>
                <a class="button ml-5" @click="() => (moveShow = false)">取消</a>
            </div>
            <div v-else class="buttons">
                <a class="button ml-5" @click="() => (moveShow = false)">取消</a>
                <a @click="moveFileConfirm" class="button solid-button ml-5">确认</a>
            </div>
        </div>
    </el-dialog>
    <new-dir-dialog v-model="newDirDialogShow" :fid="fid" @reloadTableData="reloadTableData"> </new-dir-dialog>
    <file-audit-dialog v-model="fileAuditDialogShow" @force-audit="checkOrRejecthandle(1, 1)" @skip-audit="checkOrRejecthandle(1, 2)">
    </file-audit-dialog>

    <el-dialog v-model="deleteDialogShow" title="删除" :close-on-click-modal="false" center width="440px" class="custom-confirm dialogDrag">
        <div class="reject-dialog-content" v-dialogDrag>
            <div class="newdir-main">
                <div class="d-text">请确认是否删除选中文件？</div>
                <div class="d-notice" v-show="dNoticeShow"><span>存在已审核的票据</span></div>
            </div>
            <div v-if="isErp" class="buttons">
                <a class="button solid-button ml-5" @click="deleteConfirm">删除</a>
                <a class="button ml-5" @click="() => (deleteDialogShow = false)">取消</a>
            </div>
            <div v-else class="buttons">
                <a class="button ml-5" @click="() => (deleteDialogShow = false)">取消</a>
                <a class="button solid-button ml-5" @click="deleteConfirm">删除</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onUnmounted, onMounted, watchEffect } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getGlobalToken } from "@/util/baseInfo";
import {
    formatFileState,
    convertOptionsString1,
    convertOptionsString2,
    checkFilesState,
    hasAttachOfCheckFiles,
    checkFileUpload,
    hasJournalOfCheckFiles,
    allAttachOfCheckFiles,
    addFileTypeImgPath,
    uploadTypeOptions,
    documentTypeOptions,
    checkStatesOptions,
    fileCategoryOptions,
    fileStateList,
    voucherStateList
} from "../utils";
import Select from "@/components/Select/index.vue";
import type { ITree, ISearchData, IFileTableItem, OptionObj1, OptionObj2, IhandleSearchParams, IFSearchItem } from "../types";
import { useLoading } from "@/hooks/useLoading";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import datePicker from "@/components/DatePicker/index.vue";
import { ElConfirm } from "@/util/confirm";
import { globalExport, globalWindowOpenPage } from "@/util/url";
import NewDirDialog from "./NewDirDialog.vue";
import { onBeforeRouteLeave } from "vue-router";
import FileAuditDialog from "./FileAuditDialog.vue";
import { debounce } from "../utils";
import { tryShowPayDialog } from "@/util/proPayDialog";
import Tooltip from "@/components/Tooltip/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import MobileUpload from "@/views/ERecord/components/MobileUpload.vue";
import { checkPermission } from "@/util/permission";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";
import { getFileErrorTip, showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import VirtualSelectCheckbox from "@/components/VirtualSelectCheckbox/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import { dayjs } from "element-plus";

import { isInWxWork } from "@/util/wxwork";
import { commonFilterMethod } from "@/components/Select/utils";

const isThirdPart = ref(useThirdPartInfoStoreHook().isThirdPart);
const props = defineProps({
    startDate: { type: String, required: true },
    endDate: { type: String, required: true },
    fid: { type: Number, required: true },
    filePath: { type: String, required: true },
    selectedList: { type: Array<IFileTableItem>, required: true },
    treeList: { type: Array<ITree>, required: true },
    miniDate: { type: String, required: true },
    maxDate: { type: String, required: true },
    isNewName: { type: Boolean, required: true },
    filterSearchInfo: {type: Object, required: true},
    restSpace: { type: Number, required: true },
});

const emit = defineEmits<{
    (e: "update:startDate", date: string): void;
    (e: "update:endDate", date: string): void;
    (e: "update:isNewName", date: boolean): void;
    (e: "get-table-list", date: ISearchData, headerFlag: boolean): void;
    (e: "reload-table-data"): void;
    (e: "handle-search", data: IhandleSearchParams): void;
    (e: "reload-search-date"): void;
}>();
const isErp = ref(window.isErp);
const startPid = computed({
    get() {
        return props.startDate;
    },
    set(value: string) {
        emit("update:startDate", value);
    },
});
const endPid = computed({
    get() {
        return props.endDate;
    },
    set(value: string) {
        emit("update:endDate", value);
    },
});

const isNewName = computed({
    get() {
        return props.isNewName;
    },
    set(value: boolean) {
        localStorage.setItem("isNewName", JSON.stringify(value));
        emit("update:isNewName", value);
    },
});

const forcedShutdown = ref(false);
const filePath = computed(() => props.filePath);
const selectedList = computed(() => props.selectedList);
const treeList = computed(() => props.treeList);

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();

const searchText = ref("");
const uploadFileShow = ref(false);
const uploadBaseImage = ref(false);
const uploadBaseImageShow = ref(false);
const rejectDialogShow = ref(false);
const dNoticeShow = ref(false);
const relateVoucherShow = ref(false);
const accountPeriodList = ref<any[]>([]);
const voucherList = ref<OptionObj2[]>([]);
const accountPeriod = ref("");
const startMonth = ref("");
const voucherNumber = ref("");
const attachVoucherTemplateShow = ref(false);
const voucherTemplateNumber = ref("");
const voucherTemplateList = ref<OptionObj2[]>([]);
const moveShow = ref(false);
const treeFid = ref(0);
const newDirDialogShow = ref(false);
const deleteDialogShow = ref(false);
const fileAuditDialogShow = ref(false);

const uploadPersonList = ref<any[]>([]);
const fileList = ref<File[]>([]);
const fileNameList = ref<any[]>([]);
const currentPeriodInfo = ref("");
const periodInfo = ref("");
const fid = computed(() => props.fid);
let flag = false;
const searchInfo = reactive({
    uploadPerson: "-1",
    matters: "",
    uploadType: "-1",
    fileState: "-1",
    documentType: "-1",
    voucherState: "-1",
    paperState: "-1",
    checkStates: "-1",
    fileName: "",
    fileCategory: "-1",
});
const buttonContract = ref(false);

const handleResize = () => {
    buttonContract.value = window.innerWidth < 1000;
};

const uploadPersonSelected = ref<number[]>([]);
const uploadPersonListOptions = ref<Array<Option>>([]);
const getUploadPersonList = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetUploadPersonList.ashx?r=" + Math.random(),
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    }).then((data: any) => {
        if (data.Succeed) {
            const __personList = JSON.parse(data.Message);
            if (__personList.length > 0) {
                uploadPersonList.value = __personList.map((item: any) => item + "");
                uploadPersonListOptions.value = uploadPersonList.value.map((item, index) => new Option(Number(index), item));
                uploadPersonSelected.value = uploadPersonListOptions.value.map((item) => item.id);
                searchInfo.uploadPerson = "-1";
                searchOptions.uploadPerson = uploadPersonListOptions.value;
            }
        }
    });
};
const uploadTypeSelected = ref<number[]>([0, 1, 2]); 
const documentTypeSelected = ref<number[]>([1, 2, 9, 10, 8, 3, 4, 5, 6, 7, 0]);
const checkStatesSelected = ref([1, 2, 3, 4, 5, 6, 0, 7, 8, 9, 10, 11, 12, 100]);
const fileCategorySelected = ref([0, 1, 2, 3]);
const headerMulSeletVal = reactive({
    uploadPerson: uploadPersonSelected.value,
    uploadType: uploadTypeSelected.value,
    documentType: documentTypeSelected.value,
    checkStates: checkStatesSelected.value,
    fileCategory: fileCategorySelected.value
});
watch(
    () => uploadPersonSelected.value,
    () => {
        if (uploadPersonSelected.value.length === uploadPersonListOptions.value.length) {
            searchInfo.uploadPerson = "-1"; 
        } else {
            searchInfo.uploadPerson = uploadPersonListOptions.value.filter((item)=> 
                uploadPersonSelected.value.includes(item.id)
            ).map((item) => item.name).join();
        }
        headerMulSeletVal.uploadPerson = uploadPersonSelected.value;
    },
    {immediate: true}
)
watch(
    () => uploadTypeSelected.value,
    () => {
        if (uploadTypeSelected.value.length === uploadTypeOptions.length) {
            searchInfo.uploadType = "-1"; 
        } else {
            searchInfo.uploadType = uploadTypeOptions.filter((item)=> 
                uploadTypeSelected.value.includes(item.id)
            ).map((item) => item.id).join();
        }
        headerMulSeletVal.uploadType = uploadTypeSelected.value;
    },
    {immediate: true}
)
watch(
    () => documentTypeSelected.value,
    () => {
        if (documentTypeSelected.value.length === documentTypeOptions.length) {
            searchInfo.documentType = "-1"; 
        } else {
            searchInfo.documentType = documentTypeOptions.filter((item)=> 
                documentTypeSelected.value.includes(item.id)
            ).map((item) => item.id).join();
        }
        headerMulSeletVal.documentType = documentTypeSelected.value;
    },
    {immediate: true}
)
watch(
    () => checkStatesSelected.value,
    () => {
        if (checkStatesSelected.value.length === checkStatesOptions.length) {
            searchInfo.checkStates = "-1"; 
        } else {
            searchInfo.checkStates = checkStatesOptions.filter((item)=> 
                checkStatesSelected.value.includes(item.id)
            ).map((item) => item.id).join();
        }
        headerMulSeletVal.checkStates = checkStatesSelected.value;
    },
    {immediate: true}
)
watch(
    () => fileCategorySelected.value,
    () => {
        if (fileCategorySelected.value.length === fileCategoryOptions.length) {
            searchInfo.fileCategory = "-1"; 
        } else {
            searchInfo.fileCategory = fileCategoryOptions.filter((item)=> 
                fileCategorySelected.value.includes(item.id)
            ).map((item) => item.id).join();
        }
        headerMulSeletVal.fileCategory = fileCategorySelected.value;
    },
    {immediate: true}
)
const searchOptions = reactive({
    uploadPerson: uploadPersonListOptions.value,
    uploadType: uploadTypeOptions,
    documentType: documentTypeOptions,
    checkStates: checkStatesOptions,
    fileCategory: fileCategoryOptions,
    fileState: fileStateList,
    voucherState: voucherStateList,
});
function isKeyOfIFSearchItem(key: string): key is keyof IFSearchItem {  
    return [
        'uploadPerson', 
        'uploadType', 
        'fileState',
        'documentType',
        'voucherState',
        'checkStates',
        'fileCategory',
        'matters',
        'fileName',
    ].includes(key);  
}
function getQueryVal(name: string, headerFlag:boolean) {
    if (isKeyOfIFSearchItem(name)) {  
        const baseInfo = searchInfo[name]; 
        if (!headerFlag) return baseInfo;
        const filterInfo = props.filterSearchInfo[name];
        return filterInfo ? `${baseInfo}${baseInfo ? '|-|': ''}${filterInfo}` : baseInfo;
    }
    return "";
}
function getCommonSelect(name: keyof IFSearchItem, option: Option[],seleted: any[], headerFlag:boolean) {  
    const list1 = seleted;
    let list2 = props.filterSearchInfo[name] as any[];
    if (list2.length > 0 && headerFlag) { 
        if (name === "uploadPerson") {
            list2 = [];
            props.filterSearchInfo[name].forEach((v: any) => {
                list2.push(uploadPersonListOptions.value[v].name);
            });
        }
        if (list1.length === option.length && list2.length === option.length) {  
            return "-1";  
        }  
        const set2 = new Set(list2);  
        const commonList = list1.filter((value:any) => set2.has(value));    
        if (commonList.length) {  
            return commonList.join();  
        } else if (list1.length) {  
            return list1.join();  
        } else {  
            return list2.join();  
        }   
    } else {
        return list1.length === option.length ? "-1" : list1.join();
    } 
} 
const executeFilter = (headerFlag: boolean = false) => {
    if (!startPid.value || !endPid.value) {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    if (startPid.value > endPid.value) {
        ElNotify({ type: "warning", message: "开始日期不能大于结束日期" });
        return false;
    }
    const param: ISearchData = {
        fid: fid.value,
        startDate: startPid.value,
        endDate: endPid.value,
    };
    let personName:any[] = [];
    uploadPersonSelected.value.forEach((v) => {
        personName.push(uploadPersonListOptions.value[v].name);
    });
    searchInfo.uploadPerson = getCommonSelect("uploadPerson", uploadPersonListOptions.value, personName, headerFlag);
    searchInfo.uploadType = getCommonSelect("uploadType", uploadTypeOptions, uploadTypeSelected.value, headerFlag);
    searchInfo.documentType = getCommonSelect("documentType", documentTypeOptions, documentTypeSelected.value, headerFlag);
    searchInfo.checkStates = getCommonSelect("checkStates", checkStatesOptions, checkStatesSelected.value, headerFlag);
    searchInfo.fileCategory = getCommonSelect("fileCategory", fileCategoryOptions, fileCategorySelected.value, headerFlag);
    
    if (headerFlag) {
        searchInfo.voucherState = props.filterSearchInfo.voucherState;
        searchInfo.fileState = props.filterSearchInfo.fileState
    }
    
    if (uploadPersonList.value.length > 0 && searchInfo.uploadPerson != "-1") {
        param.uploadPerson = searchInfo.uploadPerson;
    }
    if (getQueryVal("matters", headerFlag) != "") {
        param.matters = getQueryVal("matters", headerFlag);
    }
    if (getQueryVal("fileName", headerFlag) != "") {
        param.fileName = getQueryVal("fileName", headerFlag);
    }
    if (searchInfo.uploadType != "" && searchInfo.uploadType != "-1") {
        param.uploadType = searchInfo.uploadType;
    }
    if (searchInfo.fileState != "" && searchInfo.fileState != "-1") {
        param.fileState = searchInfo.fileState;
    }
    if (searchInfo.documentType != "" && searchInfo.documentType != "-1") {
        param.documentType = searchInfo.documentType;
    }
    var voucherState = searchInfo.voucherState;
    if (voucherState != "" && voucherState != "-1") {
        param.voucherState = voucherState;
    }
    if (searchInfo.paperState != "" && searchInfo.paperState != "-1") {
        param.isPaperArchives = searchInfo.paperState;
    }
    if (searchInfo.checkStates != "" && searchInfo.checkStates != "-1") {
        param.checkStates = searchInfo.checkStates;
    }
    if (searchInfo.fileCategory != "" && searchInfo.fileCategory != "-1") {
        param.fileCategory = searchInfo.fileCategory;
    }
    if (checkFlagState()) return;
    flag = true;
    emit("get-table-list", param, headerFlag);
};

const cancel = () => {
    flag = false;
    containerRef.value?.handleClose();
};

const resetSearch = () => {
    searchInfo.uploadPerson = "-1";
    searchInfo.matters = "";
    searchInfo.uploadType = "-1";
    searchInfo.fileState = "-1";
    searchInfo.documentType = "-1";
    searchInfo.voucherState = "-1";
    searchInfo.paperState = "-1";
    searchInfo.checkStates = "-1";
    searchInfo.fileName = "",
    searchInfo.fileCategory = "-1",
    uploadPersonSelected.value = uploadPersonListOptions.value.map((item) => item.id);
    uploadTypeSelected.value = uploadTypeOptions.map((item)=>item.id);
    documentTypeSelected.value = documentTypeOptions.map((item)=>item.id);
    checkStatesSelected.value = checkStatesOptions.map((item)=>item.id);
    fileCategorySelected.value = fileCategoryOptions.map((item)=>item.id);
    emit("reload-search-date");
};
const setInfos = () => (currentPeriodInfo.value = periodInfo.value);
const handleEmptySearch = () => {
    searchText.value.trim() === "" && emit("handle-search", { date: "", fileType: "-1", word: "" });
};
const handleSearch = () => {
    if (searchText.value.trim() === "") {
        ElNotify({ type: "warning", message: "请输入查询的内容" });
        return;
    }
    const date = "";
    const fileType = "-1";
    emit("handle-search", { date, fileType, word: searchText.value });
};
const spbUpload = () => globalWindowOpenPage("/MiniProgramBinding/MiniProgramBinding", "收票宝");

defineExpose({ cancel, setInfos, executeFilter, searchOptions, headerMulSeletVal });

const showUploadWin = () => {
    request({
        url: window.jLmDiskHost + "/Services/Commodity/CheckPayAccountSet.ashx",
        method: "post",
    }).then((r: any) => (uploadBaseImageShow.value = r == "True"));
    uploadFileShow.value = true;
};
const mobileShow = ref(false);
const mobileUploadRef = ref<any>(null);
const showUploadMobile = (event: any) => {
    event.stopPropagation();
    mobileUploadRef.value.getQRCode();
    mobileShow.value = true;
};
const handleMobileSucessUpload = () => {
    ElNotify({ type: "success", message: "手机上传成功" });
    emit("reload-table-data");
};
const getAccountPeriodList = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetSearchPeriod.ashx",
        method: "get",
    }).then((res: any) => {
        accountPeriodList.value = convertOptionsString1(res).map((item) => {
            let end = item.label.length === 8 ? 7: 6;
            return { 
                ...item, 
                time: item.label.slice(0, 4) + "" + item.label.slice(5, end).padStart(2, "0")
            };
        });
        accountPeriod.value = accountPeriodList.value.find((item) => item.selected)?.value || (accountPeriodList.value[0].value ?? "");
        startMonth.value = accountPeriodList.value.find((item) => item.selected)?.time || (accountPeriodList.value[0].time ?? "");
        getvoucherList();
    });
};
const getvoucherList = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetVoucherNums.ashx?pId=" + accountPeriod.value,
        method: "get",
    }).then((res: any) => {
        voucherList.value = convertOptionsString2(res);
        voucherNumber.value = voucherList.value[0]?.value ?? "";
    });
};
watch(accountPeriod, getvoucherList);

function disabledDateMonth(time: Date) {
    const start = accountPeriodList.value[accountPeriodList.value.length - 1]?.time ?? new Date();
    const end = accountPeriodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
function changeMonth() {
    accountPeriod.value = accountPeriodList.value.find((item: any) => item.time === startMonth.value)?.value || "";
}

const getvoucherTemplateList = () => {
    request({
        url: window.jLmDiskHost + "/Services/Doc/GetVoucherTemplates.ashx",
        method: "get",
    }).then((res: any) => {
        voucherTemplateList.value = convertOptionsString2(res);
        voucherTemplateNumber.value = voucherTemplateList.value[0].value ?? "";
    });
};

const addFiles = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.multiple = true;
    input.onchange = (event: any) => {
        for (let file of event.target.files) {
            if (fileNameList.value.some((f) => f.name === file.name)) {
                ElNotify({ type: "warning", message: "亲，您已选择相同文件名的文件，请勿重复选择" });
                return;
            }
            fileNameList.value.push({ name: file.name, fileType: file.type });
            fileList.value.push(file);
        }
    };
    input.click();
};
const deleteFileItem = (filename: string) => {
    const index = fileNameList.value.findIndex((item) => item.name === filename);
    fileNameList.value.splice(index, 1);
    fileList.value.splice(index, 1);
};
const uploadConfirmFn = () => {
    if (fileList.value.length === 0) {
        ElNotify({ type: "warning", message: "上传的文件数量不能为0" });
        return;
    }
    let videoExist = false;
    for (let i = 0; i < fileNameList.value.length; i++) {
        const filename = fileNameList.value[i].name;
        if (/(\.mp4|\.avi|\.rmvb|\.mpg|\.mpeg|\.mkv|\.vob|\.rm|\.wmv|\.flv)$/i.test(filename)) {
            videoExist = true;
            break;
        }
    }
    if (videoExist) {
        ElNotify({ type: "warning", message: "无法上传视频类型的文件" });
        return;
    }
    if (props.restSpace <= 0) {
        handleTrialExpired({ msg: ExpiredToBuyDialogEnum.ERecordSpaceNone, needExpired: false });
        return;
    }
    useLoading().enterLoading("努力加载中，请稍候...");
    const formData = new FormData();
    fileList.value.forEach((item) => {
        formData.append("files", item);
    });
    formData.append("fid", fid.value + "");
    formData.append("isBaseImage", String(uploadBaseImage.value));
    formData.append("isPaperArchives", paperState.value ? "True" : "False");
    request({
        url: window.jLmDiskHost + "/Services/Doc/UploadDoc.ashx",
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
        data: formData,
    }).then((data: any) => {
        useLoading().quitLoading();
        if (data.Succeed) {
            uploadFileShow.value = false;
            ElNotify({ type: "success", message: data.Message });
            fileList.value = [];
            fileNameList.value = [];
            emit("reload-table-data");
            window.dispatchEvent(new CustomEvent("reloadERecordFileList"));
        } else {
            if (isThirdPart.value) {
                if (data.Message === "您已不能再上传文件，请续费或者扩充容量") {
                    ElNotify({ type: "warning", message: "会计电子档案空间不足" });
                } else {
                    ElNotify({ type: "warning", message: getFileErrorTip(data.Message ?? "") });
                }
            } else {
                if (data.Message === "您已不能再上传文件，请续费或者扩充容量") {
                    if(window.isAccountingAgent || isInWxWork()){
                        tryShowPayDialog(1, "", "空间不足！开通专业版，立即获取10G超大会计电子档案空间。更多专业版功能如下：", "会计电子档案");
                    } else {
                        handleTrialExpired({ msg: ExpiredToBuyDialogEnum.ERecordSpaceNone, needExpired: false });
                    }
                } else {
                    const message: string = data.Message ?? "亲，上传失败了";
                    ElNotify({ type: "warning", message: getFileErrorTip(message) });
                }
            }
        }
    });
};
const uploadConfirm = debounce(uploadConfirmFn);

const paperState = ref(false);
const cancelFileUpload = () => {
    uploadFileShow.value = false;
    fileNameList.value = [];
    fileList.value = [];
    paperState.value = false;
};

const judjeFileTableSelected = () => {
    if (selectedList.value.length <= 0) {
        ElNotify({ type: "warning", message: "请先勾选文件" });
        return false;
    } else {
        return true;
    }
};
const handleCheck = () => {
    if (!judjeFileTableSelected()) return;
    checkOrRejecthandle(1, 0);
};
const handleReject = (event?: any) => {
    event?.stopPropagation();
    dNoticeShow.value = false;
    if (!judjeFileTableSelected()) return;
    if (hasAttachOfCheckFiles(selectedList.value) && checkFileUpload(selectedList.value, "0")) {
        ElNotify({ type: "warning", message: "财务系统上传/已生成凭证的文件不可驳回！" });
        return;
    }
    if (hasAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "已生成凭证的文件不可驳回！" });
        return;
    }
    if (checkFileUpload(selectedList.value, "0")) {
        ElNotify({ type: "warning", message: "财务系统上传的文件不可驳回！" });
        return;
    }
    const fileState = selectedList.value.map((item) => formatFileState(item.File_State));

    if (fileState.indexOf("已审核") != -1) {
        dNoticeShow.value = true;
    }
    rejectDialogShow.value = true;
};
const rejectFileConfirm = () => {
    Promise.resolve(checkOrRejecthandle(2, 0)).then(() => (rejectDialogShow.value = false));
};
const relateVoucher = () => {
    if (!judjeFileTableSelected()) return;
    if (checkFilesState(selectedList.value) && hasAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "未审核/已生成凭证的文件不可关联凭证！" });
        return;
    }

    if (checkFilesState(selectedList.value)) {
        ElNotify({ type: "warning", message: "未审核的文件不可关联凭证！" });
        return;
    }

    if (hasAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "已生成凭证的文件不可关联凭证！" });
        return;
    }
    var attachRes = hasJournalOfCheckFiles(selectedList.value);
    if (attachRes.length > 0) {
        ElNotify({ type: "warning", message: attachRes + " 关联日记账，不可关联凭证！" });
        return;
    }
    getAccountPeriodList();
    relateVoucherShow.value = true;
};
const relateVoucherConfirm = () => {
    const fids = selectedList.value.map((item) => item.File_Id);
    if (!voucherNumber.value) {
        ElNotify({
            type: "warning",
            message: "请选择凭证!",
        });
        return false;
    }
    request({
        url: window.jLmDiskHost + "/Services/Doc/RelateVoucher.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fids.join(), pId: accountPeriod.value, vId: voucherNumber.value },
    }).then((r: any) => {
        if (r === true) {
            ElNotify({ type: "success", message: "关联成功！" });
            relateVoucherShow.value = false;
            emit("reload-table-data");
            window.dispatchEvent(new CustomEvent("reloadVoucherList"));
        } else {
            ElNotify({ type: "warning", message: "关联失败！" });
        }
    });
};
const unrelateVoucher = (event: any) => {
    event.stopPropagation();
    if (!judjeFileTableSelected()) return;
    if (!allAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "未生成凭证的文件不可取消关联凭证！" });
        return;
    }
    const fids = selectedList.value.map((item) => item.File_Id);
    ElConfirm("亲，确定要取消该文件与凭证的关联关系吗？").then(async (r: any) => {
        if (r) {
            const needToast = await checkNeedToastWithBillAndVoucher(fids.join());
            if (!needToast) {
                unrelateVoucherFn(fids);
                return;
            }
            showDeleteBillOrVoucherConfirm("voucher").then((batchDelete: boolean) => {
                unrelateVoucherFn(fids, batchDelete);
            });
        }
    });
};
function unrelateVoucherFn(fids: string[], isNeedSaveToOther = false) {
    request({
        url: window.jLmDiskHost + "/Services/Doc/UnRelateVoucher.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fids.join(), isNeedSaveToOther },
    }).then((r: any) => {
        if (r === true) {
            ElNotify({ type: "success", message: "取消关联成功！" });
            emit("reload-table-data");
            window.dispatchEvent(new CustomEvent("reloadVoucherList"));
        } else {
            ElNotify({ type: "warning", message: "取消关联失败！" });
        }
    });
}
async function checkNeedToastWithBillAndVoucher(fids: string) {
    return await request({
        url: window.jLmDiskHost + "/Services/Doc/UnRelateNeedSaveToOthe.ashx",
        method: "post",
        params: { fids },
    })
        .then((res: any) => {
            return res === true;
        })
        .catch(() => {
            return false;
        });
}
const attachVoucher = () => {
    if (!judgeCanAttachVoucher()) return;
    let checkedFileids = props.selectedList.map((item) => item.File_Id);
    globalWindowOpenPage(`/Voucher/NewVoucher?fileIds=${checkedFileids.join(",")}`, "新增凭证");
};
const attachVoucherTemplate = (event: any) => {
    event.stopPropagation();
    if (!judgeCanAttachVoucher()) return;
    getvoucherTemplateList();
    attachVoucherTemplateShow.value = true;
};
const attachVoucherTemplateConfirm = () => {
    let checkedFileids = props.selectedList.map((item) => item.File_Id);
    attachVoucherTemplateShow.value = false;
    globalWindowOpenPage(`/Voucher/NewVoucher?fileIds=${checkedFileids.join(",")}&vtid=${voucherTemplateNumber.value}`, "新增凭证");
};
let canMark = true;
function handleMark() {
    if (!canMark) return;
    canMark = false;
    paperAttactOperate(true);
}
function handleUnMark() {
    if (!canMark) return;
    canMark = false;
    paperAttactOperate(false);
}
function paperAttactOperate(mark: boolean) {
    const isPaperArchives = mark ? "True" : "False";
    const successMessage = mark ? "标记成功" : "取消标记成功";
    if (!judjeFileTableSelected()) {
        canMark = true;
        return false;
    }
    const fids = selectedList.value.map((item) => item.File_Id);
    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx?r=" + Math.random(),
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fids.join("-"), submitType: "UpdatePaperArchives", isPaperArchives },
    })
        .then((data: any) => {
            if (data.Succeed) {
                ElNotify({ type: "success", message: successMessage });
                emit("reload-table-data");
            } else {
                ElNotify({ type: "warning", message: data.Message });
            }
        })
        .finally(() => {
            canMark = true;
        });
}
const handleMove = () => {
    if (!judjeFileTableSelected()) return false;
    moveShow.value = true;
};
const handleNodeClick = (treeData: ITree) => (treeFid.value = treeData.id);
const moveFileConfirm = () => {
    if (!treeFid.value) {
        ElNotify({
            type: "warning",
            message: "请选择要移动到的目录",
        });
        return false;
    }

    const fids = [];
    for (let i = 0; i < selectedList.value.length; i++) {
        fids.push(selectedList.value[i].File_Id);
    }

    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fids.join("-"), newFid: treeFid.value, submitType: "Move" },
    }).then((data: any) => {
        if (data.Succeed) {
            moveShow.value = false;
            ElNotify({ type: "success", message: "移动成功" });
            emit("reload-table-data");
        } else {
            ElNotify({ type: "warning", message: data.Message });
        }
    });
};
const reloadTableData = () => {
    emit("reload-table-data");
    newDirDialogShow.value = false;
};
const handleDelete = () => {
    dNoticeShow.value = false;
    if (!judjeFileTableSelected()) return;
    if (checkFileUpload(selectedList.value, "1") || hasAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "收票宝上传的文件/已生成凭证的文件不可删除" });
        return;
    }
    const fileState = selectedList.value.map((item) => formatFileState(item.File_State));

    if (fileState.indexOf("已审核") != -1) {
        dNoticeShow.value = true;
    }
    deleteDialogShow.value = true;
};
const deleteConfirm = () => {
    const fids = selectedList.value.map((item) => item.File_Id);
    request({
        url: window.jLmDiskHost + "/Services/Doc/DeleteDoc.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fids.join("-"), appasid: getGlobalToken() },
    }).then((data: any) => {
        deleteDialogShow.value = false;
        if (data.Succeed) {
            ElNotify({ type: "success", message: "文件删除成功" });
            emit("reload-table-data");
            window.dispatchEvent(new CustomEvent("reloadERecordFileList"));
        } else {
            ElNotify({ type: "warning", message: data.Message });
        }
    });
};
const downloadDoc = () => {
    if (!judjeFileTableSelected()) return;
    const fids = selectedList.value.map((item) => item.File_Id);
    globalExport(
        window.jLmDiskHost +
            `/Services/Doc/DownloadDoc.ashx?fids=${fids.join("-")}&isNewName=${isNewName.value}&appasid=${getGlobalToken()}`
    );
};

// 初始化加载一次
let loadingCount = 0;
watch(periodInfo, () => {
    if (loadingCount > 0) return;
    loadingCount++;
    getUploadPersonList();
    setInfos();
});

const checkFlagState = () => {
    if (flag) ElNotify({ type: "warning", message: "操作正在进行中，请稍后" });
    return flag;
};
const checkOrRejecthandle = (type: 1 | 2, force: 0 | 1 | 2) => {
    const fids = selectedList.value.map((item) => item.File_Id);
    request({
        url: window.jLmDiskHost + "/Services/Doc/FileOperate.ashx?r=" + Math.random(),
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data: { fids: fids.join("-"), audit: type, submitType: "Audit", force: force },
    }).then((data: any) => {
        if (data.Succeed) {
            type === 1 ? ElNotify({ type: "success", message: data.Message }) : ElNotify({ type: "success", message: "文件驳回成功" });
            emit("reload-table-data");
            if (fileAuditDialogShow.value) {
                fileAuditDialogShow.value = false;
            }
        } else {
            if (data.Message === "showDialog") {
                fileAuditDialogShow.value = true;
            } else {
                ElNotify({ type: "warning", message: data.Message });
                fileAuditDialogShow.value = false;
            }
        }
    });
};
const judgeCanAttachVoucher = () => {
    if (!judjeFileTableSelected()) return false;
    if (checkFilesState(selectedList.value) && hasAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "未审核/已生成凭证的文件不可生成凭证！" });
        return false;
    }
    if (checkFilesState(selectedList.value)) {
        ElNotify({ type: "warning", message: "未审核的文件不可生成凭证！" });
        return false;
    }
    if (hasAttachOfCheckFiles(selectedList.value)) {
        ElNotify({ type: "warning", message: "已生成凭证的文件不可生成凭证！" });
        return false;
    }
    const attachRes = hasJournalOfCheckFiles(selectedList.value);
    if (attachRes.length > 0) {
        ElNotify({ type: "warning", message: attachRes + " 关联日记账，不可生成凭证！" });
        return false;
    }
    return true;
};
const disabledDate = (time: Date) => {
    const maxDate = new Date(props.maxDate);
    const minDate = new Date(props.miniDate);
    minDate.setDate(minDate.getDate() - 1);
    return time.getTime() > maxDate.getTime() || time.getTime() < minDate.getTime();
};
const disabledEndDate = (time: Date) => {
    if (startPid.value) {
        // 禁用结束日期早于开始日期的情况
        return time.getTime() + 86400000 < new Date(startPid.value).getTime();
    }
    return false;
};

// 生成凭证下拉框鼠标移上添加hover类名
const hoverIndex = ref<null | number>(null);
const handleMouseEnter = (index: number) => {
    hoverIndex.value = index;
};

onBeforeRouteLeave((to, from, next) => {
    mobileUploadRef.value.clearFinishedTimer();
    next();
});
onMounted(() => {
    window.addEventListener("resize", handleResize);
    window.addEventListener("reloadVoucherList", getvoucherList);
});
onUnmounted(() => {
    window.removeEventListener("reloadVoucherList", getvoucherList);
    window.removeEventListener("resize", handleResize);
});

const showVoucherList = ref<OptionObj2[]>([]);
const showVoucherTemplateList = ref<OptionObj2[]>([]);
watchEffect(() => {
    showVoucherList.value = JSON.parse(JSON.stringify(voucherList.value));
    showVoucherTemplateList.value = JSON.parse(JSON.stringify(voucherTemplateList.value));
})
function voucherListFilterMethod(value: string) {
    showVoucherList.value = commonFilterMethod(value, voucherList.value, 'label');
}
function voucherTemplateFilterMethod(value: string) {
    showVoucherTemplateList.value = commonFilterMethod(value, voucherTemplateList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/ERecord/MainTopBox.less";

.search-controller {
    position: relative;
}
body[erp] {
    .main-tool-left {
        :deep(.mark) {
            &.button.dropdown {
                background-position-x: 112px;
            }
        }
    }
}
.main-tool-left {
    :deep(.mark) {
        &.button.dropdown {
            background-position-x: 106px;
        }
    }
}
.bottom {
    img.tip {
        width: 16px;
        height: 16px;
        margin-left: 5px;
        transform: translateY(1px);
    }
}
.date-picker {
    :deep(.el-date-editor) {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
        & .el-input__suffix-inner {
            position: absolute;
            right: 30px;
            top: 9px;
        }
    }
    :deep(.el-month-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
    :deep(.el-year-table) {
        td.disabled.today .cell {
            color: #a8abb2 !important;
        }
        td.current:not(.disabled) .cell {
            background-color: var(--main-color);
            color: var(--white) !important;
        }
    }
}
</style>
