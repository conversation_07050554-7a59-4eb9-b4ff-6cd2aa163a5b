import type { VoucherAttachFileModel, VoucherEntryModel } from "../Voucher/types";

export interface BaseQueryParameters {
    isMerge: boolean;
}

export class InvoiceQueryParameters implements BaseQueryParameters {
    isMerge: boolean = false;
    autoMatch: boolean = true;
    autoMatchStock: boolean = true;
    autoAIGenerate: boolean = false;
    mergeDate: boolean = false;
    mergeOpposite: boolean = false;
    mergeInvoiceType: boolean = false;
    mergeBusinessType: boolean = false;
    mergeOthers: boolean = false;
    mergeDebit: boolean = false;
    mergeCredit: boolean = false;
}

export class JournalQueryParameters implements BaseQueryParameters {
    isMerge: boolean = false;
    mergeAccount: boolean = false;
    mergeOthers: boolean = false;
    autoMatch: boolean = false;
    autoAIGenerateOpposite: boolean = false;
    mergeDate: boolean = false;
    mergeIEType: boolean = false;
    mergeDirection: boolean = false;
    mergeCredit: boolean = false;
    mergeDebit: boolean = false;
}

export class TransferQueryParameters implements BaseQueryParameters {
    isMerge: boolean = false;
    mergeAccount: boolean = false;
    mergeOthers: boolean = false;
    autoMatch: boolean = false;
    mergeDate: boolean = false;
    mergeDirection: boolean = false;
    mergeCredit: boolean = false;
    mergeDebit: boolean = false;
}

export class DraftQueryParameters implements BaseQueryParameters {
    isMerge: boolean = false;
    autoMatch: boolean = true;
}

export class BillQueryParameters implements BaseQueryParameters {
    isMerge: boolean = false;
    autoMatch: boolean = true;
    rootBillType: number = 0;
    mergeOthers: boolean = false;
    mergeDate: boolean = false;
    mergeBillType: boolean = false;
    mergeOpposite: boolean = false;
    mergeDebit: boolean = false;
    mergeCredit: boolean = false;
}
export class ExpenseBillQueryParameters implements BaseQueryParameters {
    isMerge: boolean = false;
    mergeOthers: boolean = false;
    autoMatch: boolean = false;
    mergeDate: boolean = false;
    mergeBillType: boolean = false;
    mergePayMethod: boolean = false;
    mergeCredit: boolean = false;
    mergeDebit: boolean = false;
}
export interface IDocumentModel {
    title: string;
    contentList: Array<string>;
    document: BaseDocumentModel;
    height?: number;
}

export interface IBatchGenerateVoucherModel<T extends DocumentWithVoucherModel<BaseDocumentModel>> {
    documentWithVoucherList: Array<T>;
    temporaryAccountSubjectList: any;
    temporaryAssitEntryList: any;
}

export class BaseDocumentModel {
    //错误信息
    errorInfo: string = "";
    //警告信息
    warnInfo: string = "";
}

export class DocumentWithVoucherModel<T extends BaseDocumentModel> {
    constructor(document: T, vgId: number, vgName: string, voucherLines: Array<VoucherEntryModel>) {
        this.document = document;
        this.vgId = vgId;
        this.vgName = vgName;
        this.voucherLines = voucherLines;
    }
    //单据信息
    document: T;
    //凭证字Id
    vgId: number;
    //凭证字
    vgName: string;
    //凭证行列表
    voucherLines: Array<VoucherEntryModel>;
}

export class InvoiceDocumentModel extends BaseDocumentModel {
    //发票Id
    invoiceId: number = 0;
    //发票类别(销售发票、采购发票)
    invoiceCategory: number = 0;
    //发票类型
    invoiceType: string = "";
    //发票号码
    invoiceNum: string = "";
    //开票日期
    invoiceDate: string = "";
    //业务类型
    businessType: string = "";
    //金额
    amount: number = 0;
    //税额
    tax: number = 0;
    // 客户、供应商id
    nameId: number = 0;
    //客户、供应商名称
    oppositeName: string = "";
}

export class InvoiceWithVoucherModel extends DocumentWithVoucherModel<InvoiceDocumentModel> {
    //凭证附件Id，逗号隔开
    attachFileIds: string = "";
    //凭证附件
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
}

export class JournalDocumentModel extends BaseDocumentModel {
    //日记账日期
    cdDate: string = "";
    //日记账类别
    jtype: number = 0;
    //账户Id
    cdAccount: number = 0;
    //账户名称 编码-名称
    cdAccountName: string = "";
    //创建时间
    createdDate: string = "";
    //序号
    lineSn: number = 0;
    //收支类别名称
    ietypeName: string = "";
    //摘要
    description: string = "";
    //收入
    income: number = 0;
    //支出
    expenditure: number = 0;
}

export class JournalWithVoucherModel extends DocumentWithVoucherModel<JournalDocumentModel> {
    //账户凭证行索引
    accountVoucherLineIndex: number = -1;
    //凭证附件Id，逗号隔开
    attachFileIds: string = "";
    //凭证附件
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
}

export class TransferDocumentModel extends JournalDocumentModel {
    //转入账户
    cdAccountIn: number = 0;
    //转入账户名称 编码-名称
    cdAccountInName: string = "";
}

export class TransferWithVoucherModel extends DocumentWithVoucherModel<TransferDocumentModel> { }

export class DraftDocumentModel extends BaseDocumentModel {
    //票据id
    id: string = "";
    //出票日期
    draftCreatedDate: string = "";
    //票据种类
    draftTypeName: string = "";
    //票据号
    draftNo: string = "";
    //收支类别
    ietype: number = 0;
    //收支类别名称
    ietypeName: string = "";
    //出票人
    drawerName: string = "";
    //金额
    draftAmount: number = 0;
}

export class DraftWithVoucherModel extends DocumentWithVoucherModel<DraftDocumentModel> {
    //凭证附件
    attachFiles: Array<VoucherAttachFileModel> = new Array<VoucherAttachFileModel>();
}

export class BillDocumentModel extends BaseDocumentModel {
    //单据信息
    bill: any;
    //单据号
    billNo: string = "";
    //单据类型
    billTypeText: string = "";
    //金额
    totalAmount: number = 0;
}

export class BillWithVoucherModel extends DocumentWithVoucherModel<BillDocumentModel> { }
export class ExpenseBillDocumentModel extends BaseDocumentModel {
    //单据日期
    billDate: string = "";
    //费用事由
    billDesc: string = "";
    //票据编号
    billNo: string = "";
    //费用类型
    billTypeId: number = 0;
    //费用类别名称
    billTypeName: string = "";
    //单据金额
    tax: string = "";
    //金额
    totalAmount: number = 0;
    // 结算方式
    pmName: string = "";
    pmId: number = 0;
}
export class ExpenseBillWithVoucherModel extends DocumentWithVoucherModel<ExpenseBillDocumentModel> { }

export class GenVoucherModel {
    //凭证号
    vnum: number = 0;
    //附件数
    attachments: number = 0;
    //备注
    note: string = "";
    //凭证Type
    vtype: number = 0;
    //凭证字Id
    vgId: number = 0;
    //凭证字
    vgName: string = "";
    //凭证日期
    vdate: string = "";
    //是否有错误
    isError: boolean = false;
    //凭证行
    voucherLines: Array<VoucherEntryModel> = [];
    //附件Id列表
    attachFileIds: string = "";
}

export class GenVoucherWithDocumentModel<T extends BaseDocumentModel> {
    constructor(vocher: GenVoucherModel, documentList: Array<T>) {
        this.voucher = vocher;
        this.documentList = documentList;
    }
    //凭证参数
    voucher: GenVoucherModel = new GenVoucherModel();
    //单据列表
    documentList: Array<T> = [];
}

export class GenVoucherParameters<T extends BaseDocumentModel> {
    constructor(vouchers: Array<GenVoucherWithDocumentModel<T>>, temporaryAccountSubjectList: any, temporaryAssitEntryList: any) {
        this.vouchers = vouchers;
        this.temporaryAccountSubjectList = temporaryAccountSubjectList;
        this.temporaryAssitEntryList = temporaryAssitEntryList;
    }
    //凭证列表
    vouchers: Array<GenVoucherWithDocumentModel<T>> = [];
    //临时科目
    temporaryAccountSubjectList: any;
    //临时辅助核算条目列表
    temporaryAssitEntryList: any;
}

export class InvoiceGenVoucherParameters extends GenVoucherParameters<InvoiceDocumentModel> { }

export class JournalGenVoucherParameters extends GenVoucherParameters<JournalDocumentModel> { }

export class TransferGenVoucherParameters extends GenVoucherParameters<TransferDocumentModel> { }

export class DraftGenVoucherParameters extends GenVoucherParameters<DraftDocumentModel> { }

export class BillGenVoucherParameters extends GenVoucherParameters<BillDocumentModel> { }

export class ExpenseBillGenVoucherParameters extends GenVoucherParameters<ExpenseBillDocumentModel> { }

export interface IAutoInsertAsub {
    parentAsubName: string;
    asubName: string;
}

export interface IGenVoucherNeedInsertAsub {
    autoInsertAsubList: Array<IAutoInsertAsub>;
    temporaryAccountSubjectList: any;
}

export interface IGenVoucherResult<T extends BaseDocumentModel> {
    count: number;
    errorCount: number;
    errorList: Array<T>;
}

export interface IInvocieGenVoucherResult extends IGenVoucherResult<InvoiceDocumentModel> { }

export interface IJournalGenVoucherResult extends IGenVoucherResult<JournalDocumentModel> { }

export interface ITransferGenVoucherResult extends IGenVoucherResult<TransferDocumentModel> { }

export interface IDraftGenVoucherResult extends IGenVoucherResult<DraftDocumentModel> { }

export interface IBillGenVoucherResult extends IGenVoucherResult<BillDocumentModel> { }

export interface IExpenseBillGenVoucherResult extends IGenVoucherResult<ExpenseBillDocumentModel> { }

export interface IVoucherOperateLog {
    unixTime?: number;
    sourceType?: number;
    operateType: number;
    setting: string;
    content: string;
}