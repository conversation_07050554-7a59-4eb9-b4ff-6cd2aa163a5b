<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { inject, ref, onMounted, computed, onUnmounted, watch, watchEffect } from "vue";
import { popoverHandleCloseKey, updateAsubCode<PERSON>ey, initAsubTreeKey, stopPopoverClose<PERSON>ey } from "./symbols";
import { type ISubjectTree, getAsubTree, formatData, expansionTreeList } from "./util";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { nextTick } from "vue";
// import StatusChange from "@/views/FixedAssets/FixedAssets/components/StatusChange.vue";
import useObserver from "./observe";
import { getGlobalLodash } from "@/util/lodash";

const props = defineProps({
    tabName: {type: Number, required: true},
    asubType: { type: Number, required: true },
    isById: { type: Boolean, default: false },
    isExpansion: { type: Boolean, default: false },
    isByNumberId: { type: Boolean, default: false },
    showTree: { type: Boolean, default: false },
    showDisabled: { type: Boolean, default: false },
    exposeIdAndName: { type: Boolean, default: false },
});

const data = ref(new Array<ISubjectTree>());

const updateAsubCode = inject(updateAsubCodeKey);
const popoverHandleClose = inject(popoverHandleCloseKey) as Function;
const stopPopoverClose = inject(stopPopoverCloseKey) as Function;
const emptyText = ref("");

const defaultProps = {
    children: "children",
    label: props.isExpansion ? "label" : "text",
};

function handleNodeClick(item: ISubjectTree) {
    if (updateAsubCode !== undefined) {
        if (props.exposeIdAndName) {
            updateAsubCode(item.id, props.isExpansion ? item.label : item.text);
        } else {
            props.isById
                ? updateAsubCode(item.id)
                : props.isByNumberId
                ? updateAsubCode(item.id)
                : updateAsubCode(item.attributes.code, item.attributes.fullName);
        }
    }
    if (popoverHandleClose !== undefined) {
        popoverHandleClose();
    }
    if (stopPopoverClose) {
        stopPopoverClose();
    }
}
function showLoading() {
    emptyText.value = "数据加载中...";
}
function hideLoading() {
    emptyText.value = "";
}
let startTime: number; //请求开始
let endTime: number; //请求结束
let minTime: number = 500; //最小展示加载中的时长
const maxSubjectNumber = ref(window.maxSubjectNumber);
const treeLength = computed(() => {
    return useAccountSubjectStore().accountSubjectList.length;
});

onMounted(() => {
    const list = document.querySelectorAll(".subject-popover") as unknown as HTMLElement[];
    listRef.value = Array.from(list);
    getAsubTreeData();
    window.addEventListener("modifyaccountSubject", getAsubTreeData);
});
onUnmounted(() => {
    window.removeEventListener("modifyaccountSubject", getAsubTreeData);
    unObserver = null;
});
function getAsubTreeData() {
    const injectValue = inject(initAsubTreeKey); // 用于初始化科目树
    if (injectValue && props.asubType === 1 && injectValue.value.length) {
        nextTick(() => {
            data.value = injectValue.value;
        });
    } else {
        startTime = performance.now();
        getAsubTree(props.asubType, props.showDisabled)
            .then((res: any) => {
                endTime = performance.now();
                setTimeout(() => {
                    if (res.state === 1000) {
                        if (res.data === "") {
                            data.value = [];
                            emptyText.value = "暂无数据";
                            return;
                        }
                        data.value = props.isExpansion
                            ? expansionTreeList(formatData(JSON.parse(res.data)))
                            : formatData(JSON.parse(res.data));
                        emptyText.value = data.value.length === 0 ? "暂无数据" : "  ";
                    }
                    hideLoading();
                }, endTime - startTime);
            })
            .catch((error) => {
                console.log(error);
                ElMessage.error("出现错误，请刷新页面重试");
            });
        setTimeout(() => {
            showLoading();
            if (endTime - startTime < minTime && !data.value.length) {
                emptyText.value = "暂无数据";
            }
        }, minTime);
    }
}
const isErp = ref(window.isErp);

const _ = getGlobalLodash()
const width = ref("100%");
const setWidth = ref("100%");
// 寻找最小宽度
let minWidth = 334;
let scrollTopTree = 0;

const callback = () => {
    width.value = "100%";
    setWidth.value = "100%";
    // 寻找宽度
    let maxWidth = 0;
    let maxPaddingLeft = 0;
    // 获取虚拟树dom
    const treeDom = listRef.value[listIndex.value].querySelector(`#pane-${props.tabName} .scroll-tree .el-tree .el-tree-virtual-list`) ;

    let firstChild;
    function getScrollTopTree() {
        scrollTopTree = treeDom!.scrollTop;
    }
    if (treeDom) {
        firstChild = treeDom.firstElementChild;
        Array.from(
            treeDom.children?.[0]?.children as unknown as HTMLElement[]
        ).forEach((item: HTMLElement) => {
            getWidth(item);
        });
        treeDom.addEventListener(("scroll"), getScrollTopTree);
    }
    if(firstChild) {
        if(firstChild.clientHeight > 300) {  //有竖线滚动条
            minWidth = 334;
            const element = document.querySelector(`#pane-${props.tabName} .scroll-tree .el-scrollbar__wrap`) as HTMLElement;
            if(element) {
                element.style.marginRight = "10px";
            }
        } else {
            minWidth = 344;
        }
    }
    const targetWidth = maxWidth + maxPaddingLeft;
    width.value = _.isNumber(targetWidth) ? (targetWidth > minWidth ? targetWidth : minWidth) + "px" : "100%";
    function getWidth(el: HTMLElement) {
        const elWidthNode = Array.from(el.children).find((item) => {
            return Array.from(item.classList || []).includes("el-tree-node__content");
        }) as HTMLElement;
        if (elWidthNode) {
            const paddingLeftValue =+ getElNodeAttrValue(elWidthNode, "padding-left")?.split("px")?.[0] || 0;
            let elWidthNodeList = elWidthNode?.children || ([] as HTMLElement[]);
            let elWidth = 0;
            // 获取padding
            Array.from(elWidthNodeList).forEach((item) => {
                elWidth += item.clientWidth;
            });
            maxWidth = maxWidth > elWidth ? maxWidth : elWidth;
            maxPaddingLeft = maxPaddingLeft > paddingLeftValue ? maxPaddingLeft : paddingLeftValue;
        }
        if (el.children) {
            Array.from(el.children as unknown as HTMLElement[]).forEach((item: HTMLElement) => {
                    getWidth(item);
            });
        }
    }
    if (width.value.includes("px")) {
        let num = Number(width.value.slice(0, width.value.length-2));
        if (maxWidth + maxPaddingLeft > num) {
            setWidth.value = maxWidth + maxPaddingLeft + 8 + "px";
        } else {
            setWidth.value = width.value;
        }
    }
};
const getElNodeAttrValue = (el: HTMLElement, attrKey: string) => {
  const computedStyles = getComputedStyle(el);
  return computedStyles.getPropertyValue(attrKey) as string;
};
const treeV2Height = ref(300);

const listRef = ref<Array<HTMLElement>>([]);
const listIndex = ref(0);
let unObserver:any;
watchEffect(
    () => {
        if(listRef.value.length > 0) {
            listRef.value.forEach((item: HTMLElement, i) => {
                if(item.style.display !== 'none') {
                    listIndex.value = i;
                    let defaulEl = item.querySelector(`#pane-${props.tabName} .scroll-tree .el-tree`) as HTMLElement;
                    unObserver = useObserver(defaulEl, callback);
                }
            });
        }
    },
    {flush: 'post'}
);
watch(
    ()=>props.showTree,
    (val) => {
        if(val) {
            if(listRef.value.length > 0) {
                for(let i=0; i< listRef.value.length; i++) {
                    if(listRef.value[i].style.display !== 'none') {
                        listIndex.value = i;
                    }
                }
            }
            let defaulEl = listRef.value[listIndex.value].querySelector(`#pane-${props.tabName} .scroll-tree .el-tree`) as HTMLElement;
            unObserver = useObserver(defaulEl, callback);
            requestAnimationFrame(() => {
                const el = listRef.value[listIndex.value].querySelector(`#pane-${props.tabName} .scroll-tree .el-tree .el-tree-virtual-list`) as HTMLElement;
                el && (el.scrollTop = scrollTopTree);
            });
        } else {
            listRef.value[listIndex.value].style.display = 'none';
        }
    }
)
</script>

<template>
    <template v-if="treeLength < maxSubjectNumber">
      <el-scrollbar :always="true" :height="300" class="scrollbar-container">
        <el-tree
            :data="data"
            node-key="id"
            ref="treeRef"
            :indent="21"
            default-expand-all
            :props="defaultProps"
            :expand-on-click-node="false"
            :highlight-current="true"
            :empty-text="emptyText"
            @node-click="handleNodeClick"
        >
            <template #default="{ data }">
                <span class="custom-tree-node">
                    <span :class="data.children ? 'tree-icon tree-folder tree-folder-open' : 'tree-icon tree-file'"></span>
                    <span class="tree-title">{{ props.isExpansion ? data.label : data.text }}</span>
                </span>
            </template>
        </el-tree>
    </el-scrollbar>
</template>
    <template v-else>
        <div class="new-tree-v2">
            <el-scrollbar 
                :always="true" 
                :height="treeV2Height"
                class="scroll-tree" 
            >
                <el-tree-v2
                    :style="{ width: setWidth}"
                    :data="data"
                    node-key="id"
                    ref="treeRef"
                    :indent="21"
                    :height=" (setWidth=== (minWidth+'px') || setWidth=== '100%') ? treeV2Height : (treeV2Height - 10)"
                    default-expand-all
                    :props="defaultProps"
                    :expand-on-click-node="false"
                    :highlight-current="true"
                    :empty-text="emptyText"
                    @node-click="handleNodeClick"
                >
                    <template #default="{ data }">
                        <span class="custom-tree-node">
                            <span :class="data.children ? 'tree-icon tree-folder tree-folder-open' : 'tree-icon tree-file'"></span>
                            <span class="tree-title">{{ data.text }}</span>
                        </span>
                    </template>
                </el-tree-v2>
            </el-scrollbar>
        </div>
    </template>
</template>

<style lang="less" scoped>
.scrollbar-container {
    border: 1px solid var(--border-color);

    :deep(.el-tree) {
        min-width: 100%;
        overflow: visible;
        display: inline-block;
    }

    :deep(.el-scrollbar__bar) {
        right: 1px;
        bottom: 1px;

        &.is-vertical {
            width: 8px;
        }

        &.is-horizontal {
            height: 8px;
        }
    }

    &.erp {
        :deep(.el-scrollbar__view) {
            padding: 4px 0;
        }

        :deep(.el-tree) {
            .tree-title {
                font-size: 13px;
            }
        }
    }
}

:deep(.el-tree-node__content) {
    height: 21px;
    .el-icon {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 21px;
        height: 21px;
        padding: 0;
    }

    &:hover {
        background-color: var(--table-hover-color);
    }

    .custom-tree-node {
        display: flex;
        align-items: center;
    }
}

.tree-icon {
    display: inline-block;
    vertical-align: middle;
    height: 21px;
    width: 21px;
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: center;
}

.tree-icon.tree-folder.tree-folder-open {
    background-image: url(@/assets/icons/folder.png);
}

.tree-icon.tree-file {
    background-image: url(@/assets/Icons/file.png);
}

.tree-title {
    color: var(--font-color);
    font-size: var(--h5);
    line-height: 21px;
    display: inline-block;
    padding-right: 10px;
}

:deep(.el-tree-node) {
    &.is-current {
        .el-tree-node__content {
            background-color: var(--main-color);

            .tree-title {
                color: #fff;
            }

            & ~ .el-tree-node__children {
                .el-tree-node__content {
                    background-color: #fff;
                    color: var(--font-color);

                    .tree-title {
                        color: var(--font-color);
                    }
                }
            }
        }
    }

    &.is-expanded {
        .tree-icon.tree-folder.tree-folder-open {
            background-image: url(@/assets/icons/folder-open.png);
        }
    }
}
.new-tree-v2 {
    width: 346px;
    border: 1px solid var(--border-color);
    box-sizing: border-box;
    :deep(.el-tree) {
        position: static;
        .el-vl__wrapper {
            position: static;
        }
        .el-virtual-scrollbar {
            opacity: 1;
            width: 8px !important;
        }
        .el-tree-node__content .el-icon {
            flex-shrink: 0;
        }
    }
}
:deep(.el-scrollbar) {
    &.scroll-tree {
        .el-scrollbar__bar.is-vertical {
            display: none !important;
        }
    }
}

// 兼容业财样式
body[erp] {
    .tree-icon.tree-file {
        width: 21px;
        height: 21px;
        background: url(@/assets/icons/file-erp.png) no-repeat 0 0;
        background-size: contain;
    }
}
</style>
