<template>
  <div class="status-error">
    <div class="top-tips">
      <span>以下数据因错误原因无法导入 (其他数据已导账成功) ：</span>
      <a
        class="link"
        @click.prevent="handleCopyMsg">
        复制信息
      </a>
    </div>
    <ul class="content-box">
      <li v-if="errorData.count > 0">
        <div class="content-title error-tip">
          未导入数据：{{ errorData.count }}条
          <span
            class="ml-10 link"
            :class="{ disabled: downLoading }"
            @click="downErrorFile">
            下载未导入凭证
          </span>
          <div
            class="loading-icon ml-10"
            v-if="downLoading">
            <img src="@/assets/Settings/loading-icon.png" />
          </div>
        </div>
        <div class="content-text">您可以点击下载未导入凭证，按照文件中的提示修改后，前往凭证列表导入哦！</div>
      </li>
      <li
        v-for="(item, index) in errorData.errorList"
        :key="index">
        <div class="content-title">{{ errorTypeList[item.ErrorType] }}：</div>
        <div class="content-text">{{ item.Message }}；</div>
      </li>
    </ul>
    <div class="buttons">
      <a
        class="button solid-button"
        @click.prevent="back">
        返回
      </a>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from "vue";
  import { ElNotify } from "@/util/notify";
  import type { ImportModeType, IErrorInfo } from "../types";
  import { request } from "@/util/service";
  import { getCookie } from "@/util/cookie";
  import { useAccountSetStore } from "@/store/modules/accountSet";
  import { getFormatterDate } from "@/util/date";
  const props = withDefaults(
    defineProps<{
      errorData: IErrorInfo;
      lastImportMode: ImportModeType;
    }>(),
    {
      lastImportMode: "init",
    }
  );

  const asId = useAccountSetStore().accountSet?.asId || 0;
  const emits = defineEmits<{
    (e: "changeImportMode", val: ImportModeType): void;
    (e: "resetErrorData"): void;
  }>();

  // 错误状态列表
  const errorTypeList = {
    "0": "导入错误",
    "1": "辅助核算类别导入错误",
    "2": "辅助核算项目导入错误",
    "4": "科目导入错误",
    "8": "期初导入错误",
    "16": "凭证导入错误",
    "32": "资金导入错误",
    "64": "发票导入错误",
    "128": "固定资产导入错误",
  };

  // 复制错误原因
  const handleCopyMsg = () => {
    let text = "";
    for (let item of props.errorData.errorList) {
      text += `${errorTypeList[item.ErrorType]}：${item.Message}；\n`;
    }
    navigator.clipboard.writeText(text);
    return ElNotify({ type: "success", message: "复制成功" });
  };
  const downLoading = ref(false);
  const downErrorFile = () => {
    if (downLoading.value) return;
    const appAsId = props.errorData.appAsId;
    downLoading.value = true;
    request({
      url: `${window.importFromOther}/api/AccImport/downloadErrorVoucherDataFile`,
      method: "get",
      headers: {
        Asid: asId,
        Authorization: `Bearer ${getCookie("ningmengcookie")}`,
      },
      responseType: "blob",
      params: {
        asid: appAsId,
      },
    })
      .then((res: any) => {
        const currentDate = getFormatterDate("", "");
        const fileName = `${props.errorData.asName}_未导入凭证_${currentDate}.xlsx`;

        const blob = new Blob([res], { type: res.type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      })
      .finally(() => {
        downLoading.value = false;
      });
  };
  const back = () => {
    emits("resetErrorData");
    emits("changeImportMode", props.lastImportMode);
  };
</script>
<style lang="less" scoped>
  .status-error {
    width: 840px;
    height: 410px;
    padding-top: 30px;
    margin: 0 auto;
    .top-tips {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      color: #333;
      line-height: 20px;
    }
    .content-box {
      width: 840px;
      height: 450px;
      padding: 20px 24px;
      box-sizing: border-box;
      margin-top: 10px;
      background: #fffafa;
      border-radius: 2px;
      border: 1px solid #ff8a8a;
      list-style: none;
      text-align: left;
      overflow-y: auto;
      li {
        margin-bottom: 5px;
        & > div {
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          text-align: left;
        }
      }
      .content-title {
        font-weight: 700;
        line-height: 30px;
        &.error-tip {
          display: flex;
          align-items: center;
          .loading-icon {
            width: 20px;
            height: 20px;
            img {
              width: 100%;
              animation: spin 3s linear infinite;
            }
          }
        }
      }
    }
  }
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
</style>
