<template>
  <div class="content">
    <ContentSlider
      :slots="slots"
      :currentSlot="currentSlot">
      <template #main>
        <div class="main-content">
          <div class="tabs-header">
            <el-tabs
              :model-value="activeTabName"
              lazy>
              <template #right>
                <div class="tab-right-title">
                  <span>发票税款所属期：{{ formatYearMonthToChinese(invoiceTaxPeriod) }}</span>
                </div>
              </template>
              <el-tab-pane
                label="统计确认表"
                name="statConfirmTable">
                <div class="main-top space-between">
                  <div class="main-tool-left">
                    <div class="tax-period mr-10">
                      <span>发票税款所属期：</span>
                      <el-date-picker
                        type="month"
                        v-model="invoiceTaxPeriod"
                        format="YYYYMM"
                        value-format="YYYYMM"
                        placeholder="选择税款所属日期"
                        class="date-picker" />
                    </div>
                    <a class="button">查询</a>
                  </div>
                  <div class="main-tool-right">
                    <a class="button mr-10">打印</a>
                    <a class="button">下载发票明细</a>
                  </div>
                </div>
                <div class="main-content-body">
                  <div>
                    <div class="steps-block-title">增值税申报进行抵扣汇总</div>
                    <LMTable
                      :data="deductionSummaryData"
                      :columns="deductionSummaryColumns"
                      rowKey="invoiceType"></LMTable>
                    <div class="steps-block-title">抵扣类勾选统计结果</div>
                    <LMTable
                      :data="deductionStatisticsData"
                      :columns="deductionStatisticsColumns"
                      rowKey="invoiceType"></LMTable>
                    <div class="steps-block-title">农产品加计扣除勾选统计结果</div>
                    <LMTable
                      :data="deductionStatisticsData"
                      :columns="agriculturalDeductionColumns"
                      rowKey="invoiceType"></LMTable>
                    <div class="steps-block-title">异常凭证转入统计表</div>
                    <LMTable
                      :data="deductionStatisticsData"
                      :columns="deductionStatisticsColumns"
                      rowKey="invoiceType"></LMTable>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane
                label="已抵扣勾选记录"
                name="deductionRecords">
                <Selection
                  :selType="'deductionRecords'"
                  :invoiceTaxPeriod="invoiceTaxPeriod"></Selection>
              </el-tab-pane>
              <el-tab-pane
                label="不抵扣勾选记录"
                name="nonDeductionRecords">
                <Selection
                  :selType="'nonDeductionRecords'"
                  :invoiceTaxPeriod="invoiceTaxPeriod"></Selection>
              </el-tab-pane>
            </el-tabs>
            <div
              class="header-right"
              style="display: flex">
              <span>发票税款所属期：{{ formatYearMonthToChinese(invoiceTaxPeriod) }}</span>
              <component
                :is="renderTooltip(invoiceTaxPeriodTip)"
                :key="invoiceTaxPeriodTip" />
            </div>
          </div>
        </div>
      </template>
    </ContentSlider>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "CertificationResults", // 必须设置组件名称
  })
  import { useTooltip } from "../../../hooks/useTooltip"
  import { request, IResponseModel } from "@/utils/service"
  import { getAddTaxMonthDeclarationDeadline } from "@/api/taxDeclaration"
  import { formatYearMonthToChinese } from "@/utils/format"

  const Selection = defineAsyncComponent(() => import("../components/Selection.vue"))
  const slots = ["main"]
  const currentSlot = ref("main")
  const activeTabName = ref("statConfirmTable")
  const { renderTooltip } = useTooltip()
  const invoiceTaxPeriod = ref("")
  // 勾选截止日期
  const invoiceSelectDeadline = ref("")
  function getInvoiceTaxPeriod() {
    return request({
      url: "api/invoice-selection/period",
    }).then((res: IResponseModel<string>) => {
      if (res.state === 1000) {
        invoiceTaxPeriod.value = res.data
      }
      return res
    })
  }
  function getDeadline() {
    return getAddTaxMonthDeclarationDeadline().then((res) => {
      if (res.state === 1000) {
        invoiceSelectDeadline.value = res.data.endDate
      }
      return res
    })
  }

  Promise.all([getInvoiceTaxPeriod(), getDeadline()]).catch((error) => {
    console.error("加载数据失败:", error)
  })

  const invoiceTaxPeriodTip = computed(() => {
    if (!invoiceTaxPeriod.value || !invoiceSelectDeadline.value) return ""
    const year = parseInt(invoiceTaxPeriod.value.substring(0, 4))
    const month = invoiceTaxPeriod.value.substring(4, 6)
    return `<span>当前可勾选发票的开票日期范围：${year}-${month}-01至${year}-${month}-${new Date(year, parseInt(month), 0).getDate()}</span></br>
      <span>勾选操作的截止日期：${invoiceSelectDeadline.value.slice(0, 11)}</span>`
  })

  const deductionSummaryColumns = [
    {
      label: "进项抵扣类型",
      prop: "invoiceType",
      minWidth: 200,
    },
    {
      label: "发票份数",
      prop: "invoiceCount",
      minWidth: 100,
    },
    {
      label: "金额",
      prop: "invoiceAmount",
      minWidth: 100,
    },
    {
      label: "税额",
      prop: "taxAmount",
      minWidth: 100,
    },
  ]
  const deductionSummaryData = ref([
    {
      invoiceType: "本期认证相符的增值税专用发票",
      invoiceCount: 10,
      invoiceAmount: 1000,
      taxAmount: 170,
    },
    {
      invoiceType: "海关进口值税缴款书",
      invoiceCount: 5,
      invoiceAmount: 500,
      taxAmount: 85,
    },
  ])

  const deductionStatisticsColumns = [
    {
      label: "发票类型",
      prop: "invoiceType",
      minWidth: 200,
    },
    {
      label: "合计份数",
      prop: "invoiceCount",
      minWidth: 100,
    },
    {
      label: "有效抵扣税额合计",
      prop: "invoiceAmount",
      minWidth: 100,
    },
    {
      label: "出口转内销证明份数",
      prop: "taxAmount",
      minWidth: 100,
    },
    {
      label: "出口转内销证明有效抵扣税额合计",
      prop: "taxAmount",
      minWidth: 100,
    },
    {
      label: "合计份数",
      prop: "taxAmount",
      minWidth: 100,
    },
    {
      label: "其他发票有效抵扣税额合计",
      prop: "taxAmount",
      minWidth: 100,
    },
  ]

  const deductionStatisticsData = ref([
    {
      invoiceType: "增值税专用发票",
      invoiceCount: 10,
      invoiceAmount: 1000,
      taxAmount: 170,
    },
    {
      invoiceType: "收购发票",
      invoiceCount: 5,
      invoiceAmount: 500,
      taxAmount: 85,
    },
  ])

  const agriculturalDeductionColumns = [
    {
      label: "发票类型",
      prop: "invoiceType",
      minWidth: 200,
    },
    {
      label: "正数发票",
      minWidth: 100,
      children: [
        {
          label: "发票份数",
          prop: "invoiceCount",
          minWidth: 100,
        },
        {
          label: "有效抵扣税额合计",
          prop: "invoiceAmount",
          minWidth: 100,
        },
      ],
    },
    {
      label: "负数发票",
      minWidth: 100,
      children: [
        {
          label: "发票份数",
          prop: "invoiceCount",
          minWidth: 100,
        },
        {
          label: "有效抵扣税额合计",
          prop: "invoiceAmount",
          minWidth: 100,
        },
      ],
    },
  ]
</script>

<style scoped lang="scss">
  .tabs-header {
    position: relative;
    height: 100%;

    .header-right {
      position: absolute;
      right: 10px;
      top: 12px;
    }
  }
</style>
