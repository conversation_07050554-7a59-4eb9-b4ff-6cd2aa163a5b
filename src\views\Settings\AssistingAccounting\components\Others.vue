<template>
    <searchView
        @handle-new="handleNew"
        @handle-search="btnSearch"
        @handle-clear="handleClear"
        @handle-export="handleExport"
        @handle-import="handleImport"
    />
    <div class="main-center">
        <Table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :page-is-show="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :scrollbar-show="true"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccount-canedit']" class="link" @click="handleEdit(scope.row.aaeId)"> 编辑 </a>
                            <a v-permission="['assistingaccount-candelete']" class="link" @click="handleDelete(scope.row.aaeId)"> 删除 </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { deleteHandle, clearHanele, exportHandle, getModelApi } from "../utils";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import searchView from "./SearchBox.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AssitOthers";
interface ITableItem {
    value01: string;
    value02: string;
    value03: string;
    value04: string;
    value05: string;
    value06: string;
    value07: string;
    value08: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
interface IEditModelItem {
    asId: number;
    aaType: number;
    aaeId: number;
    note: string;
    aaAcronym: string;
    value01: string;
    value02: string;
    value03: string;
    value04: string;
    value05: string;
    value06: string;
    value07: string;
    value08: string;
    displayOrder: number;
    createdBy: number;
    createdDate: string;
    aaNum: string;
    aaName: string;
    status: number;
    uscc: string;
}

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const props = defineProps<{
    aaType: number;
    rows: any;
}>();

const tableData = ref<ITableItem[]>([]);
const defineColumn = ref<string[]>([]);
const columns = ref<IColumnProps[]>([]);
const loading = ref(false);
const emit = defineEmits(["handleNew", "handleEdit", "handleImport", "handleCancel"]);

const handleInit = () => {
    setTimeout(() => {
        defineColumn.value = Object.keys(props.rows).filter((key) => {
            if (key.includes("column") && props.rows[key].length !== 0) {
                return key;
            }
        });
        const avaWidth = 980 / (defineColumn.value.length + 7);
        const columnsStart: IColumnProps[] = [
            { label: "编码", prop: "aaNum", align: "left", headerAlign: "left", minWidth: avaWidth, width: getColumnWidth(setModule, "aaNum") },
            { label: "名称", prop: "aaName", align: "left", headerAlign: "left", minWidth: avaWidth * 3, width: getColumnWidth(setModule, "aaName") },
            { label: "助记码", prop: "aaAcronym", align: "left", headerAlign: "left", minWidth: avaWidth, width: getColumnWidth(setModule, "aaAcronym") },
        ];
        const columnsEnd: IColumnProps[] = [
            { label: "备注", prop: "note", align: "left", headerAlign: "left", minWidth: avaWidth * 0.8, width: getColumnWidth(setModule, "note") },
            { slot: "operator", minWidth: avaWidth * 1.2 },
        ];
        defineColumn.value.forEach((item) => {
            columnsStart.push({
                label: props.rows[item],
                prop: "value0" + item[item.length - 1],
                align: "left",
                headerAlign: "left",
                minWidth: avaWidth,
                width: getColumnWidth(setModule, "value0" + item[item.length - 1])
            });
        });
        columns.value = columnsStart.concat(columnsEnd);
    }, 0);
};
const searchStr = ref("");
const btnSearch = (searchVal: string) => {
    searchStr.value = searchVal;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleSearch = (successBack?: Function) => {
    loading.value = true;
    const params = {
        aaType: props.aaType,
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        searchStr: searchStr.value,
    };
    request({ url: "/api/AssistingAccounting/PagingCustomList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                successBack && successBack();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleNew = () => {
    emit("handleNew", props.aaType);
};
const handleEdit = (aaeID: string | number) => {
    getModelApi(aaeID, props.aaType).then((res: IResponseModel<IEditModelItem>) => {
        if (res.state == 1000) {
            const data = res.data;
            const params = {
                aaNum: data.aaNum,
                aaName: data.aaName,
                aaeID: data.aaeId,
                Value01: data.value01,
                Value02: data.value02,
                Value03: data.value03,
                Value04: data.value04,
                Value05: data.value05,
                Value06: data.value06,
                Value07: data.value07,
                Value08: data.value08,
                note: data.note,
                status: data.status == 0,
            };
            emit("handleEdit", props.aaType, params);
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const isThisPageHasData = (type: "delete" | "clear") => {
    return function () {
        if (type === "delete") {
            if (paginationData.currentPage !== 1 && tableData.value.length === 1) {
                paginationData.currentPage--;
            } else {
                handleCancel();
            }
        } else {
            if (paginationData.currentPage !== 1) {
                paginationData.currentPage = 1;
            } else {
                paginationData.currentPage = 1;
                handleCancel();
            }
        }
    };
};
const handleCancel = () => emit("handleCancel");
const handleDelete = (aaeID: number) => deleteHandle(props.aaType, aaeID, isThisPageHasData("delete"));
const handleClear = () => clearHanele(tableData.value.length, props.aaType, isThisPageHasData("clear"));
const handleExport = () => exportHandle(props.aaType, paginationData.total);
const handleImport = () => emit("handleImport", props.aaType);

defineExpose({ handleSearch, handleInit });

handleInit();

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});
</script>

<style lang="less" scoped></style>
