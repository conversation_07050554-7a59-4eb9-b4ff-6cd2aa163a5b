<template>
    <div class="add-assist-dialog" v-dialogDrag>
        <template v-if="isCustomer">
            <table cellpadding="0" cellspacing="0">
                <tr>
                    <td><span class="highlight-red">*</span>客户编码：</td>
                    <td>
                        <input type="text" v-model="params.customerModel.custCode" maxlength="18" :disabled="aaNumDisabled" />
                    </td>
                    <td><span class="highlight-red">*</span>客户名称：</td>
                    <td>
                        <el-input v-model="params.customerModel.custName"
                            v-if="asubNameTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('name')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.customerModel.custName,'客户名称', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '客户名称', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.customerModel.custName" :isInput="true" v-else>
                            <input type="text" v-model="params.customerModel.custName" @focus="inputTypeFocus('name')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td>客户类别：</td>
                    <td>
                        <!-- <el-tree-select
                            v-model="params.customerModel.custType"
                            :data="custTypeList"
                            :render-after-expand="false"
                            :fit-input-width="true"
                            filterable="true"
                        ></el-tree-select> -->
                        <Select
                            v-model="params.customerModel.custType"
                            :fit-input-width="true"
                            :filterable="true"
                            :bottom-html="selectBottomHtml"
                            @bottom-click="handleNewType(TypeName.Customer)"
                            :filter-method="custTypeFilterMethod"
                            @visible-change="handleVisibleChange"
                        >
                            <Option 
                                v-for="item in showCustTypeList" 
                                :key="item.value" 
                                :value="item.value" 
                                :label="item.label"
                            ></Option>
                        </Select>
                    </td>
                    <td>客户等级：</td>
                    <td>
                        <Select 
                            v-model="params.customerModel.custLevel" 
                            :filterable="true"
                            :filter-method="custLevelFilterMethod"
                        >
                            <Option 
                                v-for="item in showCustLevelList" 
                                :key="item.id" 
                                :value="item.id" 
                                :label="item.value"
                            ></Option>
                        </Select>
                    </td>
                </tr>
                <tr>
                    <td><span class="highlight-red">*</span>联系人：</td>
                    <td>
                        <input type="text" v-model="params.customerModel.custContact" />
                    </td>
                    <td>联系电话：</td>
                    <td>
                        <input type="text" v-model="params.customerModel.custMobile" />
                    </td>
                </tr>
                <tr>
                    <td>微信/QQ：</td>
                    <td>
                        <input type="text" v-model="params.customerModel.custWechartOrQQ" />
                    </td>
                    <td>销售人员：</td>
                    <td>
                        <Select
                            v-model="params.customerModel.custSeller"
                            :teleported="false"
                            :clearable="true"
                            :IconClearRight="'22px'"
                            :filterable="true"
                            :filter-method="custSellerFilterMethod"
                        >
                           <Option 
                                v-for="item in showCustSellerList" 
                                :key="item.id" 
                                :value="item.id" 
                                :label="item.value"
                            ></Option>
                        </Select>
                    </td>
                </tr>
                <tr>
                    <td>收货人：</td>
                    <td>
                        <input type="text" v-model="params.customerModel.custReceiver" />
                    </td>
                    <td>收货电话：</td>
                    <td>
                        <input type="text" v-model="params.customerModel.custReceiverMobile" />
                    </td>
                </tr>
                <tr>
                    <td class="top">收货地址：</td>
                    <td class="area" colspan="3">
                        <textarea v-model="params.customerModel.custReceiverAddress"></textarea>
                    </td>
                </tr>
            </table>
        </template>
        <template v-if="isVendor">
            <table cellpadding="0" cellspacing="0">
                <tr>
                    <td><span class="highlight-red">*</span>供应商编码：</td>
                    <td>
                        <input type="text" v-model="params.vendorModel.vendCode" maxlength="18" :disabled="aaNumDisabled" />
                    </td>
                    <td><span class="highlight-red">*</span>供应商名称：</td>
                    <td>
                        <el-input v-model="params.vendorModel.vendName"
                            v-if="asubNameTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('name')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.vendorModel.vendName, '供应商名称', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '供应商名称', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.vendorModel.vendName" :isInput="true" v-else>
                            <input type="text" v-model="params.vendorModel.vendName" @focus="inputTypeFocus('name')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td>供应商类别：</td>
                    <td>
                        <!-- <el-tree-select
                            v-model="params.vendorModel.vendType"
                            :data="vendTypeList"
                            :render-after-expand="false"
                            :fit-input-width="true"
                            filterable="true"
                        ></el-tree-select> -->
                        <Select
                            v-model="params.vendorModel.vendType"
                            :fit-input-width="true"
                            :filterable="true"
                            :bottom-html="selectBottomHtml"
                            @bottom-click="handleNewType(TypeName.Vendor)"
                            :filter-method="vendTypeFilterMethod"
                            @visible-change="handleVisibleChange"
                        >
                            <Option 
                                v-for="item in showVendTypeList" 
                                :key="item.value" 
                                :value="item.value" 
                                :label="item.label"
                            ></Option>
                        </Select>
                    </td>
                    <td><span class="highlight-red">*</span>联系人：</td>
                    <td>
                        <input type="text" v-model="params.vendorModel.vendContact" />
                    </td>
                </tr>
                <tr>
                    <td>联系电话：</td>
                    <td>
                        <input type="text" v-model="params.vendorModel.vendMobile" />
                    </td>
                    <td>微信/QQ：</td>
                    <td>
                        <input type="text" v-model="params.vendorModel.vendWechartOrQQ" />
                    </td>
                </tr>
            </table>
        </template>
        <template v-if="isEmployee">
            <table class="single" cellpadding="0" cellspacing="0">
                <tr>
                    <td><span class="highlight-red">*</span>工号：</td>
                    <td>
                        <input
                            type="text"
                            id="txtAddEmployeeCodeForVoucher"
                            maxlength="18"
                            v-model="params.employeeModel.employeeCode"
                            :disabled="aaNumDisabled"
                        />
                    </td>
                </tr>
                <tr>
                    <td><span class="highlight-red">*</span>姓名：</td>
                    <td>
                        <el-input v-model="params.employeeModel.employeeName"
                            v-if="asubNameTextareaShow"
                            id="txtAddEmployeeNameForVoucher"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('name')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.employeeModel.employeeName, '姓名', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '姓名', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.employeeModel.employeeName" :isInput="true" v-else>
                            <input type="text" v-model="params.employeeModel.employeeName" @focus="inputTypeFocus('name')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td>手机号：</td>
                    <td>
                        <input 
                            type="text" 
                            v-model="params.employeeModel.employeeMobile" 
                            id="txtAddEmployeeMobileForVoucher" 
                            @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机号', 'employeeMobile', changeSearchData)"  
                            @paste="handleAAPaste(LimitCharacterSize.Phone, $event)" 
                        />
                    </td>
                </tr>
                <tr>
                    <td class="top">备注：</td>
                    <td class="area">
                        <el-input v-model="params.employeeModel.employeeNote"
                            v-if="noteTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 2.6, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('note')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.employeeModel.employeeNote, '备注', LimitCharacterSize.Note)"
                            @keypress="limitPressLength($event, '备注', LimitCharacterSize.Note)"
                        />
                        <Tooltip :content="params.employeeModel.employeeNote" :isInput="true" v-else>
                            <textarea v-model="params.employeeModel.employeeNote" id="txtAddEmployeeNoteForVoucher" @focus="inputTypeFocus('note')"></textarea>
                        </Tooltip>
                    </td>
                </tr>
            </table>
        </template>
        <template v-if="isDepartment">
            <table class="single" cellpadding="0" cellspacing="0">
                <tr>
                    <td><span class="highlight-red">*</span>部门编码：</td>
                    <td>
                        <input type="text" v-model="params.departmentModel.departmentCode" maxlength="18" :disabled="aaNumDisabled" />
                    </td>
                </tr>
                <tr>
                    <td><span class="highlight-red">*</span>部门名称：</td>
                    <td>
                        <el-input v-model="params.departmentModel.departmentName"
                            v-if="asubNameTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('name')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.departmentModel.departmentName, '部门名称', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '部门名称', LimitCharacterSize.Name)"
                            
                        />
                        <Tooltip :content="params.departmentModel.departmentName" :isInput="true" v-else>
                            <input type="text" v-model="params.departmentModel.departmentName" @focus="inputTypeFocus('name')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td>上级部门：</td>
                    <td>
                        <Select 
                            v-model="params.departmentModel.departmentParent" 
                            style="width: 240px" 
                            :filterable="true"
                            :filter-method="departFilterMethod"
                        >
                            <Option 
                                v-for="item in showDepartmentList" 
                                :key="item.id" 
                                :value="item.id" 
                                :label="item.value"
                            ></Option>
                        </Select>
                    </td>
                </tr>
                <tr>
                    <td class="top">备注：</td>
                    <td class="area">
                        <el-input v-model="params.departmentModel.departmentNote"
                            v-if="noteTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 2.6, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('note')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.departmentModel.departmentNote, '备注', LimitCharacterSize.Note)"
                            @keypress="limitPressLength($event, '备注', LimitCharacterSize.Note)"
                        />
                        <Tooltip :content="params.departmentModel.departmentNote" :isInput="true" v-else>
                            <textarea v-model="params.departmentModel.departmentNote" @focus="inputTypeFocus('note')"></textarea>
                        </Tooltip>
                    </td>
                </tr>
            </table>
        </template>
        <template v-if="isStock">
            <table cellpadding="0" cellspacing="0">
                <tr>
                    <td><span class="highlight-red">*</span>商品编码：</td>
                    <td>
                        <input type="text" v-model="params.stockModel.stockCode" maxlength="18" :disabled="aaNumDisabled" />
                    </td>
                    <td><span class="highlight-red">*</span>商品名称：</td>
                    <td>
                        <el-input v-model="params.stockModel.stockName"
                            v-if="asubNameTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('name')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.stockModel.stockName, '商品名称', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '商品名称', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.stockModel.stockName" :isInput="true" v-else>
                            <input type="text" v-model="params.stockModel.stockName" @focus="inputTypeFocus('name')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td>商品类别：</td>
                    <td>
                        <!-- <el-tree-select
                            v-model="params.stockModel.stockType"
                            :data="stockTypeList"
                            :render-after-expand="false"
                            :fit-input-width="true"
                        ></el-tree-select> -->
                        <Select
                            v-model="params.stockModel.stockType"
                            :fit-input-width="true"
                            :filterable="true"
                            :bottom-html="selectBottomHtml"
                            @bottom-click="handleNewType(TypeName.Stock)"
                            :filter-method="stockTypeFilterMethod"
                            @visible-change="handleVisibleChange"
                        >
                            <Option 
                                v-for="item in showStockTypeList" 
                                :key="item.value" 
                                :value="item.value" 
                                :label="item.label"
                            ></Option>
                        </Select>
                    </td>
                </tr>
                <tr>
                    <td>品牌：</td>
                    <td>
                        <el-input v-model="params.stockModel.stockBrand"
                            v-if="stockBrandTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('brand')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.stockModel.stockBrand, '品牌', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '品牌', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.stockModel.stockBrand" :isInput="true" v-else>
                            <input type="text" v-model="params.stockModel.stockBrand" @focus="inputTypeFocus('brand')" />
                        </Tooltip>
                    </td>
                    <td>规格型号：</td>
                    <td>
                        <el-input v-model="params.stockModel.stockModel"
                            v-if="stockModelTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('model')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.stockModel.stockModel, '规格型号', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '规格型号', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.stockModel.stockModel" :isInput="true" v-else>
                            <input type="text" v-model="params.stockModel.stockModel" @focus="inputTypeFocus('model')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td>首选仓库：</td>
                    <td>
                        <Select 
                            v-model="params.stockModel.stockStore"
                            :filterable="true"
                            :filter-method="stockStoreFilterMethod"
                        >
                            <Option 
                                v-for="item in showStockStoreList" 
                                :key="item.id" 
                                :value="item.id" 
                                :label="item.value"
                            ></Option>
                        </Select>
                    </td>
                    <td>产地：</td>
                    <td>
                        <el-input v-model="params.stockModel.stockOrigin"
                            v-if="stockOriginTextareaShow"
                            ref="custNameInputRef"
                            type="textarea"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            resize="none"
                            @blur="inputTypeBlur('origin')"
                            class='asubName-textarea'  
                            @input="limitInputLength(params.stockModel.stockOrigin, '产地', LimitCharacterSize.Name)"
                            @keypress="limitPressLength($event, '产地', LimitCharacterSize.Name)"
                        />
                        <Tooltip :content="params.stockModel.stockOrigin" :isInput="true" v-else>
                            <input type="text" v-model="params.stockModel.stockOrigin" @focus="inputTypeFocus('origin')" />
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td><span class="highlight-red">*</span>计量单位：</td>
                    <td>
                        <Select 
                            v-model="params.stockModel.stockUnit"
                            :filterable="true"
                            :filter-method="stockUnitFilterMethod"
                        >
                            <Option 
                                v-for="item in showStockUnitList" 
                                :key="item.id" 
                                :value="item.id" 
                                :label="item.value"
                            ></Option>
                        </Select>
                    </td>
                </tr>
            </table>
        </template>
        <div class="buttons">
            <a class="button" @click="emit('cancel')">取消</a>
            <a class="button solid-button ml-10" @click="save()">保存</a>
        </div>
    </div>
    <ErpType
        :title="typeTitle"
        :parentTypeList="parentTypeList"
        :codeType="codeType"
        ref="ErpTypeDialogRef"
        @save-type="saveType"
    ></ErpType>
</template>
<style lang="less" scoped>
@import "@/style/Functions.less";

.add-assist-dialog {
    table {
        margin: 0 auto;
        padding: 30px 0;

        tr {
            td {
                .asubName-textarea{
                    height:32px;
                }
                input{
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                &:nth-child(2n-1) {
                    font-size: var(--font-size);
                    line-height: 32px;
                    color: var(--font-color);
                    width: 98px;
                    max-width: 98px;
                    min-width: 98px;
                    text-align: right;

                    &.top {
                        vertical-align: top;
                    }
                }

                &:nth-child(2n) {
                    .detail-input(200px,32px);
                    width: 200px;
                    max-width: 200px;
                    min-width: 200px;

                    &.area {
                        .asubName-textarea{
                            height:60px;
                        }
                        textarea {
                            width: 100%;
                            height: 60px;
                            padding: 0 10px;
                            color: var(--font-color);
                            font-size: var(--font-size);
                            line-height: 32px;
                            box-sizing: border-box;
                            transition: all 0.2s linear;
                            border: 1px solid var(--input-border-color);
                            resize: none;

                            &:hover {
                                border-color: var(--blue);
                            }

                            &:active {
                                border-color: var(--blue);
                                box-shadow: rgba(140, 197, 255, 0.35) 0px 0px 0px 1px;
                            }
                        }
                    }
                }
            }

            & + tr {
                td {
                    padding-top: 20px;
                }
            }
        }

        &.single {
            tr {
                td {
                    &:nth-child(2n) {
                        .detail-input(240px,32px);
                        width: 240px;
                        max-width: 240px;
                        min-width: 240px;
                    }
                }
            }
        }
    }

    .buttons {
        display: flex !important;
        justify-content: flex-end;
        padding-right: 20px;
    }
}
.depart-option {
    width: 220px;
    word-break: break-all;
}
</style>
<script lang="ts" setup>
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import { reactive, ref, watchEffect, watch } from "vue";
import type { SelectorNode, TreeNode, SelectItem } from "../utils";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { computed, nextTick } from "vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { getUrlSearchParams } from "@/util/url";
import Tooltip from "@/components/Tooltip/index.vue";
import { isNumberOrLetter } from "@/util/validator";
import { LimitCharacterSize, handleAAInput, handleAAPaste } from "@/views/Settings/AssistingAccounting/utils";
import ErpType from "./ErpType.vue";
import { cloneDeep } from "lodash";
import { commonFilterMethod } from "@/components/Select/utils";

const emit = defineEmits<{
    (e: "save-success", data: any): void;
    (e: "save-fail"): void;
    (e: "cancel"): void;
}>();
const assistingAccountingStore = useAssistingAccountingStore();
const aatype = ref(0);
const isCustomer = computed(() => aatype.value === 10001);
const isVendor = computed(() => aatype.value === 10002);
const isEmployee = computed(() => aatype.value === 10003);
const isDepartment = computed(() => aatype.value === 10004);
const isStock = computed(() => aatype.value === 10006);
const params = reactive<{
    customerModel: {
        custCode: string;
        custName: string;
        custType: number | null;
        custLevel: number | null;
        custContact: string;
        custMobile: string;
        custWechartOrQQ: string;
        custSeller: number | null;
        custReceiver: string;
        custReceiverMobile: string;
        custReceiverAddress: string;
    };
    vendorModel: {
        vendCode: string;
        vendName: string;
        vendType: number | undefined;
        vendContact: string;
        vendMobile: string;
        vendWechartOrQQ: string;
    };
    employeeModel: {
        employeeCode: string;
        employeeName: string;
        employeeMobile: string;
        employeeNote: string;
    };
    departmentModel: {
        departmentCode: string;
        departmentName: string;
        departmentParent: number;
        departmentNote: string;
    };
    stockModel: {
        stockCode: string;
        stockName: string;
        stockType: number | undefined;
        stockBrand: string;
        stockModel: string;
        stockStore: number | undefined;
        stockOrigin: string;
        stockUnit: number | undefined;
    };
}>({
    customerModel: {
        custCode: "",
        custName: "",
        custType: null,
        custLevel: null,
        custContact: "",
        custMobile: "",
        custWechartOrQQ: "",
        custSeller: null,
        custReceiver: "",
        custReceiverMobile: "",
        custReceiverAddress: "",
    },
    vendorModel: {
        vendCode: "",
        vendName: "",
        vendType: undefined,
        vendContact: "",
        vendMobile: "",
        vendWechartOrQQ: "",
    },
    employeeModel: {
        employeeCode: "",
        employeeName: "",
        employeeMobile: "",
        employeeNote: "",
    },
    departmentModel: {
        departmentCode: "",
        departmentName: "",
        departmentParent: 0,
        departmentNote: "",
    },
    stockModel: {
        stockCode: "",
        stockName: "",
        stockType: undefined,
        stockBrand: "",
        stockModel: "",
        stockStore: undefined,
        stockOrigin: "",
        stockUnit: undefined,
    },
});
const custTypeList = ref<TreeNode[]>([]);
const custLevelList = ref<SelectorNode[]>([]);
const custSellerList = ref<SelectorNode[]>([]);
const vendTypeList = ref<TreeNode[]>([]);
const departmentList = ref<SelectorNode[]>([]);
const stockTypeList = ref<TreeNode[]>([]);
const stockStoreList = ref<SelectorNode[]>([]);
const stockUnitList = ref<SelectorNode[]>([]);
const aaNumDisabled = ref(false);

const asubNameTextareaShow=ref(false)
const noteTextareaShow=ref(false)
const stockBrandTextareaShow=ref(false)
const stockModelTextareaShow=ref(false)
const stockOriginTextareaShow=ref(false)
const custNameInputRef=ref()

const checkParams = reactive<{
    customerModel: {
        custCode: string;
        custName: string;
    };
    vendorModel: {
        vendCode: string;
        vendName: string;
    };
    departmentModel: {
        departmentParent: number;
    };
    stockModel: {
        stockCode: string;
        stockName: string;
    };
    employeeModel: {
        employeeName: string;
    }
}>({
    customerModel: {
        custCode: "",
        custName: "",
    },
    vendorModel: {
        vendCode: "",
        vendName: "",
    },
    departmentModel: {
        departmentParent: 0,
    },
    stockModel: {
        stockCode: "",
        stockName: "",
    },
    employeeModel: {
        employeeName: "",
    }
});
const inputTypeBlur = (val:string) => {
    switch (val) {
        case 'name':
            asubNameTextareaShow.value = false;
            break;
        case 'note':
            noteTextareaShow.value = false;
            break;
        case 'brand':
            stockBrandTextareaShow.value = false;
            break;
        case 'model':
            stockModelTextareaShow.value = false;
            break;
        case 'origin':
            stockOriginTextareaShow.value = false;
            break;
    }

};

const inputTypeFocus = (val:string) => {
    switch (val) {
        case 'name':
            asubNameTextareaShow.value = true;
            break;
        case 'note':
            noteTextareaShow.value = true;
            break;
        case 'brand':
            stockBrandTextareaShow.value = true;
            break;
        case 'model':
            stockModelTextareaShow.value = true;
            break;
        case 'origin':
            stockOriginTextareaShow.value = true;
            break;    
    }
    nextTick(()=>{
        if(val){
            getTextareaFocus(val)
        }
    })
};
const getTextareaFocus = (val:string) => {
    switch (val) {
    case 'note':
    case 'name':
    case 'brand':
    case 'model':
    case 'origin':
        custNameInputRef.value?.focus();
        break;
    }
};

function flattenArray(arr: TreeNode[], parentLabel:string = "") {  
    const result:SelectItem[] = [];  
    arr.forEach(item => {
        const currentLabel = parentLabel ? `${parentLabel}-${item.label}` : item.label;  
        result.push({ value: item.value, label: currentLabel });   
        if (item.children && item.children.length > 0) {  
            result.push(...flattenArray(item.children, currentLabel));  
        }  
    });  
    return result;  
}  


function limitInputLength(val: string, label: string, limitSize: number) {
    if (val.length > limitSize) {
        ElNotify({ type: "warning", message: `亲，${label}不能超过${limitSize}个字哦~` });
        const actualValue = val.slice(0, limitSize);
        const fieldMap:any = {  
            '客户名称': () => params.customerModel.custName = actualValue,  
            '供应商名称': () => params.vendorModel.vendName = actualValue,  
            '姓名': () => params.employeeModel.employeeName = actualValue,  
            '备注': () => {  
                if (isEmployee.value) {  
                    params.employeeModel.employeeNote = actualValue;  
                }  
                if (isDepartment.value) {  
                    params.departmentModel.departmentNote = actualValue;  
                }  
            },  
            '部门名称': () => params.departmentModel.departmentName = actualValue,  
            '商品名称': () => params.stockModel.stockName = actualValue,  
            '品牌': () => params.stockModel.stockBrand = actualValue,  
            '规格型号': () => params.stockModel.stockModel = actualValue,  
            '产地': () => params.stockModel.stockOrigin = actualValue,  
        };  
        const updateField = fieldMap[label];  
        if (updateField) {  
            updateField();  
        } 
        return;
    }
}
function limitPressLength(e: any, label: string, limitSize: number) {
    const { value, selectionStart,selectionEnd } = e.target as HTMLInputElement;
    if (value.length > limitSize - 1) {
        if (selectionStart === selectionEnd) {
            e.preventDefault();
            ElNotify({ type: "warning", message: `亲，${label}不能超过${limitSize}个字哦~` });
        }
    }
}
function changeSearchData(key: string, val: string) {
    if (key === "employeeMobile") {
        params.employeeModel.employeeMobile = val;
    }
}

watchEffect(() => {
    if (isCustomer.value) {
        checkParams.customerModel.custCode = params.customerModel.custCode;
        checkParams.customerModel.custName = params.customerModel.custName;
    }
    if (isVendor.value) {
        checkParams.vendorModel.vendCode = params.vendorModel.vendCode;
        checkParams.vendorModel.vendName = params.vendorModel.vendName;
    }
    if (isDepartment.value) {
        checkParams.departmentModel.departmentParent = params.departmentModel.departmentParent;
    }
    if (isStock.value) {
        checkParams.stockModel.stockCode = params.stockModel.stockCode;
        checkParams.stockModel.stockName = params.stockModel.stockName;
    }
    if (isEmployee.value) {
        checkParams.employeeModel.employeeName = params.employeeModel.employeeName;
    }
});

function init(_aatype: number, autoAddName: string = "") {
    // 先重置，再赋值，防止上一次的数据留存影响到下一次的显示
    if (isCustomer.value) {
        params.customerModel.custCode = "";
        params.customerModel.custName = "";
        params.customerModel.custType = null;
        params.customerModel.custLevel = null;
        params.customerModel.custContact = "";
        params.customerModel.custMobile = "";
        params.customerModel.custWechartOrQQ = "";
        params.customerModel.custSeller = null;
        params.customerModel.custReceiver = "";
        params.customerModel.custReceiverMobile = "";
        params.customerModel.custReceiverAddress = "";
        checkParams.customerModel.custCode = "";
        checkParams.customerModel.custName = "";
    }
    if (isVendor.value) {
        params.vendorModel.vendCode = "";
        params.vendorModel.vendName = "";
        params.vendorModel.vendType = undefined;
        params.vendorModel.vendContact = "";
        params.vendorModel.vendMobile = "";
        params.vendorModel.vendWechartOrQQ = "";
        checkParams.vendorModel.vendCode = "";
        checkParams.vendorModel.vendName = "";
    }
    if (isEmployee.value) {
        params.employeeModel.employeeCode = "";
        params.employeeModel.employeeName = "";
        params.employeeModel.employeeMobile = "";
        params.employeeModel.employeeNote = "";
        checkParams.employeeModel.employeeName = "";
    }
    if (isDepartment.value) {
        params.departmentModel.departmentCode = "";
        params.departmentModel.departmentName = "";
        params.departmentModel.departmentParent = 0;
        params.departmentModel.departmentNote = "";
        checkParams.departmentModel.departmentParent = 0;
    }
    if (isStock.value) {
        params.stockModel.stockCode = "";
        params.stockModel.stockName = "";
        params.stockModel.stockType = undefined;
        params.stockModel.stockBrand = "";
        params.stockModel.stockModel = "";
        params.stockModel.stockStore = undefined;
        params.stockModel.stockOrigin = "";
        params.stockModel.stockUnit = undefined;
        checkParams.stockModel.stockCode = "";
        checkParams.stockModel.stockName = "";
    }
    aatype.value = _aatype;
    Promise.all(
        (
            [
                (async () => {
                    let newNum = "";
                    let url = "";
                    if (isCustomer.value) {
                        url = `/api/ErpAssistingAccounting/GetNewCustCode?categoryId=0`;
                    } else if (isVendor.value) {
                        url = `/api/ErpAssistingAccounting/GetNewVendCode?categoryId=0`;
                    } else if (isEmployee.value) {
                        url = `/api/ErpAssistingAccounting/GetNewEmployeeCode`;
                    } else if (isDepartment.value) {
                        url = `/api/ErpAssistingAccounting/GetNewDepartmentCode`;
                    } else if (isStock.value) {
                        url = `/api/ErpAssistingAccounting/GetNewStockCode?categoryId=0`;
                    }
                    await request({
                        url: url,
                        method: "post",
                    }).then((res: IResponseModel<string>) => {
                        if (res.state === 1000) {
                            newNum = res.data;
                            if (res.msg) {
                                ElNotify({ message: res.msg, type: "warning" });
                            }
                        }
                    });
                    return newNum;
                })(),
                (async () => {
                    await request({
                        url: `/api/ErpAssistingAccounting/GetIsEntityNumberChangeable?aaType=${aatype.value}`,
                        method: "post",
                    }).then((res: IResponseModel<boolean>) => {
                        if (res.state === 1000) {
                            aaNumDisabled.value = !res.data;
                        }
                    });
                })(),
            ] as any[]
        ).concat(
            isCustomer.value
                ? [
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetCustType",
                              method: "post",
                          }).then((res: IResponseModel<TreeNode[]>) => {
                              if (res.state === 1000) {
                                  custTypeList.value = flattenArray(res.data);
                              }
                          });
                      })(),
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetCustLevel",
                              method: "post",
                          }).then((res: IResponseModel<SelectorNode[]>) => {
                              if (res.state === 1000) {
                                  custLevelList.value = res.data;
                              }
                          });
                      })(),
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetCustSeller",
                              method: "post",
                          }).then((res: IResponseModel<SelectorNode[]>) => {
                              if (res.state === 1000) {
                                  custSellerList.value = res.data;
                              }
                          });
                      })(),
                  ]
                : isVendor.value
                ? [
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetVendType",
                              method: "post",
                          }).then((res: IResponseModel<TreeNode[]>) => {
                              if (res.state === 1000) {
                                  vendTypeList.value = flattenArray(res.data);
                              }
                          });
                      })(),
                  ]
                : isEmployee.value
                ? []
                : isDepartment.value
                ? [
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetDepartmentParent",
                              method: "post",
                          }).then((res: IResponseModel<SelectorNode[]>) => {
                              if (res.state === 1000) {
                                  departmentList.value = res.data.filter((item) => item.id > 0);
                              }
                          });
                      })(),
                  ]
                : isStock.value
                ? [
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetStockType",
                              method: "post",
                          }).then((res: IResponseModel<TreeNode[]>) => {
                              if (res.state === 1000) {
                                  stockTypeList.value = flattenArray(res.data);
                              }
                          });
                      })(),
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetStockStore",
                              method: "post",
                          }).then((res: IResponseModel<SelectorNode[]>) => {
                              if (res.state === 1000) {
                                  stockStoreList.value = res.data;
                              }
                          });
                      })(),
                      (async () => {
                          await request({
                              url: "/api/ErpAssistingAccounting/GetStockUnit",
                              method: "post",
                          }).then((res: IResponseModel<SelectorNode[]>) => {
                              if (res.state === 1000) {
                                  stockUnitList.value = res.data;
                              }
                          });
                      })(),
                  ]
                : []
        )
    ).then((res) => {
        if (isCustomer.value) {
            params.customerModel.custCode = res[0];
            params.customerModel.custLevel = custLevelList.value![0].id;
            params.customerModel.custName = autoAddName.slice(0, LimitCharacterSize.Name);
            limitInputLength(autoAddName,'客户名称', LimitCharacterSize.Name);
            checkParams.customerModel.custCode = res[0];
        }
        if (isVendor.value) {
            params.vendorModel.vendCode = res[0];
            params.vendorModel.vendName = autoAddName.slice(0, LimitCharacterSize.Name);
            limitInputLength(autoAddName, '供应商名称', LimitCharacterSize.Name);
            checkParams.vendorModel.vendCode = res[0];
        }
        if (isEmployee.value) {
            params.employeeModel.employeeCode = res[0];
            params.employeeModel.employeeName = autoAddName.slice(0, LimitCharacterSize.Name);
            limitInputLength(autoAddName, '姓名', LimitCharacterSize.Name);
        }
        if (isDepartment.value) {
            params.departmentModel.departmentCode = res[0];
            params.departmentModel.departmentParent = departmentList.value![0].id;
            params.departmentModel.departmentName = autoAddName.slice(0, LimitCharacterSize.Name);
            limitInputLength(autoAddName, '部门名称', LimitCharacterSize.Name);
            checkParams.departmentModel.departmentParent = departmentList.value![0].id;
        }
        if (isStock.value) {
            params.stockModel.stockCode = res[0];
            params.stockModel.stockName = autoAddName.slice(0, LimitCharacterSize.Name);
            limitInputLength(autoAddName, '商品名称', LimitCharacterSize.Name);
            checkParams.stockModel.stockCode = res[0];
        }
    });
}

let saving = false;
function save() {
    if (saving) return;
    if (isCustomer.value) {
        if (!params.customerModel.custCode) {
            ElNotify({ message: "请填写客户编码", type: "warning" });
            return;
        }
        if (!params.customerModel.custName) {
            ElNotify({ message: "请填写客户名称", type: "warning" });
            return;
        }
        if (params.customerModel.custName.length > 256) {
            ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
            return;
        }
        if (!params.customerModel.custContact) {
            ElNotify({ message: "请填写联系人", type: "warning" });
            return;
        }
    }
    if (isVendor.value) {
        if (!params.vendorModel.vendCode) {
            ElNotify({ message: "请填写供应商编码", type: "warning" });
            return;
        }
        if (!params.vendorModel.vendName) {
            ElNotify({ message: "请填写供应商名称", type: "warning" });
            return;
        }
        if (params.vendorModel.vendName.length > 256) {
            ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
            return;
        }
        if (!params.vendorModel.vendContact) {
            ElNotify({ message: "请填写联系人", type: "warning" });
            return;
        }
    }
    if (isEmployee.value) {
        if (!params.employeeModel.employeeCode) {
            ElNotify({ message: "请填写工号", type: "warning" });
            return;
        }
        if (!params.employeeModel.employeeName) {
            ElNotify({ message: "请填写姓名", type: "warning" });
            return;
        }
        if (params.employeeModel.employeeName.length > 256) {
            ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
            return;
        }
    }
    if (isDepartment.value) {
        if (!params.departmentModel.departmentCode) {
            ElNotify({ message: "请填写部门编码", type: "warning" });
            return;
        }
        if (!params.departmentModel.departmentName) {
            ElNotify({ message: "请填写部门名称", type: "warning" });
            return;
        }
        if (!isNumberOrLetter(params.departmentModel.departmentCode)) {
            ElNotify({ message: "部门编码不是数字和字母组合，请修改后重试~", type: "warning" });
            return;
        }
        if (params.departmentModel.departmentName.length > 256) {
            ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
            return;
        }
    }
    if (isStock.value) {
        if (!params.stockModel.stockCode) {
            ElNotify({ message: "请填写商品编码", type: "warning" });
            return;
        }
        if (!params.stockModel.stockName) {
            ElNotify({ message: "请填写商品名称", type: "warning" });
            return;
        }
        if (params.stockModel.stockName.length > 256) {
            ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
            return;
        }
        if(!params.stockModel.stockUnit) {
            ElNotify({ message: "请填写商品计量单位", type: "warning" });
            return;
        }
    }
    saving = true;
    new Promise<void>((resolve) => {
        if (isEmployee.value) {
            request({
                url: "/api/ErpAssistingAccounting/CheckSameNameEmployee",
                method: "post",
                params: {
                    employeeName: checkParams.employeeModel.employeeName
                }
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    if (res.data) {
                        let msg = `${params.employeeModel.employeeCode}/${params.employeeModel.employeeName}存在同名职员，是否继续保存？`;
                        ElConfirm(msg).then((r) => {
                            if (r) {
                                resolve();
                            } else {
                                saving = false;
                                emit("save-fail");
                            }
                        });
                    } else {
                        resolve();
                    }
                } else {
                    saving = false;
                    emit("save-fail");
                    ElNotify({ message: "保存失败", type: "warning" });
                }
            })
        } else {
            const data: any = isCustomer.value
                ? checkParams.customerModel
                : isVendor.value
                ? checkParams.vendorModel
                : isDepartment.value
                ? checkParams.departmentModel
                : isStock.value
                ? checkParams.stockModel
                : null;
            let checkUrl = '';
            const checkData = {
                ...data,
                aatype: aatype.value
            }
            if(isCustomer.value) {
                checkUrl = `/api/ErpAssistingAccounting/CheckCustomer`;
            } else if(isVendor.value) {
                checkUrl = `/api/ErpAssistingAccounting/CheckSupporter`;
            } else if(isDepartment.value) {
                checkUrl = `/api/ErpAssistingAccounting/CheckDepartment?`+getUrlSearchParams(checkData);
            } else if(isStock.value) {
                checkUrl = `/api/ErpAssistingAccounting/CheckStock`;
            }
            request({
                url: checkUrl,
                method: "post",
                data:isDepartment.value ? null : checkData
            }).then((res: IResponseModel<any>) => {
                if (res.state === 1000) {
                    if (isDepartment.value) {
                        if (res.data.result) {
                            resolve();
                        } else {
                            const parentDepartmentModel = assistingAccountingStore.assistingAccountingList.find(
                                (item) => item.aaeid === params.departmentModel.departmentParent
                            );
                            const msg = `${parentDepartmentModel?.aanum}/${parentDepartmentModel?.aaname}已发生业务，新增下级部门后，业务单据和凭证上的部门信息将会转移到下级部门${params.departmentModel.departmentCode}/${params.departmentModel.departmentName}上，您确定继续吗？`;
                            ElConfirm(msg).then((r) => {
                                if (r) {
                                    resolve();
                                } else {
                                    saving = false;
                                    emit("save-fail");
                                }
                            });
                        }
                    } else {
                        if (res.data === 0) {
                            resolve();
                        } else if (res.data === 1) {
                            saving = false;
                            emit("save-fail");
                            ElNotify({ message: "该编码已存在", type: "warning" });
                        } else if (res.data === 2) {
                            let msg = res.msg;
                            if (isCustomer.value) {
                                msg = `${params.customerModel.custCode}/${params.customerModel.custName}存在同名客户，是否继续保存？`;
                            }
                            if (isVendor.value) {
                                msg = `${params.vendorModel.vendCode}/${params.vendorModel.vendName}存在同名供应商，是否继续保存？`;
                            }
                            if (isStock.value) {
                                msg = `${params.stockModel.stockCode}/${params.stockModel.stockName}存在同名商品，是否继续保存？`;
                            }
                            ElConfirm(msg).then((r) => {
                                if (r) {
                                    resolve();
                                } else {
                                    saving = false;
                                    emit("save-fail");
                                }
                            });
                        } else {
                            saving = false;
                            emit("save-fail");
                            ElNotify({ message: "保存失败", type: "warning" });
                        }
                    }
                    return;
                }
                saving = false;
                emit("save-fail");
                ElNotify({ message: "保存失败", type: "warning" });
            });
        }
    }).then(() => {
        const data: any = isCustomer.value
            ? params.customerModel
            : isVendor.value
            ? params.vendorModel
            : isEmployee.value
            ? params.employeeModel
            : isDepartment.value
            ? params.departmentModel
            : isStock.value
            ? params.stockModel
            : null;
        request({
            url: `/api/ErpAssistingAccounting/${
                isCustomer.value
                    ? "AddCustomer"
                    : isVendor.value
                    ? "AddVendor"
                    : isEmployee.value
                    ? "AddEmployee"
                    : isDepartment.value
                    ? "AddDepartment"
                    : isStock.value
                    ? "AddStock"
                    : ""
            }`,
            method: "post",
            data: data,
        }).then((res: IResponseModel<number>) => {
            saving = false;
            if (res.state === 1000) {
                data.aaeId = res.data;
                emit("save-success", data);
                assistingAccountingStore.getAssistingAccounting();
                if (isDepartment.value) {
                    assistingAccountingStore.getDepartment();
                }
                ElNotify({ message: "亲，保存成功啦", type: "success" });
                departmentList.value = [];
            } else {
                emit("save-fail");
                ElNotify({ message: res.msg || "保存失败", type: "warning" });
            }
        });
    });
}

const createTypeWatcher = (model:any, typeProperty:string, codeProperty:string,url:string) => {
  return () => {
    watch(() => model[typeProperty], () => {
      request({
        url: url + model[typeProperty],
        method: "post",
      }).then((res: any) => {
        if (res.state === 1000) {
          model[codeProperty] = res.data;
          (checkParams as any)[model.name][codeProperty] = res.data; 
          if (res.msg) {
            ElNotify({ message: res.msg, type: "warning" });
          }
        }
      });
    });
  };
};

//客户，供应商，商品？？？
createTypeWatcher(params.vendorModel, 'vendType', 'vendCode', '/api/ErpAssistingAccounting/GetNewVendCode?categoryId=')();
createTypeWatcher(params.customerModel, 'custType', 'custCode', '/api/ErpAssistingAccounting/GetNewCustCode?categoryId=')();


defineExpose({
    init,
});

const handleVisibleChange = (visible: boolean) => {
    if (visible) {
        if (isCustomer.value) {
            showCustTypeList.value = JSON.parse(JSON.stringify(custTypeList.value));
        }
        if (isVendor.value) {
            showVendTypeList.value = JSON.parse(JSON.stringify(vendTypeList.value));
        }
        if (isStock.value) {
            showStockTypeList.value = JSON.parse(JSON.stringify(stockTypeList.value));
        }
    }
}

const autoAddTypeName = ref("");
//拼音首字母模糊搜索
const showCustTypeList = ref<TreeNode[]>([]);
const showCustLevelList = ref<SelectorNode[]>([]);
const showCustSellerList = ref<SelectorNode[]>();
const showVendTypeList = ref<TreeNode[]>([]);
const showStockTypeList = ref<TreeNode[]>([]);
const showStockStoreList = ref<SelectorNode[]>([]);
const showStockUnitList = ref<SelectorNode[]>([]);
const showDepartmentList = ref<SelectorNode[]>([]);
watchEffect(() => {
    showCustTypeList.value = JSON.parse(JSON.stringify(custTypeList.value));
    showVendTypeList.value = JSON.parse(JSON.stringify(vendTypeList.value));
    showStockTypeList.value = JSON.parse(JSON.stringify(stockTypeList.value));
    
    showCustLevelList.value = JSON.parse(JSON.stringify(custLevelList.value));
    showCustSellerList.value = JSON.parse(JSON.stringify(custSellerList.value));
    showStockStoreList.value = JSON.parse(JSON.stringify(stockStoreList.value));
    showStockUnitList.value = JSON.parse(JSON.stringify(stockUnitList.value));
    showDepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
});
function custTypeFilterMethod(value: string) {
    showCustTypeList.value = commonFilterMethod(value, custTypeList.value, 'label');
    autoAddTypeName.value = showCustTypeList.value.length === 0 ? value.trim() : ""; 
}
function vendTypeFilterMethod(value: string) {
    showVendTypeList.value = commonFilterMethod(value, vendTypeList.value, 'label');
    autoAddTypeName.value = showVendTypeList.value.length === 0 ? value.trim() : ""; 
}
function stockTypeFilterMethod(value: string) {
    showStockTypeList.value = commonFilterMethod(value, stockTypeList.value, 'label');
    autoAddTypeName.value = showStockTypeList.value.length === 0 ? value.trim() : ""; 
}
function custLevelFilterMethod(value: string) {
    showCustLevelList.value = commonFilterMethod(value, custLevelList.value, 'value');
}
function custSellerFilterMethod(value: string) {
    showCustSellerList.value = commonFilterMethod(value, custSellerList.value, 'value');
}
function stockStoreFilterMethod(value: string) {
    showStockStoreList.value = commonFilterMethod(value, stockStoreList.value, 'value');
}
function stockUnitFilterMethod(value: string) {
    showStockUnitList.value = commonFilterMethod(value, stockUnitList.value, 'value');
}
function departFilterMethod(value: string) {
    showDepartmentList.value = commonFilterMethod(value, departmentList.value, 'value');
}

//新增客户类别/供应商类别/商品类别
const selectBottomHtml = `
    <div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">+点击添加</a>
    </div>
`;
enum TypeName {
    Customer = 1,
    Vendor = 2,
    Stock = 3,
}
const ErpTypeDialogRef = ref();
const typeTitle = ref("");
const codeType = ref(1);
const parentTypeList = ref<TreeNode[]>([]);
const handleNewType = (aType: TypeName) => {
    switch (aType) {
        case 1:
            typeTitle.value = "新增客户类别";
            parentTypeList.value = cloneDeep(custTypeList.value);
            codeType.value = 1;
            break;
        case 2:
            typeTitle.value = "新增供应商类别";
            parentTypeList.value = cloneDeep(vendTypeList.value);
            codeType.value = 2;
            break;
        case 3:
            typeTitle.value = "新增商品类别";
            parentTypeList.value = cloneDeep(stockTypeList.value);
            codeType.value = 3;
            break;
    }
    ErpTypeDialogRef.value?.showTypeDialog(codeType.value, autoAddTypeName.value);
}
const saveType = (aType: number, id: number) => {
    if (aType === TypeName.Customer) {
        request({
            url: "/api/ErpAssistingAccounting/GetCustType",
            method: "post",
        }).then((res: IResponseModel<TreeNode[]>) => {
            if (res.state === 1000) {
                custTypeList.value = flattenArray(res.data);
                params.customerModel.custType = id;
            }
        });
    } else if (aType === TypeName.Vendor) {
        request({
            url: "/api/ErpAssistingAccounting/GetVendType",
            method: "post",
        }).then((res: IResponseModel<TreeNode[]>) => {
            if (res.state === 1000) {
                vendTypeList.value = flattenArray(res.data);
                params.vendorModel.vendType = id;
            }
        });
    } else {
        request({
            url: "/api/ErpAssistingAccounting/GetStockType",
            method: "post",
        }).then((res: IResponseModel<TreeNode[]>) => {
            if (res.state === 1000) {
                stockTypeList.value = flattenArray(res.data);
                params.stockModel.stockType = id;
            }
        });
    }
}
</script>
