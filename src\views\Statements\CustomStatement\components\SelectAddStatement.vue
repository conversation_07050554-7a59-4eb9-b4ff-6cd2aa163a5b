<template>
    <div class="edit-content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #addMain>
                <div class="pre-create-statement-content">
                    <div class="title">新建报表</div>
                    <div class="form-line" style="margin-top: 40px">
                        <el-radio-group v-model="statementType">
                            <el-radio
                                class="form-item with-width"
                                size="large"
                                v-for="item in addSelectList"
                                :key="item.value"
                                :label="item.value"
                            >
                                <template #default>
                                    <div class="radio-box">{{ item.title }}</div>
                                    <div class="radio-item-desc">
                                        {{ item.desc }}
                                    </div>
                                </template>
                            </el-radio>
                        </el-radio-group>
                    </div>
                    <div class="btns">
                        <a class="button mr-10" @click="emits('goMain')">取消</a>
                        <a class="button solid-button" @click="preCreateNextStep()">下一步</a>
                    </div>
                </div>
            </template>
            <template #copyList>
                <div class="slot-content align-center create-by-current-statement-content">
                    <div class="title">复制固定报表</div>
                    <el-radio-group v-model="copyStatementChecked" class="statement-type-selector">
                        <el-radio :label="1" size="large">资产负债表</el-radio>
                        <el-radio :label="2" size="large">利润表</el-radio>
                    </el-radio-group>
                    <div class="btns">
                        <a class="button mr-10" @click="currentSlot = 'addMain'">上一步</a>
                        <a class="button solid-button" @click="createByCurrentStatementNextStep()">下一步</a>
                    </div>
                </div>
            </template>
            <template #importFile>
                <div class="slot-content align-center import-from-file-content">
                    <div class="title">导入报表模板</div>
                    <div class="file-content">
                        <div class="file-content-title">请选择报表模板文件：</div>
                        <input type="text" id="importTemplateFileName" readonly v-model="fileName" />
                        <label class="file-button">
                            <input
                                id="importTemplateFile"
                                ref="importFileInputRef"
                                name="myfile"
                                type="file"
                                @change="onFileSelected"
                                accept=".zip,.json"
                            /><a class="button solid-button">选取文件</a>
                        </label>
                        <!-- <span class="file-box" v-show="fileName">{{ fileName }}</span> -->
                    </div>
                    <div class="tips">
                        <div>操作步骤：</div>
                        <div>1. 在自定义报表列表导出报表模板</div>
                        <div>2. 选择报表文件导入，如利润表.zip</div>
                        <div>3. 在报表设计器中对报表模板进行调整，如有必要</div>
                        <div>注意事项：</div>
                        <div>一次只能导入一个报表模板。</div>
                    </div>
                    <div class="btns">
                        <a class="button mr-10" @click="currentSlot = 'addMain'">上一步</a>
                        <a class="button solid-button" @click="importFromFileNextStep()">下一步</a>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";
import { request, type IResponseModel } from "@/util/service";
import ContentSlider from "@/components/ContentSlider/index.vue";
import { ElNotify } from "@/util/notify";
import $bus from "@/bus";
import type { ICustomStatementDetail } from "../types";
import { useLoading } from "@/hooks/useLoading";

const slots = ["addMain", "copyList", "importFile"];
const currentSlot = ref("addMain");
const emits = defineEmits(["preCreateNextStep", "goMain"]);
const statementType = ref(1);
const addSelectList = ref([
    {
        title: "跟随向导新建",
        desc: "按照新建指引批量设置报表项目和取数公式，一键完成报表的设计，并可及时预览报表效果。",
        value: 1,
    },
    {
        title: "复制固定报表",
        desc: "复制系统预设的资产负债表、利润表生成新的自定义报表，复制成功后可进入报表设计器修改报表项目和取数公式。",
        value: 2,
    },
    {
        title: "导入报表模板",
        desc: "通过报表模板新建自定义报表，导入后可进入报表设计器修改报表项目和取数公式。",
        value: 3,
    },
    {
        title: "新建空白报表",
        desc: "新建一个空白报表，然后进入报表设计器编辑报表项目和取数公式，适合熟练用户使用哦。",
        value: 4,
    },
]);
const importFileInputRef = ref();
const copyStatementChecked = ref(1);
const resetAddSelect = () => {
    statementType.value = 1;
    currentSlot.value = "addMain";
};
const createByCurrentStatementNextStep = () => {
    useLoading().enterLoading("正在加载数据...");
    request({
        url: "/api/CustomStatement/GetStatementModelByStatements?statementType=" + copyStatementChecked.value,
        method: "post",
    }).then((res: IResponseModel<ICustomStatementDetail>) => {
        useLoading().quitLoading();
        $bus.emit("setStatementModel", res.data);
        emits("preCreateNextStep", 4);
    });
};
const importFromFileNextStep = () => {
    if (!selectedFile.value) {
        ElNotify({
            type: "warning",
            message: "请选择文件",
        });
        return;
    }
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    useLoading().enterLoading("正在加载数据...");
    request({
        url: `/api/CustomStatement/Import`,
        method: "post",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data",
        },
    }).then((res: IResponseModel<ICustomStatementDetail>) => {
        useLoading().quitLoading();
        if (res.state === 1000) {
            if (res.data) {
                ElNotify({
                    message: "导入成功",
                    type: "success",
                });
                $bus.emit("setStatementModel", res.data);
                emits("preCreateNextStep", 4);
            } else {
                ElNotify({
                    message: "文件格式不正确，请重新选择。",
                    type: "warning",
                });
            }
        } else {
            ElNotify({
                message: res.msg,
                type: "warning",
            });
        }
    });
};
const preCreateNextStep = () => {
    switch (statementType.value) {
        case 1:
            emits("preCreateNextStep", 1);
            break;
        case 2:
            currentSlot.value = "copyList";
            copyStatementChecked.value = 1;
            break;
        case 3:
            currentSlot.value = "importFile";
            fileName.value = "";
            selectedFile.value = null;
            importFileInputRef.value.value = "";
            break;
        case 4:
            $bus.emit("setStatementModel", {});
            emits("preCreateNextStep", 4);
            break;
    }
};
const fileName = ref("");
const selectedFile = ref();
const onFileSelected = (event: Event) => {
    nextTick(() => {
        const input = event.target as HTMLInputElement;
        const file: File = (input.files as FileList)[0];
        if (!file) {
            fileName.value = "";
            selectedFile.value = null;
            return;
        }
        fileName.value = "C:\\fakepath\\" + file.name;
        selectedFile.value = file;
    });
};
defineExpose({
    resetAddSelect,
});
</script>

<style lang="less" scoped>
.pre-create-statement-content {
    height: 587px;
    overflow: auto;
    margin-bottom: 20px;
    .form-line {
        font-size: 0;
        margin-top: 16px;
        width: 580px;
        margin: 0 auto;
        .el-radio.el-radio--large.form-item.with-width {
            width: 580px;
            margin-top: 25px;
            text-align: left;
            height: auto;
            :deep(.el-radio__input) {
                position: absolute;
                top: 0;
                left: 0;
                .el-radio__inner {
                    width: 18px;
                    height: 18px;
                }
            }
            :deep(.el-radio__label) {
                text-align: left;
                padding-left: 28px;
            }
            .radio-box {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                position: relative;
                display: inline-block;
            }
            .radio-item-desc {
                font-size: var(--font-size);
                line-height: 22px;
                color: var(--weaker-font-color);
                margin-top: 5px;
                white-space: pre-wrap;
            }
        }
    }
    .btns {
        text-align: center;
        margin-top: 48px;
    }
}

.create-by-current-statement-content {
    margin: auto;
    .statement-type-selector {
        margin-top: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        .radio-button {
            height: 20px;
            display: inline-block;
        }
    }
    .btns {
        font-size: 0;
        text-align: center;
        margin-top: 50px;
        margin-bottom: 40px;
    }
}
.import-from-file-content {
    margin: auto;
    .file-content {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        .file-content-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }
        input {
            width: 200px;
            height: 28px;
            border: 1px solid var(--input-border-color);
            outline: none;
            padding: 0;
            padding-left: 10px;
            padding-right: 10px;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 28px;
            box-sizing: border-box;
            border-radius: var(--input-border-radius);
        }
        .file-button {
            margin-left: -3px;
            height: 28px;
            position: relative;
            display: inline-block;
            overflow: hidden;
        }
        .file-box {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            margin-left: 20px;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            vertical-align: top;
        }
    }
    .tips {
        width: 423px;
        margin: 28px auto 0;
        div {
            font-size: var(--h5);
            color: var(--weaker-font-color);
            line-height: 16px;
            text-align: left;
        }
        div + div {
            margin-top: 10px;
        }
    }
    .btns {
        font-size: 0;
        text-align: center;
        margin-top: 50px;
        margin-bottom: 40px;
    }
}
</style>
