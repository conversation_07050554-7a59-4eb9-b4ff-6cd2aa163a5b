import type { IAsubRelationType } from "./types";

export const formatAsub = (asub: string) => {
    return asub === "" ? "0" : asub;
};

interface TabPane {
    label: string;
    name: keyof IAsubRelationType;
    explain: string;
}
export const tabPanes: Array<TabPane> = [
    {
        label: "商品",
        name: "commodity",
        explain: "系统会先匹配本级商品类别对应的科目，如果本级类别没有则取上级类别对应的科目，最后才会使用默认科目",
    },
    {
        label: "客户",
        name: "customer",
        explain: "系统会先匹配本级客户类别对应的科目，如果本级类别没有则取上级类别对应的科目，最后才会使用默认科目",
    },
    {
        label: "供应商",
        name: "vendor",
        explain: "系统会先匹配本级供应商类别对应的科目，如果本级类别没有则取上级类别对应的科目，最后才会使用默认科目",
    },
    {
        label: "职员",
        name: "employee",
        explain: "系统会匹配职员对应的科目，如果职员没有对应科目则使用默认科目",
    },
    {
        label: "银行账户",
        name: "account",
        explain: "系统会匹配账户对应的科目，如果账户没有对应科目则使用默认科目",
    },
    {
        label: "收入类别",
        name: "income",
        explain: "系统会先匹配本级收入类别对应的科目，如果本级类别没有则取上级类别对应的科目，最后才会使用默认科目",
    },
    {
        label: "支出类别",
        name: "expense",
        explain: "系统会先匹配本级支出类别对应的科目，如果本级类别没有则取上级类别对应的科目，最后才会使用默认科目",
    },
];
