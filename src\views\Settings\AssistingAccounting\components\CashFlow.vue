<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <div v-if="!isErp" class="label">输入编码或名称：</div>
            <el-input v-if="!isErp" v-model="searchText" clearable/>
            <a v-if="!isErp" class="button solid-button ml-10" @click="btnSearch()">查询</a>
            <SearchInfo
                v-if="isErp"
                :width="320"
                :height="30"
                :placeholder="'输入编码/名称/助记码/备注等关键字搜索'"
                @search="btnSearch"
            ></SearchInfo>
            <ErpRefreshButton></ErpRefreshButton>
        </div>
        <div class="main-tool-right">
            <a v-permission="['assistingaccount-canexport']" class="button ml-10" @click="handleExport">导出</a>
        </div>
    </div>
    <div class="main-center">
        <Table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :page-is-show="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            :use-normal-scroll="true"
            :scrollbar-show="true"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :tableName="setModule"
        >
            <template #operator v-if="hasChanged">
                <el-table-column label="操作" min-width="90px" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccount-canedit']" class="link" @click="handleEdit(scope.row.aaeId)"> 编辑 </a>
                            <a v-permission="['assistingaccount-candelete']" class="link" @click="handleDelete(scope.row.aaeId)"> 删除 </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { request } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { getUrlSearchParams } from "@/util/url";
import { exportHandle } from "../utils";
import SearchInfo from "@/components/SearchInfo/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AssitCash";
interface ITableItem {
    cashType: number;
    cashTypeName: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
const isErp = ref(window.isErp);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const cashTypeList = [
    "经营活动产生的现金流量",
    "投资活动产生的现金流量",
    "筹资活动产生的现金流量",
    "汇率变动对现金的影响额",
    "业务活动产生的现金流量",
];
const formatCashFlowType = (row: any, column: any, value: number) => {
    const type = Number((value + "")[2]) as 1 | 2 | 3 | 4 | 5;
    return cashTypeList[type - 1] ?? "";
};

let hasChanged = ref(false);
const searchText = ref("");
const searchStr = ref("");
const loading = ref(false);
const tableData = ref<ITableItem[]>([]);
const columns: IColumnProps[] = [
    { label: "现金流编码", prop: "aaNum", minWidth: 120, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaNum") },
    { label: "现金流名称", prop: "aaName", minWidth: 260, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaName") },
    { label: "现金流类别", prop: "cashType", minWidth: 240, align: "left", headerAlign: "left", formatter: formatCashFlowType, width: getColumnWidth(setModule, "cashType") },
    { label: "助记码", prop: "aaAcronym", minWidth: 110, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaAcronym") },
    { label: "备注", prop: "note", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "note") },
    { slot: "operator" },
];
const btnSearch = (search?: string) => {
    searchStr.value = searchText.value;
    if (search !== undefined) {
        searchStr.value = search;
    }
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleSearch = (successBack?: Function) => {
    loading.value = true;
    const params = {
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        searchStr: searchStr.value,
    };
    request({ url: "/api/AssistingAccounting/PagingCashFlowList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                successBack && successBack();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleInit = () => {
    request({ url: "/api/CashFlowStatement/IsCashFlowAssistAccountChange", method: "post" }).then((res: any) => {
        if (res.state == 1000) hasChanged.value = res.data.isChange;
    });
};
const handleEdit = (aaeId: number) => console.log(aaeId);
const handleDelete = (aaeId: number) => console.log(aaeId);
const handleExport = () => exportHandle(10007, paginationData.total);

handleInit();

defineExpose({ handleSearch });

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
</style>
