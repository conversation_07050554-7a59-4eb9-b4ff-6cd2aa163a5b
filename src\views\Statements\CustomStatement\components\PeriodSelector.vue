<template>
    <div class="period-selector">
        <el-select
            v-model="valuePeriod.valuePeriodType"
            style="width: 94px"
            @visible-change="visibleChange"
            :disabled="props.disable"
            :teleported="false"
        >
            <el-option label="月份" :value="1" />
            <el-option label="季度" :value="2" />
            <el-option label="年度" :value="3" />
        </el-select>
        <Select
            v-model="valuePeriod.startValuePeriod"
            :teleported="false"
            class="period-select-list"
            :disabled="props.disable"
            :style="{ width: valuePeriod.valuePeriodType === 2 ? '146px' : '124px' }"
            @visible-change="visibleChange"
        >
            <el-option v-for="item in accountantList" :label="item.label" :value="item.pid" :key="item.pid" /> </Select
        >至
        <Select
            v-model="valuePeriod.endValuePeriod"
            :teleported="false"
            class="period-select-list"
            :style="{ width: valuePeriod.valuePeriodType === 2 ? '146px' : '124px' }"
            :disabled="props.disable"
            @visible-change="visibleChange"
        >
            <el-option v-for="item in accountantList" :label="item.label" :value="item.pid" :key="item.pid" />
        </Select>
    </div>
</template>
<script setup lang="ts">
import { ref, watch } from "vue";
import type { IPeriod } from "@/api/period";
import cloneDeep from "lodash/cloneDeep";
import Select from "@/components/Select/index.vue";
import type { IValuePeriod } from "../types";
import { ElNotify } from "@/util/notify";
import { getPeriodList } from "../utils";
const { monPeriodList, quarterPeriodList, yearPeriodList } = getPeriodList();
interface IPeriodList extends IPeriod {
    label: string;
}
const props = withDefaults(
    defineProps<{
        disable?: boolean;
        modelValue: IValuePeriod;
    }>(),
    {
        disable: false,
        modelValue: () => {
            return {
                valuePeriodType: 1,
                startValuePeriod: 0,
                endValuePeriod: 0,
            };
        },
    }
);
const emits = defineEmits(["update:modelValue", "visibleChange"]);
const valuePeriod = ref(cloneDeep(props.modelValue));
watch(
    valuePeriod,
    (newVal, old) => {
        if (newVal.endValuePeriod < newVal.startValuePeriod) {
            ElNotify({
                type: "warning",
                message: "结束期间不能小于开始期间哦~",
            });
            newVal.endValuePeriod = newVal.startValuePeriod;
        }

        emits("update:modelValue", newVal);
    },
    { deep: true }
);

let accountantList = ref();

function getAccountantList() {
    let selectedList: IPeriodList[] | Array<{ pid: number; label: string }> = [];
    switch (valuePeriod.value.valuePeriodType) {
        case 1:
            selectedList = monPeriodList;
            break;
        case 2:
            selectedList = quarterPeriodList;
            break;
        case 3:
            selectedList = yearPeriodList;
            break;
        default:
            selectedList = [];
            break;
    }

    accountantList.value = selectedList;
    valuePeriod.value.startValuePeriod =
        valuePeriod.value.startValuePeriod > 0 && selectedList.findIndex((v) => v.pid === valuePeriod.value.startValuePeriod) >= 0
            ? valuePeriod.value.startValuePeriod
            : selectedList[0].pid;
    valuePeriod.value.endValuePeriod =
        valuePeriod.value.endValuePeriod > 0 && selectedList.findIndex((v) => v.pid === valuePeriod.value.endValuePeriod) >= 0
            ? valuePeriod.value.endValuePeriod
            : selectedList[0].pid;
}
watch(
    () => valuePeriod.value.valuePeriodType,
    () => {
        getAccountantList();
    },
    { immediate: true }
);
const visibleChange = (visible: boolean) => {
    emits("visibleChange", visible);
};
const changeValuePeriod = (value: IValuePeriod) => {
    valuePeriod.value = value;
};
defineExpose({
    changeValuePeriod,
});
</script>
<style lang="less" scoped>
.period-selector {
    display: flex;
    justify-content: start;
    align-items: center;
    :deep(.custom-select) {
        width: 120px;
        margin: 0 10px;
    }
}
</style>
