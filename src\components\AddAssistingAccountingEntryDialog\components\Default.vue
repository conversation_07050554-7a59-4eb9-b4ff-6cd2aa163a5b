<template>
    <div class="add-aae-dialog-container">
        <div class="form-item">
            <div class="input-title">{{inputTitle}}编号：</div>
            <div class="input-field">
                <el-input v-model="aaNum"></el-input>
            </div>
        </div>
        <div class="form-item">
            <div class="input-title">{{inputTitle}}名称：</div>
            <div class="input-field">
                <el-input v-model="aaName"
                    v-if="aaNameTextareaShow"
                    ref="aaNameInputRef"
                    type="textarea"
                    :autosize="{minRows: 1, maxRows: 3.5 }"
                    resize="none"
                    @blur="inputTypeBlur"
                    class='asubName-textarea'  
                    @input="limitInputLength(aaName, inputTitle + '名称', LimitCharacterSize.Name)"
                />
                <Tooltip :content="aaName" :isInput="true" v-else>
                    <el-input type="text" v-model="aaName" @focus="inputTypeFocus" />
                </Tooltip>
            </div>
        </div>
        <div class="buttons" :class="isErp ? 'erp' : ''">
            <a class="button" @click="emit('cancel')">取消</a>
            <a class="button solid-button ml-10" @click="save()">确定</a>
        </div>
    </div>
</template>
<style lang="less" scoped>
@import "@/style/Functions.less";

.add-aae-dialog-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-top: 19px;

    .form-item {
        align-self: center;
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .input-title {
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
        }

        .input-field {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .detail-input(256px, 30px);
            .detail-el-select(256px, 30px);
            display: flex;
            align-items: center;
            .asubName-textarea{
                width: 192px;
                height: 32px;
            }
        }
    }

    .buttons {
        margin-top: 11px;
        padding: 10px;
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

body[erp] .custom-confirm .el-dialog__body .buttons {
    &.erp {
        display: flex;
        justify-content: flex-end;
    }
}
</style>
<script lang="ts" setup>
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ref, nextTick } from "vue";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import Tooltip from "@/components/Tooltip/index.vue";
import { LimitCharacterSize } from "@/views/Settings/AssistingAccounting/utils";
const isErp = ref(window.isErp);
const props = defineProps({
    inputTitle:{
        type: String,
        default: "辅助核算项目",
    }
});

const emit = defineEmits<{
    (e: "save-success", data: any): void;
    (e: "save-fail"): void;
    (e: "cancel"): void;
}>();
const aatype = ref(0);
const aaNum = ref("");
const aaName = ref("");
const aaNameTextareaShow = ref(false);
const aaNameInputRef = ref();

const inputTypeBlur = () => {
    aaNameTextareaShow.value = false;
};

const inputTypeFocus = () => {
    aaNameTextareaShow.value = true;
    nextTick(()=>{
        aaNameInputRef.value?.focus();
    })
};

function limitInputLength(val: string, label: string, limitSize: number) {
    if (val.length > limitSize) {
        ElNotify({ type: "warning", message: `亲，${label}不能超过${limitSize}个字哦~` });
        aaName.value = val.slice(0, limitSize);
    } else {
        aaName.value = val;
    }
}
function init(_aatype: number, autoAddName:string = "") {
    aatype.value = _aatype;
    aaNum.value = "";
    // aaName.value = "";
    limitInputLength(autoAddName, props.inputTitle + '名称', LimitCharacterSize.Name)
    request({
        url: "/api/AssistingAccounting/GetNewAANum",
        method: "post",
        params: {
            aaType: aatype.value,
            categoryId: 0,
        },
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            aaNum.value = res.data;
        }
    });
}

let saving = false;
function save() {
    if (saving) return;
    if (!aaNum.value) {
        ElNotify({
            message: "请输入编码！",
            type: "warning",
        });
        return;
    }
    if (!/^[0-9a-zA-Z_]+$/.test(aaNum.value) || aaNum.value.length > 18) {
        ElNotify({
            message: "编码为不超过18位的字符或数字组合！",
            type: "warning",
        });
        return;
    }
    if (!aaName.value) {
        ElNotify({
            message: "请输入名称！",
            type: "warning",
        });
        return;
    }
    if (/[\\:'"\n]/.test(aaName.value)) {
        ElNotify({
            message: "亲,你输入的不能包含\\:'\"等特殊字符串！",
            type: "warning",
        });
        return;
    }
    saving = true;
    new Promise<void>((resolve) => {
        request({
            url: "/api/AssistingAccounting/Check",
            method: "post",
            params: {
                aaType: aatype.value,
            },
            data: {
                aaeId: 0,
                aaNum: aaNum.value,
                aaName: aaName.value,
            },
        }).then((res: IResponseModel<string>) => {
            saving = false;
            if (res.state === 1000) {
                if (res.data === "Y") {
                    emit("save-fail");
                    ElNotify({
                        message: "编号重复，请重新编号！",
                        type: "warning",
                    });
                } else if (res.data === "Name") {
                    ElConfirm("亲，此名称已存在，是否继续保存？").then((r) => {
                        if (r) {
                            resolve();
                        } else {
                            emit("save-fail");
                        }
                    });
                } else {
                    resolve();
                }
            } else {
                emit("save-fail");
            }
        });
    }).then(() => {
        if (saving) return;
        saving = true;
        request({
            url: "/api/AssistingAccounting/QuickCreate",
            method: "post",
            data: {
                aaType: aatype.value,
                aaNum: aaNum.value,
                aaName: aaName.value,
            },
        }).then((res: IResponseModel<string>) => {
            saving = false;
            if (res.state === 1000) {
                emit("save-success", { aaeId: Number(res.data), aaNum: aaNum.value, aaName: aaName.value });
                ElNotify({
                    message: "保存成功",
                    type: "success",
                });
                useAssistingAccountingStore().getAssistingAccounting(aatype.value);
                useAssistingAccountingStore().getDepartment();
            } else {
                emit("save-fail");
                ElNotify({
                    message: res.msg,
                    type: "warning",
                });
            }
        });
    });
}

defineExpose({
    init,
});
</script>
