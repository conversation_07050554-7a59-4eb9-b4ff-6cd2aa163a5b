<template>
    <div class="content">
        <div class="main-content">
            <div class="title">发票资金一览表</div>
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">日期：</div>
                            <div class="line-item-field">
                                <el-date-picker
                                    v-model="searchInfo.StartDate"
                                    type="month"
                                    placeholder="选择月份"
                                    :teleported="false"
                                    :format="'YYYY年MM月'"
                                    value-format="YYYY-MM"
                                    style="width: 130px"
                                />
                                &nbsp; 至 &nbsp;
                                <el-date-picker
                                    v-model="searchInfo.EndDate"
                                    type="month"
                                    placeholder="选择月份"
                                    :teleported="false"
                                    :format="'YYYY年MM月'"
                                    value-format="YYYY-MM"
                                    style="width: 130px"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">分析条件：</div>
                            <div class="line-item-field">
                                <div style="display: flex; padding-right: 5px">
                                    <el-select v-model="searchInfo.analysisConditions" :teleported="false">
                                        <el-option value="2" label="销项发票+资金收款"></el-option>
                                        <el-option value="1" label="进项发票+资金付款"></el-option>
                                        <el-option value="3" label="进销项发票+资金收付款"></el-option>
                                    </el-select>
                                </div>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <span class="ml-10">往来单位：</span>
                    <SelectV2
                        style="width: 215px"
                        v-model="searchParams.Name"
                        placeholder="请选择"
                        :teleported="false"
                        :fit-input-width="true"
                        :filterable="true"
                        :isOuterTooltip="false"
                        :options="showCompanyList"
                        :toolTipOptions="{ dynamicWidth: true }"
                        @change="handleCompanyChange"
                        :remote="companyList.length > 0"
                        :filter-method="companyMethod"
                        @visible-change="companyVisibleChange"
                        :isSuffixIcon="true"
                    ></SelectV2>
                </div>
                <div class="main-tool-right">
                    <a v-if="checkPermission(['cashierinvoicetable-canexport'])" class="button" @click="handleExport">导出</a>
                </div>
            </div>
            <div :class="['main-center', { erp: isErp }]">
                <Table
                    ref="TableCom"
                    style="width: 100%"
                    :loading="loading"
                    :data="detailTableData"
                    :columns="columns"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :scrollbarShow="true"
                    :showOverflowTooltip="true"
                    :objectSpanMethod="objectSpanMethod"
                    @current-change="handleCurrentChange"
                    @size-change="handleSizeChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #invoiceNumber>
                        <el-table-column 
                            prop="invoiceNumber" 
                            label="发票号码" 
                            show-overflow-tooltip 
                            header-align="right"
                            :width="getColumnWidth(setModule, 'invoiceNumber')"
                        >
                            <template #default="scope">
                                <span
                                    v-if="
                                        checkPermission([
                                            scope.row.invoiceCategory == 10070 ? 'invoice-output-canview' : 'invoice-input-canview',
                                        ])
                                    "
                                    class="link"
                                    @click="showInvoice(scope.row)"
                                >
                                    {{ scope.row.invoiceNumber }}
                                </span>
                                <span v-else>{{ scope.row.invoiceNumber }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #line_sn_name>
                        <el-table-column 
                            prop="line_sn_name" 
                            label="日记账序号" 
                            show-overflow-tooltip 
                            header-align="right"
                            :width="getColumnWidth(setModule, 'line_sn_name')"
                        >
                            <template #default="scope">
                                <a
                                    v-if="
                                        scope.row.line_sn_name &&
                                        checkPermission(scope.row.jType == '1020' ? ['depositjournal-canview'] : ['cashjournal-canview'])
                                    "
                                    class="link show"
                                    style="display: flex; align-items: center"
                                    @click.stop="goToDetail(scope.row)"
                                >
                                    {{ scope.row.line_sn_name }}
                                </a>
                                <span v-else style="display: flex; align-items: center">{{ scope.row.line_sn_name }}</span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
export default {
    name: "CashierInvoiceInfo",
};
</script>
<script setup lang="ts">
import { ref, reactive, onMounted, onActivated, watch, onUnmounted, watchEffect } from "vue";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import { getGlobalToken } from "@/util/baseInfo";
import Table from "@/components/Table/index.vue";
import { usePagination } from "@/hooks/usePagination";
import SelectV2 from "@/components/SelectV2/index.vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { globalWindowOpenPage, getUrlSearchParams, globalExport } from "@/util/url";
import { InvoiceCategoryList, CashierCategoryList, getCurrentMonth, getOppositePartyName, setCIIColumns } from "../utils";
import type { ICashierInvoiceInfoItem } from "../types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { checkPermission } from "@/util/permission";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "CashierInvoiceInfo";
const route = useRoute();

const isErp = window.isErp;
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const currentPeriodInfo = ref("");

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();

// 搜索参数
const searchInfo = reactive({
    StartDate: dayjs().format("YYYY-MM"),
    EndDate: dayjs().format("YYYY-MM"),
    // 往来单位名称
    Name: "",
    analysisConditions: "2",
});

const searchParams = reactive({
    Name: searchInfo.Name,
    StartDate: searchInfo.StartDate,
    EndDate: searchInfo.EndDate,
    analysisConditions: searchInfo.analysisConditions,
    InvoiceCategory: 0,
    CashierCategory: 0,
});

const handleClose = () => {
    containerRef.value?.handleClose();
};

const handleReset = () => {
    searchInfo.StartDate = dayjs().format("YYYY-MM");
    searchInfo.EndDate = dayjs().format("YYYY-MM");
    searchInfo.Name = "";
    searchInfo.analysisConditions = "2";
};

const loading = ref(false);
const TableCom = ref<InstanceType<typeof Table>>();

// 同步params数据
const syncParamsData = () => {
    currentPeriodInfo.value = getCurrentMonth(searchInfo.StartDate, searchInfo.EndDate);
    // searchParams.Name = searchInfo.Name;
    searchParams.StartDate = searchInfo.StartDate;
    searchParams.EndDate = searchInfo.EndDate;
    searchParams.analysisConditions = searchInfo.analysisConditions;
    searchParams.InvoiceCategory = InvoiceCategoryList[searchInfo.analysisConditions as "1" | "2" | "3"];
    searchParams.CashierCategory = CashierCategoryList[searchInfo.analysisConditions as "1" | "2" | "3"];
};

const handleSearch = () => {
    if (!searchInfo.StartDate || !searchInfo.EndDate) {
        return ElNotify({
            type: "warning",
            message: "起始月份或结束月份不能为空",
        });
    }
    if (new Date(searchInfo.StartDate).getTime() > new Date(searchInfo.EndDate).getTime()) {
        return ElNotify({
            type: "error",
            message: "起始月份不能大于结束月份",
        });
    }
    syncParamsData();
    loadData();
    handleClose();
};

const loadData = () => {
    loading.value = true;
    let params = {
        PageIndex: paginationData.currentPage,
        PageSize: paginationData.pageSize,
        Name: searchParams.Name,
        StartDate: searchParams.StartDate,
        EndDate: searchParams.EndDate,
        InvoiceCategory: searchParams.InvoiceCategory,
        CashierCategory: searchParams.CashierCategory,
    };
    request({
        url: `/api/CashierInvoiceTable/PagingListForInfo`,
        method: "get",
        params,
    })
        .then((res: IResponseModel<{ rows: ICashierInvoiceInfoItem[]; total: number }>) => {
            detailTableData.value = res.data.rows;
            paginationData.total = res.data.total;
            columns.value = setCIIColumns(searchParams.analysisConditions as "1" | "2" | "3");
        })
        .finally(() => {
            loading.value = false;
        });
};

const handleExport = () => {
    if (paginationData.total === 0) {
        ElNotify({ message: "没有数据可导出！", type: "warning" });
        return;
    }
    let params = {
        Name: searchParams.Name,
        StartDate: searchParams.StartDate,
        EndDate: searchParams.EndDate,
        InvoiceCategory: searchParams.InvoiceCategory,
        CashierCategory: searchParams.CashierCategory,
    };
    globalExport(`/api/CashierInvoiceTable/info/Export?${getUrlSearchParams(params)}`);
};

const detailTableData = ref<ICashierInvoiceInfoItem[]>([]);

const columns = ref<IColumnProps[]>([]);

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }: any) => {
    if (row.cashierDate === "合计" || row.invoiceDate === "合计") {
        if (searchParams.analysisConditions === "3") {
            if ([0, 4].includes(columnIndex)) {
                return [1, 2];
            } else if ([1, 5].includes(columnIndex)) {
                return [0, 0];
            } else {
                return [1, 1];
            }
        } else {
            if ([0, 3].includes(columnIndex)) {
                return [1, 2];
            } else if ([1, 4].includes(columnIndex)) {
                return [0, 0];
            } else {
                return [1, 1];
            }
        }
    }
};

// 跳转发票
const showInvoice = (row: any) => {
    const invoiceParams = {
        invoiceCode: row.invoiceNumber,
        searchStartDate: row.invoiceDate,
        searchEndDate: row.invoiceDate,
        r: Math.random(),
        from: route.path,
    };
    if (row.invoiceCategory == 10080) {
        globalWindowOpenPage(`/Invoice/PurchaseInvoice?` + getUrlSearchParams(invoiceParams), "进项发票");
    } else {
        globalWindowOpenPage(`/Invoice/SalesInvoice?` + getUrlSearchParams(invoiceParams), "销项发票");
    }
};

// 日记账序号跳转
const goToDetail = (row: any) => {
    const { ac_id, cashierDate, line_sn, CashierCategory, created_date } = row;
    const params = {
        AC_ID: ac_id,
        CD_DATE: cashierDate,
        CREATED_DATE: created_date,
        JOURNAL_TYPE: CashierCategory,
        from: route.fullPath,
        appasid: getGlobalToken(),
    };
    globalWindowOpenPage("/Cashier/JournalPage?" + getUrlSearchParams(params), params.JOURNAL_TYPE === "INCOME" ? "收款凭据" : "付款凭据");
};

const companyList = ref<Array<{ value: string; label: string }>>([]);
const getCompanyList = () => {
    getOppositePartyName().then((res: IResponseModel<string[]>) => {
        companyList.value = res.data.map((item) => ({ value: item, label: item }));
    });
};
const handleCompanyChange = () => {
    if (paginationData.currentPage === 1) {
        loadData();
    } else {
        paginationData.currentPage = 1;
    }
};

const rd = ref(0);
onActivated(() => {
    const { StartDate, EndDate, opposite_party, analysisConditions, r } = route.query;
    if (rd.value !== (r as unknown as number)) {
        rd.value = r as unknown as number;

        searchParams.Name = opposite_party as string;
        searchInfo.StartDate = (StartDate as string) || "";
        searchInfo.EndDate = (EndDate as string) || "";
        searchInfo.analysisConditions = analysisConditions as string;
        syncParamsData();
        loadData();
    }
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    // syncParamsData();
    loadData();
});

onMounted(() => {
    window.addEventListener("reloadOppositeParty", getCompanyList);
    currentPeriodInfo.value = getCurrentMonth(searchInfo.StartDate, searchInfo.EndDate);
    getCompanyList();
});
onUnmounted(() => {
    window.removeEventListener("reloadOppositeParty", getCompanyList);
});

const showCompanyList = ref<any[]>([]);
watchEffect(() => {
    showCompanyList.value = JSON.parse(JSON.stringify(companyList.value));
});
const companyMethod = (value: string) => {
    showCompanyList.value = commonFilterMethod(value, companyList.value, 'label');
}
const companyVisibleChange = (visible: boolean) => {
    if (visible) {
        showCompanyList.value = JSON.parse(JSON.stringify(companyList.value));
    }
}
</script>
<style lang="less" scoped>
@import url(@/style/SelfAdaption.less);

.content {
    .main-content {
        :deep(.el-select-v2__placeholder) {
            text-align: left;
        }
        .main-center {
            position: relative;
            box-sizing: border-box;
            :deep(.el-table) {
                .el-popper.is-light {
                    max-width: 300px;
                    text-align: left;
                }
            }
        }
    }
}
</style>
