(function (w) {
    w.productType = "acc";
    w.isAccountingAgent = false;
    w.isErp = false;
    w.remainingDays = 9;
    w.isProSystem = false;
    w.isExpired = false;
    w.isBossSystem = false;

    if (w.location.origin === w.config.jLocalH5Url) {
        if (w.isProSystem) {
            w.jHost = w.config.jProUrl;
            w.jApiHost = w.config.jProApiUrl;
            w.jAccountBooksHost = w.config.jProAccountBooksUrl;
            w.jLmDiskHost = w.config.jProLmDiskUrl;
            w.jmHost = w.config.jmProUrl;
            w.jtaxHost = w.config.jProTaxUrl;
            w.shareReportHost = w.config.proShareReportUrl;
            w.importFromOther = w.config.proImportFromOther;
            w.printHost = w.config.jProPrintUrl;
            w.jReceiptImportHost = w.config.jProReceiptImportUrl;
        } else if (w.isErp) {
            w.jHost = w.config.jErpUrl;
            w.jApiHost = w.config.jErpApiUrl;
            w.jAccountBooksHost = w.config.jErpAccountBooksUrl;
            w.jLmDiskHost = w.config.jErpLmDiskUrl;
            w.jmHost = w.config.jmErpUrl;
            w.jtaxHost = w.config.jErpTaxUrl;
            w.shareReportHost = w.config.erpShareReportUrl;
            w.printHost = w.config.jErpPrintUrl;
            w.jReceiptImportHost = w.config.jErpReceiptImportUrl;
        } else if (w.isAccountingAgent) {
            w.productType = "aa";
            w.jHost = w.config.jAAUrl;
            w.jApiHost = w.config.jAAApiUrl;
            w.jAccountBooksHost = w.config.jAAAccountBooksUrl;
            w.jLmDiskHost = w.config.jAALmDiskUrl;
            w.jmHost = w.config.jmAAUrl;
            w.jtaxHost = w.config.jAATaxUrl;
            w.shareReportHost = w.config.aaShareReportUrl;
            w.importFromOther = w.config.freeImportFromOther;
            w.printHost = w.config.jAAprintUrl;
            w.jReceiptImportHost = w.config.jAAReceiptImportUrl;
        } else {
            w.jHost = w.config.jLocalUrl;
            w.jApiHost = w.config.jLocalApiUrl;
            w.jAccountBooksHost = w.config.jLocalAccountBooksUrl;
            w.jLmDiskHost = w.config.jLocalLmDiskUrl;
            w.jmHost = w.config.jmLocalUrl;
            w.jtaxHost = w.config.jLocalTaxUrl;
            w.shareReportHost = w.config.localShareReportUrl;
            w.importFromOther = w.config.freeImportFromOther;
            w.printHost = w.config.jPrintUrl;
            w.jReceiptImportHost = w.config.jReceiptImportUrl;
        }
    } else if (w.location.origin === w.config.jH5Url) {
        w.jHost = w.config.jUrl;
        w.jApiHost = w.config.jApiUrl;
        w.jAccountBooksHost = w.config.jAccountBooksUrl;
        w.jLmDiskHost = w.config.jLmDiskUrl;
        w.jmHost = w.config.jmUrl;
        w.jtaxHost = w.config.jTaxUrl;
        w.shareReportHost = w.config.freeShareReportUrl;
        w.importFromOther = w.config.freeImportFromOther;
        w.printHost = w.config.jPrintUrl;
        w.jReceiptImportHost = w.config.jReceiptImportUrl;
    } else if (w.location.origin === w.config.jProH5Url) {
        w.isProSystem = true;
        w.jHost = w.config.jProUrl;
        w.jApiHost = w.config.jProApiUrl;
        w.jAccountBooksHost = w.config.jProAccountBooksUrl;
        w.jLmDiskHost = w.config.jProLmDiskUrl;
        w.jmHost = w.config.jmProUrl;
        w.jtaxHost = w.config.jProTaxUrl;
        w.shareReportHost = w.config.proShareReportUrl;
        w.importFromOther = w.config.proImportFromOther;
        w.printHost = w.config.jProPrintUrl;
        w.jReceiptImportHost = w.config.jProReceiptImportUrl;
    } else if (w.location.origin === w.config.jAAH5Url) {
        w.productType = "aa";
        w.isAccountingAgent = true;
        w.jHost = w.config.jAAUrl;
        w.jApiHost = w.config.jAAApiUrl;
        w.jAccountBooksHost = w.config.jAAAccountBooksUrl;
        w.jLmDiskHost = w.config.jAALmDiskUrl;
        w.jmHost = w.config.jmAAUrl;
        w.jtaxHost = w.config.jAATaxUrl;
        w.shareReportHost = w.config.aaShareReportUrl;
        w.importFromOther = w.config.aaFreeImportFromOther;
        w.printHost = w.config.jAAprintUrl;
        w.jReceiptImportHost = w.config.jAAReceiptImportUrl;
    } else if (w.location.origin === w.config.jAAProH5Url) {
        w.productType = "aa";
        w.isProSystem = true;
        w.isAccountingAgent = true;
        w.jHost = w.config.jAAProUrl;
        w.jApiHost = w.config.jAAProApiUrl;
        w.jAccountBooksHost = w.config.jAAProAccountBooksUrl;
        w.jLmDiskHost = w.config.jAAProLmDiskUrl;
        w.jmHost = w.config.jmAAProUrl;
        w.jtaxHost = w.config.jAAProTaxUrl;
        w.shareReportHost = w.config.aaProShareReportUrl;
        w.importFromOther = w.config.aaProImportFromOther;
        w.printHost = w.config.jAAProPrintUrl;
        w.jReceiptImportHost = w.config.jAAProReceiptImportUrl;
    } else if (w.location.origin === w.config.jAABossH5Url) {
        w.productType = "aa";
        w.isAccountingAgent = true;
        w.isBossSystem = true;
        w.jHost = w.config.jAABossUrl;
        w.jApiHost = w.config.jAABossApiUrl;
        w.jAccountBooksHost = w.config.jAABossAccountBooksUrl;
        w.jLmDiskHost = w.config.jAALmDiskUrl;
        w.jmHost = w.config.jmAABossUrl;
        w.jtaxHost = w.config.jAATaxUrl;
        w.shareReportHost = w.config.aaShareReportUrl;
        w.importFromOther = w.config.freeImportFromOther;
    } else if (w.location.origin === w.config.jAAProBossH5Url) {
        w.productType = "aa";
        w.isProSystem = true;
        w.isAccountingAgent = true;
        w.isBossSystem = true;
        w.jHost = w.config.jAAProBossUrl;
        w.jApiHost = w.config.jAAProBossApiUrl;
        w.jAccountBooksHost = w.config.jAAProBossAccountBooksUrl;
        w.jLmDiskHost = w.config.jAAProLmDiskUrl;
        w.jmHost = w.config.jmAABossProUrl;
        w.jtaxHost = w.config.jAAProTaxUrl;
        w.shareReportHost = w.config.aaProShareReportUrl;
        w.importFromOther = w.config.proImportFromOther;
    } else if (w.location.origin === w.config.jErpH5Url) {
        w.isErp = true;
        w.jHost = w.config.jErpUrl;
        w.jApiHost = w.config.jErpApiUrl;
        w.jAccountBooksHost = w.config.jErpAccountBooksUrl;
        w.jLmDiskHost = w.config.jErpLmDiskUrl;
        w.jmHost = w.config.jmErpUrl;
        w.jtaxHost = w.config.jErpTaxUrl;
        w.shareReportHost = w.config.erpShareReportUrl;
        w.printHost = w.config.jErpPrintUrl;
        w.jReceiptImportHost = w.config.jErpReceiptImportUrl;
    }

    w.jFreeHost = w.config.jUrl;
    w.jProHost = w.config.jProUrl;
    w.jAAFreeHost = w.config.jAAUrl;
    w.jAAProHost = w.config.jAAProUrl;
    w.jFreeApiHost = w.config.jApiUrl;
    w.jProApiHost = w.config.jProApiUrl;
    w.jAAFreeApiHost = w.config.jAAApiUrl;
    w.jAAProApiHost = w.config.jAAProApiUrl;
    w.jFreeH5Host = w.config.jH5Url;
    w.jProH5Host = w.config.jProH5Url;
    w.jAAH5Host = w.config.jAAH5Url;
    w.jAAProH5Host = w.config.jAAProH5Url;
    w.jAAProBossH5Host = w.config.jAAProBossH5Url;
    w.jAABossH5Host = w.config.jAABossH5Url;
    w.accountSrvHost = w.config.accountSrvUrl;
    w.aaHost = w.config.aaUrl;
    w.eHost = w.config.eUrl;
    w.epHost = w.config.epUrl;
    w.scmHost = w.config.scmUrl;
    w.scmProHost = w.config.scmProUrl;
    w.apimHost = w.config.apimUrl;
    w.wwwHost = w.config.wwwUrl;
    w.erpHost = w.config.erpUrl;
    w.salaryHost = w.config.salaryUrl;
    w.paBankUrl = w.config.paBankUrl;
    w.spdbBankUrl = w.config.spdbBankUrl;
    w.cmbBankUrl = w.config.cmbBankUrl;
    w.psbcLoginUrl = w.config.psbcLoginUrl;
    w.maxRowNumber = w.config.maxRowNumber;
    w.wxworkSuitId = w.config.wxworkSuitId;
    w.wxGzhAppId = w.config.wxGzhAppId;
    w.preOpenBankUrl = w.config.preOpenBankUrl;
    w.fpHost = w.config.fpUrl;
    w.fpApiHost = w.config.fpApiUrl;
    w.authHost = w.config.authUrl;
    w.invoiceConfigUrl = w.config.invoiceConfigUrl;
    w.erpApiUrl = w.config.erpApiUrl;
    w.downLoadHost = w.config.downLoadUrl;
    w.appHost = "https://apptest.ningmengyun.com"

    w.isStopAIFunction = w.config.isStopAIFunction;
    w.invoiceTaskPolllingInterval = w.config.invoiceTaskPolllingInterval;
    w.FetchInoiceCodeTime = w.config.FetchInoiceCodeTime;
    delete window.config;
})(window);
