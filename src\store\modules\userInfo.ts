import { defineStore } from "pinia"
import { ref } from "vue"
import { Jrequest } from "@/utils/service"

// 定义用户信息接口
export interface ITaxBureauUserInfo {
  userSn: number
  userId: string
  userName: string
  nickName: string
  mobile: string
}

export const useUserInfoStore = defineStore("userInfo", () => {
  const userInfo = ref<ITaxBureauUserInfo | null>(null)

  // 获取用户信息
  const getUserInfo = () => {
    return new Promise<ITaxBureauUserInfo | null>((resolve, reject) => {
      if (userInfo.value) {
        resolve(userInfo.value)
        return
      }

      Jrequest({
        url: "/api/User/UserInfo",
        method: "post",
        data: {},
      })
        .then((res: any) => {
          if (res.state === 1000) {
            userInfo.value = res.data.data
            resolve(res.data)
          } else {
            reject(res.msg)
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  // 清除用户信息
  const clearUserInfo = () => {
    userInfo.value = null
  }

  return {
    userInfo,
    getUserInfo,
    clearUserInfo,
  }
})
