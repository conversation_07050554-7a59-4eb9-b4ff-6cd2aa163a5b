<template>
    <div class="content" style="overflow-x: hidden;">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="title">资产类别设置</div>
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <a class="button" v-permission="['fixedassettype-canedit']" @click="addFAType">新增</a>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <SearchView :width="160" :height="30" placeholder="输入编码或名称" @search="searchHandle" />
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div class="main-center">
                        <Table
                            id="tableLogs"
                            :data="tableData"
                            :columns="columns"
                            :loading="tableLoading"
                            :pageIsShow="true"
                            :page-sizes="paginationData.pageSizes"
                            :page-size="paginationData.pageSize"
                            :total="paginationData.total"
                            :current-page="paginationData.currentPage"
                            :use-normal-scroll="true"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            @refresh="handleRerefresh"
                            :scrollbar-show="true"
                            :tableName="setModule"
                        >
                            <template #operation>
                                <el-table-column 
                                    label="操作" 
                                    min-width="115" 
                                    align="left" 
                                    header-align="left" 
                                    :resizable="false"
                                >
                                    <template #default="scope">
                                        <a class="link" @click="EditHandle(scope.row.fa_type_id)" v-permission="['fixedassettype-canedit']">
                                            编辑
                                        </a>
                                        <a
                                            class="link"
                                            @click="deleteHandle(JSON.stringify(scope.row.fa_type_id))"
                                            v-permission="['fixedassettype-candelete']"
                                        >
                                            删除
                                        </a>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center">
                    <div class="slot-title">资产类别设置</div>
                    <AddFATypeView
                        ref="addFATypeRef"
                        :form="form"
                        :title="handleStatus"
                        @saveData="saveHandle"
                        @cancelData="cancelHandle2"
                        @clickLoad="clickLoad"
                    />
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "FixedAssetType",
};
</script>
<script setup lang="ts">
import ContentSlider from "@/components/ContentSlider/index.vue";
import Table from "@/components/Table/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ref, reactive, onMounted, watch,  } from "vue";
import { request } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { ValidateFixedAssetType } from "./validator";
import SearchView from "@/components/SearchInfo/index.vue";
import AddFATypeView from "./components/addFAType.vue";
import type { IForm } from "./types";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "FixedAssetType";
const addFATypeRef = ref<InstanceType<typeof AddFATypeView>>();

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const slots = ["main", "add"];
const currentSlot = ref("main");

const accountingStandard = ref(0);
const accountsetStore = useAccountSetStore();
accountingStandard.value = Number(accountsetStore.accountSet?.accountingStandard);

const handleStatus = ref("");
const editId = ref(0);
const columns: IColumnProps[] = [
    { 
        label: "资产类别编码", 
        prop: "fa_type_num", 
        minWidth: 147, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'fa_type_num')
    },
    { 
        label: "资产类别名称", 
        prop: "fa_type", 
        minWidth: 171, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'fa_type')
    },
    { 
        label: "折旧方法", 
        prop: "depreciation_type_name", 
        minWidth: 122, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'depreciation_type_name') 
    },
    { 
        label: "资产属性", 
        prop: "fa_property", 
        minWidth: 122, 
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, 'fa_property'),
        formatter: (row: any) => {
            switch(row.fa_property) {
                case 0:
                    return '固定资产';
                case 1:
                    return '无形资产';
                case 2:
                    return '长期待摊费用';
                default:
                    return '';
            }
        }
    },
    { 
        label: "资产科目", 
        prop: "fa_asub_name", 
        minWidth: 128, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'fa_asub_name') 
    },
    { 
        label: "使用月份", 
        prop: "monthes", 
        minWidth: 110, 
        align: "right", 
        headerAlign: "right",
        width: getColumnWidth(setModule, 'monthes')  
    },
    {
        label: "预计净残值率",
        prop: "netsalvage_rate",
        minWidth: 132,
        align: "right",
        headerAlign: "right",
        formatter: (row: any) => row.netsalvage_rate + "%",
        width: getColumnWidth(setModule, 'netsalvage_rate')  
    },
    { 
        label: "备注", 
        prop: "note", 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, 'note')  
    },
    { slot: "operation" },
];
const tableLoading = ref(false);
const tableData = ref([]);

const fanum = ref("");
const searchHandle = async (searchData: any) => {
    fanum.value = searchData;
    request({
        url: `/api/FixedAssetsType/PagingList?fanum=${fanum.value}&PageIndex=${paginationData.currentPage}&PageSize=${paginationData.pageSize}`,
        method: "get",
    }).then((res: any) => {
        tableData.value = res.data.data;
        paginationData.total = res.data.count;
    });
};

const clickLoad = () => {
    window.location.reload();
};

onMounted(() => {
    // Promise.all([getFixedAssetsAsubList(), getDeprecationAsubList()]).then(() => {
        getTableList();
    // });
});

const getTableList = () => {
    tableLoading.value = true;
    request({
        url: `/api/FixedAssetsType/PagingList?fanum=${fanum.value}&PageIndex=${paginationData.currentPage}&PageSize=${paginationData.pageSize}`,
        method: "get",
    })
        .then((res: any) => {
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
        })
        .finally(() => {
            tableLoading.value = false;
        });
};

function isValidKey(key: string | number | symbol, object: object): key is keyof typeof object {
    return key in object;
}

const editForm = (data: IForm) => {
    Object.keys(data).forEach((key) => {
        if (Object.keys(form).includes(key)) {
            if (isValidKey(key, data)) {
                form[key] = data[key];
            }
        }
    });
};

const EditHandle = async (FA_TYPE_ID: number) => {
    handleStatus.value = "edit";
    editId.value = FA_TYPE_ID;
    request({
        url: `/api/FixedAssetsType?fatypeId=${FA_TYPE_ID}`,
        method: "get",
    }).then((res: any) => {
        if (res.state == 1000) {
            editForm(res.data);
        }
    });
    currentSlot.value = "add";
    const timer = setTimeout(() => {
        addFATypeRef.value?.changeInit(true);
        addFATypeRef.value?.handleFocus();
        clearTimeout(timer);
    }, 1000);
};

const deleteHandle1 = async () => {
    const data = {
        page: paginationData.currentPage,
        rows: paginationData.pageSize,
        IsSearch: 0,
        fanum: "",
        ran: Math.random(),
    };
    request({
        url: `/api/FixedAssetsType/PagingList?fanum=${data.fanum}&PageIndex=${paginationData.currentPage}&PageSize=${paginationData.pageSize}`,
        method: "get",
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data.data.length === 0 && paginationData.currentPage) {
                paginationData.currentPage = paginationData.currentPage - 1;
                deleteHandle1();
            }
            tableData.value = res.data.data;
            paginationData.total = res.data.count;
        }
    });
};

const deleteHandle = async (FA_TYPE_ID: string) => {
    ElConfirm("亲，确认要删除吗?").then((r: any) => {
        if (r) {
            request({
                url: `/api/FixedAssetsType/IsUsed?faTypeId=${FA_TYPE_ID}`,
                method: "post",
            }).then((res: any) => {
                if (res.state == 1000) {
                    if (!res.data) {
                        request({
                            url: `/api/FixedAssetsType?fatypeId=${FA_TYPE_ID}`,
                            method: "delete",
                        }).then((res: any) => {
                            if (res.state == 1000) {
                                deleteHandle1();
                                ElNotify({
                                    type: "success",
                                    message: "删除成功！",
                                });
                            window.dispatchEvent(new CustomEvent("deleteFAType", { detail: FA_TYPE_ID }));
                            } else {
                                ElNotify({
                                    type: "warning",
                                    message: res.msg,
                                });
                            }
                        });
                    } else {
                        ElNotify({
                            type: "warning",
                            message: "亲，此资产类别已使用，不能删除哦！",
                        });
                    }
                }
            });
        }
    });
};

//新增资产类别
const form = reactive({
    fa_type_num: "",
    fa_type: "",
    fa_property: 0,
    depreciation_type: 1,
    monthes: "",
    netsalvage_rate: "",
    fa_asub: 0,
    depreciation_asub: 0,
    note: "",
});

//新增
const addFAType = () => {
    form.fa_property = 0;
    editForm(form);
    handleStatus.value = "new";
    currentSlot.value = "add";
    const timer = setTimeout(() => {
        addFATypeRef.value?.handleFocus();
        addFATypeRef.value?.changeInit(true);
        clearTimeout(timer);
    }, 1000);
};
const AddTypeFlag = ref(false);

//保存 DEPRECIATION_TYPE_NAME: number;
const saveFlag = ref(false); //防止重复提交
const saveHandle = async (form: any) => {
    if (saveFlag.value) return;
    const data = {
        fa_type_num: form.fa_type_num,
        fa_type: form.fa_type,
        fa_property: form.fa_property,
        depreciation_type: form.depreciation_type === 1 ? 1 : 0,
        monthes: form.monthes,
        netsalvage_rate: form.netsalvage_rate,
        fa_asub: form.fa_asub,
        depreciation_asub: form.depreciation_asub,
        note: form.note,

    };
    if (ValidateFixedAssetType(form,accountingStandard.value)) {
        saveFlag.value = true;
        request({
            url: handleStatus.value === "new" ? `/api/FixedAssetsType` : `/api/FixedAssetsType?fatypeId=${editId.value}`,
            method: handleStatus.value === "new" ? "post" : "put",
            data,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            },
        })
            .then(async (res: any) => {
                if (res.state == 1000) {
                    ElNotify({
                        type: "success",
                        message: "保存成功！",
                    });
                    await getTableList();
                    cancelHandle2()
                    window.dispatchEvent(new CustomEvent('AddTypeFlag', { detail: AddTypeFlag.value }))
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            })
            .finally(() => {
                saveFlag.value = false;
            });
    }
};
function restForm() {
    form.fa_type_num = "";
    form.fa_type = "";
    form.depreciation_type = 1;
    form.monthes = "";
    form.netsalvage_rate = "";
    form.note = "";
}
//取消
const cancelHandle2 = () => {
    restForm();
    currentSlot.value = "main";
    addFATypeRef.value?.resetInit();
};

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    getTableList();
});
onMounted(() => {
    window.addEventListener("modifyaccountSubject", () => {
    if(useRouterArrayStoreHook().routerArray.find((item) => item.title.includes('资产类别设置'))) {
        getTableList()
    }
    });
})

</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
@import "@/style/FixedAssets/FixedMulti-tabs.less";

:deep(.el-input),
:deep(.el-input--small) {
    .el-input__wrapper {
        padding: 1px 10px;
    }
    .el-input__inner {
        font-size: var(--el-form-label-font-size);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

:deep(.el-table__cell) {
    .cell.el-tooltip {
        min-width: 0px !important;
    }
}
</style>
