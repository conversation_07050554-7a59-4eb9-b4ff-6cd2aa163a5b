<template>
    <div class="content narrow-content">
        <div class="title">币别设置</div>
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between">
                        <div class="main-tool-left">
                            <a class="button solid-button" v-permission="['currency-canedit']" @click="handleNew">新增</a>
                            <ErpRefreshButton></ErpRefreshButton>
                        </div>
                        <div class="main-tool-right">
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <div v-if="isErp" class="divider-line"></div>
                    <div class="main-center">
                        <Table
                            :columns="columns"
                            :data="tableData"
                            :loading="loading"
                            :scrollbar-show="true"
                            :max-height="isErp ? 'calc(100vh - 110px)' : 'calc(100vh - 220px)'"
                            :tableName="setModule"
                        >
                            <template #operator>
                                <el-table-column label="操作" min-width="180px" align="left" header-align="left" :resizable="false">
                                    <template #default="scope">
                                        <span v-show="!scope.row.isBaseCurrency">
                                            <a class="link" v-permission="['currency-canedit']" @click="handleEdit(scope.row.id)"> 编辑 </a>
                                            <a class="link" v-permission="['currency-candelete']" @click="handleDelete(scope.row.id)">
                                                删除
                                            </a>
                                        </span>
                                    </template>
                                </el-table-column>
                            </template>
                        </Table>
                    </div>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ slotTitle }}</div>
                    <div class="slot-mini-content">
                        <el-form :model="form" label-width="120px">
                            <el-form-item label="币别：">
                                <input
                                    type="text"
                                    v-model="form.code"
                                    placeholder="示例：USD"
                                    ref="codeRef"
                                    maxlength="3"
                                    @keyup="handleCodeKeyUp($event)"
                                    @blur="validatorCode"
                                    @keyup.enter="handleKeyUp('code')"
                                />
                                <span class="input-tip">{{ spFcCode }}</span>
                            </el-form-item>
                            <el-form-item label="名称：">
                                <input
                                    type="text"
                                    v-model="form.name"
                                    ref="nameRef"
                                    placeholder="示例：美元"
                                    @input="validatorName"
                                    @blur="validatorName"
                                    @keyup.enter="handleKeyUp('name')"
                                />
                                <span class="input-tip">{{ spFcName }}</span>
                            </el-form-item>
                            <el-form-item label="汇率：">
                                <input
                                    type="text"
                                    v-model="form.rate"
                                    ref="rateRef"
                                    class="no-arrows"
                                    placeholder="示例：6.8"
                                    @keypress="handleRateKeyUp($event)"
                                    @input="handleRateInput($event)"
                                    @blur="validatorRate"
                                    @keyup.enter="handleKeyUp('rate')"
                                />
                                <span class="input-tip">{{ spFcRate }}</span>
                            </el-form-item>
                        </el-form>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSave">保存</a>
                            <a class="button ml-10" @click="handleCancel">取消</a>
                        </div>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
</template>

<script lang="ts">
export default {
    name: "Currency",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, onMounted, nextTick} from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useRoute } from "vue-router";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { useCurrencyStore } from "@/store/modules/currencyList";
import type { ICurrency } from "@/api/currency";

const setModule = "SetCurrency";
const handleKeyUp = (type: "code" | "name" | "rate") => {
    if (type === "code") {
        nameRef.value?.focus();
        return;
    }
    if (type === "name") {
        rateRef.value?.focus();
        return;
    }
    if (type === "rate") {
        codeRef.value?.focus();
        return;
    }
};

const codeRef = ref<HTMLInputElement>();
const nameRef = ref<HTMLInputElement>();
const rateRef = ref<HTMLInputElement>();
const columns: IColumnProps[] = [
    { label: "编码", prop: "code", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "code") },
    { label: "名称", prop: "name", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "name") },
    { label: "汇率", prop: "rate", minWidth: 180, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "rate") },
    {
        label: "是否本位币",
        prop: "isBaseCurrency",
        minWidth: 180,
        align: "left",
        headerAlign: "left",
        formatter: function (_row, _column, value) {
            return value ? "是" : "否";
        }, 
        width: getColumnWidth(setModule, "isBaseCurrency")
    },
    { slot: "operator" },
];
const isErp = ref(window.isErp);
const loading = ref(false);
const tableData = ref<ICurrency[]>([]);
const slots = ["main", "add"];
const currentSlot = ref("main");
let EditType: "New" | "Edit" = "New";
const slotTitle = ref<"新增币别" | "编辑币别">("新增币别");
const form = reactive({
    fcid: "",
    code: "",
    name: "",
    rate: "",
});

const currencyStore = useCurrencyStore();
const handleSearch = async(flag?: boolean) => {
    loading.value = true;
    await currencyStore.getCurrencyList();
    flag && (window.dispatchEvent(new CustomEvent("reloadCurrency")));
    tableData.value = currencyStore.fcList;
    loading.value = false;
};
let editItemFcid = -1;
const handleEdit = (fcid: number) => {
    EditType = "Edit";
    slotTitle.value = "编辑币别";
    request({ url: "/api/Currency?fcId=" + fcid }).then((res: IResponseModel<ICurrency>) => {
        editItemFcid = fcid;
        if (res.state == 1000) {
            const row = res.data;
            form.fcid = row.id + "";
            form.code = row.code;
            form.name = row.name;
            form.rate = row.rate + "";
            currentSlot.value = "add";
            nextTick(() => {
                init = true;
            });
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const handleNew = () => {
    EditType = "New";
    slotTitle.value = "新增币别";
    currentSlot.value = "add";
    init = true;
};
let canDelete = true;
const handleDelete = (fcid: number) => {
    if (!canDelete) return;
    ElConfirm("亲，确定要删除吗？").then((r: boolean) => {
        if (r) {
            canDelete = false;
            request({ url: "/api/Currency?fcid=" + fcid, method: "delete" })
                .then((res: IResponseModel<boolean>) => {
                    if (res.state == 1000) {
                        if (res.data) {
                            ElNotify({ type: "success", message: "亲，删除成功啦！" });
                            currencyStore.resetCurrencyList();
                            handleSearch(true);
                        } else {
                            ElNotify({ type: "warning", message: res.msg });
                        }
                    } else {
                        ElNotify({ type: "warning", message: res.msg });
                    }
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "亲，删除失败啦！" });
                })
                .finally(() => {
                    canDelete = true;
                });
        }
    });
};
let requestIng = false;
const handleSave = () => {
    if (requestIng) return;
    if (form.code.trim() === "") {
        spFcCode.value = "币别编码不能为空！";
        return;
    }
    if (spFcCode.value !== "" || spFcName.value !== "" || spFcRate.value !== "") return;
    const filterList = tableData.value.filter((item) => item.id !== editItemFcid);
    const codeList = filterList.map((item) => item.code);
    if (codeList.findIndex((item) => item === form.code) !== -1 && EditType === "Edit") {
        ElNotify({ type: "warning", message: "币别编码已经存在！" });
        return;
    }
    const params = {
        name: form.name,
        code: form.code,
        exchangeRate: form.rate,
    };
    const urlPath = EditType === "New" ? "Currency" : "Currency?fcid=" + form.fcid;
    requestIng = true;
    request({
        url: "/api/" + urlPath,
        method: EditType === "New" ? "post" : "put",
        data: params,
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state == 1000) {
                if (res.data) {
                    ElNotify({ type: "success", message: "亲，保存成功啦！" });
                    currencyStore.resetCurrencyList();
                    handleCancel();
                } else {
                    ElNotify({ type: "warning", message: res.msg });
                }
            } else {
                ElNotify({ type: "warning", message: res.msg });
            }
            requestIng = false;
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "亲，保存失败啦！" });
            requestIng = false;
        });
};
const handleCancel = () => {
    handleSearch(true);
    currentSlot.value = "main";
    setTimeout(() => {
        spFcCode.value = "";
        spFcName.value = "";
        spFcRate.value = "";
        form.fcid = "";
        form.code = "";
        form.name = "";
        form.rate = "";
        editItemFcid = -1;
        resetInit()
    }, 500);
};

handleSearch();

watch(
    () => form.name,
    (val) => {
        if (val.length > 12) form.name = val.slice(0, 12);
    }
);
const spFcCode = ref("");
const spFcName = ref("");
const spFcRate = ref("");

const handleCodeKeyUp = (e: KeyboardEvent) => {
    if ((e.which > 64 && e.which < 91) || (e.which > 96 && e.which < 122) || e.which < 21 || (e.which > 35 && e.which < 41)) {
        let v = form.code;
        v = v.toUpperCase();
        form.code = v;
        if (v.length < 3) {
            spFcCode.value = "币别需为三位的大写英文字母！";
        } else {
            spFcCode.value = "";
        }
        return;
    } else {
        const code = form.code;
        const rega = /^[A-Z]/;
        if (rega.exec(code) && code.length === 3) {
            return;
        } else {
            spFcCode.value = "币别需为三位的大写英文字母！";
            return;
        }
    }
};

const handleRateKeyUp = (e: KeyboardEvent) => {
    if ((e.keyCode < 48 || e.keyCode > 57) && e.keyCode != 46) {
        e.returnValue = false;
    } else {
        if (form.rate.includes(".") && e.keyCode == 46) {
            e.returnValue = false;
        }
    }
};
const handleRateInput = (e: Event) => {
    const rate = (e.target as HTMLInputElement).value;
    let integer = rate.split(".")[0];
    const decimal = rate.split(".")[1];
    if (integer.length > 9) integer = integer.slice(0, 9);
    let decimalStr = "";
    if (decimal) {
        const sliceStr = decimal.slice(0, 8);
        decimalStr = sliceStr;
    }
    let str = integer;
    if (rate.includes(".")) {
        str = integer + "." + (decimalStr ? decimalStr : "");
    } else {
        str = integer + (decimalStr ? "." + decimalStr : "");
    }
    form.rate = str;
};
const validatorCode = () => {
    const codeList = tableData.value.map((item) => item.code);
    const reg = /^[A-Z]{3}$/;
    if (form.code.trim() === "") {
        spFcCode.value = "币别编码不能为空！";
        return false;
    } else if (codeList.includes(form.code) && EditType === "New") {
        spFcCode.value = "币别编码已经存在！";
        return false;
    } else if (!reg.test(form.code)) {
        spFcCode.value = "币别需为三位的大写英文字母！";
        return false;
    } else {
        spFcCode.value = "";
        return true;
    }
};
const validatorName = () => {
    spFcName.value = form.name.trim() === "" ? "名称不能为空！" : "";
    return form.name.trim() !== "";
};
const validatorRate = () => {
    spFcRate.value = (form.rate + "").trim() === "" ? "汇率不能为空！" : "";
    return (form.rate + "").trim() !== "";
};

let init = false;
const route = useRoute();
const routerArrayStore = useRouterArrayStoreHook();
const currentPath = ref(route.path);
const isEditting = ref(false);
const resetInit = () => {
    init = false;
    isEditting.value = false;
};
watch(
    form,
    () => {
        if (!init) return;
        isEditting.value = true;
    },
    { deep: true }
);
watch(isEditting, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";
.no-arrows {
    .detail-spin-button();
}
.content {
    .main-content {
        text-align: left;
        height: auto;
    }
    .slot-content {
        .slot-mini-content {
            width: 1000px;
            padding: 32px 0;
            border: 1px solid var(--slot-title-color);
            margin-top: 32px;
            .input-tip {
                margin-left: 10px;
                color: var(--red);
                font-size: var(--font-size);
                line-height: 32px;
            }
            input {
                .detail-original-input(238px, 32px);
            }
            :deep(.el-form) {
                padding-left: 300px;
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
            .buttons {
                border-top: none;
            }
        }
    }
}
body[erp] {
    .content .main-content .main-center .table {
        height: auto;
    }
    .slot-content {
        .slot-mini-content {
            height: auto;
        }
    }
}
</style>
