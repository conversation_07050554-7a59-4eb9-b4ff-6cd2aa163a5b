<template>
    <div id="pcWxworkPermissionAlertDialog" class="pc-wxwork-permission-alert-dialog" v-if="pcWxworkPermissionAlertDialogShow">
        <div class="asset-line"></div>
        <div class="pic-container">
            <img class="pic" src="@/assets/Settings/pic1.png" />
            <img class="close-btn" src="@/assets/Settings/close-icon.png" @click="() => (pcWxworkPermissionAlertDialogShow = false)" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
const pcWxworkPermissionAlertDialogShow = ref(false);

function showPcWxworkPermissionAlertDialog() {
    pcWxworkPermissionAlertDialogShow.value = true;
}

defineExpose({
    showPcWxworkPermissionAlertDialog,
});
</script>

<style scoped lang="less">
.pc-wxwork-permission-alert-dialog {
    background-color: var(--shadow-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    text-align: center;
    font-size: 0;
    & .asset-line {
        height: 100%;
        width: 1px;
        margin-right: -1px;
        display: inline-block;
        vertical-align: middle;
    }
    & .pic-container {
        display: inline-block;
        vertical-align: middle;
        position: relative;
        & .pic {
            width: 596px;
            height: 388px;
        }
        & .close-btn {
            width: 20px;
            height: 20px;
            cursor: pointer;
            position: absolute;
            top: 40px;
            right: 40px;
        }
    }
}
</style>
