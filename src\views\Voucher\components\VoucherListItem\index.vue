<template>
    <div class="voucher-head">
        <div class="voucher-title" style="width: 214px; padding-left: 32px">
            <el-checkbox v-model="allState" :disabled="!voucherList.length" @change="handleAllChange"></el-checkbox>
            <span>摘要</span>
        </div>
        <div class="voucher-title" style="width: 346px">科目</div>
        <div class="voucher-title qut-title" style="width: 104px; text-align: right" v-show="[1, 3].includes(columnType)">数量</div>
        <div class="voucher-title qut-title" style="width: 104px; text-align: right" v-show="[1, 3].includes(columnType)">单价</div>
        <div class="voucher-title fc-title" style="width: 104px; text-align: right" v-show="[2, 3].includes(columnType)">币别</div>
        <div class="voucher-title fc-title" style="width: 104px; text-align: right" v-show="[2, 3].includes(columnType)">原币金额</div>
        <div class="voucher-title fc-title" style="width: 104px; text-align: right" v-show="[2, 3].includes(columnType)">汇率</div>
        <div class="voucher-title" style="width: 104px; text-align: right">借方金额</div>
        <div class="voucher-title" style="width: 104px; text-align: right; padding-right: 20px">贷方金额</div>
    </div>
    <div class="voucher-list-container" :class="{ 'empty-container': voucherList.length === 0 }">
        <el-scrollbar height="100%" :always="true" ref="scrollRef" @scroll="handleScroll">
            <div :class="[{ 'virtual-list': voucherList.length > hasVirtualCount }]">
                <div
                    class="voucher-list-item"
                    v-for="(rootItem, index) in voucherList.length > hasVirtualCount ? virtuallyData : voucherList"
                    :key="rootItem.vid + '-' + index"
                    :class="showFirstVoucherSource && index === 0 ? 'active-firstSourse' : ''"
                    @dblclick="handleCheckOrEdit(rootItem)"
                    @mouseleave="handleMouseLeave(index)"
                    @mouseenter="handleMouseEnter(index)"
                >
                    <div class="voucher-list-head">
                        <div class="left">
                            <el-checkbox v-model="rootItem.checked" @change="handleItemChange(rootItem, rootItem.checked)"></el-checkbox>

                            <span class="voucher-date">日期：{{ formatDate(rootItem.vdate) }} </span>
                            <span class="voucher-num ml-20">凭证字号：{{ rootItem.vgName + "-" + rootItem.vnum }}</span>
                            <a
                                class="link ml-20"
                                v-show="listType === 'VoucherList'"
                                @dblclick.stop
                                @mousedown.stop
                                @click="handleOpenFileCenter(rootItem.pid, rootItem.vid)"
                            >
                                附件：{{ rootItem.attachments }}
                            </a>
                            <div style="display: flex" v-if="listType === 'VoucherList' && rootItem.sourceWay.length">
                                <span class="voucher-source ml-40 hover-display"
                                    >凭证关联：{{ formatSourceName(rootItem.sourceWay) }}
                                </span>
                                <span class="voucher-source ml-20 hover-flex-display">
                                    <span>关联单号：</span>
                                    <el-tooltip placement="right" effect="light" :teleported="false">
                                        <template #content>
                                            <template v-if="checkHasErpBill(rootItem.sourceWay)">
                                                <div class="source-ls">
                                                    <span>业务凭证：</span>
                                                    <span
                                                        :class="{ link: checkPermission(['businessvoucher-canview']) }"
                                                        @click="handleRouteToErpPage(rootItem)"
                                                    >
                                                        {{ getErpBillInfo(rootItem.sourceWay, rootItem.source).billNoStr }}
                                                    </span>
                                                </div>
                                            </template>
                                            <template
                                                v-for="(sourceWay, sourceIndex) in getErpAccSourceWay(rootItem.sourceWay)"
                                                :key="sourceIndex"
                                            >
                                                <template v-if="isCashierOrInvoice(sourceWay)">
                                                    <div
                                                        class="source-ls"
                                                        v-for="(source, index) in getSourceBillInfo(sourceWay, rootItem.source).totalList"
                                                        :key="index"
                                                    >
                                                        <span>{{ getSourceDescription(sourceWay, source) }}</span>
                                                        <span
                                                            @click="
                                                                handleRouteToBillPage(
                                                                    rootItem,
                                                                    sourceWay,
                                                                    getSourceCategory(sourceWay, source)
                                                                )
                                                            "
                                                            :class="{
                                                                link: checkHasModulePermission(
                                                                    sourceWay,
                                                                    getSourceCategory(sourceWay, source)
                                                                ),
                                                            }"
                                                        >
                                                            {{ getSourceContent(rootItem.source, sourceWay, source) }}
                                                        </span>
                                                    </div>
                                                </template>
                                                <div class="source-ls" v-else>
                                                    <span>{{ getModuleName(sourceWay) + "：" }}</span>
                                                    <span
                                                        @click="handleRouteToBillPage(rootItem, sourceWay)"
                                                        :class="{ link: checkHasModulePermission(sourceWay) }"
                                                    >
                                                        {{ getBillPopContent(rootItem.source, sourceWay) }}
                                                    </span>
                                                </div>
                                            </template>
                                        </template>
                                        <span class="mr-10 source-a" @click.stop @dblclick.stop>
                                            {{ getBillContent(rootItem.source, rootItem.sourceWay) }}
                                        </span>
                                    </el-tooltip>
                                </span>
                            </div>
                        </div>
                        <div class="right">
                            <template v-if="listType === 'VoucherList'">
                                <span class="status check" v-if="rootItem.approveStatus === 1"></span>
                                <span class="offset status" v-if="rootItem.rf?.length"></span>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="
                                        checkPermission(
                                            rootItem.status === 3 || rootItem.approveStatus === 1
                                                ? ['voucher-canview']
                                                : ['voucher-canedit']
                                        )
                                    "
                                    @click="handleCheckOrEdit(rootItem)"
                                >
                                    {{ rootItem.status === 3 || rootItem.approveStatus === 1 ? "查看" : "修改" }}
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['voucher-canedit'])"
                                    @click="emit('handleCopy', rootItem.vid, rootItem.pid)"
                                >
                                    复制
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['voucher-candelete'])"
                                    @click="handleDelete(rootItem)"
                                >
                                    删除
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['voucher-canedit'])"
                                    @click="emit('handleInsert', rootItem.vid, rootItem.pid)"
                                >
                                    插入
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['voucher-canedit'])"
                                    @click="emit('handleRed', rootItem.vid, rootItem.pid, rootItem)"
                                >
                                    {{rootItem.rf?.length ? '查看' : ''}}红冲
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['vouchertemplate-canedit'])"
                                    @click="emit('handleSaveAsTemp', rootItem.voucherLines)"
                                >
                                    存为模板
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['voucher-canprint'])"
                                    @click="emit('handlePrint', rootItem.vid, rootItem.pid)"
                                >
                                    打印
                                </a>
                                <div class="voucher-note" v-show="rootItem.note !== ''">
                                    <a class="link ml-20">备注</a>
                                    <div class="voucher-note-content">
                                        {{ rootItem.note }}
                                    </div>
                                </div>
                            </template>
                            <template v-else>
                                <a
                                    class="link hover-display"
                                    v-if="checkPermission(['voucher-canedit'])"
                                    @click="emit('handleRestore', rootItem.rid)"
                                >
                                    还原
                                </a>
                                <a
                                    class="link hover-display ml-20"
                                    v-if="checkPermission(['voucher-candelete'])"
                                    @click="handleDeleteRecyclebin(rootItem.rid)"
                                >
                                    删除
                                </a>
                                <div class="voucher-note" v-show="rootItem.note !== ''">
                                    <a class="link ml-20">备注</a>
                                    <div class="voucher-note-content">
                                        {{ rootItem.note }}
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="voucher-list-lines">
                        <div
                            class="voucher-list-line"
                            v-show="rootItem.voucherLines.length > 0"
                            v-for="item in rootItem.voucherLines"
                            :key="item.veId"
                        >
                            <div class="voucher-list-description" v-html="replaceOnmouseleave(item)"></div>
                            <div class="voucher-list-asubName">{{ item.asubName }}</div>
                            <div class="voucher-list-quantity" v-show="[1, 3].includes(columnType)">
                                <template v-if="formatDecimals(item.quantity ?? 0) === item.quantity + ''">
                                    {{ item.quantity }}
                                </template>
                                <template v-else>
                                    <span :title="item.quantity + ''">{{ formatDecimals(item.quantity ?? 0) }}</span>
                                </template>
                            </div>
                            <div class="voucher-list-price" v-show="[1, 3].includes(columnType)">
                                <template v-if="formatDecimals(item.price ?? 0) === item.price + ''">
                                    {{ item.price }}
                                </template>
                                <template v-else>
                                    <span :title="item.price + ''">{{ formatDecimals(item.price ?? 0) }}</span>
                                </template>
                            </div>
                            <div class="voucher-list-fcname" v-show="[2, 3].includes(columnType)">
                                <span v-show="item.foreigncurrency">{{ item.fcName }}</span>
                            </div>
                            <div class="voucher-list-fcamount" v-show="[2, 3].includes(columnType)">
                                <span v-show="item.foreigncurrency">{{ formatMoney(item.fcAmount ?? 0, true, true) }}</span>
                            </div>
                            <div class="voucher-list-fcrate" v-show="[2, 3].includes(columnType)">
                                <span v-show="item.foreigncurrency">
                                    <template v-if="formatDecimals(item.fcRate ?? 0) === item.fcRate + ''">
                                        {{ item.fcRate }}
                                    </template>
                                    <template v-else>
                                        <span :title="item.fcRate + ''">{{ formatDecimals(item.fcRate ?? 0) }}</span>
                                    </template>
                                </span>
                            </div>
                            <div class="voucher-list-debit">{{ formatMoney(item.debit ?? 0) }}</div>
                            <div class="voucher-list-credit">{{ formatMoney(item.credit ?? 0) }}</div>
                        </div>
                        <div class="voucher-list-line">
                            <div class="voucher-list-description">合计</div>
                            <div class="voucher-list-asubName">{{ digitUppercase(getDebitOrCredit(rootItem.voucherLines, "credit")) }}</div>
                            <div class="voucher-list-quantity" v-show="[1, 3].includes(columnType)"></div>
                            <div class="voucher-list-price" v-show="[1, 3].includes(columnType)"></div>
                            <div class="voucher-list-fcname" v-show="[2, 3].includes(columnType)"></div>
                            <div class="voucher-list-fcamount" v-show="[2, 3].includes(columnType)"></div>
                            <div class="voucher-list-fcrate" v-show="[2, 3].includes(columnType)"></div>
                            <div class="voucher-list-debit">{{ formatMoney(getDebitOrCredit(rootItem.voucherLines, "debit")) }}</div>
                            <div class="voucher-list-credit">{{ formatMoney(getDebitOrCredit(rootItem.voucherLines, "credit")) }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-show="voucherList.length === 0" class="dv-no-data">
                <span class="no-data">{{ noDataText }}</span>
            </div>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
import { computed, watch, ref, nextTick, onActivated, inject, onDeactivated } from "vue";
import { formatMoney, digitUppercase } from "@/util/format";
import { ElNotify } from "@/util/notify";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { checkPermission } from "@/util/permission";
import type { IVoucherListItem } from "./types";
import {
    formatSourceName,
    getBillPopContent,
    checkHasModulePermission,
    getBillContent,
    handleRouteToBillPage,
    getModuleName,
    isCashierOrInvoice,
    getSourceDescription,
    getSourceCategory,
    getSourceContent,
    getSourceBillInfo,
    handleRouteToErpPage,
    checkHasErpBill,
    getErpAccSourceWay,
    getErpBillInfo,
} from "@/views/Voucher/VoucherList/utils";
import type { IVoucherLine } from "@/views/Voucher/VoucherList/types";
import { replaceAll } from "@/util/common";
import { pageScrollKey } from "@/symbols";
import { getOffsetTop, getSize, getSEPos } from "@/components/Table/VirtualScroll";
import { cloneDeep } from "lodash";

const formatDecimals = (val: number) => {
    if (val === 0) return "";
    const valStr = val.toString();
    // 存在小数位
    if (valStr.includes(".")) {
        const integer = valStr.split(".")[0];
        let decimals = valStr.split(".")[1];
        const decimalPlace = useAccountSetStore().accountSet?.decimalPlace ?? 2;
        decimals = decimals.slice(0, decimalPlace);
        let formattedStr = `${integer}.${decimals}`;
        if (decimalPlace) {
            formattedStr = formattedStr.replace(new RegExp(`\\.?0{1,${decimalPlace}}$`), ""); // 去除小数位末尾的0
        } else {
            formattedStr = formattedStr.replace(".", ""); // 去除小数点
        }
        return formattedStr;
    } else {
        return valStr;
    }
};

const props = withDefaults(
    defineProps<{
        columnType: number;
        tableData?: IVoucherListItem[];
        listType?: "VoucherList" | "VoucherRecyclebinList";
    }>(),
    {
        listType: "VoucherList",
        tableData: () => [],
    }
);
const allState = ref(false);
const columnType = computed(() => props.columnType);
const initEnd = ref(false);
const noDataText = computed(() => {
    return initEnd.value ? "暂无数据" : "正在加载中";
});
const setInitEnd = (val: boolean) => {
    initEnd.value = val;
};

const tableData = computed(() => props.tableData);
const voucherList = ref<IVoucherListItem[]>([]);
const listType = computed(() => {
    return props.listType;
});

const emit = defineEmits([
    "handlePrint",
    "handleDelete",
    "handleDeleteRecyclebin",
    "handleRestore",
    "handleCheck",
    "handleCopy",
    "handleRed",
    "handleInsert",
    "handleEdit",
    "handleOpenFileCenter",
    "handleSaveAsTemp",
]);
const handleOpenFileCenter = (pid: number, vid: number) => {
    emit("handleOpenFileCenter", pid, vid);
};
const handleCheckOrEdit = (item: IVoucherListItem) => {
    const event = item.status === 3 || item.approveStatus === 1 ? "handleCheck" : "handleEdit";
    emit(event, item.vid, item.pid, item.vstatus);
};
const handleDelete = (item: IVoucherListItem) => {
    if (item.approveStatus === 1) {
        ElNotify({ type: "warning", message: "亲，已审核的凭证不能直接删除，请先将该凭证取消审核！" });
        return;
    }
    emit("handleDelete", item.vid, item.pid);
};

const getDebitOrCredit = (list: any[], type: "debit" | "credit") => {
    const result: number = list.reduce((prev, cur) => {
        prev += cur[type] - 0;
        return prev;
    }, 0);
    return result;
};

const formatDate = (date: string) => {
    // 2023/1/31 0:00:00 => 2023-1-31
    const dateArr = date.split(" ")[0].split("/");
    return dateArr.join("-");
};

const handleItemChange = (row: IVoucherListItem, checked: boolean) => {
    if (voucherList.value.length > hasVirtualCount) {
        voucherList.value.find((item) => item.pid + item.vgName + item.vnum === row.pid + row.vgName + row.vnum)!.checked = checked;
    }
    const select = voucherList.value.filter((item: any) => item.checked);
    allState.value = select.length === voucherList.value.length;
};
const showFirstVoucherSource = ref(true);
const handleMouseLeave = (index: number) => {
    if (index === 0) {
        showFirstVoucherSource.value = false;
    }
};
const handleMouseEnter = (index: number) => {
    if (index !== 0) {
        showFirstVoucherSource.value = false;
    }
};

const getSelectedVoucherList = () => {
    return voucherList.value.filter((item: any) => item.checked);
};

const clearSelectedVoucherList = () => {
    allState.value = false;
    voucherList.value = voucherList.value.map((item: any) => {
        item.checked = false;
        return item;
    });
};

watch(tableData, (val) => {
    voucherList.value = val;
});
const handleAllChange = () => {
    for (let i = 0; i < mapData.value.length; i++) {
        mapData.value[i][selectKey] = !!allState.value;
    }
    if (allState.value) {
        voucherList.value = voucherList.value.map((item: any) => {
            item.checked = true;
            return item;
        });
    } else {
        voucherList.value = voucherList.value.map((item: any) => {
            item.checked = false;
            return item;
        });
    }
};
const setSelectList = (list: IVoucherListItem[]) => {
    nextTick().then(() => {
        voucherList.value = voucherList.value.map((item: IVoucherListItem) => {
            const index = list.findIndex((listItem) => listItem.pid === item.pid && listItem.vid === item.vid);
            if (index !== -1) {
                item.checked = true;
            }
            return item;
        });
    });
};
const scrollRef = ref();
function setScrollTop(top = 0) {
    scrollRef.value?.setScrollTop(top);
}
let toTop = true;
let requestTop = 0;
let cacheSizes: any = null;
function debugScroll() {
    toTop = false;
    const timer = setTimeout(() => {
        toTop = true;
        requestTop = 0;
        cacheSizes = null;
        clearTimeout(timer);
    }, 300);
}
function cacheLastScrollTop() {
    requestTop = cacheScrollTop;
    cacheSizes = cloneDeep(sizes.value);
}

defineExpose({
    getSelectedVoucherList,
    clearSelectedVoucherList,
    setSelectList,
    setInitEnd,
    setScrollTop,
    debugScroll,
    cacheLastScrollTop,
});

const decimalPlace = useAccountSetStore().accountSet?.decimalPlace || 2;
function replaceOnmouseleave(item: IVoucherLine): string {
    const str = item.descriptionForPc.length === 0 ? item.description : item.descriptionForPc;
    let returnStr = str;
    if (item.price !== 0) {
        const quantityPrice = `单价:<span title='${item.price}'>` + formatDecimals(item.price) + `</span>`;
        if (getDecimalPlaces(item.price) > decimalPlace) returnStr = replaceAll(returnStr, "单价:" + item.price, quantityPrice);
    }
    if (item.quantity !== 0) {
        const quantityPrice = `数量:<span title='${item.quantity}'>` + formatDecimals(item.quantity) + `</span>`;
        if (getDecimalPlaces(item.quantity) > decimalPlace) returnStr = replaceAll(returnStr, "数量:" + item.quantity, quantityPrice);
    }
    if (item.fcRate !== 0) {
        const fcRate = `汇率:<span title='${item.fcRate}'>` + formatDecimals(item.fcRate) + `</span>`;
        if (getDecimalPlaces(item.fcRate) > decimalPlace) returnStr = replaceAll(returnStr, "汇率:" + item.fcRate, fcRate);
    }
    return returnStr;
}
function getDecimalPlaces(num: number): number {
    const decimalPart = num.toString().split(".")[1];
    return decimalPart ? decimalPart.length : 0;
}
const pageScroll = inject(pageScrollKey);
onActivated(() => {
    showFirstVoucherSource.value = true;
    setScrollTop(cacheScrollTop);
    pageScroll &&
        pageScroll(cacheScrollTop > 0, () => {
            cacheScrollTop > 0 && setScrollTop(0);
        });
});
onDeactivated(() => {
    pageScroll && pageScroll(false, () => {});
});
let cacheScrollTop = 0;
function handleScroll(event: any) {
    cacheScrollTop = event.scrollTop;
    pageScroll &&
        pageScroll(event.scrollTop > 0, () => {
            event.scrollTop > 0 && setScrollTop(0);
        });
    voucherList.value.length > hasVirtualCount && updateView();
}
watch(
    () => voucherList.value,
    () => {
        mapData.value = voucherList.value.map((item, index) => ({
            ...item,
            [onlyKey]: index,
            [selectKey]: false,
        }));
        updateView();
    }
);
//虚拟滚动
function findScrollDom() {
    return scrollRef.value?.$el.querySelector(".el-scrollbar__wrap") as HTMLElement;
}
const hasVirtualCount = 300;
const onlyKey = "onlyKey";
const selectKey = "selectKey";
const mapData = ref<any[]>([]);

const rowHeight = 138; //39+33*3
const virtuallyData = ref<any[]>([]);
const sizes = ref<any>({});
const buffer = 500;
const start = ref(0);
const end = ref(0);
let timer: any = null;
const offsetMap = computed(() => {
    const res: any = {};
    let total = 0;
    for (let i = 0; i < voucherList.value.length; i++) {
        const key = mapData.value[i][onlyKey];
        res[key] = total;
        const curSize = sizes.value[key];
        const size = typeof curSize === "number" ? curSize : rowHeight;
        total += size;
    }
    return res;
});
const renderVirtualTable = (shouldUpdate = true) => {
    if (voucherList.value.length <= hasVirtualCount) {
        if (!toTop) {
            scrollRef.value?.setScrollTop(requestTop);
            cacheSizes = null;
        }
    }
    if (cacheSizes && !toTop) {
        sizes.value = cloneDeep(cacheSizes);
    }
    updateSizes();
    calcRenderData();
    calcPosition();
    shouldUpdate && updatePosition();
};
const updateSizes = () => {
    const rows = scrollRef.value?.$el.querySelectorAll(".voucher-list-item") as unknown as any[];
    if (rows) {
        Array.from(rows).forEach((row, index) => {
            const item = virtuallyData.value[index];
            if (!item) return;
            const key = item[onlyKey];
            const offsetHeight = row.offsetHeight;
            if (sizes.value[key] !== offsetHeight) {
                sizes.value[key] = offsetHeight;
            }
        });
    }
};
const calcRenderData = () => {
    const scroller = findScrollDom();
    let top = scroller.scrollTop;
    if (!toTop) {
        top = requestTop;
    }
    const thisTop = top - buffer;
    const thisBottom = top + scroller.offsetHeight + buffer;

    const result = getSEPos(thisTop, thisBottom, voucherList.value, mapData.value, offsetMap.value, onlyKey);

    start.value = result.thisStart;
    end.value = result.thisEnd;
    virtuallyData.value = mapData.value.slice(result.thisStart, result.thisEnd + 1);
};
const calcBodyHeight = () => {
    const last = voucherList.value.length - 1;
    const wrapHeight =
        getOffsetTop(last, mapData.value, offsetMap.value, onlyKey) + getSize(last, virtuallyData.value, sizes.value, onlyKey, rowHeight);
    const virtualBody = scrollRef.value?.$el.querySelector(".el-scrollbar__view") as HTMLElement;
    virtualBody && (virtualBody.style.height = wrapHeight + "px");
};
const calcPosition = () => {
    const offsetTop = getOffsetTop(start.value, mapData.value, offsetMap.value, onlyKey);
    const listBody = scrollRef.value?.$el.querySelector(".virtual-list") as HTMLElement;
    listBody && (listBody.style.transform = `translateY(${offsetTop}px)`);
    !toTop && setScrollTop(requestTop);
    calcBodyHeight();
};
const updatePosition = () => {
    timer && clearTimeout(timer);
    timer = setTimeout(() => {
        timer && clearTimeout(timer);
        renderVirtualTable(false);
    }, 100);
};
const updateView = () => {
    renderVirtualTable();
};
const handleDeleteRecyclebin = (rid: number) => {
    emit("handleDeleteRecyclebin", [rid]);
};
</script>

<style lang="less" scoped>
.voucher-head {
    display: flex;
    background-color: var(--table-title-color);
    height: 37px;
    align-items: center;
    text-align: left;

    .voucher-title {
        color: var(--font-color);
        font-size: var(--h5);
        line-height: 37px;
        font-weight: bold;
        float: left;
        padding-left: 20px;
        position: relative;

        :deep(.el-checkbox) {
            height: 37px;
            position: absolute;
            left: 10px;
        }
    }
}

.voucher-list-item {
    border: 1px solid var(--table-border-color);
    margin-top: 10px;

    &:first-child {
        margin-top: 0;
    }
    &.active-firstSourse {
        &:first-child {
            .voucher-list-head {
                .hover-display {
                    display: inline-block !important;
                }
                .hover-flex-display {
                    display: inline-flex !important;
                }
                .left {
                    :deep(.el-checkbox) {
                        display: inline-flex;
                    }
                }
            }
        }
    }

    &:hover {
        .voucher-list-head {
            .hover-display {
                display: inline-block !important;
            }
            .hover-flex-display {
                display: inline-flex !important;
            }
            .left {
                :deep(.el-checkbox) {
                    display: inline-flex;
                }
            }
        }
    }

    .voucher-list-head {
        height: 39px;
        position: relative;
        padding-left: 32px;
        padding-right: 15px;
        background-color: var(--table-title-color);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            display: flex;
            align-items: center;

            :deep(.el-checkbox) {
                height: 29px;
                position: absolute;
                left: 10px;
                display: none;
                &.is-checked {
                    display: inline-flex;
                }
            }
        }

        .right {
            min-width: 500px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            position: relative;
            .status {
                display: inline-block;
                width: 80px;
                height: 40px;
                position: absolute;
                background-size: 100% 100%;
                top: 5px;
                &:first-child {
                    right: 450px;
                }
                &:nth-child(2) {
                    right: 550px;
                }
            }
            .offset {
                background-image: url("@/assets/Voucher/offset.png");
            }
            .check {
                background-image: url("@/assets/Voucher/check.png");
            }
        }

        .hover-display,
        .hover-flex-display {
            display: none !important;

            &.checked {
                display: inline-block !important;
            }
        }

        .voucher-note {
            position: relative;
            display: inline-flex;

            &:hover {
                .voucher-note-content {
                    display: block;
                }
            }

            .voucher-note-content {
                z-index: 10;
                position: absolute;
                top: 28px;
                right: -10px;
                border: 1px solid var(--border-color);
                min-height: 100px;
                width: 200px;
                background-color: var(--white);
                word-break: break-all;
                font-family: 微软雅黑 !important;
                color: #333;
                font-size: 12px !important;
                text-align: left;
                display: none;
                padding: 10px;
                border-radius: 4px;
                &::before {
                    content: "";
                    position: absolute;
                    top: -6px;
                    right: 14px;
                    width: 10px; 
                    height: 10px; 
                    transform: rotate(45deg); 
                    background-color: #fff;
                    border-left: 1px solid var(--border-color);
                    border-top: 1px solid var(--border-color);
                }
            }
        }

        // 本次提交改的凭证列表样式均为20231208由颜园园提出
        // 开发表示觉得很怪异，但得到不碍事的回复
        .voucher-date,
        .voucher-num,
        .voucher-source {
            display: inline-block;
            color: #333;
            font-size: var(--h5);
            line-height: 17px;
        }
        .voucher-date,
        .voucher-num {
            font-weight: bolder;
        }
        .voucher-source {
            .source-a {
                display: inline-block;
                line-height: 17px;
                text-align: left;
                white-space: nowrap;
                &.link {
                    text-decoration: none;
                }
            }
            .source-ls {
                text-align: left;
            }
        }
        .ml-40 {
            margin-left: 40px;
        }
        .link {
            line-height: 17px;
            display: inline-block;
            font-size: var(--h5);
        }
    }
    &:nth-last-child(2) {
        .voucher-note-content {
            bottom: 28px;
            top: auto !important;
        }
    }
    &:first-child {
        .voucher-note-content {
            top: 28px !important;
            bottom: auto !important;
        }
    }
    .voucher-list-lines {
        .voucher-list-line:nth-child(even) {
            background-color: #f8f8f8;
        }
        .voucher-list-line {
            display: flex;
            align-items: center;
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 17px;
            padding: 8px 20px 7px 12px;
            cursor: pointer;
            border-top: 1px solid var(--table-border-color);

            & > div {
                padding-left: 20px;
                display: inline-block;
                word-break: break-all;
                vertical-align: top;
                text-align: left;

                &.voucher-list-description {
                    width: 214px;
                }

                &.voucher-list-asubName {
                    width: 344px;
                }

                &.voucher-list-quantity,
                &.voucher-list-price,
                &.voucher-list-fcname,
                &.voucher-list-fcamount,
                &.voucher-list-fcrate,
                &.voucher-list-debit,
                &.voucher-list-credit {
                    width: 104px;
                    text-align: right;
                }
            }

            &:hover {
                background-color: var(--table-hover-color);
            }
        }
    }
}

:deep(.el-checkbox:last-of-type) {
    margin-right: 10px;
}

.dv-no-data {
    min-height: 450px;
    .no-data {
        text-align: center;
        color: var(--weaker-font-color);
        font-size: var(--h5);
        line-height: 450px;
        padding: 8px 20px 7px 12px;
    }
}
</style>
