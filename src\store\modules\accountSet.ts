import { ref } from "vue";
import store from "@/store";
import { defineStore } from "pinia";
import { getUserPermission<PERSON>pi, getUserInfoApi, type IUserInfo } from "@/api/login";
import { getGlobalToken } from "@/util/baseInfo";
import { getAccountSetBaseInfoApi, getAccountSetInfoApi, type IAccountSetBaseInfo, type IAccountSetInfo } from "@/api/accountSet";
import type { IResponseModel } from "@/util/service";

export const useAccountSetStore = defineStore("accountset", () => {
    const appasid = ref<string>("");
    const permissions = ref<string[]>([]);
    const accountSet = ref<IAccountSetBaseInfo>();
    const accountSetInfo = ref<IAccountSetInfo>();
    const userInfo = ref<IUserInfo>();
    const getPermission = () => {
        return new Promise<string[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getUserPermissionApi()
                    .then((res: IResponseModel<any>) => {
                        const data = res.data;
                        permissions.value.length = 0;
                        for (const key in data) {
                            if (data[key] === 1) {
                                permissions.value.push(key);
                            }
                        }
                        appasid.value = globalToken;
                        resolve(permissions.value);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    const getAccountSet = () => {
        return new Promise<IAccountSetBaseInfo>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                getAccountSetBaseInfoApi()
                    .then((data) => {
                        if (data.state === 1000) {
                            accountSet.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    const getUserInfo = () => {
        return new Promise<IUserInfo>((resolve, reject) => {
                getUserInfoApi()
                    .then((res: any) => {
                        if (res.state === 1000) {
                            userInfo.value = res.data.data;
                            resolve(res.data);
                        } else {
                            reject(res.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });

        });
    };

    const getAccountSetInfo = (forceUpdate = false) => {
        return new Promise<IAccountSetInfo>((resolve, reject) => {
            const asId = accountSet.value?.asId
            if (accountSetInfo.value && !forceUpdate) {
                resolve(accountSetInfo.value);
                return;
            }
            if (!asId) {
                reject("token为空");
            } else {
                getAccountSetInfoApi(asId)
                    .then((data) => {
                        if (data.state === 1000) {
                            accountSetInfo.value = data.data;
                            resolve(data.data);
                        } else {
                            reject(data.msg);
                        }
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    }
    return { appasid, permissions, accountSet, accountSetInfo, userInfo, getPermission, getAccountSet, getUserInfo, getAccountSetInfo };
});

export enum AccountStandard {
    LittleCompanyStandard = 1,
    CompanyStandard = 2,
    FolkComapnyStandard = 3,
    FarmerCooperativeStandard = 4,
    FarmerCooperativeStandard2023 = 5,
    UnionStandard = 6, // 工会会计制度
    VillageCollectiveEconomyStandard = 7, // 农村集体经济组织会计制度
}
// 报表类型的枚举
export enum ReportTypeEnum {
    // 资产负债表
    BalanceSheet = 1,
    // 利润表
    IncomeSheet = 2,
    // 现金流量表
    FlowSheet = 3,
    // 现金流量调整表
    FlowAdjustSheet = 4,
    // 纳税统计表
    TaxSheet = 5,
    // 财务概要信息表
    ManagerSheet = 6,
    // 主要财务指标
    FinanceIndexSheet = 7,
    // 利润表季报
    IncomeQuaterSheet = 8,
    // 业务活动表
    BusinessActivitySheet = 9,
    // 所有者损益变动表
    OwnChangeSheet = 10,
    // 研发支出辅助汇总表
    YanFaZhuChuFuZhuSheet = 11,
    // 资产负债表月报
    BalanceSheetForMonthWithTax = 12,
    // 资产负债表季报
    BalanceSheetForQuarterWithTax = 13,
    // 资产负债表年报
    BalanceSheetForYearWithTax = 14,
    // 利润表月报
    IncomeSheetForMonthWithTax = 15,
    // 利润表季报
    IncomeSheetForQuarterWithTax = 16,
    // 利润表年报
    IncomeSheetForYearWithTax = 17,
    // 现金流量表月表
    FlowSheetForMonthWithTax = 18,
    // 现金流量表季报
    FlowSheetForQuarterWithTax = 19,
    // 现金流量表年报
    FlowSheetForYearWithTax = 20,
    // 辅助核算利润表
    AssitIncomeSheet = 21,
    // 资产负债表（未执行）
    NewBalanceSheetUnExecuted = 22,
    // 资产负债表（已执行）
    NewBalanceSheetExecuted = 23,
    // 利润表（未执行）
    NewIncomeSheetUnExecuted = 24,
    // 利润表（已执行）
    NewIncomeSheetExecuted = 25,
    // 现金流量表（未执行）
    NewFlowSheetUnExecuted = 26,
    // 现金流量表（已执行）
    NewFlowSheetExecuted = 27,
    // 利润表季报（未执行）
    NewIncomeQuaterSheetUnExecuted = 28,
    // 利润表季报（已执行）
    NewIncomeQuaterSheetExecuted = 29,
    // 辅助核算利润表季报
    AssitIncomeQuaterSheet = 30,
    // 辅助核算利润表（未执行）
    NewAssitIncomeSheetUnExecuted = 31,
    // 辅助核算利润表（已执行）
    NewAssitIncomeSheetExecuted = 32,
    // 辅助核算利润表季报（未执行）
    NewAssitIncomeQuaterSheetUnExecuted = 33,
    // 辅助核算利润表季报（已执行）
    NewAssitIncomeQuaterSheetExecuted = 34,
    // 盈余及盈余分配表
    SurplusDistributionStatement = 35,
    // 成员权益变动表
    MemberEquityChangesStatement = 36,
    // 利润表（不含本年）
    IncomeSheetWithoutInitialAmount = 37,
    // 收入支出表
    IncomeAndExpenditureSheet = 43,
    // 成本费用表
    CostSheet = 44,
    // 收益及收益分配表
    IncomeAndDistributionSheet = 45,
    // 所有者权益变动表（小企业 + 企业2007）
    OwerEquityChangeStatementDefault = 47,
    // 所有者权益变动表（企业已执行）
    OwerEquityChangeStatementExecuted = 48,
    // 所有者权益变动表（企业未执行）
    OwerEquityChangeStatementUnExecuted = 49,
}

export enum AccountSubjectType {
    // 资产类
    Asset = 1,
    // 负债类
    Debit = 2,
    // 共同类
    Common = 3,
    // 权益类
    Owe = 4,
    // 成本类
    Cost = 5,
    // 损益类
    Income = 6,
    // 净资产类
    NetWorth = 7,
    // 收入类
    Revenue = 8,
    // 费用类
    Expenses = 9,
    // 支出类
    Disbursement = 10,
    // 所有科目 (WebApi 区分接口One or All)
    All = 20,
}

/** 在 setup 外使用 */
export function useAccountSetStoreHook() {
    return useAccountSetStore(store);
}
