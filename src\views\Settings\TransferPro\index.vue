<template>
    <div class="content">
        <div class="title">账套升级</div>
        <div class="main-content">
            <div class="main-transfer-top">
                <div class="notice-cst">
                    <img src="@/assets/Cashier/zhuyi.png" />
                    <span class="notice-txt">
                        <span
                            >只有账套管理员和主管能直接升级账套，其他人需授权再升级账套。账套管理员升级后，原免费版账套将被放到回收站</span
                        >
                    </span>
                </div>
                <div class="account-operate">
                    <span class="main-top-txt"
                        >剩余可升级账套数：<span id="selectTotal" class="highlight-green">{{ availableTotal }}</span> 个，
                    </span>
                    <span class="main-top-txt"
                        >您已选择 <span id="selectTotal" class="highlight-green">{{ selectTotal }}</span> 个账套，</span
                    >
                    <span class="main-top-txt"
                        >剩余用户数 <span id="selectTotal" class="highlight-green">{{ avaUser }}</span> 个，</span
                    >
                    <span class="main-top-txt"
                        >选择的账套权限中电话号码有 <span id="selectUser" class="highlight-green">{{ selectUser.length }}</span> 个</span
                    >
                    <div style="float: right; padding-top: 4px">
                        <RefreshButton></RefreshButton>
                    </div>
                    <a
                        id="btnCommit"
                        class="solid-button"
                        :class="transferDiabled ? 'disable-button' : ''"
                        style="margin-right: 0"
                        @click="transferDiabled ? '' : commitTransfer(0)"
                        >批量升级</a
                    >
                    <!-- <a id="btnCancel" class="solid-button" style="margin-right: 10px" @click="cancel" v-show="!isLmClient">取消</a> -->
                </div>
            </div>
            <div class="main-center">
                <!-- :max-height="628" -->
                <!-- :height="628" -->
                <Table
                    ref="tranferTableRef"
                    :data="tableData"
                    :columns="columns"
                    @selection-change="handleSelect"
                    :empty-text="emptyText"
                    :selectable="setSelectable"
                    :scrollbarShow="true"
                    :highlightCurrentRow="true"
                    :loading="loading"
                    :tableName="setModule"
                >
                    <template #permission>
                        <el-table-column 
                            label="账套权限" 
                            header-align="left" 
                            min-width="120px"
                            prop="permission"
                            :width="getColumnWidth(setModule, 'permission')"
                        >
                            <template #default="scope">
                                <template v-if="scope.row.permission === '10001'">
                                    <span>账套管理员</span>
                                </template>
                                <template v-else-if="scope.row.permission === '10005'">
                                    <span>主管</span>
                                </template>
                                <template v-else-if="scope.row.permission === '10002'">
                                    <span>制单人</span>
                                    <a
                                        class="button solid-button ml-10"
                                        style="width: 58px; height: 20px; line-height: 20px; font-size: 12px"
                                        @click="applyPermission(0, scope.row)"
                                        >申请授权</a
                                    >
                                </template>
                                <template v-else-if="scope.row.permission === '10006'">
                                    <span>出纳</span>
                                    <a
                                        class="button solid-button ml-10"
                                        style="width: 58px; height: 20px; line-height: 20px; font-size: 12px"
                                        @click="applyPermission(0, scope.row)"
                                        >申请授权</a
                                    >
                                </template>
                                <template v-else-if="scope.row.permission === '10003'">
                                    <span>查看</span>
                                    <a
                                        class="button solid-button ml-10"
                                        style="width: 58px; height: 20px; line-height: 20px; font-size: 12px"
                                        @click="applyPermission(0, scope.row)"
                                        >申请授权</a
                                    >
                                </template>
                                <template v-else>
                                    <span>{{ scope.row.permissionName.join(",") }}</span>
                                    <a
                                        class="button solid-button ml-10"
                                        style="width: 58px; height: 20px; line-height: 20px; font-size: 12px"
                                        @click="applyPermission(0, scope.row)"
                                        >申请授权</a
                                    >
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <!-- 申请授权升级弹窗 -->
    <el-dialog v-model="showApplyPermissionDialog" title="申请授权升级" width="410" class="dialogDrag">
        <div class="permission-dialog-content" v-dialogDrag>
            <p class="permission-dialog-tips">向管理员或会计主管获取账套升级验证码</p>
            <el-form label-position="right" label-width="100px" :model="permissionDialogForm" style="max-width: 370px; margin: 30px auto">
                <el-form-item label="管理员手机号">
                    <el-select v-model="permissionDialogForm.phone">
                        <el-option v-for="item in managerPhoneNumberList" :key="item" :label="item" :value="item"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="验证码">
                    <el-input v-model="permissionDialogForm.vertifyCode" :input-style="{ width: '120px', marginRight: '10px' }" />
                    <a v-if="!vertifyCodeLoading" @click="getVertifyCode" class="button solid-button" style="margin-left: 25px">
                        获取验证码
                    </a>
                    <a
                        v-if="vertifyCodeLoading"
                        class="button solid-button disabled"
                        style="margin-left: 25px; background-color: #dadada; user-select: none"
                    >
                        {{ vertifyCodeCountDown }}s
                    </a>
                </el-form-item>
            </el-form>
            <p class="tip">
                无法获取验证码？
                <a @click="openVertifycationDialog">点此解决</a>
            </p>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <a class="button" @click="cancelApplyPermission">取消</a>
                <a class="button solid-button ml-20" @click="handleApplyPermission"> 继续升级</a>
            </div>
        </template>
    </el-dialog>
    <!-- 申请权限升级二级弹窗 扫码验证 -->
    <el-dialog v-model="showApplyPermissionQrcodeDialog" title="扫码验证" width="600" class="dialogDrag">
        <div class="permission-dialog-content" v-dialogDrag>
            <p class="permission-dialog-tips">请账套管理员或会计主管进行扫码验证，授权后才可以升级账套</p>
            <div class="qrcode-img">
                <div id="applyPermissionQrCode" :title="permissionQrcode">
                    <canvas width="142" height="142" style="display: none"></canvas
                    ><img :src="qrcodeUrl" style="display: block; width: 142px; height: 142px" />
                </div>
            </div>
            <p class="permission-dialog-info">本次升级账套：{{ vertifyAsname }}</p>
            <p class="permission-dialog-info">管理员或主管：<span v-html="mobiles"></span></p>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <a class="button" @click="cancelVertify">取消</a>
                <a class="button solid-button ml-20" @click="showApplyPermissionQrcodeDialog = false"> 返回 </a>
            </div>
        </template>
    </el-dialog>
    <el-dialog v-model="applyPermissionQrCodeSuccessDialog" title="扫码验证" width="410px" center class="dialogDrag">
        <div id="applyPermissionQrCodeSuccessDialog" title="扫码验证" v-dialogDrag>
            <div class="apply-permission-qrcode-success-dialog">
                <div class="dialog-title"><img src="@/assets/Settings/correct-icon.png" />扫码验证成功！</div>
                <div class="accountset-name">
                    账套名：<span class="applyPermissionAsName">{{ accountSetName }}</span>
                </div>
                <div class="tip"><img src="@/assets/Icons/warn.png" />验证成功后，将升级账套为云财务专业版</div>
                <div class="buttons">
                    <a class="button" @click="() => (applyPermissionQrCodeSuccessDialog = false)">取消</a
                    ><a class="button solid-button ml-10" @click="() => applyPermissionQrCodeSuccess()">继续升级</a>
                </div>
            </div>
        </div>
    </el-dialog>
    <el-dialog center width="480" title="提示" v-model="proOverFlowShow" class="dialogDrag">
        <div class="company-container" v-dialogDrag>
            <div class="all-line mt-10 full-text mb-10" :style="CenterText?'line-height: 80px;text-align: center;':''" v-html="proOverFlowInfo.fullText"></div>
            <div class="all-line mt-10 notice-text" v-show="proOverFlowInfo.noticeDisplay">
                <img class="img-notice" src="@/assets/Icons/warn.png" />
                <span class="notice-txt">{{ proOverFlowInfo?.noticeTxt }}</span>
            </div>
            <div class="all-line mb-20 mt-20 pt-10 border-top" v-show="proOverFlowInfo.acclimitDisplay && !isWxwork">
                <a class="button" @click="proOverFlowShow = false">取消</a>
                <a class="button solid-button ml-20" @click="goPurchase()">立即增购</a>
            </div>
            <div class="all-line mb-20 mt-20 pt-10 border-top" v-show="proOverFlowInfo.userlimitDisplay && !isWxwork">
                <a class="button" style="font-weight: normal" @click="proOverFlowInfo.continueTransfer()">继续升级</a>
                <a class="button solid-button ml-20" @click="goPurchase()">立即增购</a>
            </div>
            <div class="all-line mb-20 pt-10 border-top" v-show="proOverFlowInfo.acclimitDisplay && isWxwork">
                <a class="button solid-button" @click="proOverFlowShow = false">确定</a>
            </div>
            <div class="all-line mb-20 mt-20 pt-10 border-top" v-show="proOverFlowInfo.userlimitDisplay && isWxwork">
                <a class="button" @click="proOverFlowShow = false">取消</a>
                <a class="button solid-button ml-20" style="font-weight: normal" @click="proOverFlowInfo.continueTransfer()">继续升级</a>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts">
export default {
    name: "TransferPro",
};
</script>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { globalWindowOpen, getUrlSearchParams, closeCurrentTab,setTopLocationhref } from "@/util/url";
import { getQueryStringByName,getGlobalToken } from "@/util/baseinfo";
import { getLemonClient, isLemonClient } from "@/util/lmClient";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { GetAccountStandardText } from "@/views/Settings/AccountSets/utils";
import { reloadAccountSetList } from "@/util/accountset";

import { request, type IResponseModel } from "@/util/service";
import { ref, onMounted, reactive, computed } from "vue";
import type { IAccountSetTranferResult, ICopyAccountSetResult } from "./types";
import { useLoading } from "@/hooks/useLoading";
import { isInWxWork, isWxworkService } from "@/util/wxwork";
import { getServiceId, gotoBuy } from "@/util/proUtils";
import { getCookie, setCookie } from "@/util/cookie";
import { watch } from "vue";
import { nextTick } from "vue";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { thirdPartNotify, thirtPartNotifyTypeEnum } from "@/util/thirdpart";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "SettingTransfer";
const permissionQrcode = ref("");
const qrcodeUrl = ref("");
const transferDiabled = ref(false);
const availableTotal = ref<number>(0);
const avaUser = ref<number>();
const selectTotal = ref<number>(0);
const selectUser = ref<string[]>([]);
const selectAccount = ref<any>([]);
const proOverFlowShow = ref<boolean>(false);
const CenterText = ref<boolean>(false);
const applyPermissionVisible = ref<boolean>(false);
const accountSetName = ref<string>("");

const tranferTableRef = ref();
const columns = ref<Array<IColumnProps>>([
    { slot: "selection" },
    {
        label: "单位名称",
        prop: "asName",
        align: "left",
        headerAlign: "left",
        minWidth: 180,
        className: "asName",
        width: getColumnWidth(setModule, "asName")
        // useHtml: true,
        // formatter: (row: any) => {
        //     return `<a title="${row.asName}" style="color: #3385ff; outline: none;text-decoration: none;">${row.asName}</a>`;
        // },
    },
    {
        label: "当前记账年月",
        prop: "asCurrentYear",
        align: "left",
        headerAlign: "left",
        minWidth: 80,
        formatter: (row: any) => {
            return row.asCurrentYear + "年" + (Number(row.asCurrentMonth) < 10 ? "0" : "") + row.asCurrentMonth + "月";
        },
        width: getColumnWidth(setModule, "asCurrentYear")
    },
    {
        label: "账套启用年月",
        prop: "asStartYear",
        align: "left",
        headerAlign: "left",
        minWidth: 80,
        formatter: (row: any) => {
            return row.asStartYear + "年" + (Number(row.asStartMonth) < 10 ? "0" : "") + row.asStartMonth + "月";
        },
        width: getColumnWidth(setModule, "asStartYear")
    },
    { slot: "permission" },
    {
        label: "会计准则",
        prop: "accountingStandard",
        align: "left",
        headerAlign: "left",
        minWidth: 166,
        formatter: (row: any) => GetAccountStandardText(row.accountingStandard, row.subAccountingStandard),
        width: getColumnWidth(setModule, "accountingStandard")
    },
    {
        label: "资金模块",
        prop: "cashJournal",
        align: "left",
        headerAlign: "left",
        minWidth: 60,
        formatter: (row: any) => (row.cashJournal === "1" ? "启用" : "未启用"),
        width: getColumnWidth(setModule, "cashJournal")
    },
    {
        label: "资产模块",
        prop: "fixedAsset",
        align: "left",
        headerAlign: "left",
        minWidth: 80,
        formatter: (row: any) => (row.fixedAsset === "1" ? "启用" : "未启用"),
        width: getColumnWidth(setModule, "fixedAsset")
    },
    {
        label: "凭证审核",
        prop: "checkNeeded",
        align: "left",
        headerAlign: "left",
        minWidth: 58,
        formatter: (row: any) => (row.checkNeeded === "1" ? "启用" : "未启用"),
        width: getColumnWidth(setModule, "checkNeeded")
    },
    {
        label: "关联进销存",
        prop: "scmCompanyName",
        align: "left",
        headerAlign: "left",
        minWidth: 58,
        width: getColumnWidth(setModule, "scmCompanyName")
    },
    {
        label: "关联云发票",
        prop: "yfpcompanyName",
        align: "left",
        headerAlign: "left",
        minWidth: 58,
        width: getColumnWidth(setModule, "yfpcompanyName")
    },
    {
        label: "用户（是手机号）",
        prop: "phoneList",
        align: "left",
        headerAlign: "left",
        minWidth: 231,
        formatter: (row: any) => row.phoneList?.join(","),
        resizable: false,
    },
]);

const isWxwork = computed(() => isInWxWork() || !!window.isWxworkService);
const isLmClient = ref(isLemonClient());
// 管理员手机号
const managerMobile = ref<string>("");

function setSelectable(row: any, rowIndex: number): boolean {
    if (row.permission !== "10001" && row.permission !== "10005") {
        return false;
    }
    return true;
}
function handleSelect(row: any) {
    selectTotal.value = row.length;
    selectAccount.value = row;
    selectUser.value = [];
}

watch(selectAccount, () => {
    selectAccount.value.forEach((item: any) => {
        item.phoneList.reduce((prev: any, project?: any) => {
            if (prev.indexOf(project) === -1) prev.push(project);
            return selectUser.value;
        }, selectUser.value);
    });
});

// 申请权限升级弹窗
const showApplyPermissionDialog = ref<boolean>(false);
const showApplyPermissionQrcodeDialog = ref<boolean>(false);
const applyPermissionQrCodeSuccessDialog = ref<boolean>(false);
const permissionDialogForm = reactive({
    phone: "",
    vertifyCode: "",
});

// 验证码按钮加载状态
const vertifyCodeLoading = ref<boolean>(false);
// 验证码按钮倒计时
const vertifyCodeCountDown = ref<number>(60);

// 管理员手机号列表
const managerPhoneNumberList = ref<string[]>([]);
// 管理员数字信息
const managerNumberList = ref<string[]>([]);
//管理员或主管
const mobiles = ref<string>("");
const vertifyAsid = ref();
const vertifyAsname = ref("");
const cancelVertify = () => {
    showApplyPermissionQrcodeDialog.value = false;
    showApplyPermissionDialog.value = false;
};

let applyPermissionQrCodeSuccess: Function = () => {};
const applyAccountSets = ref<any[]>([]);
// 打开申请权限升级弹框
function applyPermission(forceFlag: number, row: any) {
    managerNumberList.value = [];
    if (availableTotal.value > 0) {
        accountSetName.value = row.asName;
    }
    managerPhoneNumberList.value = [];
    mobiles.value = "";
    vertifyAsid.value = row.asId;
    vertifyAsname.value = row.asName;
    let j = 0;
    for (let usersn in row.userRoleDict) {
        if (
            row.userRoleDict[usersn].filter(function (p: string) {
                return p == "10001" || p == "10005";
            }).length > 0
        ) {
            for (let i = 0; i < row.userList.length; i++) {
                if (row.userList[i] == usersn) {
                    managerNumberList.value.push(usersn);
                    managerPhoneNumberList.value.push(row.phoneList[i]);
                    if (j % 2 === 0) {
                        if (j !== 0) {
                            mobiles.value += "<br/>";
                        }
                        mobiles.value += row.phoneList[i];
                    } else {
                        mobiles.value += "、" + row.phoneList[i];
                    }
                    j++;
                }
            }
        }
    }
    permissionDialogForm.phone = managerPhoneNumberList.value[0];
    let accountSet = {
        AsId: row.asId,
        AsName: row.asName,
        TaxType: row.taxType,
        UnifiedNumber: row.unifiedNumber,
        TaxNumbers: row.taxNumberS,
        CheckNeeded: row.checkNeeded,
        AsStartYear: row.asStartYear,
        AsStartMonth: row.asStartMonth,
        FixdAsset: row.fixedAsset,
        UserList: forceFlag == 0 ? row.userList : useAccountSetStoreHook().userInfo?.userSn,
        Permission: row.permission,
    };

    applyAccountSets.value = [accountSet];
    request({
        url: `/api/AccountSetOnlyAuth/CheckTransferResource?serviceId=${getServiceId()}`,
        method: "post",
        data: applyAccountSets.value,
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            showApplyPermissionDialog.value = true;
            applyPermissionQrCodeSuccess = () => {
                commitTransfer(forceFlag, row);
                applyPermissionQrCodeSuccessDialog.value = false;
            };
        } else {
            showProOverFlowDialog(res.msg, () => {
                proOverFlowShow.value = false;
                applyPermission(1, row);
            });
        }
    });
}

// 获取验证码
const getVertifyCode = () => {
    vertifyCodeLoading.value = true;
    vertifyCodeCountDown.value = 60;
    let timer = setInterval(() => {
        vertifyCodeCountDown.value--;
        if (vertifyCodeCountDown.value === 0) {
            vertifyCodeLoading.value = false;
            clearInterval(timer);
        }
    }, 1000);

    request({
        url: `/api/AccountSetOnlyAuth/SendApplyConfirmCode?mobile=${permissionDialogForm.phone}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            console.log("验证码获取成功");
        } else {
            vertifyCodeLoading.value = false;
            clearInterval(timer);
            return ElNotify({
                message: "获取验证码失败，请重试",
                type: "warning",
            });
        }
    });
};
// 打开二维码验证弹框
const openVertifycationDialog = () => {
    showApplyPermissionDialog.value = false;
    let interval: number;
    // 获取生成二维码的guid
    let users = managerNumberList.value.join(",");
    let managers = managerPhoneNumberList.value.join(",");
    request({
        url: `/api/AccountSetOnlyAuth/CreateApplyQrCodeGuid?asid=${vertifyAsid.value}&users=${users}`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        // guid
        permissionQrcode.value =
            "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
            window.wxGzhAppId +
            "&redirect_uri=" +
            encodeURIComponent(window.jmHost + "/Permissions/ApplyTransferAccountSet") +
            "&response_type=code&scope=snsapi_userinfo&state=" +
            encodeURIComponent(
                "asId=" +
                    vertifyAsid.value +
                    "&guid=" +
                    res.data +
                    "&asName=" +
                    vertifyAsname.value +
                    "&mobile=" +
                    useAccountSetStoreHook().userInfo?.mobile +
                    "&managers=" +
                    managers +
                    "&appid=" +
                    window.wxGzhAppId
            );
        qrcodeUrl.value = window.accountSrvHost + "/api/WxPay/MakeQRCode.ashx?data=" + encodeURIComponent(permissionQrcode.value);
        interval = setInterval(function () {
            request({
                url: "/api/AccountSetOnlyAuth/CheckApplyQcCodeScreenResult?asId=" + vertifyAsid.value + "&guid=" + res.data + "",
                method: "post",
            }).then((res: any) => {
                if (res.state === 1000 && res.data === true) {
                    clearInterval(interval);
                    showApplyPermissionQrcodeDialog.value = false;
                    applyPermissionQrCodeSuccessDialog.value = true;
                }
            });
        }, 1000);
    });
    showApplyPermissionQrcodeDialog.value = true;
};

// 关闭升级弹窗
const cancelApplyPermission = () => {
    showApplyPermissionDialog.value = false;
    permissionDialogForm.phone = "";
    permissionDialogForm.vertifyCode = "";
};

// 继续升级按钮
const handleApplyPermission = () => {
    if (permissionDialogForm.vertifyCode.trim() === "") {
        return ElNotify({
            message: "请输入验证码",
            type: "warning",
        });
    }
    useLoading().enterLoading("账套升级中，请稍候...");
    // 校验二维码
    request({
        url: `/api/AccountSetOnlyAuth/VerifyApplyConfirmCode?mobile=${permissionDialogForm.phone}&confirmCode=${permissionDialogForm.vertifyCode}`,
        method: "post",
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state === 1000 && res.data) {
                // 升级
                request({
                    url: `/api/AccountSetOnlyAuth/Tranfer?serviceId=${getServiceId()}
                    &notCopyFiles=${getQueryStringByName("notCopyFiles") || false}`,
                    method: "post",
                    data: applyAccountSets.value,
                })
                    .then((resp: any) => {
                        if (resp.state === 1000) {
                            let successAsIdsNotManager: any[] = [];
                            let successManagerAsIds: any[] = [];
                            let successCount = resp.data.data.filter((item: any) => {
                                if (item.result === true) {
                                    if (
                                        applyAccountSets.value.filter(function (m) {
                                            return m.AsId === item.asId;
                                        })[0].Permission !== "10001"
                                    ) {
                                        successAsIdsNotManager.push(item.asId);
                                    } else {
                                        successManagerAsIds.push(item.asId);
                                    }
                                }
                                return item.result === true;
                            }).length;
                            const params = {
                                asids: successAsIdsNotManager.join(","),
                                managerAsIds: successManagerAsIds.join(","),
                            };
                            const requestOrigin = window.isAccountingAgent ? window.jAAFreeApiHost : window.jFreeApiHost;
                            request({
                                url: requestOrigin + "/api/PermissionsOnlyAuth/DeleteAccountSetsPermission?" + getUrlSearchParams(params),
                                method: "post",
                            })
                                .then((res: IResponseModel<boolean>) => {
                                    useLoading().quitLoading();
                                    if (res.state === 1000 && res.data) {
                                        ElConfirm("升级完成！升级成功" + successCount + "个账套", true).then(() => {
                                            window.location.reload();
                                        });
                                    } else {
                                        return ElNotify({
                                            message: `升级失败`,
                                            type: "warning",
                                        });
                                    }
                                })
                                .catch(() => {
                                    useLoading().quitLoading();
                                });
                        } else {
                            useLoading().quitLoading();
                            return ElNotify({
                                message: `操作失败`,
                                type: "warning",
                            });
                        }
                    })
                    .catch((err) => {
                        useLoading().quitLoading();
                        return ElNotify({
                            message: err,
                            type: "warning",
                        });
                    });
            } else {
                useLoading().quitLoading();
                return ElNotify({
                    message: "验证码输入有误",
                    type: "warning",
                });
            }
        })
        .catch((err) => {
            useLoading().quitLoading();
            return ElNotify({
                message: err,
                type: "warning",
            });
        })
        .finally(() => {
            // 关闭升级弹窗
            cancelApplyPermission();
        });
};

const successAsId = ref('')
function commitTransfer(forceFlag: number, row?: any) {
    let accountSets: any[] = [];
    let currUser = useAccountSetStoreHook().userInfo?.userSn;
    if (row) {
        let accountSet = {
            AsId: row.asId,
            AsName: row.asName,
            TaxType: row.taxType,
            UnifiedNumber: row.unifiedNumber,
            TaxNumbers: row.taxNumberS,
            CheckNeeded: row.checkNeeded,
            AsStartYear: row.asStartYear,
            AsStartMonth: row.asStartMonth,
            FixdAsset: row.fixedAsset,
            UserList: forceFlag == 0 ? row.userList : [currUser],
            Permission: row.permission,
            DecimalPlace: row.decimalPlace,
        };
        accountSets.push(accountSet);
    } else {
        for (let i in selectAccount.value) {
            if (
                selectAccount.value[i].asId !== 0 &&
                (selectAccount.value[i].permission == "10001" || selectAccount.value[i].permission == "10005")
            ) {
                let accountSet = {
                    AsId: selectAccount.value[i].asId,
                    AsName: selectAccount.value[i].asName,
                    TaxType: selectAccount.value[i].taxType,
                    UnifiedNumber: selectAccount.value[i].unifiedNumber,
                    TaxNumbers: selectAccount.value[i].taxNumberS,
                    CheckNeeded: selectAccount.value[i].checkNeeded,
                    AsStartYear: selectAccount.value[i].asStartYear,
                    AsStartMonth: selectAccount.value[i].asStartMonth,
                    FixdAsset: selectAccount.value[i].fixedAsset,
                    UserList: forceFlag === 0 ? selectAccount.value[i].userList : [currUser],
                    Permission: selectAccount.value[i].permission,
                };
                accountSets.push(accountSet);
            }
        }
    }
    if (accountSets.length === 0) {
        ElNotify({
            type: "warning",
            message: "未勾选账套，请勾选账套后升级",
        });
        return;
    }
    exceptInfo = {};
    useLoading().enterLoading("账套升级中，请稍候...");
    request({
        url: `/api/AccountSetOnlyAuth/Tranfer?serviceId=${getServiceId()}&notCopyFiles=${getQueryStringByName("notCopyFiles") || false}`,
        method: "post",
        data: accountSets,
    })
        .then((res: IResponseModel<IAccountSetTranferResult>) => {
            useLoading().quitLoading();
            if (res.state === 1000) {
                let successAsIdsNotManager: any[] = [];
                let successManagerAsIds: any[] = [];
                let newProAsIds: any[] = [];
                let successCount = res.data.data.filter((item: ICopyAccountSetResult) => {
                    if (item.result === true) {
                        if (
                            accountSets.filter(function (m) {
                                return m.AsId === item.asId;
                            })[0].Permission !== "10001"
                        ) {
                            successAsIdsNotManager.push(item.asId);
                        } else {
                            successManagerAsIds.push(item.asId);
                        }
                        newProAsIds.push(item.newAsId);
                    }
                    return item.result === true;
                }).length;
                thirdPartNotify(thirtPartNotifyTypeEnum.globalRecycledAccountSet, { asIds: successManagerAsIds }).then(() => {});
                thirdPartNotify(thirtPartNotifyTypeEnum.globalNewProAccountSet, { asIds: newProAsIds }).then(() => {});
                let failedCount = res.data.data.length - successCount;
                successAsId.value = res.data.appAsId;
                if (successAsIdsNotManager.length > 0) {
                    const params = {
                        asids: successAsIdsNotManager.join(","),
                        managerAsIds: successManagerAsIds.join(","),
                    };
                    const requestOrigin = window.isAccountingAgent ? window.jAAFreeApiHost : window.jFreeApiHost;
                    request({
                        url: requestOrigin + "/api/PermissionsOnlyAuth/DeleteAccountSetsPermission?" + getUrlSearchParams(params),
                        method: "post",
                    }).then((res: any) => {
                        if (res.state === 1000 && res.data === true) {
                            successCallback(successCount, failedCount);
                        }
                    });
                } else {
                    successCallback(successCount, failedCount);
                }
                // 刷新账套列表
                window.dispatchEvent(new CustomEvent("refresh-accountsets-list"));
            } else {
                thirdPartNotify(thirtPartNotifyTypeEnum.transferProAccountSetOverflow).then(() => {
                    showProOverFlowDialog(res.msg || "操作失败");
                });
            }
        })
        .catch((err: any) => {
            useLoading().quitLoading();
            if (err.response?.status === 400) {
                thirdPartNotify(thirtPartNotifyTypeEnum.transferProAccountSetOverflow).then(() => {
                    showProOverFlowDialog(err.responseText);
                });
            }
        });
}

function successCallback(successCount: number, failedCount: number) {
    proOverFlowShow.value = false;
    ElConfirm(
        failedCount == 0
            ? "升级完成！升级成功" + successCount + "个账套"
            : "升级完成！升级成功" + successCount + "个账套，失败" + failedCount + "个账套。您可以重新选择账套升级或联系您的专业版客服哦~",
        true
    ).then(() => {
        if (isLemonClient()) {
            getLemonClient().init();
            window.location.reload();
        } else {
            if(!getGlobalToken()){
                setTopLocationhref("/Settings/transferPro?stay=true&appasid=" + successAsId.value)
            }else{
                window.postMessage({ type: "refreshCurrent" }, window.location.origin);
                reloadAccountSetList()
            }
        }
    });
}
function cancel() {
    closeCurrentTab();
}

let exceptInfo: any = {};
function parseExceptInfo(text: string) {
    var exceptInfoText = text.split("@");
    if (exceptInfoText.length != 2) {
        return {
            userneed: 0,
            userrest: 0,
            usermore: 0,
            accneed: 0,
            accrest: 0,
            accmore: 0,
        };
    }
    var exceptJson = JSON.parse(exceptInfoText[1]);
    var res: any = {};
    for (var i = 0; i < exceptJson.length; i++) {
        if (exceptJson[i].bizTag.name == "user") {
            res.userneed = exceptJson[i].need;
            res.userrest = exceptJson[i].rest;
            res.usermore = exceptJson[i].more;
        } else if (exceptJson[i].bizTag.name == "accountSet") {
            res.accneed = exceptJson[i].need;
            res.accrest = exceptJson[i].rest;
            res.accmore = exceptJson[i].more;
        }
    }
    exceptInfo = res;
}
const proOverFlowInfo = reactive<{
    noticeTxt: string;
    noticeDisplay: boolean;
    userlimitDisplay: boolean;
    acclimitDisplay: boolean;
    fullText: string;
    continueTransfer: () => void;
}>({
    noticeTxt: "",
    noticeDisplay: false,
    userlimitDisplay: false,
    acclimitDisplay: false,
    fullText: "",
    continueTransfer: () => {},
});
function showProOverFlowDialog(msg: string, continueTransfer?: () => void) {
    parseExceptInfo(msg);
    if (msg.indexOf("您的用户数量") !== -1) {
        proOverFlowInfo.noticeDisplay = true;
        proOverFlowInfo.noticeTxt = "选择继续升级，专业版将只保留当前管理员权限(" + managerMobile.value + ")";
        proOverFlowInfo.userlimitDisplay = true;
        proOverFlowInfo.acclimitDisplay = false;
        msg =
            '您的剩余用户数为<span style="color: #FF7500;">' +
            exceptInfo.userrest +
            '个</span>，本次升级账套需要用户数（电话号码）<span style="color: #FF7500;">' +
            exceptInfo.userneed +
            '个</span>，需<span style="color: #FF7500;">增购用户' +
            exceptInfo.usermore +
            "个</span>，您可增购用户数或继续升级";
        if (isWxwork.value) {
            msg =
                '您的剩余用户数为<span style="color: #FF7500;">' +
                exceptInfo.userrest +
                '个</span>，本次升级账套需要用户数（电话号码）<span style="color: #FF7500;">' +
                exceptInfo.userneed +
                "个</span>，您可以联系管理员加购或继续升级";
            proOverFlowInfo.continueTransfer = () => {
                continueTransfer ? continueTransfer() : commitTransfer(1);
            };
        } else {
            proOverFlowInfo.continueTransfer = () => {
                continueTransfer ? continueTransfer() : commitTransfer(1);
            };
        }
    } else if (msg.indexOf("您的用户和账套数量") !== -1 || msg.indexOf("您的账套和用户数量") !== -1) {
        proOverFlowInfo.noticeDisplay = true;
        proOverFlowInfo.noticeTxt = "账套升级会将免费版账套的用户权限升级到专业版";
        proOverFlowInfo.userlimitDisplay = false;
        proOverFlowInfo.acclimitDisplay = true;
        msg = "您的账套数及选择升级的免费版账套的用户数均超过已购买数量，建议您去增购";

        if (isWxwork.value) {
            msg = "您的账套数及用户数已超过已购买数量，请联系管理员加购哦~";
        }
    } else {
        proOverFlowInfo.noticeDisplay = false;
        proOverFlowInfo.userlimitDisplay = false;
        proOverFlowInfo.acclimitDisplay = true;
        CenterText.value = true;
        msg = "您的账套数量已超过已购买账套数，建议您去增购~";

        if (isWxwork.value) {
            msg = "您的账套数量已超过已购买账套数，请联系管理员加购哦~";
        }
    }
    proOverFlowInfo.fullText = msg;
    proOverFlowShow.value = true;
}

watch(()=>proOverFlowShow,()=>{
    if(!proOverFlowShow.value){
        CenterText.value = false;
    }
})

function goPurchase() {
    proOverFlowShow.value = false;
    if (isWxwork.value) {
        ElNotify({ message: "请联系管理员加购", type: "warning" });
    } else {
        nextTick(() => {
            gotoBuy();
        });
    }
}
const tableData = ref([]);
const emptyText = ref(" ");
const loading = ref(false);
function getTableData() {
    loading.value = true;
    //需要在专业版调免费版的service，
    request({
        url: window.jFreeApiHost + `/api/AccountSetOnlyAuth/TanferProList?searchText=&hasOrder=&permission=&scmFlag=1`,
    }).then((res: any) => {
        loading.value = false;
        if (res.data === 0) {
            transferDiabled.value = true;
        }
        tableData.value = res.data;
        if (tableData.value.length === 0) {
            emptyText.value = "暂无数据";
        }
    });
}
const accountsetStore = useAccountSetStore();
function getServiceCount() {
    let url = window.eHost + "/wb/" + (isInWxWork() ? "wecom/" : "") + "plan/subscription?productType=" + window.productType;
    if (isInWxWork()) {
        url += "&asId=" + accountsetStore.accountSet?.asId;
    } else {
        url += "&serviceId=" + getServiceId();
    }
    request({
        url: url,
    }).then((res: any) => {
        if (res.data.accountSet != null) {
            availableTotal.value = res.data.accountSet.amount - res.data.accountSet.used;
            avaUser.value = res.data.user.amount - res.data.user.used;
        }
    });
}
onMounted(() => {
    getTableData();
    getServiceCount();
    isWxworkService();

    // 获取管理员手机号
    managerMobile.value = useAccountSetStoreHook().userInfo?.mobile ?? "";
});
</script>

<style scoped lang="less">
@import "@/style/Common.less";
@import "@/style/SelfAdaption.less";

.content {
    height: 100%;
    .main-content {
        height: 100%;
        .main-center {
            flex: 1;

            :deep(.table) {
                height: 100%;
                display: flex;
                flex-direction: column;

                .el-table {
                    flex: 1;
                }
            }
        }
    }
}
.main-transfer-top {
    height: 85px;
    padding: 10px;
    padding-left: 20px;
    text-align: left;
    & .notice-cst {
        height: 32px;
        background-color: #ffedec;
        align-items: center;
        align-content: flex-start;
        padding-left: 16px;
        padding-right: 16px;
        margin-right: 10px;
        display: inline-flex;
        flex-direction: row;
        .notice-txt {
            font-size: 14px;
        }
    }
    & .account-operate {
        margin-top: 16px;
        justify-content: space-between;
        & .main-top-txt {
            float: left;
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 32px;
            // margin-left: 10px;
        }
        & .solid-button {
            float: right;
            margin-right: 10px;
        }
    }
    & .disable-button {
        background-color: gray;
        cursor: not-allowed;
    }
}
:deep(.el-scrollbar__view) {
    height: 452px;
}
.phone-list {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}
// 申请权限升级弹框
.permission-dialog-content {
    width: 100%;
    min-height: 210px;
    color: #666;
    .permission-dialog-tips {
        font-size: 16px;
        color: #333;
        padding-top: 20px;
        margin: 0;
    }
    :deep(.el-select) {
        width: 240px;
    }
    p {
        a {
            color: #3385ff;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
        }
    }
    & .qrcode-img {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }
}
.permission-dialog-info {
    width: 100%;
    margin-top: 18px;
    padding-left: 110px;
    box-sizing: border-box;
    font-size: 16px;
    color: #333;
    text-align: left;
}
.dialog-footer {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    text-align: center;
    border-top: 1px solid #dadada;
}
.company-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    align-content: center;
    text-align: center;
    font-size: 16px;
    font-weight: 600;

    .all-line {
        width: 100%;

        &.tx-center {
            text-align: center;
        }

        &.company-title {
            font-size: 16px;
            font-weight: 600;
            height: 38px;
        }

        &.full-text {
            height: 70px;
            font-size: 15px;
            font-weight: normal;
            width: 420px;
            line-height: 26px;
            margin-top: 20px;
            text-align: left;
        }

        &.notice-text {
            height: 20px;
            font-size: 14px;
            width: 420px;
            font-weight: 400;
            line-height: 24px;
            align-items: center;
            margin: 0 auto;
            display: flex;
            flex-direction: row;

            .img-notice {
                display: inline-block;
                height: 17px;
                width: 17px;
            }

            .notice-txt {
                margin-left: 10px;
                display: inline-block;
                line-height: 32px;
                text-align: left;
                color: var(--weaker-font-color);
                word-break: break-all;
            }
        }

        &.border-bottom {
            border-bottom: 1px solid var(--border-color);
        }

        &.border-top {
            border-top: 1px solid var(--border-color);
        }
    }

    .btn {
        border: 0;
        background: #44b449;
        color: white;
        width: 70px;
        height: 30px;
    }
}
:deep(.asName) {
    & div.el-tooltip {
        color: #3385ff !important;
    }
}
.apply-permission-qrcode-success-dialog {
    .dialog-title {
        font-size: 24px;
        font-weight: 500;
        color: var(--font-color);
        line-height: 33px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 31px;
        img {
            width: 41px;
            height: 41px;
            margin-right: 8px;
        }
    }
    .accountset-name {
        margin-top: 10px;
        font-size: 16px;
        color: var(--font-color);
        line-height: 23px;
        text-align: center;
    }
    .tip {
        margin-top: 38px;
        font-size: 16px;
        color: #666666;
        line-height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
            width: 14px;
            height: 14px;
            margin-right: 4px;
        }
    }
    .buttons {
        margin-top: 20px;
        padding: 10px;
        border-top: 1px solid var(--border-color);
        text-align: center;
    }
}
</style>
