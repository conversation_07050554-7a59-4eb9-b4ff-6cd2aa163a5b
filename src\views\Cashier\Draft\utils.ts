import type { IColumnProps } from "@/components/Table/IColumnProps";
import { ref } from "vue";
import { formatMoney } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";


export const Columns = (showAllInfo: boolean): IColumnProps[] => {
  const setModule = "Draft";
    if (showAllInfo) {
        return [
            { 
                slot: "selection", 
                width: 36, 
                headerAlign: "center", 
                align: "center", 
                fixed: "left" 
            },
            { 
                slot: "draftType", 
                label: "收支类别", 
                className: "colCellMove" 
            },
            { 
                label: "出票日", 
                prop: "draftCreateDateStr", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'draftCreateDateStr') 
            },
            {
                slot: "draftNo", 
                label: "票据号", 
                className: "colCellMove"
            },
            {
                label: "票据金额", 
                prop: "draftAmount", 
                align: "left", 
                headerAlign: "left",
                className: "colCellMove",
                width: getColumnWidth(setModule, 'draftAmount'),
                formatter: (row, column, value) => {
                    return formatMoney(value);
                }
            },
            {
                slot: "draftTypeName", 
                label: "票据种类", 
                className: "colCellMove"
            },
            { 
                label: "汇票到期日", 
                prop: "draftEndDateStr", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                minWidth: 100,
                width: getColumnWidth(setModule, 'draftEndDateStr') 
            },
            {
                slot: "drawerName", 
                label: "出票人", 
                className: "colCellMove"
            },
            {
                slot: "acceptorName", 
                label: "承兑人", 
                className: "colCellMove"
            },
            {
                slot: "draftStatusName", 
                label: "票据状态", 
                className: "colCellMove"
            },
            {
                label: "能否转让",
                prop: "canTransfer",
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
                width: getColumnWidth(setModule, 'canTransfer'),
                formatter: (row, column, value) => {
                    if (value === 1) {
                    return '可转让';
                    } else {
                    return '否';
                    }
                },
            },
            {
                label: "贴现金额", 
                prop: "discountAmount", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove",
                width: getColumnWidth(setModule, 'discountAmount'),
                formatter: (row, column, value) => {
                    if (value != null && value != "") {
                    return value.toFixed(2);
                    }
                }
            },
            { 
                label: "贴现状态", 
                prop: "discountStatusName", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'discountStatusName') 
            },
            { 
                label: "贴现日期", 
                prop: "discountDateStr", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'discountDateStr') 
            },
            { 
                label: "贴现方", 
                prop: "discountBank", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'discountBank') 
            },
            { 
                label: "贴现率", 
                prop: "discountRate", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'discountRate') 
            },
            {
                label: "是否收款", 
                prop: "isGotMoney", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove",
                width: getColumnWidth(setModule, 'isGotMoney'),
                formatter: (row, column, value) => {
                    if (value === true) {
                    return '是';
                    } else {
                    return '否';
                    }
                },
            },
            {
                label: "收款日期",
                prop: "gotMoneyDateStr",
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
                width: getColumnWidth(setModule, 'gotMoneyDateStr')
            },
            {
                label: "是否转让", 
                prop: "isTransfered", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove",
                width: getColumnWidth(setModule, 'isTransfered'),
                formatter: (row, column, value) => {
                    if (value === true) {
                    return '是';
                    } else {
                    return '否';
                    }
                },
            },
            {
                label: "受让方",
                prop: "transferee",
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
                width: getColumnWidth(setModule, 'transferee'),
            },
            {
                label: "转让金额", 
                prop: "transfereeAmount", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove",
                width: getColumnWidth(setModule, 'transfereeAmount'),
                formatter: (row, column, value) => {
                    if (value != null && value != "") {
                    return value.toFixed(2);
                    }
                }
            },
            { 
                label: "转让日期", 
                prop: "transfereeDateStr", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'transfereeDateStr') 
            },
            { 
                slot: "voucherGroup", 
                label: "票据凭证", 
                className: "colCellMove" 
            },
            { 
                slot: "discountVoucherGroup", 
                label: "贴现凭证", 
                className: "colCellMove" 
            },
            { 
                slot: "getMoneyVoucherGroup", 
                label: "收款凭证", 
                className: "colCellMove" 
            },
            { 
                slot: "operation", 
                fixed: "right" 
            }
        ];
    } else {
        return [
            { 
                slot: "selection", 
                width: 36, 
                headerAlign: "center", 
                align: "center", 
                fixed: "left" 
            },
            { 
                slot: "draftType", 
                label: "收支类别", 
                className: "colCellMove" 
            },
            { 
                label: "出票日", 
                prop: "draftCreateDateStr", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                width: getColumnWidth(setModule, 'draftCreateDateStr') 
            },
            {
                slot: "draftNo", 
                label: "票据号", 
                className: "colCellMove"
            },
            { 
                label: "票据金额", 
                prop: "draftAmount", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove",
                width: getColumnWidth(setModule, 'draftAmount'),
                formatter: (row, column, value) => {
                    return formatMoney(value);
                }
            },
            {
                slot: "draftTypeName", 
                label: "票据种类", 
                className: "colCellMove"
            },
            { 
                label: "汇票到期日", 
                prop: "draftEndDateStr", 
                align: "left", 
                headerAlign: "left", 
                className: "colCellMove", 
                minWidth: 100,
                width: getColumnWidth(setModule, 'draftEndDateStr') 
            },
            {
                slot: "drawerName", 
                label: "出票人", 
                className: "colCellMove"
            },
            {
                slot: "acceptorName", 
                label: "承兑人", 
                className: "colCellMove"
            },
            {
                slot: "draftStatusName", 
                label: "票据状态", 
                className: "colCellMove"
            },
            {
                label: "能否转让",
                prop: "canTransfer",
                align: "left",
                headerAlign: "left",
                className: "colCellMove",
                width: getColumnWidth(setModule, 'canTransfer'),
                formatter: (row, column, value) => {
                    if (value === 1) {
                    return '可转让';
                    } else {
                    return '否';
                    }
                },
            }, 
            { 
                slot: "operation", 
                fixed: "right" 
            }
        ];
    }
}
export const ColumnsInquiry: IColumnProps[] = [
    { slot:"plus"},
    { label: "票据号", prop: "draftNo", align: "left", headerAlign: "left" },
    { label: "汇票到期日", prop: "draftEndDateStr", align: "left", headerAlign: "left" },
    { label: "票据金额", 
      prop: "draftAmount", 
      align: "left", 
      headerAlign: "left",
      formatter: (row, column, value) => {
        return formatMoney(value);
      } 
    },
    { label: "承兑人行号", prop: "acceptorAccount", align: "left", headerAlign: "left" },
    { label: "银行", prop: "discountBank", align: "left", headerAlign: "left" },
    { label: "贴现率", prop: "discountRate", align: "left", headerAlign: "left" },
    { label: "预计成交价格(元)", prop: "discountResult", align: "left", headerAlign: "left" },
    { slot: "operation" }
];

export const addColumns = ref<Array<IColumnProps>>([
    { slot: "tradeName" },
    { slot: "specificationModel" },
    { slot: "unit" },
    { slot: "number" },
    { slot: "money" },
    { slot: "taxRate" },
    { slot: "taxAmount" },
    { slot: "taxationItems" },
    { slot: "taxMethod" },
]);
