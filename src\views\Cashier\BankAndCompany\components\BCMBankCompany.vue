<template>
    <BankCompany
        v-model:show-main="showMain"
        v-model:impower-value="impowerValue"
        v-model:accname="autoBankForm.accname"
        v-model:uscc="autoBankForm.uscc"
        v-model:mainaccno="autoBankForm.mainaccno"
        v-model:acid="autoBankForm.acid"
        :bank-account-list="bankAccountList"
        :bank-help-link="bankHelpLink"
        :currency-list="currencyList"
        :bank-type="props.bankType"
        :tip-array="tipArray"
        :acname="props.acname"
        :uscc-number="props.usccNumber"
        @save-success="saveSuccessHandle"
        @confirm-bank="CheckBCMBankInfo(0)"
    />
    <div class="slot-content align-center edit-temp-table" style="height: 350px; margin-top: 70px" v-show="confirmTable">
        <div class="slot-content-mini edit-temp-table-content bcm-bank">
            <div class="autoBanktitle" style="top: -60px" v-show="!isHideBarcode">
                <a class="link" @click="globalWindowOpen(bankHelpLink)"> 点此查看操作手册 </a>
            </div>
            <div class="autoBankSelect">
                <div class="autoBankItem">
                    <span style="font-weight: 600">第一步：</span>打开IE或EDGE浏览器使用管理员UKey，登录交行企业网银
                </div>
                <div class="autoBankItem">
                    <span style="font-weight: 600">第二步：</span>进入交行网银端【首页-我的待办】页面，进行授权处理
                </div>
                <div class="autoBankItem">
                    <p style="margin-left: 56px; margin-top: -8px; font-size: 12px; color: rgba(102, 102, 102, 1)">
                        签约平台名称选择柠檬云
                    </p>
                </div>
                <div class="autoBankItem"><span style="font-weight: 600">第三步：</span>确认授权信息，点击【授权】按钮</div>
                <div class="autoBankItem"><span style="font-weight: 600">第四步：</span>阅读并同意授权服务协议，完成授权签约</div>
            </div>
            <div class="autoBankDescription">
                <div style="margin-left: 40px; padding-top: 20px; line-height: 16px">
                    <span style="font-weight: 500">交通网银地址：</span>
                    <span class="copy-file copy-key" data-target="bcm-apply-url" @click="handleCopy">复制</span>
                </div>
                <p class="copy-key pub-key" data-target="bcm-apply-url" @click="handleCopy">
                    https://ebank.95559.com.cn/CEBS/cebs/logon.do
                </p>
            </div>
            <div class="authButton" style="margin-bottom: 20px; margin-top: 40px">
                <a class="button back-button" attr-click="1" @click="handleBack">上一步</a>
                <a class="solid-button-large" attr-click="1" @click="CheckBCMBankInfo(1)">{{ authorizedText }}</a>
            </div>
            <div class="autoBankToOpen" style="height: 16px">&nbsp;</div>
            <CommonBottom></CommonBottom>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, computed, onMounted, watch, nextTick, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getAccountList } from "@/views/Cashier/CashOrDepositJournal/utils";
import { BankType } from "@/constants/bankKey";
import { GetBankLink } from "@/util/bankType";
import { ElConfirm } from "@/util/confirm";
import { appendStyle, copyText, handleCheck, upgradeApi, getSuccessMsg } from "../utils";
import { getUrlSearchParams, globalWindowOpen } from "@/util/url";

import type { ICurrencyList } from "@/views/Cashier/components/types";
import type { IBankAccount, IBankHandleResult, CMBGradeResult } from "../types";
import type { PropType } from "vue";

import BankCompany from "./BankCompany.vue";
import CommonBottom from "./CommonBottom.vue";
import { replaceAll } from "@/util/common";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const tipArray = [
    "1、请确认公司名称和统一社会信用代码",
    "2、选择对应的银行账户，并确认对应的银行账号",
    "3、在电脑上插入“交通银行”U盾",
    "4、设置授权记账",
];

const props = defineProps({
    bankName: { type: String, default: "" },
    bankAccountList: { type: Array<IBankAccount>, required: true },
    currencyList: { type: Array<ICurrencyList>, required: true },
    bankType: { type: Number as PropType<BankType>, default: BankType.NONE, required: false },
    checkAuthorization: { type: Function, required: true },
    acname: { type: String, default: "" },
    usccNumber: { type: String, default: "" },
    updateBankAccountList: { type: Function, required: true },
});
const currencyList = computed(() => props.currencyList);
const bankAccountList = computed(() => props.bankAccountList);

const showMain = ref(true);
const impowerValue = ref("立即授权");
const autoBankForm = reactive({
    accname: "",
    uscc: "",
    acid: "",
    bankName: "",
    mainaccno: "",
});
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);

const saveSuccessHandle = (ac_no: string) => {
    getAccountList(1020).then((res: any) => {
        props.updateBankAccountList(res.data);
        nextTick().then(() => {
            const item = bankAccountList.value.find((item: IBankAccount) => item.ac_no == ac_no);
            autoBankForm.acid = item?.ac_id || "";
        });
    });
};

let canNext = true;
const handleConfirmLock = () => {
    canNext = false;
    impowerValue.value = "申请中...";
};
const handleConfirmUnLock = () => {
    canNext = true;
    impowerValue.value = "立即授权";
};

const CheckBCMBankInfo = (type: 0 | 1) => {
    if (!handleCheck(autoBankForm, props.bankType)) return;
    if (!canNext) {
        ElNotify({ type: "warning", message: "申请中，请稍后！" });
        return;
    }
    handleConfirmLock();
    const bankNo = replaceAll(autoBankForm.mainaccno, " ", "");
    if (type == 0) {
        props.checkAuthorization(props.bankType, bankNo, function () {
            request({ url: "/api/CDAccount/CheckName?acId=" + autoBankForm.acid, method: "post" }).then((res: IResponseModel<boolean>) => {
                if (res.state !== 1000 || !res.data) {
                    ElNotify({ type: "warning", message: res.msg || "申请失败，请重试！" });
                    handleConfirmUnLock();
                    return;
                }
                handleQuery();
            });
        });
    } else {
        if (!canNextAuthorized) {
            ElNotify({ type: "warning", message: "申请中，请稍后" });
            return;
        }
        handleAuthorizedLock();
        JumpToBCMAuthorizePage();
    }
};
const confirmTable = ref(false);

const authorizedText = ref("立即授权");
let canNextAuthorized = true;
const handleAuthorizedLock = () => {
    authorizedText.value = "申请中...";
    canNextAuthorized = false;
};
const handleAuthorizedUnLock = () => {
    authorizedText.value = "立即授权";
    canNextAuthorized = true;
};
const handleToConfirm = () => {
    showMain.value = false;
    confirmTable.value = true;
    handleConfirmUnLock();
    handleAuthorizedUnLock();
};
const handleBack = () => {
    confirmTable.value = false;
    showMain.value = true;
    handleConfirmUnLock();
    handleAuthorizedUnLock();
};
const handleQuery = () => {
    upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
        handleResult(res, "Query");
    });
};
const JumpToBCMAuthorizePage = () => {
    ElConfirm("是否已前往“交通银行-专项服务-银企付”完成签约确认？").then((r: boolean) => {
        handleAuthorizedUnLock();
        if (r) {
            upgradeApi(props, autoBankForm).then((res: IResponseModel<CMBGradeResult>) => {
                handleResult(res, "Apply");
            });
        }
    })
};

function handleResult(res: IResponseModel<CMBGradeResult>, type: string) {
    const { successMsg, acctNo,  acname} =  getSuccessMsg(autoBankForm, bankAccountList.value);
    if (res.state !== 1000) {
        ElNotify({ type: "warning", message: res.msg || "申请签约失败，请重试！" });
        handleConfirmUnLock();
        return;
    }
    const result = res.data;
    if (result.status === 1 || result.status === 2) {
        //已签约成功
        ElConfirm(appendStyle(successMsg), true, () => {}, "授权成功").then(() => {
            handleTrialExpired({ msg: ExpiredToBuyDialogEnum.normal, needExpired: false });
        });
        handleConfirmUnLock();
        if (type === "Apply") {
            handleBack();
        }
        return;
    }
    if (result.status === 3 && type === "Query") { //签约中查询
        handleToConfirm();
        handleConfirmUnLock();
        return;
    } 
    if (result.status === 3 && type === "Apply") { //签约中申请
        ElNotify({ type: "warning", message: "未查询到本银行账户“" + acname + "”账号“" + acctNo + "”在交通银行的授权信息。" });
        handleConfirmUnLock();
        return;
    } 
    if (result.status === 4) {
        let errorMsg = result.message;
        if (result.data && result.data.Msg) {
            errorMsg = result.data.Msg;
        }
        ElConfirm(errorMsg || "申请签约失败", true, () => {}, "授权失败");
        handleConfirmUnLock();
        return;
    }
    if (result.status === 5 || result.status === 6) {
        //其他账套成功签约/其他账套签约中
        ElConfirm(result.message, true, () => {}, "授权失败");
        handleConfirmUnLock();
        return;
    }
}

const bankHelpLink = ref("");
onMounted(() => {
    let res = GetBankLink(props.bankType);
    bankHelpLink.value = res;
});
watchEffect(() => {
    autoBankForm.accname = props.acname;
    autoBankForm.uscc = props.usccNumber;
});

watch(
    () => autoBankForm.acid,
    (val) => {
        const item = bankAccountList.value.find((item: IBankAccount) => item.ac_id == val);
        autoBankForm.mainaccno = item?.bank_account || "";
    }
);

const handleCopy = () => {
    copyText("https://ebank.95559.com.cn/CEBS/cebs/logon.do");
    ElNotify({ type: "success", message: "复制成功" });
};

defineExpose({ handleConfirmUnLock });
</script>

<style lang="less" scoped>
@import "@/style/Cashier/BankAndCompany.less";
</style>
