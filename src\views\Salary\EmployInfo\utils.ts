import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoneyWithZero } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

export const setColumns = (showAllInfo: boolean, columns: Array<IColumnProps> | undefined, firstList: any) => {
    const setModule = "EmployInfo";
    columns = [
        { slot: "selection", width: 36, headerAlign: "center", align: "center" },
        { label: "编号", prop: "e_code", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'e_code') },
        { label: "姓名", prop: "e_name", align: "left", headerAlign: "left", minWidth: 88, width: getColumnWidth(setModule, 'e_name') },
        { label: "部门", prop: "department_name", align: "left", headerAlign: "left", minWidth: 127, width: getColumnWidth(setModule, 'department_name') },
        { label: "手机", prop: "mobile_phone", align: "left", headerAlign: "left", minWidth: 123, width: getColumnWidth(setModule, 'mobile_phone') },
        { label: "邮箱", prop: "email", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'email') },
        { slot: "idcard"},
        { label: "计提工资科目", prop: "asub_name", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'asub_name') },
        {
            label: "员工状态",
            prop: "status",
            align: "left",
            headerAlign: "left",
            minWidth: 80,
            width: getColumnWidth(setModule, 'status'),
            formatter: (row: any) => {
                return row.status == 0 ? "在职" : "离职";
            },
        },
        { slot: "operator" },
    ];
    if (showAllInfo) {
        let ssList = [
            {
                e_sn: 1,
                ss_type: 1001,
                ss_name: "养老保险",
                base: 0.0,
                com_percent: 0.0,
                com_amount: 0.0,
                per_percent: 0.0,
                per_amount: 0.0,
                pay_mode: 0,
            },
            {
                e_sn: 1,
                ss_type: 1002,
                ss_name: "医疗保险",
                base: 0.0,
                com_percent: 0.0,
                com_amount: 0.0,
                per_percent: 0.0,
                per_amount: 0.0,
                pay_mode: 0,
            },
            {
                e_sn: 1,
                ss_type: 1003,
                ss_name: "失业保险",
                base: 0.0,
                com_percent: 0.0,
                com_amount: 0.0,
                per_percent: 0.0,
                per_amount: 0.0,
                pay_mode: 0,
            },
            {
                e_sn: 1,
                ss_type: 1004,
                ss_name: "工伤保险",
                base: 0.0,
                com_percent: 0.0,
                com_amount: 0.0,
                per_percent: 0.0,
                per_amount: 0.0,
                pay_mode: 0,
            },
            {
                e_sn: 1,
                ss_type: 1005,
                ss_name: "生育保险",
                base: 0.0,
                com_percent: 0.0,
                com_amount: 0.0,
                per_percent: 0.0,
                per_amount: 0.0,
                pay_mode: 0,
            },
            {
                e_sn: 1,
                ss_type: 1006,
                ss_name: "住房公积金",
                base: 0.0,
                com_percent: 0.0,
                com_amount: 0.0,
                per_percent: 0.0,
                per_amount: 0.0,
                pay_mode: 0,
            },
        ];
        const columnWithSS: any[] = [];
        if (firstList && firstList.insuranceSettings != null) {
            ssList = firstList.insuranceSettings;
            if (ssList != null && ssList.length > 0) {
                for (let i = 0; i < ssList.length; i++) {
                    const ssCol: IColumnProps = { 
                        label: ssList[i].ss_name as string, 
                        headerAlign: "center", 
                        minWidth: 120, 
                        children: [],
                        prop: ssList[i].ss_type.toString(),
                        width: getColumnWidth(setModule, ssList[i].ss_type.toString()),
                    };
                    //子项
                    const ssColSub1: IColumnProps = {
                        label: "缴纳基数",
                        prop: `insuranceSettings[${i}].base`,
                        align: "left",
                        headerAlign: "left",
                        minWidth: 80,
                        width: getColumnWidth(setModule, `insuranceSettings[${i}].base`),
                        formatter: function (row, column, val) {
                            return formatMoneyWithZero(val);
                        },
                    };
                    const ssColSub2: IColumnProps = {
                        label: "公司比例",
                        prop: `insuranceSettings[${i}].com_percent`,
                        align: "left",
                        headerAlign: "left",
                        minWidth: 80,
                        width: getColumnWidth(setModule, `insuranceSettings[${i}].com_percent`),
                        formatter: function (row, column, val) {
                            if (val == 0 || val == "0" || val == "" || val == null) {
                                return "0%";
                            } else {
                                return val + "%";
                            }
                        },
                    };
                    const ssColSub3: IColumnProps = {
                        label: "个人比例",
                        prop: `insuranceSettings[${i}].per_percent`,
                        align: "left",
                        headerAlign: "left",
                        minWidth: 80,
                        width: getColumnWidth(setModule, `insuranceSettings[${i}].per_percent`),
                        formatter: function (row, column, val) {
                            if (val == 0 || val == "0" || val == "" || val == undefined || val == null) {
                                if (ssList[i].ss_type === 1004 || ssList[i].ss_type === 1005) { //工伤和生育的个人部分
                                    return "";
                                }
                                return "0%";
                            } else {
                                return val + "%";
                            }
                        },
                    };
                    const ssColSub4: IColumnProps = {
                        label: "公司金额",
                        prop: `insuranceSettings[${i}].com_amount`,
                        align: "left",
                        headerAlign: "left",
                        minWidth: 100,
                        width: getColumnWidth(setModule, `insuranceSettings[${i}].com_amount`),
                        formatter: function (row, column, val) {
                            return formatMoneyWithZero(val);
                        },
                    };
                    const ssColSub5: IColumnProps = {
                        label: "个人金额",
                        prop: `insuranceSettings[${i}].per_amount`,
                        align: "left",
                        headerAlign: "left",
                        minWidth: 100,
                        width: getColumnWidth(setModule, `insuranceSettings[${i}].per_amount`),
                        formatter: function (row, column, val) {
                            return formatMoneyWithZero(val);
                        },
                    };
                    if (!ssList[i].pay_mode)  {
                        (ssCol.children as Array<IColumnProps>).push(ssColSub1);
                        (ssCol.children as Array<IColumnProps>).push(ssColSub2);
                        (ssCol.children as Array<IColumnProps>).push(ssColSub3);
                    } else {
                        (ssCol.children as Array<IColumnProps>).push(ssColSub4);
                        (ssCol.children as Array<IColumnProps>).push(ssColSub5);
                    }
                    columnWithSS.push(ssCol);
                }
            }
            columns = [
                { slot: "selection", width: 36, headerAlign: "center", align: "center" },
                { label: "编号", prop: "e_code", align: "left", headerAlign: "left", fixed: "left", minWidth: 100,borderRight: true, width: getColumnWidth(setModule, 'e_code') },
                { label: "姓名", prop: "e_name", align: "left", headerAlign: "left", fixed: "left", minWidth: 88,borderRight: true, width: getColumnWidth(setModule, 'e_name') },
                { label: "部门", prop: "department_name", align: "left", headerAlign: "left", fixed: "left", minWidth: 127,borderRight: true, width: getColumnWidth(setModule, 'department_name') },
                { label: "岗位", prop: "position", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'position')},
                { label: "手机", prop: "mobile_phone", align: "left", headerAlign: "left", minWidth: 123, width: getColumnWidth(setModule, 'mobile_phone') },
                { label: "邮箱", prop: "email", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'email') },
                { slot: "idcard"},
                {
                    label: "员工状态",
                    prop: "status",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    width: getColumnWidth(setModule, 'status'),
                    formatter: (row: any) => {
                        return row.status == 0 ? "在职" : "离职";
                    },
                },
                {
                    label: "学历",
                    prop: "education",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 60,
                    width: getColumnWidth(setModule, 'education'),
                    formatter: (row, column, val) => {
                        switch (val) {
                            case 8:
                                return "其他";
                            case 7:
                                return "博士";
                            case 6:
                                return "硕士";
                            case 5:
                                return "本科";
                            case 4:
                                return "大专";
                            case 3:
                                return "中专";
                            case 2:
                                return "高中";
                            case 1:
                                return "初中";
                            default:
                                return "";
                        }
                    },
                },
                { label: "职称", prop: "title", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'title') },
                { label: "计提工资科目", prop: "asub_name", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'asub_name') },
                { label: "银行名称", prop: "bank", align: "left", headerAlign: "left", minWidth: 150, width: getColumnWidth(setModule, 'bank') },
                { label: "银行账号", prop: "bank_account", align: "left", headerAlign: "left", minWidth: 200, width: getColumnWidth(setModule, 'bank_account') },
                {
                    label: "出生日期",
                    prop: "birthday",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'birthday'),
                    formatter: (row: any) => {
                        return row.birthday.split("T")[0];
                    },
                },
                {
                    label: "入职日期",
                    prop: "start_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'start_date'),
                    formatter: (row: any) => {
                        return row.start_date.split("T")[0];
                    },
                },
                {
                    label: "工资始发日期",
                    prop: "salary_start_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'salary_start_date'),
                    formatter: (row: any) => {
                        return row.salary_start_date.split("T")[0];
                    },
                },
                {
                    label: "离职日期",
                    prop: "end_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'end_date'),
                    formatter: (row: any) => {
                        return row.status == 0 ? "" : row.end_date.split("T")[0];
                    },
                },
                {
                    label: "工资停发日期",
                    prop: "salary_end_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'salary_end_date'),
                    formatter: (row: any) => {
                        return row.status == 0 ? "" : row.salary_end_date.split("T")[0];
                    },
                },
                {
                    label: "是否缴纳五险一金",
                    prop: "ss",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    width: getColumnWidth(setModule, 'ss'),
                    formatter: (row) => {
                        return row.ss ? "是" : "否";
                    },
                },
                ...columnWithSS,
                { label: "备注", prop: "note", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'note') },
                { slot: "operator" },
            ];
        } else {
            for (let i = 0; i < ssList.length; i++) {
                const ssCol: IColumnProps = { 
                    label: ssList[i].ss_name as string, 
                    headerAlign: "center", 
                    minWidth: 120, 
                    children: [],
                    prop: ssList[i].ss_type.toString(),
                    width: getColumnWidth(setModule, ssList[i].ss_type.toString()),
                };
                //子项
                const ssColSub1: IColumnProps = {
                    label: "缴纳基数",
                    prop: `insuranceSettings[${i}].base`,
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    width: getColumnWidth(setModule, `insuranceSettings[${i}].base`),
                    formatter: function (row, column, val) {
                        return formatMoneyWithZero(val);
                    },
                };
                const ssColSub2: IColumnProps = {
                    label: "公司比例",
                    prop: `insuranceSettings[${i}].com_percent`,
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    width: getColumnWidth(setModule, `insuranceSettings[${i}].com_percent`),
                    formatter: function (row, column, val) {
                        if (val == 0 || val == "0" || val == "" || val == null) {
                            return "0%";
                        } else {
                            return val + "%";
                        }
                    },
                };
                const ssColSub3: IColumnProps = {
                    label: "个人比例",
                    prop: `insuranceSettings[${i}].per_percent`,
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    width: getColumnWidth(setModule, `insuranceSettings[${i}].per_percent`),
                    formatter: function (row, column, val) {
                        if (val == 0 || val == "0" || val == "" || val == undefined || val == null) {
                            if (ssList[i].ss_type === 1004 || ssList[i].ss_type === 1005) { //工伤和生育的个人部分
                                return "";
                            }
                            return "0%";
                        } else {
                            return val + "%";
                        }
                    },
                };
                const ssColSub4: IColumnProps = {
                    label: "公司金额",
                    prop: `insuranceSettings[${i}].com_amount`,
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, `insuranceSettings[${i}].com_amount`),
                    formatter: function (row, column, val) {
                        return formatMoneyWithZero(val);
                    },
                };
                const ssColSub5: IColumnProps = {
                    label: "个人金额",
                    prop: `insuranceSettings[${i}].per_amount`,
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, `insuranceSettings[${i}].per_amount`),
                    formatter: function (row, column, val) {
                        return formatMoneyWithZero(val);
                    },
                };
                if (!ssList[i].pay_mode)  {
                    (ssCol.children as Array<IColumnProps>).push(ssColSub1);
                    (ssCol.children as Array<IColumnProps>).push(ssColSub2);
                    (ssCol.children as Array<IColumnProps>).push(ssColSub3);
                } else {
                    (ssCol.children as Array<IColumnProps>).push(ssColSub4);
                        (ssCol.children as Array<IColumnProps>).push(ssColSub5);
                }
                columnWithSS.push(ssCol);
            }
            columns = [
                { slot: "selection", width: 36, headerAlign: "center", align: "center" },
                { label: "编号", prop: "e_code", align: "left", headerAlign: "left", fixed: "left", minWidth: 100, width: getColumnWidth(setModule, 'e_code') },
                { label: "姓名", prop: "e_name", align: "left", headerAlign: "left", fixed: "left", minWidth: 88, width: getColumnWidth(setModule, 'e_name') },
                { label: "部门", prop: "department_name", align: "left", headerAlign: "left", fixed: "left", minWidth: 127, width: getColumnWidth(setModule, 'department_name') },
                { label: "岗位", prop: "position", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'position') },
                { label: "手机", prop: "mobile_phone", align: "left", headerAlign: "left", minWidth: 123, width: getColumnWidth(setModule, 'mobile_phone') },
                { label: "邮箱", prop: "email", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'email') },
                { slot: "idcard"},
                {
                    label: "员工状态",
                    prop: "status",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 80,
                    width: getColumnWidth(setModule, 'email'),
                    formatter: (row: any) => {
                        return row.status == 0 ? "在职" : "离职";
                    },
                },
                {
                    label: "学历",
                    prop: "education",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 60,
                    width: getColumnWidth(setModule, 'education'),
                    formatter: (row, column, val) => {
                        switch (val) {
                            case 8:
                                return "其他";
                            case 7:
                                return "博士";
                            case 6:
                                return "硕士";
                            case 5:
                                return "本科";
                            case 4:
                                return "大专";
                            case 3:
                                return "中专";
                            case 2:
                                return "高中";
                            case 1:
                                return "初中";
                            default:
                                return "";
                        }
                    },
                },
                { label: "职称", prop: "title", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'title') },
                { label: "计提工资科目", prop: "asub_name", align: "left", headerAlign: "left", minWidth: 160, width: getColumnWidth(setModule, 'asub_name') },
                { label: "银行名称", prop: "bank", align: "left", headerAlign: "left", minWidth: 150, width: getColumnWidth(setModule, 'bank') },
                { label: "银行账号", prop: "bank_account", align: "left", headerAlign: "left", minWidth: 200, width: getColumnWidth(setModule, 'bank_account') },
                {
                    label: "出生日期",
                    prop: "birthday",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'birthday'),
                    formatter: (row: any) => {
                        return row.birthday.split("T")[0];
                    },
                },
                {
                    label: "入职日期",
                    prop: "start_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'start_date'),
                    formatter: (row: any) => {
                        return row.start_date.split("T")[0];
                    },
                },
                {
                    label: "工资始发日期",
                    prop: "salary_start_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'salary_start_date'),
                    formatter: (row: any) => {
                        return row.salary_start_date.split("T")[0];
                    },
                },
                {
                    label: "离职日期",
                    prop: "end_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'end_date'),
                    formatter: (row: any) => {
                        return row.status == 0 ? "" : row.end_date.split("T")[0];
                    },
                },

                {
                    label: "工资停发日期",
                    prop: "salary_end_date",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 100,
                    width: getColumnWidth(setModule, 'salary_end_date'),
                    formatter: (row: any) => {
                        return row.status == 0 ? "" : row.salary_end_date.split("T")[0];
                    },
                },
                {
                    label: "是否缴纳五险一金",
                    prop: "ss",
                    align: "left",
                    headerAlign: "left",
                    minWidth: 120,
                    width: getColumnWidth(setModule, 'ss'),
                    formatter: (row) => {
                        return row.ss ? "是" : "否";
                    },
                },
                ...columnWithSS,
                { label: "备注", prop: "note", align: "left", headerAlign: "left", minWidth: 100, width: getColumnWidth(setModule, 'note') },
                { slot: "operator" },
            ];
        }
    }
    return columns;
};
export enum LimitCharacterSize {
    // 编码
    Code = 18,
    // 备注
    Note = 1024,
    // 名称
    Name = 256,
    // 统一社会信用代码
    USCC = 18,
    // 手机号
    Phone = 11,
    // 默认
    Default = 64,
}
export const idCardList = [
    {value: "1", label: "居民身份证"},
    {value: "2", label: "中国护照"},
    {value: "3", label: "港澳居民来往内地通行证"},
    {value: "4", label: "台湾居民来往内地通行证"},
    {value: "5", label: "港澳居民居住证"},
    {value: "6", label: "台湾居民居住证"},
    {value: "7", label: "外国护照"},
    {value: "8", label: "外国人永久居留身份证"},
    {value: "9", label: "外国人工作许可证（A类"},
    {value: "10", label: "外国人工作许可证（B类"},
    {value: "11", label: "外国人工作许可证（C类"},
];
export const educationList = [
    {value: "1", label: "初中"},
    {value: "2", label: "高中"},
    {value: "3", label: "中专"},
    {value: "4", label: "大专"},
    {value: "5", label: "本科"},
    {value: "6", label: "硕士"},
    {value: "7", label: "博士"},
    {value: "8", label: "其他"},
];
