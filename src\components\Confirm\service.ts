import { type App } from "vue"
import ElementPlus from "element-plus"
import Confirm from "@/components/Confirm/index.vue"
import type { DialogOptions } from "./types"

export class ConfirmService {
  private static instance: ConfirmService
  private app: App | null = null
  private container: HTMLElement | null = null

  private constructor() {}

  static getInstance(): ConfirmService {
    if (!this.instance) {
      this.instance = new ConfirmService()
    }
    return this.instance
  }

  private createContainer(options: DialogOptions) {
    const { mount = {} } = options
    const { global = false, inTabsContent = true } = mount

    const content = inTabsContent ? document.querySelector(".router-container .content") : document.querySelector(".router-container")

    return global ? document.body : content
  }

  private cleanup() {
    if (this.app) {
      this.app.unmount()
      this.app = null
    }
    if (this.container?.parentNode) {
      this.container.parentNode.removeChild(this.container)
      this.container = null
    }
  }

  show(options: DialogOptions): Promise<boolean> {
    return new Promise((resolve) => {
      const contentBox = this.createContainer(options)
      if (!contentBox) {
        resolve(false)
        return
      }

      this.container = document.createElement("div")

      const app = createApp(Confirm, {
        visible: true,
        options: { showCancel: true, ...options },
        onClose: () => {
          options?.onClose?.()
          resolve(false)
          this.cleanup()
        },
        onConfirm: async () => {
          try {
            await options?.onConfirm?.()
            resolve(true)
          } catch (error) {
            resolve(false)
          } finally {
            this.cleanup()
          }
        },
        onCancel: () => {
          options?.onCancel?.()
          resolve(false)
          this.cleanup()
        },
      })

      this.app = app
      app.use(ElementPlus)
      app.mount(this.container)

      contentBox.insertBefore(this.container, contentBox.firstChild)
    })
  }
}
