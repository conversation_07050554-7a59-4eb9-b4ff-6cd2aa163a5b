@import "../Functions.less";
.sort-voucherno-content {
    text-align: center;
    color: #404040;
    font-size: 12px;
    .buttons {
        padding: 10px 0;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }
    .sort-voucherno-main {
        padding: 20px 0 4px;
        display: inline-block;
        text-align: left;
        .options-form {
            .item {
                display: flex;
                align-items: center;
                margin-bottom: 16px;
                .item-title {
                    width: 80px;
                    height: 30px;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 30px;
                }
                .item-content {
                    width: 210px;
                    height: 30px;
                    .detail-el-select(210px, 30px);
                }
            }
        }
        .break-nums-border {
            border: 1px solid var(--border-color);
            padding: 12px;
            margin: -12px -12px 4px -12px;
            .break-nums {
                display: flex;
                align-items: flex-start;
                width: 384px;
                max-height: 286px;
                overflow: auto;
                .tip-icon {
                    width: 14px;
                    height: 14px;
                    margin-top: 4px;
                    margin-right: 6px;
                }
                .break-nums-content {
                    font-size: var(--h5);
                    line-height: 22px;
                    color: #666666;
                }
            }
        }
    }
}

.line-item {
    &.expend {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        .line-item-field {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding-right: 20px;
            :deep(.el-icon) {
                padding-left: 5px;
                color: var(--link-color);
            }
        }
    }
}

.line-item-title {
    .detail-el-select(97px, 30px);
    :deep(.el-select) {
        position: relative;
        left: -6px;
        .el-input__wrapper {
            padding: 1px 5px;
            padding-left: 10px;
        }
        .el-icon {
            margin: 0;
        }
    }
}
.line-item-field {
    :deep(.el-select) {
        width: 298px;
    }
    input {
        .detail-original-input(298px, 32px);
        &.small {
            .detail-original-input(132px, 32px);
        }
    }
    &.aae {
        :deep(.el-select) {
            &.type {
                width: 110px;
            }
        }
        :deep(.el-select-v2) {
            width: 178px;
            margin-left: 10px;
        }
    }
}
.auto {
    width: auto;
    .downlist-buttons {
        display: inline-flex;
        flex-direction: column;
        padding-left: 10px;
        padding-right: 30px;
    }
}
.inject-table-content {
    color: #404040;
    font-size: 12px;
    .inject-table-main {
        padding: 20px 40px;
        .txt {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            .file-box {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                margin-left: 20px;
                max-width: 160px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                vertical-align: top;
                display: inline-block;
            }
        }
    }
}
.export-main {
    padding: 30px 0 30px 95px;
    display: flex;
    .left {
        line-height: 32px;
    }
    :deep(.el-radio-group) {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .el-radio + .el-radio {
            margin-top: 5px;
        }
    }
}
.print-content {
    color: #404040;
    font-size: 12px;
    .print-main {
        width: 100%;
        box-sizing: border-box;
        padding: 10px 32px;
        .txt {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            margin-bottom: 10px;
        }
        .print-main-title {
            .switch-btns {
                font-size: 0;
                display: inline-block;
                .switch-btn {
                    box-sizing: border-box;
                    height: 28px;
                    width: 76px;
                    text-align: center;
                    border: 1px solid var(--border-color);
                    color: var(--font-color);
                    font-size: 13px;
                    line-height: 26px;
                    cursor: pointer;
                    display: inline-block;
                    &.left-btn {
                        border-radius: 2px 0px 0px 2px;
                    }
                    &.right-btn {
                        border-radius: 0px 2px 2px 0px;
                    }
                    &.selected {
                        background-color: var(--main-color);
                        color: var(--white);
                        border-color: var(--main-color);
                    }
                }
            }
        }
        .switch-content {
            display: block;
            height: 370px;
            .line-item {
                margin-top: 24px;
                font-size: 0px;
                &.height18 {
                    height: 18px;
                    .line-item-title {
                        line-height: 18px;
                    }
                }
                &.height28 {
                    height: 28px;
                    .line-item-title {
                        line-height: 28px;
                    }
                }
                &.margintop16 {
                    margin-top: 15px;
                }
                .line-item-title {
                    width: 104px;
                    color: var(--font-color);
                    font-size: 13px;
                    display: inline-block;
                    vertical-align: top;
                }
                .line-item-field {
                    :deep(.el-checkbox) {
                        margin-right: 20px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }
                    span {
                        line-height: 28px;
                        font-size: 13px;
                        vertical-align: top;
                    }
                    &.input-size1 {
                        .detail-el-select(195px, 28px);
                    }
                    &.input-size2 {
                        .detail-el-select(100px, 28px);
                    }
                    &.input-size3 {
                        > input {
                            .detail-original-input(152px, 28px);
                        }
                    }
                    &.input-size4 {
                        .detail-el-select(76px, 28px);
                    }
                    &.span-content {
                        > input {
                            .detail-original-input(56px, 28px);
                            display: inline-block;
                            margin: 0 8px;
                        }
                    }
                }
            }
        }
    }
}
