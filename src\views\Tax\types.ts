export interface IPeriodData {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
}
export interface ISelectList {
    label: string;
    pid: number;
}
export interface IBasetype {
    label: string;
    type: string;
    dataTaxType: string;
    isShow: boolean;
}
export interface ICheckTaxInfoIsCompleteBack {
    isTaxInfoComplete: boolean;
    nationalTaxBureauUrl: string;
    taxDeclarationTypes: number[];
    taxAreaName: string;
    operationManualUrl: string;
    issupportAllTaxType: boolean;
    issupportCompanyStandard: boolean;
}
