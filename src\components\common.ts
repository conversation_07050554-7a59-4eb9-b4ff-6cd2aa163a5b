/* eslint-disable vue/no-reserved-component-names */
import { App, defineAsyncComponent } from "vue"

const globalComponents = {
  install(app: App) {
    app.component(
      "Confirm",
      defineAsyncComponent(() => import("./Confirm/index.vue")),
    )

    app.component(
      "LMTable",
      defineAsyncComponent(() => import("./Table/index.vue")),
    )

    app.component(
      "ToolTip",
      defineAsyncComponent(() => import("./ToolTip/index.vue")),
    )

    app.component(
      "LMSelect",
      defineAsyncComponent(() => import("./Select/index.vue")),
    )

    app.component(
      "LMOption",
      defineAsyncComponent(() => import("./Option/index.vue")),
    )

    app.component(
      "LMInput",
      defineAsyncComponent(() => import("./Input/index.vue")),
    )

    app.component(
      "Popover",
      defineAsyncComponent(() => import("./Popover/index.vue")),
    )

    app.component(
      "CheckableInput",
      defineAsyncComponent(() => import("./CheckableInput/index.vue")),
    )

    app.component(
      "ContentSlider",
      defineAsyncComponent(() => import("./ContentSlider/index.vue")),
    )

    app.component(
      "LMButton",
      defineAsyncComponent(() => import("./LMButton/index.vue")),
    )
  },
}

export default globalComponents
