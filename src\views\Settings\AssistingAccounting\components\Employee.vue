<template>
    <searchView
        @handle-new="handleNew"
        @handle-search="btnSearch"
        @handle-clear="handleClear"
        @handle-export="handleExport"
        @handle-import="handleImport"
    />
    <div class="main-center">
        <Table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :page-is-show="true"
            :page-sizes="paginationData.pageSizes"
            :page-size="paginationData.pageSize"
            :total="paginationData.total"
            :current-page="paginationData.currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            @refresh="handleRerefresh"
            :scrollbar-show="true"
            :tableName="setModule"
        >
            <template #operator>
                <el-table-column label="操作" min-width="90px" align="left" header-align="left" :resizable="false">
                    <template #default="scope">
                        <span v-show="scope.row.option">
                            <a v-permission="['assistingaccount-canedit']" class="link" @click="handleEdit(scope.row.aaeId)"> 编辑 </a>
                            <a v-permission="['assistingaccount-candelete']" class="link" @click="handleDelete(scope.row.aaeId)"> 删除 </a>
                        </span>
                    </template>
                </el-table-column>
            </template>
        </Table>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getUrlSearchParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { deleteHandle, clearHanele, exportHandle, getModelApi, formatDate } from "../utils";

import type { IColumnProps } from "@/components/Table/IColumnProps";

import Table from "@/components/Table/index.vue";
import searchView from "./SearchBox.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AssitEmploy";
interface ITableItem {
    gender: number;
    departmentId: string;
    departmentName: string;
    position: string;
    title: string;
    mobilePhone: string;
    birthday: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
interface IEditModelItem {
    asId: number;
    aaeId: number;
    gender: number;
    departmentId: string;
    departmentName: string;
    title: string;
    position: string;
    mobilePhone: string;
    birthday: string;
    startDate: string;
    endDate: string;
    note: string;
    aaNum: string;
    aaName: string;
    status: number;
    uscc: string;
}

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const columns: IColumnProps[] = [
    { label: "职员编码", prop: "aaNum", minWidth: 70, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaNum") },
    { label: "职员名称", prop: "aaName", minWidth: 70, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaName") },
    { label: "助记码", prop: "aaAcronym", minWidth: 70, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "aaAcronym") },
    {
        label: "性别",
        prop: "gender",
        minWidth: 40,
        align: "left",
        headerAlign: "left",
        formatter: function (_row, _column, value) {
            return value == "1" ? "男" : "女";
        }, 
        width: getColumnWidth(setModule, "gender")
    },
    { label: "部门编码", prop: "departmentId", minWidth: 70, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "departmentId") },
    { label: "部门名称", prop: "departmentName", minWidth: 70, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "departmentName") },
    { label: "职务", prop: "title", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "title") },
    { label: "岗位", prop: "position", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "position") },
    { label: "手机", prop: "mobilePhone", minWidth: 85, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "mobilePhone") },
    { label: "出生日期", prop: "birthday", minWidth: 80, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "birthday") },
    { label: "入职日期", prop: "startDate", minWidth: 80, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "startDate") },
    { label: "离职日期", prop: "endDate", minWidth: 80, align: "left", headerAlign: "left", formatter: formatDate, width: getColumnWidth(setModule, "endDate") },
    { label: "备注", prop: "note", minWidth: 55, align: "left", headerAlign: "left", width: getColumnWidth(setModule, "note") },
    { slot: "operator" },
];

const loading = ref(false);
const tableData = ref<ITableItem[]>([]);
const emit = defineEmits(["handleNew", "handleEdit", "handleImport", "handleCancel"]);

const searchStr = ref("");
const btnSearch = (searchVal: string) => {
    searchStr.value = searchVal;
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
};
const handleSearch = (successBack?: Function) => {
    loading.value = true;
    const params = {
        showAll: false,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        searchStr: searchStr.value,
    };
    request({ url: "/api/AssistingAccounting/PagingEmployeeList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.data;
                paginationData.total = res.data.count;
                successBack && successBack();
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleNew = () => {
    emit("handleNew", 10003);
};
const handleEdit = (aaeID: number) => {
    getModelApi(aaeID, 10003).then((res: IResponseModel<IEditModelItem>) => {
        if (res.state == 1000) {
            const data = res.data;
            const params = {
                aaNum: data.aaNum,
                aaName: data.aaName,
                gender: data.gender + "",
                departmentId: data.departmentId,
                departmentName: data.departmentName,
                title: data.title,
                position: data.position,
                mobilePhone: data.mobilePhone,
                birthday: data.birthday,
                startDate: data.startDate,
                endDate: data.endDate,
                note: data.note,
                status: data.status == 0,
                aaeID: data.aaeId,
            };
            emit("handleEdit", 10003, params);
        } else {
            ElNotify({ type: "warning", message: res.msg });
        }
    });
};
const isThisPageHasData = (type: "delete" | "clear") => {
    return function () {
        if (type === "delete") {
            if (paginationData.currentPage !== 1 && tableData.value.length === 1) {
                paginationData.currentPage--;
            } else {
                handleCancel();
            }
        } else {
            if (paginationData.currentPage !== 1) {
                paginationData.currentPage = 1;
            } else {
                paginationData.currentPage = 1;
                handleCancel();
            }
        }
    };
};
const handleCancel = () => emit("handleCancel");
const handleDelete = (aaeID: number) => deleteHandle(10003, aaeID, isThisPageHasData("delete"));
const handleClear = () => clearHanele(tableData.value.length, 10003, isThisPageHasData("clear"));
const handleExport = () => exportHandle(10003, paginationData.total);
const handleImport = () => emit("handleImport", 10003);

defineExpose({ handleSearch });

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});
</script>

<style lang="less" scoped></style>
