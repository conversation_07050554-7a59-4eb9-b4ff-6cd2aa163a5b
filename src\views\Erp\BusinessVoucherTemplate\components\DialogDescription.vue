<template>
    <el-dialog destroy-on-close center width="590px" v-model="display" title="自定义摘要配置" class="custom-confirm" @closed="handleClosed">
        <div class="dialog-description-content">
            <div class="description-content-main">
                <div class="tip">选择单据字段或固定文本，点击'添加字段到摘要'后，系统将按照您添加的顺序组成摘要信息</div>
                <div class="description-options" :class="{ bottom: descriptionType === 2 }">
                    <el-radio-group v-model="descriptionType">
                        <el-radio :label="1">单据字段</el-radio>
                        <el-radio :label="2">固定文本</el-radio>
                    </el-radio-group>
                    <div class="select-options">
                        <el-select ref="selectRef" :teleported="false" v-model="field" placeholder="请选择单据字段">
                            <el-option-group v-for="group in groups" :key="group.label" :label="group.label">
                                <el-option v-for="item in group.options" :key="item.id" :label="item.label" :value="item.id"></el-option>
                            </el-option-group>
                        </el-select>
                        <Tooltip :content="fixedText" :line-clamp="1" :max-width="216" :font-size="14">
                            <div
                                v-show="!isFocus"
                                class="fixed-input-display"
                                :class="{ 'no-content': fixedText.length === 0 }"
                                @click="handleFocusFixedText"
                            >
                                {{ fixedText || "请输入摘要信息" }}
                            </div>
                        </Tooltip>
                        <el-input
                            ref="fixedTextRef"
                            v-show="isFocus"
                            type="textarea"
                            resize="none"
                            :autosize="{ minRows: 1, maxRows: 3 }"
                            placeholder="请输入摘要信息"
                            v-model="fixedText"
                            @blur="isFocus = false"
                            @input="handleInput"
                        ></el-input>
                        <div class="append-button" @click="handleAppendtoDescription">添加字段到摘要</div>
                    </div>
                    <el-popover placement="top" :width="350" trigger="hover" content="摘要设置表体字段时，凭证会按照单据明细生成多行">
                        <template #reference>
                            <span class="tip-icon"></span>
                        </template>
                    </el-popover>
                </div>
                <div class="preview mt-20" :class="{ 'has-option': displayArr.length }">
                    <div class="placeholder" v-show="!displayArr.length">
                        <div class="top">
                            例如：
                            <span class="oval">收到</span>
                            <span class="oval">表头.客户名称</span>
                            <span class="oval">货款</span>
                        </div>
                        <div class="rows mt-10">
                            <div class="row">1.选择固定文本，录入'收到'，点击'添加字段到摘要'</div>
                            <div class="row">2.选择单据字段，下拉选择'客户名称'字段，点击'添加字段到摘要'</div>
                            <div class="row">3.选择固定文本，录入'货款'，点击'添加字段到摘要'</div>
                            <div class="row">4.摘要结果：收到客户一货款</div>
                        </div>
                    </div>
                    <div
                        class="display"
                        ref="containerRef"
                        @dragstart="handleDragStart"
                        @dragenter="handleDragEnter"
                        @dragend="handleDragEnd"
                    >
                        <div
                            v-for="(item, index) in displayArr"
                            :key="item.onlyKey"
                            :draggable="!item.editing"
                            :class="{
                                oval: true,
                                editing: item.editing,
                                fixed: item.type === DescriptionType.FixedText,
                            }"
                            :data-set-key="item.onlyKey"
                            :style="{ width: item.width + 'px' }"
                            @dblclick="handleEditDesc(item)"
                        >
                            <template v-if="item.editing">
                                <el-input
                                    v-model="item.fixedText"
                                    type="textarea"
                                    ref="textarea"
                                    resize="none"
                                    :autosize="{ minRows: 1, maxRows: 3 }"
                                    @input="(text: string) => handleInput(text, item)"
                                    @blur="handleBlur(item)"
                                />
                            </template>
                            <template v-else>
                                <span class="hidden">{{ item.fixedText }}</span>
                                <span class="delete" @mousedown.stop @click.stop="handleDeleteOption(index)"></span>
                            </template>
                        </div>
                    </div>
                    <span class="clear" v-show="displayArr.length" @click="handleClear">清空全部</span>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="save">确定</a>
                <a class="button" @click="display = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, ref } from "vue";
import { ElNotify } from "@/util/notify";
import {
    DescriptionType,
    DescriptionInfo,
    getOffsetWidth,
    descLimitLength,
    mapRowDescToDescriptionInfo,
    getFieldInfo,
    type RequestCustomDesc,
    type IDescGroup,
} from "../utils";

import { ElInput, ElSelect } from "element-plus";
import Tooltip from "@/components/Tooltip/index.vue";

const emit = defineEmits<{
    (event: "save", descList: Array<DescriptionInfo>): void;
    (event: "closed"): void;
}>();

const display = ref(false);
const descriptionType = ref<DescriptionType>(DescriptionType.Field);
const field = ref("");
const fixedText = ref("");
const isFocus = ref(false);
const fixedTextRef = ref();
function handleFocusFixedText() {
    isFocus.value = true;
    nextTick().then(() => {
        fixedTextRef.value?.focus();
    });
}

function clearDescription() {
    field.value = "";
    fixedText.value = "";
}

const displayArr = ref<DescriptionInfo[]>([]);
function handleAppendtoDescription() {
    if (descriptionType.value === DescriptionType.Field && !field.value) {
        ElNotify({ type: "warning", message: "请选择单据字段" });
        return;
    }
    if (descriptionType.value === DescriptionType.FixedText && !fixedText.value.trim()) {
        ElNotify({ type: "warning", message: "请输入摘要信息" });
        fixedText.value = "";
        return;
    }
    let fieldVal = 0;
    let fixedTextVal = "";
    if (descriptionType.value === DescriptionType.Field) {
        if (checkBatchSelect(~~field.value)) return;
        fieldVal = ~~field.value;
        fixedTextVal = getFieldInfo(fieldVal, groups.value);
    } else {
        fixedTextVal = fixedText.value.trim();
    }
    const width = getOffsetWidth(fixedTextVal);
    displayArr.value.push(new DescriptionInfo(descriptionType.value, fixedTextVal, fieldVal, width));
    clearDescription();
}
function checkBatchSelect(id: number) {
    if (groups.value.length !== 3) return false;
    let hasBody1 = false;
    let hasBody2 = false;
    const currentBody1 = groups.value[1].options.find((option) => option.id === id);
    const currentBody2 = groups.value[2].options.find((option) => option.id === id);
    for (let i = 0; i < displayArr.value.length; i++) {
        const item = displayArr.value[i];
        if (item.type !== DescriptionType.Field) continue;
        if (item.type === DescriptionType.Field) {
            const body1 = groups.value[1].options.find((option) => option.id === item.field);
            if (body1) {
                hasBody1 = true;
                continue;
            }
            const body2 = groups.value[2].options.find((option) => option.id === item.field);
            if (body2) {
                hasBody2 = true;
                continue;
            }
        }
    }
    if ((currentBody1 && hasBody2) || (currentBody2 && hasBody1)) {
        ElNotify({ type: "warning", message: "亲，其他支出单表体和采购入库单表体不能同时选择哦~" });
        field.value = "";
        return true;
    }
    return false;
}
function handleDeleteOption(itemIndex: number) {
    clearLastActive();
    itemIndex !== -1 && displayArr.value.splice(itemIndex, 1);
}
function handleClear() {
    displayArr.value.length = 0;
    clearLastActive();
}
function otherClick(event: Event) {
    const target = event.target as HTMLElement;
    const allowList = ["oval", "el-textarea__inner", "hidden"];
    if (!target || allowList.every((className) => !target.classList.contains(className))) {
        clearLastActive();
        window.removeEventListener("mousedown", otherClick);
    }
}
let lastActiveDesc: DescriptionInfo | null = null;
function clearLastActive() {
    if (lastActiveDesc) {
        lastActiveDesc.editing = false;
        lastActiveDesc = null;
    }
}

const containerRef = ref<HTMLElement | null>(null);
let currentDragTab: null | HTMLElement = null;
function handleDragStart(event: DragEvent) {
    const target = event.target as HTMLElement;
    if (!target || !target.classList.contains("oval")) return;
    const timer = setTimeout(() => {
        target.classList.add("moving");
        clearTimeout(timer);
    });
    currentDragTab = target;
}
let enterDragTab: null | HTMLElement = null;
function handleDragEnter(event: DragEvent) {
    event.preventDefault();
    if (!currentDragTab) return;
    if (event.target === containerRef.value) {
        enterDragTab = null;
        return;
    }
    const target = event.target as HTMLElement;
    if (!target.classList.contains("oval")) return;
    if (enterDragTab === target) return;
    enterDragTab = target;
    const doms = containerRef.value?.querySelectorAll(".oval");
    if (!doms) return;
    const children: Array<Element> = [];
    for (let i = 0; i < doms.length; i++) {
        const dom = doms[i];
        children.push(dom);
    }
    const dragedIndex = children.indexOf(currentDragTab);
    const targetIndex = children.indexOf(target);
    if (dragedIndex === targetIndex) return;
    const targetElement = dragedIndex < targetIndex ? target.nextElementSibling : target;
    containerRef.value?.insertBefore(currentDragTab, targetElement);
}
function handleDragEnd(event: DragEvent) {
    if (!currentDragTab) return;
    nextTick().then(() => {
        const doms = containerRef.value?.querySelectorAll(".oval");
        if (!doms) return;
        const datas: Array<DescriptionInfo> = [];
        for (let i = 0; i < doms.length; i++) {
            const dom = doms[i];
            const key = dom.getAttribute("data-set-key");
            const data = displayArr.value.find((item) => item.onlyKey + "" === key);
            data && datas.push(data);
        }
        displayArr.value = datas;
        trySwitchPosition(event);
        currentDragTab?.classList.remove("moving");
        currentDragTab = null;
        enterDragTab = null;
    });
}
function trySwitchPosition(event: DragEvent) {
    const target = event.target as HTMLElement;
    if (!target || !target.classList.contains("oval")) return;
    if (currentDragTab !== target) return;
    const { width, height } = target.getBoundingClientRect();
    const doms = containerRef.value?.querySelectorAll(".oval");
    if (!doms) return;
    const domList: Array<Element> = [];
    for (let i = 0; i < doms.length; i++) {
        const dom = doms[i];
        domList.push(dom);
    }
    const lastDom = domList[domList.length - 1];
    const { left, top, width: lw } = lastDom.getBoundingClientRect();
    const errorSize = 20;
    const maxLeft = left + lw + width + errorSize;
    const maxTop = top + height + errorSize;
    const minLeft = left + lw - errorSize;
    const minTop = top - errorSize;
    if (event.clientX > maxLeft || event.clientX < minLeft || event.clientY > maxTop || event.clientY < minTop) return;
    const originIndex = domList.indexOf(currentDragTab);
    const data = displayArr.value.splice(originIndex, 1)[0];
    displayArr.value.push(data);
}
const textarea = ref<Array<InstanceType<typeof ElInput>>>();
function handleEditDesc(description: DescriptionInfo) {
    if (description.type === DescriptionType.Field) return;
    description.editing = true;
    nextTick().then(() => {
        textarea.value && textarea.value[0]?.focus();
    });
}
function handleInput(text: string, item?: DescriptionInfo) {
    if (text.length > descLimitLength) {
        ElNotify({ type: "warning", message: `固定文本不能超过${descLimitLength}个字符` });
        if (item) {
            item.fixedText = item.fixedText.slice(0, descLimitLength);
            return;
        }
        fixedText.value = text.slice(0, descLimitLength);
    }
}
function handleBlur(description: DescriptionInfo) {
    description.editing = false;
    description.width = getOffsetWidth(description.fixedText);
    window.removeEventListener("mousedown", otherClick);
}
function save() {
    if (!displayArr.value.length) {
        ElNotify({ type: "warning", message: "请设置摘要信息" });
        return;
    }
    const filterNoneArr = displayArr.value.filter((item) => item.fixedText.trim());
    emit("save", filterNoneArr);
    display.value = false;
}
const selectRef = ref<InstanceType<typeof ElSelect>>();
function resetDialogInfo() {
    displayArr.value.length = 0;
    clearLastActive();
    clearDescription();
}
function handleClosed() {
    resetDialogInfo();
    emit("closed");
}
function showDialog(list: Array<RequestCustomDesc>) {
    const formatList = mapRowDescToDescriptionInfo(list, groups.value);
    descriptionType.value = DescriptionType.Field;
    displayArr.value = formatList;
    display.value = true;
}
const groups = ref<Array<IDescGroup>>([]);
function initSelectOptions(
    title: string,
    headers: Array<{ id: number; label: string }>,
    body: Array<{ id: number; label: string }>,
    footerList: Array<{ id: number; label: string }>
) {
    groups.value.length = 0;
    const _groups = [
        { label: title + "-表头", options: headers },
        { label: title + "-表体", options: body },
        { label: "采购入库单-表体", options: footerList },
    ];
    nextTick().then(() => {
        groups.value = _groups.filter((group) => group.options.length);
    });
}
function reset() {
    groups.value.length = 0;
}
defineExpose({ showDialog, initSelectOptions, reset });
</script>
<style lang="less" scoped>
.dialog-description-content {
    .description-content-main {
        padding: 20px 40px 40px;
        .tip {
            font-size: 12px;
            color: #666666;
            line-height: 22px;
            padding-left: 20px;
            background: url("@/assets/Icons/hint.png") no-repeat 0 50%;
            background-size: 14px;
        }
        .description-options {
            position: relative;
            display: flex;
            :deep(.el-radio-group) {
                flex-direction: column;
                .el-radio {
                    margin-right: 25px;
                    margin-top: 14px;
                }
            }
            .select-options {
                display: flex;
                flex-direction: column;
                & > div {
                    margin-top: 14px;
                    width: 238px;
                    height: 32px;
                }
                .fixed-input-display {
                    margin-top: 14px;
                    border-radius: 2px;
                    box-shadow: 0 0 0 1px #dcdfe6 inset;
                    box-sizing: border-box;
                    line-height: 28px;
                    padding: 1px 10px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 238px;
                    white-space: nowrap;
                    height: 32px;
                    &.no-content {
                        color: #c2c7ce;
                    }
                    &:hover {
                        box-shadow: 0 0 0 1px var(--main-color) inset;
                    }
                }
                .append-button {
                    margin-top: 0;
                    position: absolute;
                    right: 50px;
                    top: 12px;
                    display: inline-block;
                    padding: 6px;
                    width: auto;
                    color: var(--main-color);
                    cursor: pointer;
                    line-height: 24px;
                    box-sizing: border-box;
                }
                :deep(.el-select-dropdown__item) {
                    padding-left: 20px;
                }
                :deep(.el-textarea__inner) {
                    z-index: 1;
                }
            }
            &.bottom .select-options .append-button {
                top: 58px;
            }
            .tip-icon {
                display: inline-block;
                width: 16px;
                height: 16px;
                background: url("@/assets/Scm/question.png") no-repeat center;
                background-size: 14px;
                cursor: pointer;
                position: absolute;
                top: 22px;
                left: 80px;
            }
        }
        .preview {
            min-height: 160px;
            border-radius: 2px;
            border: 1px solid rgba(221, 221, 221, 1);
            position: relative;
            color: #333;
            font-size: 12px;
            line-height: 16px;
            padding: 9px 16px;
            box-sizing: border-box;
            .top {
                color: #999;
            }
            &.has-option {
                padding: 0 10px 20px 0;
            }
            .clear {
                font-size: 12px;
                line-height: 16px;
                display: inline-block;
                position: absolute;
                right: 8px;
                bottom: 4px;
                cursor: pointer;
                &:hover {
                    color: var(--main-color);
                }
            }
            .oval {
                display: inline-block;
                padding: 3px 11px;
                border-radius: 12px;
                border: 1px solid #e9e9e9;
                position: relative;
                cursor: default;
                margin: 10px 0 0 10px;
                max-width: 224px;
                box-sizing: border-box;
                height: 24px;
                .delete {
                    display: none;
                    height: 12px;
                    width: 12px;
                    background: url("@/assets/Icons/delete.png") no-repeat;
                    background-size: 12px;
                    position: absolute;
                    right: -2px;
                    top: -4px;
                    cursor: pointer;
                }
                .hidden {
                    display: inline-block;
                    width: 100%;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                &:hover .delete {
                    display: inline-block;
                }
                &.fixed:hover,
                &.editing {
                    border-color: var(--main-color);
                }
                &.editing {
                    padding-left: 5px;
                    padding-right: 5px;
                    min-width: 100px;
                    height: auto;
                }
                &.moving {
                    transition: all 0.5s ease;
                    background-color: transparent;
                    color: transparent;
                    border: 1px dashed var(--border-color);
                }
                :deep(.el-textarea) {
                    .el-textarea__inner {
                        padding: 0;
                        box-shadow: none;
                        font-size: 12px;
                        word-break: break-all;
                    }
                }
            }
            .rows {
                .row {
                    line-height: 20px;
                    color: #999;
                }
            }
            .display {
                display: inline-flex;
                flex-wrap: wrap;
            }
        }
    }
}
</style>
