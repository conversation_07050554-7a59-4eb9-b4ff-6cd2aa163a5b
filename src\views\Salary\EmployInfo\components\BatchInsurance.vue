<template>
    <el-dialog title="批量修改五险一金" center v-model="batchInsuranceShow" width="800px" @close="closeDialog" class="dialogDrag">
        <div class="batch-insurance-dialog" v-dialogDrag>
            <div class="batch-insurance-content">
                <div class="batch-insurance-top">
                    <span class="mr-20"><span class="required">*</span>是否缴纳五险一金：</span>
                    <el-radio-group v-model="isSetInsurance">
                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                </div>
                <div class="batch-insurance-center" v-if="isSetInsurance === 1">
                    <Table 
                        :data="insuranceData" 
                        :columns="columnsInsurance" 
                        :loading="insuranceLoading" 
                        :showOverflowTooltip="false"
                        max-height="400"
                        :tableName="setModule"
                    >
                        <template #name>
                            <el-table-column 
                                label="项目" 
                                header-align="left" 
                                align="left"
                                prop="name"
                                :width="getColumnWidth(setModule, 'name')"
                            >
                                <template v-slot="scope">
                                    {{ scope.row.e_code_name }}
                                </template>
                            </el-table-column>
                        </template>
                        <template #base>
                            <el-table-column 
                                label="缴纳基数" 
                                header-align="center" 
                                align="center"
                                prop="base"
                                :width="getColumnWidth(setModule, 'base')"
                            >
                                <template v-slot="scope">
                                    <el-input
                                        v-if="!scope.row.pay_mode"
                                        :ref="
                                            (el) => {
                                                baseRef[scope.$index] = el;
                                            }
                                        "
                                        autocomplete="on"
                                        name="base"
                                        v-model="scope.row.base"
                                        style="width: 100%"
                                        type="text"
                                        @blur="formatBaseNum($event, scope.row)"
                                        @keyup.enter="formatBaseNum($event, scope.row, scope.$index)"
                                    ></el-input>
                                </template>
                            </el-table-column>
                        </template>
                        <template #company>
                            <el-table-column label="公司承担部分" header-align="center">
                                <el-table-column 
                                    label="公司缴纳比例" 
                                    header-align="left" 
                                    align="right" 
                                    prop="companyPercent"
                                    :width="getColumnWidth(setModule, 'companyPercent')"
                                >
                                    <template #default="scope">
                                        <div class="item-percent" v-if="!scope.row.pay_mode && (!(
                                            (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                            scope.row.com_percent == '0'
                                        ))">
                                            <el-input
                                                :ref="
                                                    (el) => {
                                                        comPercentRef[scope.$index] = el;
                                                    }
                                                "
                                                autocomplete="on"
                                                name="com_percent"
                                                type="text"
                                                v-model="scope.row.com_percent"
                                                style="width: 89%"
                                                @blur="formatCPercent($event, scope.row, 'com')"
                                                @keyup.enter="formatCPercent($event, scope.row, 'com', scope.$index)"
                                            ></el-input>
                                            <span style="padding-left: 2px">%</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column 
                                    label="公司缴纳金额" 
                                    header-align="left" 
                                    align="right" 
                                    prop="companyAmount"
                                    :width="getColumnWidth(setModule, 'companyAmount')"
                                >
                                    <template #default="scope">
                                        <el-input
                                            :ref="
                                                (el) => {
                                                    comAmountRef[scope.$index] = el;
                                                }
                                            "
                                            v-if="!(
                                                (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                scope.row.com_percent == '0'&& (!scope.row.pay_mode)
                                            )"
                                            v-model="scope.row.com_amount"
                                            style="width: 100%"
                                            autocomplete="on"
                                            name="com_amount"
                                            type="text"
                                            @blur="handleCPAmount($event, scope.row, 'com')"
                                            @keyup.enter="handleCPAmount($event, scope.row, 'com', scope.$index)"
                                        ></el-input>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                        </template>
                        <template #person>
                            <el-table-column label="个人承担部分" header-align="center">
                                <el-table-column 
                                    label="个人缴纳比例" 
                                    header-align="left" 
                                    align="right" 
                                    prop="personPercent"
                                    :width="getColumnWidth(setModule, 'personPercent')"
                                >
                                    <template #default="scope">
                                        <div
                                            class="item-percent"
                                            style="text-align: left"
                                            v-if="
                                                !scope.row.pay_mode && (
                                                !(
                                                    (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                    scope.row.per_percent == '0'
                                                ))
                                            "
                                        >
                                            <el-input
                                                :ref="
                                                    (el) => {
                                                        perPercentRef[scope.$index] = el;
                                                    }
                                                "
                                                autocomplete="on"
                                                name="per_percent"
                                                type="text"
                                                v-model="scope.row.per_percent"
                                                style="width: 89%"
                                                @blur="formatCPercent($event, scope.row, 'per')"
                                                @keyup.enter="formatCPercent($event, scope.row, 'per', scope.$index)"
                                            ></el-input>
                                            <span style="padding-left: 2px">%</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="个人缴纳金额" header-align="left" align="right" :resizable="false">
                                    <template #default="scope">
                                        <el-input
                                            :ref="
                                                (el) => {
                                                    perAmountRef[scope.$index] = el;
                                                }
                                            "
                                            name="per_amount"
                                            autocomplete="on"
                                            type="text"
                                            v-model="scope.row.per_amount"
                                            v-if="
                                                !(
                                                    (scope.row.e_code_name === '工伤保险' || scope.row.e_code_name === '生育保险') &&
                                                     scope.row.per_percent == '0' && (!scope.row.pay_mode)
                                                )
                                            "
                                            style="width: 100%"
                                            @blur="handleCPAmount($event, scope.row, 'per')"
                                            @keyup.enter="handleCPAmount($event, scope.row, 'per', scope.$index)"
                                        ></el-input>
                                    </template>
                                </el-table-column>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="saveBatchInsurance">保存</a>
                <a class="button ml-10" @click="closeDialog">取消</a>
            </div>
        </div>
    </el-dialog>
</template>
<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IInsuranceSettings, IInsuranceItem, IEmployItem } from "../types";
import { request, type IResponseModel } from "@/util/service";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { ref } from "vue";
import { toDecimal2 } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "EditBatchInsurance";
const props = defineProps({
    checkedTableData: {
        type: Array,
        default: () => [],
    }
});
const emits = defineEmits([
    "batchSuccess"
]);
const batchInsuranceShow = ref(false);
const isSetInsurance = ref(1);
const columnsInsurance = ref<Array<IColumnProps>>([
    { slot: "name" },
    { slot: "base" },
    { slot: "company" },
    { slot: "person" },
]);
const insuranceData = ref<IInsuranceItem[]>([]);
const insuranceLoading = ref(false);
const initComPercent = ref()
function init() {
    if (!props.checkedTableData.length) {
        ElNotify({
            type: "warning",
            message: "请选择员工信息数据",
        });
        return;
    }
    batchInsuranceShow.value = true;
    getInsuranceData();
}
function getInsuranceData() {
    insuranceLoading.value = true;
    request({
        url: `/api/EmployeeInsurance/Default`,
    }).then((res: IResponseModel<IInsuranceItem[]>) => {
        insuranceData.value = res.data;
        initComPercent.value = insuranceData.value.map((item: IInsuranceItem) => item.com_percent);
        insuranceData.value.forEach((item: IInsuranceItem) => {
            item.base = item.base ? toDecimal2(item.base) : "";
            item.com_amount = item.com_amount ? toDecimal2(item.com_amount) : "";
            item.per_amount = item.per_amount ? toDecimal2(item.per_amount) : "";
        });
    }).catch((error) => {
        console.log(error);
    }).finally(() => {
        insuranceLoading.value = false;
    });
}
function getEmployNo() {
    let eSns: number[] = [];
    props.checkedTableData.forEach((v: any) => {
        eSns.push(v.e_sn);
    });
    return eSns;
}
function saveBatchInsurance() {
    let txt = "";
    let insuranceSettings: IInsuranceSettings[] = insuranceData.value.map((item: IInsuranceItem) => ({
        itemId: item.ss_type,
        itemName: item.e_code_name,
        base: Number(item.base),
        companyPercent: Number(item.com_percent),
        personPercent: Number(item.per_percent),
        companyAmount: Number(item.com_amount),
        personAmount: Number(item.per_amount),
        paymentMode: item.pay_mode || 0,
    }));
    if (props.checkedTableData.length > 1) {
        txt = `确认修改${(props.checkedTableData[0] as IEmployItem).e_name}等${props.checkedTableData.length}位员工的五险一金数据吗？`;
    } else {
        txt = `确认修改${(props.checkedTableData[0] as IEmployItem).e_name}员工的五险一金数据吗？`;
    }
    ElConfirm(txt).then((r) => {
        if (r) {
            let data = {
                eSns: getEmployNo(),
                ss: isSetInsurance.value,
                insuranceSettings,
            }
            request({
                url: `/api/Employee/BatchUpdateInsuranceSet`,
                method: "post",
                data
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000 && res.data) {
                    ElNotify({
                        type: "success",
                        message: "修改成功"
                    })
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg
                    })
                }
            }).finally(()=>{
                closeDialog();
                emits("batchSuccess");
            })
        }
    });
}
const closeDialog = () => {
    batchInsuranceShow.value = false;
    isSetInsurance.value = 1;
}

const baseRef = ref<any>([]);
const comPercentRef = ref<any>([]);
const comAmountRef = ref<any>([]);
const perPercentRef = ref<any>([]);
const perAmountRef = ref<any>([]);
//五险一金缴纳比例数字格式化
const formatPercent = (value: string | undefined | number) => {
    if (typeof value === "number" && !isNaN(value)) {
        return value;
    } else if (typeof value === "string") {
        let reg = /^[0-9]+(\.[0-9]{1,3})?$/;
        if (reg.test(value)) {
            let baseValue = Number(value);
            return baseValue;
        } else {
            ElNotify({
                type: "warning",
                message: "缴纳比例必须为数字，最多3位小数",
            });
            return "";
        }
    }
    return "";
};
const formatDeduction = (value: string | undefined | number) => {
    if (typeof value === "number" && !isNaN(value)) {
        return value.toFixed(2);
    } else if (typeof value === "string") {
        let reg = /^[0-9]+(\.[0-9]{1,2})?$/;
        if (reg.test(value) || value === "") {
            let baseValue = Math.round(Number(value) * 100) / 100;
            return baseValue.toFixed(2);
        } else {
            ElNotify({
                type: "warning",
                message: "缴纳基数必须为数字，最多2位小数",
            });
            return "";
        }
    }
    return "";
};
const formatDeAmount = (value: string | undefined | number) => {
    if (typeof value === "number" && !isNaN(value)) {
        return value.toFixed(2);
    } else if (typeof value === "string") {
        let reg = /^[0-9]+(\.[0-9]{1,2})?$/;
        if (reg.test(value) || value === "") {
            let baseValue = Math.round(Number(value) * 100) / 100;
            return baseValue.toFixed(2);
        } else {
            ElNotify({
                type: "warning",
                message: "缴纳金额必须为数字，最多2位小数",
            });
            return "";
        }
    }
    return "";
};
//处理缴纳基数
function formatBaseNum(e: any, row: any, index?: number) {
    e.target.value = formatDeduction(e.target.value);
    if (e.target.value === "") {
        row.com_amount = "";
        row.per_amount = "";
    } else {
        row.com_amount = row.com_percent ? (Number(((e.target.value * row.com_percent * 100) / 100).toFixed(0)) / 100).toFixed(2) : "";
        row.per_amount = row.per_percent ? (Number(((e.target.value * row.per_percent * 100) / 100).toFixed(0)) / 100).toFixed(2) : "";
    }
    row.base = e.target.value;
    if (index === undefined) return;
    enterNext("base", index);
}
//处理缴纳比例
function formatCPercent(e: any, row: any, type: string, index?: number) {
    //非NaN可计算
    e.target.value = formatPercent(e.target.value);
    if (e.target.value === "") {
        row[`${type}_amount`] = "";
        row[`${type}_percent`] = "";
    } else {
        row[`${type}_amount`] = (Number(((row.base * e.target.value * 100) / 100).toFixed(0)) / 100).toFixed(2);
    }
    if (index === undefined) return;
    enterNext(`${type}_percent`, index);
}
//处理缴纳金额
function handleCPAmount(e: any, row: any, type: string, index?: number) {
    e.target.value = formatDeAmount(e.target.value);
    row[`${type}_amount`] = e.target.value;
    if (index === undefined) return;
    enterNext(`${type}_amount`, index);
}

function enterNext(field: string, index: number) {
    if (field === "base") {
        if (comPercentRef.value[index]) {
            comPercentRef.value[index].focus();
        } else {
            enterNext("com_percent", index);
        }
    } else if (field === "com_percent") {
        if (comAmountRef.value[index]) {
            comAmountRef.value[index].focus();
        } else {
            enterNext("com_amount", index);
        }
    } else if (field === "com_amount") {
        if (perPercentRef.value[index]) {
            perPercentRef.value[index].focus();
        } else {
            enterNext("per_percent", index);
        }
    } else if (field === "per_percent") {
        if (perAmountRef.value[index]) {
            perAmountRef.value[index].focus();
        } else {
            enterNext("per_amount", index);
        }
    } else if (field === "per_amount") {
        if (index === insuranceData.value.length - 1) {
            if (baseRef.value[0]) {
                baseRef.value[0].focus();
            }
        } else {
            if (baseRef.value[index + 1]) {
                baseRef.value[index + 1].focus();
            }
        }
    }
}
defineExpose({
    init
})

</script>
<style scoped lang="less">
.batch-insurance-dialog {
    .batch-insurance-content {
        padding: 10px 50px 20px;
    }
    .batch-insurance-top {
        display: flex;
        align-items: center;
    }
    .required {
        color: var(--red);
    }
    :deep(.custom-table) {
        margin-top: 10px;
        tbody tr td .cell input[type="text"] {
            border: none;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.item-percent {
    display: flex;
    align-items: center;
    justify-content: center
}
</style>