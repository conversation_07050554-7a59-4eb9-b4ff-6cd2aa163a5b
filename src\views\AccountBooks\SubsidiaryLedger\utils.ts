import type { ITree } from "./types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { getshortdata } from "@/views/Cashier/CDJournal/utils";
import { formatMoney, formatQuantity } from "@/util/format";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "SubsidiaryLedger";
export const findTreeIdById = (tree: ITree[], id: string): string => {
    // 遍历树的每个节点
    for (const node of tree) {
        // 如果当前节点的id与要查询的id匹配，则返回该节点的id
        if (node.id === id) {
            return node.id;
        }
        // 如果当前节点有子节点，则递归查找子节点
        if (node.children && node.children.length > 0) {
            const result = findTreeIdById(node.children, id);
            // 如果找到了匹配的数据，则返回结果
            if (result !== "") {
                return result;
            }
        }
    }
    // 如果没有找到匹配的数据，则返回空字符串
    return "";
};

// 判断当前点击的节点是不是最末级节点
export const CheckIfQuantity = (node: ITree) => {
    const isLeaf = node?.children.length === 0;
    if (isLeaf) {
        const num = node.attributes["ifnum"];
        if (num == "1") {
            return "1";
        } else {
            return "0";
        }
    } else {
        const nodes = node?.children as ITree[];
        for (let i = 0; i < nodes?.length; i++) {
            if (CheckIfQuantity(nodes[i]) == "1") {
                return "1";
            } else {
                if (i == nodes.length - 1) {
                    return "0";
                }
            }
        }
    }
};

// formatter搜索列表
export const formatterSearchList = (searchList: ITree[]) => {
    const list: ITree[] = [];
    searchList.forEach((item) => {
        list.push(item);
        if (item.children) {
            list.push(...formatterSearchList(item.children));
        }
    });
    return list;
};

export const changeColumnName = (showNumber: boolean, fc_code: string, contrastSubject: boolean, ifas: boolean) => {
    const baseShowNumberColumns: Array<IColumnProps> = [
        {
            label: "日期",
            prop: "v_date",
            minWidth: 100,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value == "" ? "" : getshortdata(value)),
            width: getColumnWidth(setModule, 'v_date'),
        },
        { slot: "vgname" },
        {
            label: "科目",
            prop: "asub_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'asub_names'),
        },
        {
            label: "辅助核算",
            prop: "aa_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'aa_names'),
        },
        { 
            label: "摘要", 
            prop: "description", 
            minWidth: 173, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'description'),
        },
        { 
            label: "对方科目", 
            prop: "contrastsubject", 
            minWidth: 130, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'contrastsubject'), 
        },
        {
            label: "借方发生额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "debit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'debit_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "debit_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'debit_price'),
                },
                {
                    label: "金额",
                    prop: "debit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit'),
                },
            ],
        },
        {
            label: "贷方发生额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "credit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'credit_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "credit_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'credit_price'),
                },
                {
                    label: "金额",
                    prop: "credit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit'),
                },
            ],
        },
        {
            label: "余额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "方向",
                    prop: "direction",
                    minWidth: 50,
                    align: "left",
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'direction'),
                },
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "total_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'total_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "total_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'total_price'),
                },
                {
                    label: "金额",
                    prop: "total",
                    minWidth: 110,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total'),
                    resizable: false,
                },
            ],
        },
    ];
    const notOriginalCurrencyShowNumberBaseColumns: Array<IColumnProps> = [
        {
            label: "日期",
            prop: "v_date",
            minWidth: 100,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value == "" ? "" : getshortdata(value)),
            width: getColumnWidth(setModule, 'v_date'),
        },
        { slot: "vgname" },
        {
            label: "科目",
            prop: "asub_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'asub_names'),
        },
        {
            label: "辅助核算",
            prop: "aa_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'aa_names'),
        },
        { 
            label: "摘要", 
            prop: "description", 
            minWidth: 173, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'description'),
        },
        { 
            label: "对方科目", 
            prop: "contrastsubject", 
            minWidth: 130, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'contrastsubject'), 
        },
        { 
            label: "币别", 
            prop: "fc_code", 
            minWidth: 70, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'fc_code'), 
        },
        {
            label: "汇率",
            prop: "fc_rate",
            minWidth: 80,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value == "0" ? "" : value),
            width: getColumnWidth(setModule, 'fc_rate'), 
        },
        {
            label: "借方发生额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "debit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'debit_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价(原币)",
                    prop: "debit_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'debit_price'),
                },
                {
                    label: "金额(原币)",
                    prop: "debit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "debit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit'),
                },
            ],
        },
        {
            label: "贷方发生额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "credit_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'credit_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价(原币)",
                    prop: "credit_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'credit_price'),
                },
                {
                    label: "金额(原币)",
                    prop: "credit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "credit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit'),
                },
            ],
        },
        {
            label: "余额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "方向",
                    prop: "direction",
                    minWidth: 50,
                    align: "left",
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'direction'),
                },
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "total_qut",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'total_qut'),
                },
                {
                    slot: "customizePrompt",
                    label: "单价(原币)",
                    prop: "total_price",
                    minWidth: 80,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatQuantity(value),
                    width: getColumnWidth(setModule, 'total_price'),
                },
                {
                    label: "金额(原币)",
                    prop: "total_fc",
                    minWidth: 110,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total_fc'),
                },
                {
                    label: "金额(本位币)",
                    prop: "total",
                    minWidth: 110,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total'),
                    resizable: false,
                },
            ],
        },
    ];
    const baseColumns: Array<IColumnProps> = [
        {
            label: "日期",
            prop: "v_date",
            minWidth: 80,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value ? value.split("T")[0] : ""),
            width: getColumnWidth(setModule, 'v_date'), 
        },
        { slot: "vgname" },
        {
            label: "科目",
            prop: "asub_names",
            minWidth: 125,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'asub_names'), 
        },
        {
            label: "辅助核算",
            prop: "aa_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'aa_names'),
        },
        { 
            label: "摘要", 
            prop: "description", 
            minWidth: 115, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'description'),
        },
        { 
            label: "对方科目", 
            prop: "contrastsubject", 
            minWidth: 125, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'contrastsubject'), 
        },
        {
            label: "借方",
            prop: "debit",
            minWidth: 80,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => formatMoney(value),
            width: getColumnWidth(setModule, 'debit'), 
        },
        {
            label: "贷方",
            prop: "credit",
            minWidth: 80,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => formatMoney(value),
            width: getColumnWidth(setModule, 'credit'), 
        },
        { 
            label: "方向", 
            prop: "direction", 
            minWidth: 50, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'direction'), 
        },
        {
            label: "余额",
            prop: "total",
            minWidth: 100,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => formatMoney(value),
            resizable: false,
        },
    ];
    const noContrastsubjectBaseColumns: Array<IColumnProps> = [
        {
            label: "日期",
            prop: "v_date",
            minWidth: 80,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value ? value.split("T")[0] : ""),
            width: getColumnWidth(setModule, 'v_date'), 
        },
        { slot: "vgname" },
        {
            label: "科目",
            prop: "asub_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'asub_names'),
        },
        {
            label: "辅助核算",
            prop: "aa_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'aa_names'),
        },
        { 
            label: "摘要", 
            prop: "description", 
            minWidth: 160, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'description'), 
        },
        {
            label: "借方",
            prop: "debit",
            minWidth: 100,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => formatMoney(value),
            width: getColumnWidth(setModule, 'debit'),
        },
        {
            label: "贷方",
            prop: "credit",
            minWidth: 100,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => formatMoney(value),
            width: getColumnWidth(setModule, 'credit'),
        },
        { 
            label: "方向", 
            prop: "direction", 
            minWidth: 55, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'direction'),
        },
        {
            label: "余额",
            prop: "total",
            minWidth: 110,
            align: "right",
            headerAlign: "right",
            formatter: (row, column, value) => formatMoney(value),
            resizable: false,
        },
    ];
    const notContrastSubjectShowNumberBaseColumns: Array<IColumnProps> = [
        {
            label: "日期",
            prop: "v_date",
            minWidth: 100,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value == "" ? "" : getshortdata(value)),
            width: getColumnWidth(setModule, 'v_date'),
        },
        { slot: "vgname" },
        {
            label: "科目",
            prop: "asub_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'asub_names'),
        },
        {
            label: "辅助核算",
            prop: "aa_names",
            minWidth: 130,
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, 'aa_names'),
        },
        { 
            label: "摘要", 
            prop: "description", 
            minWidth: 175, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'description'), 
        },
        { 
            label: "对方科目", 
            prop: "contrastsubject", 
            minWidth: 130, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'contrastsubject'), 
        },
        { 
            label: "币别", 
            prop: "fc_code", 
            minWidth: 70, 
            align: "left", 
            headerAlign: "left",
            width: getColumnWidth(setModule, 'fc_code'), 
        },
        {
            label: "汇率",
            prop: "fc_rate",
            minWidth: 80,
            align: "left",
            headerAlign: "left",
            formatter: (row, column, value) => (value == "0" ? "" : value),
            width: getColumnWidth(setModule, 'fc_rate'),
        },
        {
            label: "借方发生额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "原币",
                    prop: "debit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit_fc'),
                },
                {
                    label: "本位币",
                    prop: "debit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'debit'),
                },
            ],
        },
        {
            label: "贷方发生额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "原币",
                    prop: "credit_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit_fc'),
                },
                {
                    label: "本位币",
                    prop: "credit",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'credit'),
                },
            ],
        },
        {
            label: "余额",
            align: "left",
            headerAlign: "center",
            children: [
                {
                    label: "方向",
                    prop: "direction",
                    minWidth: 50,
                    align: "left",
                    headerAlign: "left",
                    width: getColumnWidth(setModule, 'direction'),
                },
                {
                    label: "原币",
                    prop: "total_fc",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total_fc'),
                },
                {
                    label: "本位币",
                    prop: "total",
                    minWidth: 100,
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => formatMoney(value),
                    width: getColumnWidth(setModule, 'total'),
                    resizable: false,
                },
            ],
        },
    ];
    let columns: Array<IColumnProps> = [];
    if (showNumber) {
        if (fc_code === "-1" || Number(fc_code) === 1) {
            if (contrastSubject) {
                columns = baseShowNumberColumns.slice(0);
            } else {
                const index = baseShowNumberColumns.findIndex((item) => item.prop === "contrastsubject");
                const backupList = baseShowNumberColumns.slice(0);
                backupList.splice(index, 1);
                columns = backupList;
            }
        } else {
            if (contrastSubject) {
                columns = notOriginalCurrencyShowNumberBaseColumns.slice(0);
            } else {
                const index = notOriginalCurrencyShowNumberBaseColumns.findIndex((item) => item.prop === "contrastsubject");
                const backupList = notOriginalCurrencyShowNumberBaseColumns.slice(0);
                backupList.splice(index, 1);
                columns = backupList;
            }
        }
    } else {
        if (fc_code === "-1" || Number(fc_code) === 1) {
            columns = contrastSubject ? baseColumns.slice(0) : noContrastsubjectBaseColumns.slice(0);
        } else {
            if (contrastSubject) {
                columns = notContrastSubjectShowNumberBaseColumns.slice(0);
            } else {
                const index = notContrastSubjectShowNumberBaseColumns.findIndex((item) => item.prop === "contrastsubject");
                const backupList = notContrastSubjectShowNumberBaseColumns.slice(0);
                backupList.splice(index, 1);
                columns = backupList;
            }
        }
    }
    // 显示辅助核算
    if (!ifas) {
        const backupList = columns.slice(0);
        const index = backupList.findIndex((item) => item.prop === "aa_names");
        if (index !== -1) {
            columns.splice(index, 1);
        }
    }
    return columns;
};

export function treeClick(type: number, flag: any) { //0收起， 1展开
    if (type === 1) {
        (document.querySelector(".main-center") as HTMLElement).classList.remove("hidden-tree");
        (document.querySelector(".drag-right") as  HTMLElement).style.width = "480px";
        flag.expandFlag = false;
        flag.closeFlag = true;
    } else {
        (document.querySelector(".main-center") as HTMLElement).classList.add("hidden-tree");
        (document.querySelector(".drag-right") as  HTMLElement).style.width = "";
        flag.closeFlag = false;
        flag.expandFlag = true;
    }
}
