<template>
    <div class="add-content">
        <el-form :model="formList" label-width="222px" class="formRef" ref="formRef">
            <div class="isRow">
                <el-form-item label="职员编码：">
                    <div class="row">
                        <input
                            type="text"
                            @input="handleAAInput(LimitCharacterSize.Code, $event, '职员编码', 'aaNum', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                            v-model="formList.aaNum"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="职员名称：">
                    <div class="row">
                        <el-input
                            v-model="formList.aaName"
                            class="input-ellipsis"
                            @blur="inputTypeBlur('aaNameInputRef')"
                            v-if="aaNameTextareaShow"
                            :autosize="{minRows: 1, maxRows: 3.5 }"
                            type="textarea"
                            maxlength="256"
                            @input="limitInputLength(formList.aaName,'职员名称')"
                            @focus="inputTypeFocus()"
                            resize="none"
                            ref="aaNameInputRef"
                        />
                        <Tooltip :content="formList.aaName" :isInput="true" v-else placement="right">
                            <input
                                @focus="inputTypeFocus(1)"
                                ref="aaNameInputRef"
                                type="text"
                                v-model="formList.aaName"
                                class="input-ellipsis"
                            />
                        </Tooltip>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="性别：">
                    <div class="row">
                        <el-select v-model="formList.gender">
                            <el-option label="男" value="1" />
                            <el-option label="女" value="2" />
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item label="部门编码：">
                    <div class="row">
                        <input
                            type="text"
                            v-model="formList.departmentId"
                            @input="handleAAInput(LimitCharacterSize.Code, $event, '部门编码', 'departmentId', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Code, $event)"
                        />
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="部门名称：">
                    <div class="row">
                        <Tooltip :content="formList.departmentName" :is-input="true" placement="right">
                        <input
                            type="text"
                            class="input-ellipsis"
                            v-model="formList.departmentName"
                            @input="handleAAInput(LimitCharacterSize.Name, $event, '部门名称', 'departmentName', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Name, $event)"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="职务：">
                    <div class="row">
                        <Tooltip :content="formList.title" :is-input="true" placement="right">
                        <input
                            type="text"
                            class="input-ellipsis"
                            v-model="formList.title"
                            @input="handleAAInput(LimitCharacterSize.Default, $event, '职务', 'title', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="岗位：">
                    <div class="row">
                        <Tooltip :content="formList.position" :is-input="true" placement="right">
                        <input
                            type="text"
                            class="input-ellipsis"
                            v-model="formList.position"
                            @input="handleAAInput(LimitCharacterSize.Default, $event, '岗位', 'position', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Default, $event)"
                        />
                    </Tooltip>
                    </div>
                </el-form-item>
                <el-form-item label="手机：">
                    <div class="row">
                        <input
                            type="text"
                            v-model="formList.mobilePhone"
                            @input="handleAAInput(LimitCharacterSize.Phone, $event, '手机', 'mobilePhone', changeFormListData)"
                            @paste="handleAAPaste(LimitCharacterSize.Phone, $event)"
                        />
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="出生日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.birthday"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="入职日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.startDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateStart"
                        />
                    </div>
                </el-form-item>
            </div>
            <div class="isRow">
                <el-form-item label="离职日期：">
                    <div class="row">
                        <el-date-picker
                            v-model="formList.endDate"
                            type="date"
                            style="width: 100%"
                            :teleported="false"
                            value-format="YYYY-MM-DD"
                            :disabled-date="disabledDateEnd"
                        />
                    </div>
                </el-form-item>
            </div>
            <el-form-item label="备注：">
                <div class="max">
                    <el-input
                        class="more-long input-ellipsis"
                        v-model="formList.note"
                        @blur="inputTypeBlur('noteInputRef')"
                        v-if="noteTextareaShow"
                        :autosize="{minRows: 1, maxRows: 3.5 }"
                        type="textarea"
                        maxlength="1024"
                        @input="limitInputLength(formList.note,'备注')"
                        @focus="inputTypeFocus()"
                        resize="none"
                        ref="aaNameInputRef"
                    />
                    <div v-else class='note-tooltip-width'>
                        <Tooltip :content="formList.note" :isInput="true" placement="bottom">
                            <input
                                @focus="inputTypeFocus(2)"
                                ref="aaNameInputRef"
                                class="more-long input-ellipsis"
                                type="text"
                                v-model="formList.note"
                            />
                        </Tooltip>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="是否启用：" class="status">
                <el-checkbox label="启用" v-model="formList.status" @change="handleStatusChange" />
            </el-form-item>
        </el-form>
        <div class="buttons" style="margin-top: 4px; width: 100%; border-top: none">
            <a class="button solid-button" @click="handleSave">保存</a>
            <a class="button" @click="handleCancel">返回</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick, watch } from "vue";
import { createCheck, LimitCharacterSize, handleAAInput, handleAAPaste, getNextAaNum, textareaBottom } from "../utils";
import Tooltip from "@/components/Tooltip/index.vue";
import { dayjs } from "element-plus";
import { ValidataEMployee } from "../validator";
import { ElConfirm } from "@/util/confirm";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
let EditType: "New" | "Edit" = "New";
const changeType = (val: "New" | "Edit") => (EditType = val);

const formList = reactive<any>({
    aaNum: "1",
    aaName: "",
    gender: "1",
    departmentId: "",
    departmentName: "",
    title: "",
    position: "",
    mobilePhone: "",
    birthday: "",
    startDate: "",
    endDate: "",
    note: "",
    status: true,
    aaeID: "",
    USCC: "",
    hasEntity: true,
});
const changeFormListData = (key: string, val: string) => {
    const keys = Object.keys(formList);
    for (let i = 0; i < keys.length; i++) {
        if (keys[i] === key) {
            formList[key] = val;
            break;
        }
    }
};

const formRef = ref<any>(null);
const emit = defineEmits(["formCancel", "formChanged", "formCancelEdit"]);

const aaNameTextareaShow=ref(false)
const noteTextareaShow=ref(false)
const aaNameInputRef=ref()
const inputTypeBlur = (value:string) => {
    switch (value) {
        case 'aaNameInputRef':
            aaNameTextareaShow.value = false;
            break;
        case 'noteInputRef':
            noteTextareaShow.value = false;
            break;
    }
};
const inputTypeFocus = (num?:number) => {
    textareaBottom(formRef)
    switch (num) {
        case 1:
            aaNameTextareaShow.value = true;
            break;
        case 2:
            noteTextareaShow.value = true;
            break;
    }
    nextTick(()=>{
        if(num){
            getTextareaFocus(num)
        }
    })
};
const getTextareaFocus = (num:number) => {
    switch (num) {
        case 1:
        case 2:
            aaNameInputRef.value.focus();
            break;
    }
};

let isSaving = false;
const handleSave = () => {
    if (isSaving) return;
    const aaeID = EditType === "Edit" ? formList.aaeID : 0;
    const aaNum = formList.aaNum;
    const aaName = formList.aaName;
    createCheck(10003, aaeID, aaNum, aaName, Save);
};
function limitInputLength(val: string,label: string) {
    switch (label) {
        case '备注':
            if (val.length === 1024) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过1024个字哦~` });
            }
            break;
        case '职员名称':
            if (val.length === 256) {
                ElNotify({ type: "warning", message: `亲，${label}不能超过256个字哦~` });
            }
            break;
    }
}
const Save = () => {
    const entityParams = {
        gender: formList.gender,
        departmentId: formList.departmentId,
        departmentName: formList.departmentName,
        title: formList.title,
        position: formList.position,
        mobilePhone: formList.mobilePhone,
        birthday: formList.birthday,
        startDate: formList.startDate,
        endDate: formList.endDate,
        note: formList.note,
    };
    const params = {
        entity: entityParams,
        aaNum: formList.aaNum,
        aaName: formList.aaName,
        uscc: formList.USCC,
        status: formList.status ? 0 : 1,
        hasEntity: formList.hasEntity,
        ifvoucher: true,
    };
    const urlPath = EditType === "Edit" ? "Employee?aaeid=" + formList.aaeID : "Employee";
    if (ValidataEMployee(entityParams, params.aaNum, params.aaName)) {
        isSaving = true;
        request({
            url: "/api/AssistingAccounting/" + urlPath,
            method: EditType === "New" ? "post" : "put",
            headers: { "Content-Type": "application/json" },
            data: JSON.stringify(params),
        })
            .then((res: IResponseModel<string>) => {
                if (res.state !== 1000 || "Failed" === res.data) {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                    isSaving = false;
                    return;
                }
                ElNotify({ type: "success", message: "保存成功" });
                useAssistingAccountingStore().getAssistingAccounting();
                if (EditType === "New") {
                    getNextAaNum(10003)
                        .then((res: IResponseModel<string>) => {
                            resetForm();
                            formList.aaNum = res.data;
                            emit("formCancelEdit");
                        })
                        .finally(() => {
                            isSaving = false;
                        });
                } else {
                    handleCancel();
                    isSaving = false;
                }
            })
            .catch(() => {
                ElNotify({ type: "warning", message: "保存出现错误，请稍后重试。" });
                isSaving = false;
            })
            .finally(() => {
                window.dispatchEvent(new CustomEvent("refreshAssistingAccountingType"));
            });
    }
};
const handleCancel = () => {
    emit("formCancel");
};
const resetForm = () => {
    const initParams: any = {
        aaNum: "1",
        aaName: "",
        gender: "1",
        departmentId: "",
        departmentName: "",
        title: "",
        position: "",
        mobilePhone: "",
        birthday: "",
        startDate: "",
        endDate: "",
        note: "",
        status: true,
        aaeID: "",
        USCC: "",
        hasEntity: true,
    };
    editForm(initParams);
};
const editForm = (data: any) => {
    Object.keys(data).forEach((key) => {
        if (Object.keys(formList).includes(key)) {
            formList[key] = data[key];
        } else if (Object.keys(formList.aaEntity).includes(key)) {
            formList.aaEntity[key] = data[key];
        }
    });
};
const editAANum = (aaNum: string) => (formList.aaNum = aaNum);
defineExpose({ resetForm, editForm, editAANum, changeType });

function disabledDateStart(time: Date) {
    let endDate = dayjs(formList.endDate).valueOf();
    return time.getTime() > endDate;
}
function disabledDateEnd(time: Date) {
    let startDate = dayjs(formList.startDate).valueOf();
    return time.getTime() < startDate;
}
const handleStatusChange = (check: any) => {
    if (!check) {
        ElConfirm("亲，辅助核算项目停用后不能再在凭证中使用哦，是否确认停用？").then((r: boolean) => {
            formList.status = !r;
        });
    }
};
watch(
    formList,
    () => {
        emit("formChanged");
    },
    { deep: true }
);
</script>

<style lang="less" scoped>
@import "@/style/Settings/AssistingAccounting.less";
:deep(.el-textarea__inner){
    z-index: 1000;
}

.add-content {
    :deep(.el-form) {
        .isRow {
            &:first-child {
                .el-form-item__label {
                    &::before {
                        content: "*";
                        color: var(--red);
                    }
                }
            }
        }
    }
}
.formRef {
    input {
        .detail-original-input(188px, 32px);
        &.middle {
            .detail-original-input(288px, 32px);
        }
        &.more-long {
            .detail-original-input(598px, 32px);
        }
        &.big {
            .detail-original-input(698px, 32px);
        }
    }
    .isRow {
        display: flex;
        align-items: center;
    }
}
</style>
