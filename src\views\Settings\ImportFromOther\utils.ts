export const getYearArray = () => {
    const currentYear = new Date().getFullYear();
    const endYear = currentYear + 6;
    const startYear = 2000;
    const yearArray = [];
    for (let i = startYear; i <= endYear; i++) {
        yearArray.push(i);
    }
    return yearArray;
};

//是否需要另存
export const needSaveAs = (softName: string) => {
    if (softName.indexOf("金蝶") >= 0) {
        if (softName.indexOf("金蝶账无忧V5.0") >= 0) {
            return false;
        }
        return true;
    } else if (softName.indexOf("安易") >= 0) {
        return true;
    } else if (softName.indexOf("小精灵") >= 0) {
        return true;
    } else if (softName.indexOf("浪潮") >= 0) {
        return true;
    } else if (softName.indexOf("新中大") >= 0) {
        return true;
    } else if (softName.indexOf("浩天") >= 0) {
        return true;
    } else if (softName.indexOf("天思") >= 0) {
        return true;
    } else if (softName.indexOf("亿企") >= 0) {
        return true;
    } else if (softName.indexOf("金算盘eERP-B") >= 0) {
        return true;
    } else if (softName.indexOf("用友通") >= 0) {
        return true;
    } else if (softName.indexOf("快账财务") >= 0) {
        return true;
    } else if (softName.indexOf("卓账财务") >= 0) {
        return true;
    } else if (softName.indexOf("管家婆") >= 0) {
        return true;
    } else {
        return false;
    }
};

// 是否需要显示科目导入
export const needSubjectImport = (softName: string) => {
    if (
        softName.indexOf("畅捷通T+13.0") >= 0 ||
        softName.indexOf("畅捷通好会计") >= 0 ||
        softName.indexOf("畅捷通易代账") >= 0 ||
        softName.indexOf("亿企代账") >= 0 ||
        softName.indexOf("金蝶账无忧V5.0") >= 0 ||
        softName.indexOf("诺诺云记账") >= 0 ||
        softName.indexOf("金蝶云星空") >= 0 ||
        softName.indexOf("用友U8") >= 0
    ) {
        return true;
    } else {
        return false;
    }
};

export function canUploadZip(softName: string, type: "account" | "voucher" | "subject" | "divNMYFile") {
    if (type === "voucher") {
        if (softName.indexOf("畅捷通易代账") >= 0) {
            return true;
        } else if (softName.indexOf("芸豆会计") >= 0) {
            return true;
        }
    }
    if (type === "account") {
        if (softName.indexOf("畅捷通易代账") >= 0) {
            return true;
        }
    }
    return false;
}
//是否可以上传txt
export function canUploadTxt(softName: string) {
    if (softName.indexOf("亿企代账") >= 0) {
        return true;
    }
    return false;
}

// 新版在线导账历史记录列表与状态列表相关方法
// 导入错误信息
export const getErrorMsg = (msg: string) => {
    if (!msg || msg.trim() === "") {
        return "导账失败";
    }
    return JSON.parse(msg.replace(/\n|\r/g, ""))[0].Message || "导账失败";
};