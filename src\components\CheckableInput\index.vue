<template>
  <el-input
    :class="borderClass"
    v-model="formatValue"
    @blur="handleBlur(formatValue)"
    @change="handleChange($event)"
    @focus="handleFocus(formatValue)">
    <template
      v-if="!checkResult"
      #prefix>
      <!-- 修改提示内容绑定的信息 -->
      <el-tooltip
        effect="light"
        :content="checkPrompt">
        <el-icon><WarningFilled /></el-icon>
      </el-tooltip>
    </template>
  </el-input>
</template>
<script setup lang="ts">
  import { ElNotify } from "@/utils/notify"

  const modelValue = defineModel<string | number>({ default: 0 })
  const props = withDefaults(
    defineProps<{
      rowData?: { [key: string]: any }
      checkTypeField?: string // 校验类型取哪一个字段
      checkRule?: string // 校验规则
      checkPrompt?: string // 校验后提示信息
    }>(),
    {
      rowData: () => {
        return {}
      },
      checkTypeField: "",
    },
  )

  const emits = defineEmits<{
    (e: "change", value: number): void
    (e: "blur", value: number): void
    (e: "focus", value: number): void
  }>()

  let isFocus = ref(false)
  const formatValue = computed({
    get() {
      return isFocus.value
        ? modelValue.value
        : modelValue.value !== ""
          ? String(parseFloat(modelValue.value as string).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
          : ""
    },
    set(newVal) {
      modelValue.value = newVal
    },
  })

  watch(
    () => modelValue.value,
    (newVal, oldValue) => {
      const splitValue = String(newVal).split(".")
      const integerPart = splitValue[0]
      const decimalPart = splitValue[1]

      if (integerPart.length > 13) {
        modelValue.value = oldValue
        ElNotify({
          type: "warning",
          message: "整数位仅支持输入13位",
        })
      } else if (decimalPart && decimalPart.length > 2) {
        modelValue.value = oldValue
        ElNotify({
          type: "warning",
          message: "小数仅支持输入2位",
        })
      }
    },
  )

  function handleChange(value: number) {
    emits("change", value)
  }

  // 挂载时进行输入框校验
  onMounted(() => {
    handleBlur(modelValue.value, true)
    // if ("checkResult" in props.rowData && `${props.checkTypeField}` in props.rowData.checkResult) {
    //   checkResult.value = props.rowData.checkResult[props.checkTypeField]
    // }
  })

  let checkResult = ref<boolean>(true)

  const borderClass = computed(() => {
    if (checkResult.value) {
      return "common-border"
    } else {
      // 暂定提示型校验为1 强制型校验为2
      return props.rowData![props.checkTypeField] === 1 ? "prompt-border" : "force-border"
    }
  })

  function handleBlur(value: any, isMounted: boolean = false) {
    isFocus.value = false

    const regex = /^[-]?(0|[1-9]\d*)(\.\d+)?$/
    if (!regex.test(value)) {
      ElNotify({
        type: "warning",
        message: "请输入有效的数字",
      })
      modelValue.value = ""
      return
    } else {
      modelValue.value = value
    }

    if (!isMounted) {
      emits("blur", Number(modelValue.value))
    }
    // check(value)

    // 保留本次输入框视角后校验结果
    // if (props.rowData.checkResult) {
    //   props.rowData!.checkResult[props.checkTypeField] = checkResult.value
    // } else {
    //   Object.assign(props.rowData, { checkResult: { [props.checkTypeField]: checkResult.value } })
    // }
  }

  function handleFocus(value: any) {
    isFocus.value = true
    emits("focus", Number(value))
  }

  // TODO: 校验逻辑
  function check(value: number) {
    checkResult.value = false
  }
</script>
<style lang="scss" scoped>
  .common-border {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--main-color) inset;
    }
  }
  .prompt-border {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--yellow) inset;

      .el-input__prefix {
        color: var(--yellow);
      }
    }
  }
  .force-border {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--red) inset;

      .el-input__prefix {
        color: var(--red);
      }
    }
  }
  :deep(.el-input__wrapper) {
    padding: 1px 8px;

    .el-input__prefix {
      font-size: 16px;
    }
    .el-input__inner {
      text-align: right;
    }
  }
</style>
