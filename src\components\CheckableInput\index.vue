<template>
  <div :id="boxId">
    <el-input
      :class="borderClass"
      v-model="formatValue"
      @blur="handleBlur(formatValue)"
      @change="handleChange($event)"
      @focus="handleFocus(formatValue)">
      <template
        v-if="!checkResult"
        #prefix>
        <!-- 修改提示内容绑定的信息 -->
        <el-tooltip effect="light">
          <el-icon><WarningFilled /></el-icon>
          <template #content>
            <div style="max-width: 500px">
              {{ checkPrompt }}
            </div>
          </template>
        </el-tooltip>
      </template>
    </el-input>
  </div>
</template>
<script setup lang="ts">
  import { ElNotify } from "@/utils/notify"
  import { useCheckable } from "@/hooks/useCheckable"

  const modelValue = defineModel<string | number>({ default: 0 })
  const props = withDefaults(
    defineProps<{
      boxId?: string
      tableValidateMessages?: any[]
    }>(),
    {},
  )

  const ruleCellRelationships = inject<any[]>("ruleCellRelationships")

  const { checkResult, checkType, checkPrompt, ruleToCell } = useCheckable()

  watch(
    () => props.tableValidateMessages,
    (newVal) => {
      ruleToCell(newVal, ruleCellRelationships, props.boxId as string)
    },
    { immediate: true },
  )

  const emits = defineEmits<{
    (e: "change", value: number): void
    (e: "blur", value: number): void
    (e: "focus", value: number): void
  }>()

  let isFocus = ref(false)
  const formatValue = computed({
    get() {
      if (modelValue.value === null) {
        return modelValue.value
      }
      return isFocus.value
        ? modelValue.value
        : modelValue.value !== ""
          ? String(parseFloat(modelValue.value as string).toFixed(2)).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
          : ""
    },
    set(newVal) {
      modelValue.value = newVal
    },
  })

  watch(
    () => modelValue.value,
    (newVal, oldValue) => {
      const splitValue = String(newVal).split(".")
      const integerPart = splitValue[0]
      const decimalPart = splitValue[1]

      if (integerPart.length > 13) {
        modelValue.value = oldValue
        ElNotify({
          type: "warning",
          message: "整数位仅支持输入13位",
        })
      } else if (decimalPart && decimalPart.length > 2) {
        modelValue.value = oldValue
        ElNotify({
          type: "warning",
          message: "小数仅支持输入2位",
        })
      }
    },
  )

  function handleChange(value: number) {
    emits("change", value)
  }

  const borderClass = computed(() => {
    if (checkResult.value) {
      return "common-border"
    } else {
      return checkType.value === "warning" ? "prompt-border" : "force-border"
    }
  })

  function handleBlur(value: any, isMounted: boolean = false) {
    isFocus.value = false

    const regex = /^[-]?(0|[1-9]\d*)(\.\d+)?$/
    if (!regex.test(value)) {
      ElNotify({
        type: "warning",
        message: "请输入有效的数字",
      })
      modelValue.value = ""
      return
    } else {
      modelValue.value = value
    }

    if (!isMounted) {
      emits("blur", Number(modelValue.value))
    }
  }

  function handleFocus(value: any) {
    isFocus.value = true
    emits("focus", Number(value))
  }
</script>
<style lang="scss" scoped>
  .common-border {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--main-color) inset;
    }
  }
  .prompt-border {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--yellow) inset;

      .el-input__prefix {
        color: var(--yellow);
      }
    }
  }
  .force-border {
    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px var(--red) inset;

      .el-input__prefix {
        color: var(--red);
      }
    }
  }
  :deep(.el-input__wrapper) {
    padding: 1px 8px;

    .el-input__prefix {
      font-size: var(--h3);
      cursor: pointer;
    }
    .el-input__inner {
      text-align: right;
    }
  }
</style>
