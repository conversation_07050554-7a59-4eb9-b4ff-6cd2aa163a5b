<template>
    <div
        class="dropdown-button"
        @mouseleave.prevent.self="
            panelShow = false;
            showDetail = false;
        "
    >
        <a
            :id="'dropdown-button-' + props.btnValue"
            :class="'button ' + props.class"
            @click.stop.prevent="showOnePanel"
            @mouseenter="removeBtnShow = true"
            @mouseleave="removeBtnShow = false"
        >
            {{ btnTxt }}<span v-show="btnType !== 'money'">&nbsp;: {{ detailedQuantity }}项</span>
            <el-icon v-if="dropIconShow" class="ml-10"><ArrowDown /></el-icon>
            <el-icon class="delete-icon" v-show="removeBtnShow" @click="emits('removeBtn')"><CircleClose /></el-icon>
        </a>
        <div v-show="panelShow" class="dropdown-list" :style="{ height: visibleOptions ? '250px' : '' }">
            <div v-if="btnType === 'money'" class="dropdown-period-selector">
                <PeriodSelector
                    ref="periodSelectorRef"
                    v-model="options"
                    @update:modelValue="updateValueOptions"
                    @visibleChange="visibleChange"
                ></PeriodSelector>
            </div>

            <div v-else class="dropdown-cascader-panel" :style="{ width: dropCascadeWidth * 2 + 10 + 'px' }">
                <div class="cascader-menu cascader-menu-first">
                    <div class="cascader-menu-wrap">
                        <ul class="cascader-menu-list" :style="{ width: dropCascadeWidth + 'px' }">
                            <li
                                v-for="(item, key) in options"
                                :class="['cascader-node', { select: currentDetailShowIndex === String(key) }]"
                                :key="key"
                                @click="currentDetailShowIndex = String(key)"
                                @mouseenter="mouseEnterNode(key)"
                            >
                                <div v-if="item.label === '启用筛选'" @click="item.value = !item.value">
                                    {{ item.label }}
                                    <el-icon v-show="item.value"><Select /></el-icon>
                                </div>
                                <div v-else-if="!item.children" @click="openSelectDialog">
                                    {{ item.label }}
                                </div>
                                <div v-else @mouseenter="showDetailOption(key)">
                                    {{ item.label }}<el-icon><ArrowRight /></el-icon>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div
                    class="cascader-menu cascader-menu-second"
                    :style="{ top: btnType === 'auxi' ? '15px' : '', width: dropCascadeWidth + 'px' }"
                    v-show="showDetail"
                >
                    <div class="cascader-menu-wrap">
                        <el-scrollbar>
                            <el-checkbox-group v-model="currentChecked" class="cascader-menu-list" @change="changeChecked">
                                <el-checkbox
                                    v-for="item in options[currentDetailShowIndex]?.children"
                                    :key="item.value"
                                    :label="item.value"
                                    :disabled="item.required"
                                    :value="item.value"
                                    >{{ item.label }}</el-checkbox
                                >
                            </el-checkbox-group>
                        </el-scrollbar>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import type { IValueDropDownOPtions, IDropDownOPtions } from "../types";
import type { CheckboxValueType } from "element-plus";
import PeriodSelector from "./PeriodSelector.vue";

const props = withDefaults(
    defineProps<{
        btnTxt: string;
        class?: string;
        downlistWidth?: number;
        btnType?: string;
        modelValue: Object;
        btnValue: number | string;
        detailedQuantity?: number;
        dropIconShow?: boolean;
    }>(),
    {
        class: "",
        downlistWidth: 242,
        btnType: "auxi",
        detailedQuantity: 0,
        dropIconShow: true,
    }
);
const emits = defineEmits(["optionsRes", "update:modelValue", "openSelectDialog", "removeBtn", "dropCascadeShow", "updateValueOptions"]);
const panelShow = ref(false);
const visibleOptions = ref(false);
const visibleChange = (v: boolean) => {
    if (!v) {
        let timer = setTimeout(() => {
            visibleOptions.value = v;
            clearTimeout(timer);
        }, 5000);
    } else {
        visibleOptions.value = v;
    }
};
const options = computed({
    get() {
        return props.modelValue as IDropDownOPtions | IValueDropDownOPtions;
    },
    set(value) {
        if (props.btnType !== "money") {
            emits("update:modelValue", props.btnTxt, props.btnType, value);
        }
    },
});

const updateValueOptions = (newVal: any) => {
    options.value = newVal;

    emits("updateValueOptions", newVal);
};
const currentDetailShowIndex = ref("");
const dropCascadeWidth = ref(110);
const periodSelectorRef = ref();
const showOnePanel = () => {
    if (!props.dropIconShow) return;
    if (props.btnType === "money") {
        periodSelectorRef.value?.changeValuePeriod(options.value);
    } else {
        const myElement = document.getElementById("dropdown-button-" + props.btnValue) as Element;
        const width = myElement?.clientWidth;
        dropCascadeWidth.value = width > 110 ? width : 110;
    }

    panelShow.value = true;
    currentDetailShowIndex.value = "";
};
const removeBtnShow = ref(false);
const showDetail = ref(false);
const currentChecked = ref([]);
const showDetailOption = (key: string | number) => {
    showDetail.value = true;
    currentDetailShowIndex.value = String(key);
    currentChecked.value = (options.value as any)[key].checked;
};
const mouseEnterNode = (key: string | number) => {
    if (currentDetailShowIndex.value !== String(key)) {
        showDetail.value = false;
        currentDetailShowIndex.value = "";
    }
};
const changeChecked = (v: CheckboxValueType[]): any => {
    if (props.btnType === "money") {
        if ((options.value as any)[currentDetailShowIndex.value].label === "月份") {
            (options.value as any)["quarter"].checked = [];
        } else if ((options.value as any)[currentDetailShowIndex.value].label === "季度") {
            (options.value as any)["month"].checked = [];
        }
    }
    (options.value as any)[currentDetailShowIndex.value].checked = v.sort();
};
watch(panelShow, () => {
    emits("dropCascadeShow", panelShow.value);
});

const openSelectDialog = () => {
    emits("openSelectDialog", props.btnType, props.btnTxt, props.btnValue);
};
</script>

<style lang="less" scoped>
.dropdown-button {
    position: relative;
    display: flex;
    .button {
        display: flex;
        align-items: center;
        position: relative;
        .el-icon {
            color: #666;
            margin-left: 10px;
        }
        .delete-icon {
            position: absolute;
            top: -9px;
            right: -10px;
            font-size: 16px;
            background-color: #fff;
            color: #969799;
        }
        min-width: 76px;
        height: 28px;
        box-sizing: border-box;
        padding: 0 12px;
        font-size: var(--font-size);
        white-space: nowrap;
        width: auto;
        &:hover {
            color: var(--font-color);
            border-color: var(--main-color);
            background-color: var(--white);
        }
    }
    .dropdown-list {
        position: absolute;
        top: 28px;
        z-index: 99;
        height: auto;
        .dropdown-period-selector {
            background-color: #fff;
            border: 1px solid var(--el-border-color-light);
            box-sizing: border-box;
            padding: 4px 0 4px 10px;
            margin-top: 4px;
        }
    }
    .dropdown-cascader-panel {
        margin-top: 4px;
        position: relative;
        width: auto;
        display: flex;
        .cascader-menu {
            overflow: hidden;
            width: auto;
            box-sizing: border-box;
            border: 1px solid var(--el-border-color-light);
            background-color: #fff;
            border-radius: 4%;

            &.cascader-menu-first {
                margin-right: 4px;
                height: auto;
            }
            &.cascader-menu-second {
                position: absolute;
                right: 0;
            }
            .cascader-menu-wrap {
                max-height: 204px;
                overflow: auto;
                .cascader-menu-list {
                    position: relative;
                    min-height: 100%;
                    margin: 0;
                    padding: 6px 0;
                    list-style: none;
                    box-sizing: border-box;
                    .cascader-node {
                        padding-right: 20px;
                        position: relative;
                        display: flex;
                        align-items: center;
                        padding: 0 10px 0 20px;
                        height: 32px;
                        line-height: 32px;
                        outline: 0;
                        &:hover {
                            color: #3d7fff;
                        }
                        &.select {
                            color: #3d7fff;
                        }
                        div {
                            display: flex;
                            justify-content: space-between;
                            width: 100%;
                            align-items: center;
                        }
                    }
                }
                .el-checkbox-group {
                    display: flex;
                    flex-direction: column;
                    padding-left: 15px;
                }
            }
        }
    }
}
</style>
