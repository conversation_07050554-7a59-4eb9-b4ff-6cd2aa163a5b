<template>
    <PurchaseInvoice :invoiceCategory="'10080'" :isFetchInvoicePurchase="isFetchInvoice"></PurchaseInvoice>
</template>

<script lang="ts">
export default {
    name: "PurchaseInvoice",
};
</script>
<script setup lang="ts">
import PurchaseInvoice from "@/views/Invoice/index.vue";
import { ref, watch, onMounted } from "vue";
import { onBeforeRouteLeave, useRoute } from "vue-router";
const route = useRoute();
const isFetchInvoice = ref(false);
watch(
    () => route.query,
    (val) => {
        if (isLeavingFromInvoice) {
            isLeavingFromInvoice = false;
            return;
        }
        const { fetch } = val;
        if (fetch && route.path === "/Invoice/PurchaseInvoice") {
            isFetchInvoice.value = true;
        } else {
            isFetchInvoice.value = false;
        }
    }
);

let isLeavingFromInvoice = false;
onBeforeRouteLeave((to, from) => {
    isLeavingFromInvoice = from.path === "/Invoice/PurchaseInvoice" && from.query.fetch === "true";
});

onMounted(() => {
    const { fetch } = route.query;
    if (fetch) {
        isFetchInvoice.value = true;
    }
});
</script>
