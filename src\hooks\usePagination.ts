import { reactive } from "vue"
import { getLocalStorage, setLocalStorage } from "@/utils/localStorageOperate"

interface IDefaultPaginationData {
  total: number
  currentPage: number
  pageSizes: number[]
  pageSize: number
  layout: string
  refreshFlag?: boolean
}

export interface IPaginationData {
  total?: number
  currentPage?: number
  pageSizes?: number[]
  pageSize?: number
  layout?: string
  refreshFlag?: boolean
}

/** 默认的分页参数 */
const defaultPaginationData: IDefaultPaginationData = {
  total: 0,
  currentPage: 1,
  pageSizes: [50, 200, 500, 1000],
  pageSize: 50,
  layout: "total, sizes, prev, pager, next, jumper",
  refreshFlag: false,
}

//pageTabFlag为true则表示此页下此tab为大的页码数
//subMenu页面子表格的名
export const usePagination = (subMenu = "", pageTabFlag = false, _paginationData: IPaginationData = {}) => {
  // TODO /** 获取当前路由的名称 */

  /** 合并分页参数 */
  const paginationData = reactive(Object.assign({ ...defaultPaginationData }, _paginationData))

  // 从storage中获取当前表格的pageSize
  const getDefaultSize = (val: number) => {
    const size = getLocalStorage(subMenu + "Size")
    if (size) {
      paginationData.pageSize = paginationData.pageSizes.includes(parseInt(size)) ? parseInt(size) : val
    } else {
      paginationData.pageSize = val
    }
  }

  /** 改变当前页码 */
  const handleCurrentChange = (value: any) => {
    paginationData.currentPage = value.pageNum
    document.body.scrollTo(0, 0)
  }

  /** 改变页面大小 */
  const handleSizeChange = (value: any) => {
    paginationData.pageSize = value.num
    paginationData.currentPage = value.pageNum
    setLocalStorage(`${subMenu}Size`, value.num.toString())
    document.body.scrollTo(0, 0)
  }

  const handleRefresh = () => {
    paginationData.refreshFlag = !paginationData.refreshFlag
    document.body.scrollTo(0, 0)
  }

  return { paginationData, handleCurrentChange, handleSizeChange, handleRefresh, getDefaultSize }
}
