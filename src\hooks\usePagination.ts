import { reactive } from "vue";
import { getLocalStorage, setLocalStorage } from "@/util/localStorageOperate";
import router from "@/router";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";

interface IDefaultPaginationData {
    total: number;
    currentPage: number;
    pageSizes: number[];
    pageSize: number;
    layout: string;
    refreshFlag?: boolean;
}

export interface IPaginationData {
    total?: number;
    currentPage?: number;
    pageSizes?: number[];
    pageSize?: number;
    layout?: string;
    refreshFlag?: boolean;
}

/** 默认的分页参数 */
const defaultPaginationData: IDefaultPaginationData = {
    total: 0,
    currentPage: 1,
    pageSizes: [50, 200, 500, 1000],
    pageSize: 50,
    layout: "total, sizes, prev, pager, next, jumper",
    refreshFlag: false,
};
const storageSizePage = [
    "AASubsidiaryLedger",
    "SubsidiaryLedger",
    "GeneralLedger",
    "TrialBalance",
    "CategorizedAccountsSummary",
    "Journal",
    "AATrialBalance",
    "MultiColumnLedger",
    "AssistCombineStatement",
    "AgeAnalysis",
    "ScmVoucher",
    "CDCheck",
    "CDCheckDetail",
    "IEType",
    "Report",
    "CDAccount",
    "CashierInvoiceTable",
    "CashierInvoiceInfo",
    "RiskAnaly",
    "SalaryManage",
    "EmployInfo",
    "SalarySummary",
    "DepreciationLedger",
    "FASummary",
    "FixedAssetType",
    "CustomStatement",
    "AssistingAccounting",
    "VoucherTemplate",
    "OperationLog",
    "VoucherList"
];
//无需生成凭证页面
const storageSizePageNoVoucher = [
    "ERecord",
    "CashJournal",
    "DepositJournal",
    "Transfer",
    "Draft",
    "ExpenseBill",
    "SalesInvoice",
    "PurchaseInvoice",
    "ExpenseBill",
    "FixedAssets",
    "ScmVoucher",
    "BusinessVoucher"
]

//pageTabFlag为true则表示此页下此tab为大的页码数
//subMenu页面子表格的名
export const usePagination = (subMenu = "", pageTabFlag = false, _paginationData: IPaginationData = {}) => {
    const currentRoute = useRouterArrayStoreHook().routerArray.find((item) => item.alive);
    const currentPage: string = String(currentRoute?.name ?? "");

    /** 合并分页参数 */
    const paginationData = reactive(Object.assign({ ...defaultPaginationData }, _paginationData));
    const getDefaultSize = (val: number) => {
        const size = getLocalStorage(currentPage + subMenu + "Size");
        if (size) {
            paginationData.pageSize = paginationData.pageSizes.includes(parseInt(size)) ? parseInt(size) : val
        } else {
            paginationData.pageSize = val;
        }
    }
    if (storageSizePageNoVoucher.includes(currentPage) && !pageTabFlag) {
        paginationData.pageSizes = [20, 50, 100, 200, 300];
        getDefaultSize(20);
    } else {
        getDefaultSize(50);
    }
    
    /** 改变当前页码 */
    const handleCurrentChange = (value: any) => {
        paginationData.currentPage = value.pageNum;
        document.body.scrollTo(0,0);

    };

    /** 改变页面大小 */
    const handleSizeChange = (value: any) => {
        paginationData.pageSize = value.num;
        paginationData.currentPage = value.pageNum;
        setLocalStorage(`${currentPage}${subMenu}Size`, value.num.toString());
        document.body.scrollTo(0,0);
    };

    const handleRerefresh = () => {
        paginationData.refreshFlag = !paginationData.refreshFlag;
        document.body.scrollTo(0,0);

    };

    return { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh };
};
