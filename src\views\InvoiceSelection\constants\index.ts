import type { IColumnProps } from "@/components/Table/types"
// 发票类型选项
export const TYPE_LIST: SelectOption[] = [
  { label: "发票", value: "1" },
  { label: "海关缴款书", value: "2" },
  { label: "代扣代缴完税凭证", value: "3" },
]

// 勾选状态选项
export const STATUS_LIST: SelectOption[] = [
  { label: "未勾选", value: "0" },
  { label: "已勾选", value: "1" },
]

// 发票种类选项
export const INVOICE_TYPE_LIST: SelectOption[] = [
  { label: "增值税专用发票", value: "01" },
  { label: "数电票（增值税专用发票）", value: "81" },
  { label: "机动车销售统一发票", value: "03" },
  { label: "数电票（机动车销售统一发票）", value: "83" },
  { label: "道路通行费电子普通发票", value: "14" },
  { label: "数电票（通行费发票）", value: "84" },
  { label: "数电票（航空运输电子客票行程单）", value: "61" },
  { label: "数电票（铁路电子客票）", value: "51" },
  { label: "增值税电子普通发票", value: "09" },
  { label: "增值税普通发票", value: "10" },
  { label: "数电票（普通发票）", value: "11" },
]

// 发票状态选项
export const INVOICE_STATUS_LIST: SelectOption[] = [
  { label: "正常", value: "0" },
  { label: "作废", value: "2" },
  { label: "已红冲-部分", value: "7" },
  { label: "已红冲-全部", value: "8" },
]
//发票风险等级
export const INVOICE_RISK_LEVEL_LIST: SelectOption[] = [
  { label: "正常", value: "01" },
  { label: "疑点发票", value: "02" },
  { label: "异常凭证", value: "03" },
]
// 表格列配置
export const TABLE_COLUMNS: Array<IColumnProps> = [
  {
    slot: "selection",
    selectable: (row) =>
      ["正常", "已红冲-部分"].includes(row.invoiceStatus) &&
      row.isRedLock === "否" &&
      row.nonDeductible === "否" &&
      ["正常", "疑点发票"].includes(row.invoiceRiskLevel),
  },
  { label: "发票号码", prop: "invNum", formatter: (row) => row.invNum || row.electricInvoice },
  { label: "发票代码", prop: "invCode" },
  { label: "不抵扣原因", prop: "nonDeductibleReason", slot: "nonDeductibleReason" },
  { label: "开票日期", prop: "invDate" },
  { label: "金额", prop: "amount" },
  { label: "税额", prop: "tax" },
  { label: "有效抵扣税额", prop: "deductionTax" },
  { label: "价税合计", prop: "amountTax" },
  { label: "发票种类", prop: "invType" },
  { label: "销方名称", prop: "salesName" },
  { label: "红字锁定标识", prop: "isRedLock" },
  { label: "发票状态", prop: "invoiceStatus" },
  { label: "不得抵扣及退税发票", prop: "nonDeductible" },
  { label: "勾选状态", prop: "selectedStatus" },
  { label: "发票风险等级", prop: "invoiceRiskLevel" },
  { slot: "operation" },
]

export const SELECT_COLUMNS: Array<IColumnProps> = [
  {
    slot: "selection",
  },
  { label: "发票号码", prop: "invNum" },
  { label: "开票日期", prop: "invDate" },
  { label: "发票种类", prop: "invType" },
  { label: "销方名称", prop: "salesName" },
  { label: "金额", prop: "amount" },
  { label: "税额", prop: "tax" },
  { label: "有效抵扣税额", prop: "deductionTax" },
  { label: "价税合计", prop: "amountTax" },
]

export const COLLECT_COLUMNS: Array<IColumnProps> = [
  { label: "任务名称", prop: "taskName" },
  { label: "发票日期/税款所属期", prop: "invoiceDate" },
  { label: "任务状态", prop: "taskStatusName" },
  { label: "执行情况", prop: "salesName" },
  { label: "提交时间", prop: "createdTime" },
  { label: "操作人", prop: "createdByName" },
]

export const NONDEDUCTION_REASON_LIST: SelectOption[] = [
  { label: "用于非应税项目", value: "1" },
  { label: "用于免税项目", value: "2" },
  { label: "用于集体福利或者个人消费", value: "3" },
  { label: "非正常损失的", value: "4" },
  { label: "其他", value: "5" },
]

export const DEDUCTION_TABS = [
  {
    label: "待处理农产品",
    name: "unprocAgriProd",
  },
  {
    label: "抵扣勾选",
    name: "deducSelect",
    component: "Selection",
  },
  {
    label: "统计确认",
    name: "statConfirm",
    component: "StatConfirm",
  },
]

export const NONDEDUCTION_TABS = [
  {
    label: "不抵扣勾选",
    name: "nonDeducSelect",
    component: "Selection",
  },
]
