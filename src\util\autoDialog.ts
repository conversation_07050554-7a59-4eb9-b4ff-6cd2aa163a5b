import NewProUserDialog from "@/components/NewProUserDialog/index.vue";
import NewFunctionDialog from "@/components/NewFunctionDialog/index.vue";
import ReNewProDialog from "@/components/ReNewProDialog/index.vue";
import NewUserDialog from "@/components/NewUserDialog/index.vue";
import AdvertisementDialog from "@/components/AdvertisementDialog/index.vue";
import ElementPlus from "element-plus";
import { getCookie, setCookie } from "./cookie";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
import { useTrialStatusStoreHook } from "@/store/modules/trialStatus";
import { createApp } from "vue";
import { request, type IResponseModel } from "./service";
import { isInWxWork } from "./wxwork";
import { getGlobalToken } from "./baseInfo";
import { tryShowPayDialog } from "./proPayDialog";
import { getServiceId } from "./proUtils";
import { ElNotify } from "./notify";
import { isLemonClient } from "./lmClient";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { newFunctionVersion, newFunctionFn, newFunctionHref } from "@/components/NewFunctionDialog/utils";
import dayjs from "dayjs";

const isClient = isLemonClient();
const tryShowNewProUserDialog = (): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if (!window.isProSystem && !window.isErp && !useThirdPartInfoStoreHook().isThirdPart && !window.isAccountingAgent && !isClient) {
            const accountsetStore = useAccountSetStoreHook();
            const key = "newProUserDialog-" + accountsetStore.userInfo?.userSn;
            if (getCookie(key) == "1") {
                resolve(false);
            } else {
                request({
                    url:
                        window.eHost +
                        "/wb/" +
                        (isInWxWork() ? "wecom/" : "") +
                        "redirect/info?type=trial&productType=" +
                        window.productType,
                    method: "get",
                })
                    .then((result: any) => {
                        if (result.statusCode && result.data.serviceId != 0) {
                            const capp = createApp(NewProUserDialog);
                            const container = document.createElement("div");
                            capp.use(ElementPlus);
                            capp.mount(container);
                            document.body.insertBefore(container, document.body.firstChild);
                            setCookie(key, "1", "d365");
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    })
                    .catch(() => {
                        resolve(false);
                    });
            }
        } else {
            resolve(false);
        }
    });
};
const tryShowNewFunctionDialog = async (): Promise<boolean> => {
    const accountsetStore = useAccountSetStoreHook();
    const versionKey = "newFunctionKey-" + accountsetStore.userInfo?.userSn;
    const storedJsonStr = localStorage.getItem(versionKey);
    if (storedJsonStr !== newFunctionVersion) {
        await newFunctionFn();
    }
    return new Promise<boolean>((resolve) => {
        if (!window.isErp && !useThirdPartInfoStoreHook().isThirdPart && !window.isAccountingAgent && !isClient) {
            if (!newFunctionHref) {
                resolve(false);
                return;
            }

            const capp = createApp(NewFunctionDialog);
            const container = document.createElement("div");
            capp.use(ElementPlus);
            capp.mount(container);
            document.body.insertBefore(container, document.body.firstChild);
            localStorage.setItem(versionKey, newFunctionVersion);
            resolve(true);
        } else {
            resolve(false);
        }
    });
};

const tryShowUserScanDialog = (): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if (!window.isProSystem && !window.isErp && !window.isAccountingAgent) {
            const accountsetStore = useAccountSetStoreHook();
            request({
                url: "/api/PermissionsOnlyAuth/GetAccountSetUserNumber?asId=" + accountsetStore.accountSet?.asId,
                method: "post",
            }).then((res: IResponseModel<number>) => {
                if (res.data > 1) {
                    const key = "permission-" + accountsetStore.userInfo?.userSn + "-" + getGlobalToken() + "pay-dialog-oncce";
                    if (!tryShowPayDialog(1, "permission", "开通专业版，立即解锁多用户协同。更多专业版功能如下：", "用户限制")) {
                        const trialStatusStore = useTrialStatusStoreHook();
                        if (trialStatusStore.isExpired && !getCookie(key)) {
                            document.cookie = key + "=1;path=/";
                            tryShowPayDialog(1, "", "开通专业版，立即解锁多用户协同。更多专业版功能如下：", "用户限制");
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    } else {
                        document.cookie = key + "=1;path=/";
                        resolve(true);
                    }
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(false);
        }
    });
};

const tryShowReNewProDialog = (): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if (window.isProSystem) {
            const accountsetStore = useAccountSetStoreHook();
            const proExpiredData = useTrialStatusStoreHook().proExpiredData;
            if (proExpiredData.needRemind) {
                const capp = createApp(ReNewProDialog, {
                    remainingDays: proExpiredData.remainingDays,
                    expiredTime: dayjs(proExpiredData.expiredTime).format("YYYY-MM-DD"),
                });
                const container = document.createElement("div");
                capp.use(ElementPlus);
                capp.mount(container);
                document.body.insertBefore(container, document.body.firstChild);
                //这块用来设置弹窗时间
                request({
                    url:
                        window.eHost +
                        "/wb/reminder/pushNewPromptMessage?userSn=" +
                        accountsetStore.userInfo?.userSn +
                        "&serviceId=" +
                        getServiceId() +
                        "&channelId=1",
                    method: "get",
                }).then((res: any) => {
                    if (res.statusCode != 200) {
                        ElNotify({ message: "设置续费弹窗失败", type: "error" });
                    }
                });
                resolve(true);
            } else {
                resolve(false);
            }
            if (proExpiredData.remainingDays <= 30) {
                window.postMessage({ type: "proRemainingDays", proRemainingDays: proExpiredData.remainingDays }, window.location.origin);
            }
        }else{
            resolve(false);
        }
    });
};

//新用户见面礼-去掉
const tryShowNewUserDialog = (): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if (!window.isProSystem && !window.isErp && !useThirdPartInfoStoreHook().isThirdPart) {
            const accountsetStore = useAccountSetStoreHook();
            if (getCookie("isWatchedNewUserDialog" + accountsetStore.userInfo?.userSn) == "true") {
                if (getCookie("NewUserDialogDate" + accountsetStore.userInfo?.userSn) != new Date().toLocaleDateString()) {
                    resolve(false);
                } else {
                    resolve(true);
                }
            }
            request({
                url: "/api/User/NeedNewUserDialog",
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000 && res.data) {
                    request({
                        url: "/api/User/SetNewUserDialogState",
                        method: "post",
                    }).then((res: IResponseModel<boolean>) => {
                        if (res.state === 1000 && res.data) {
                            const capp = createApp(NewUserDialog);
                            const container = document.createElement("div");
                            capp.use(ElementPlus);
                            capp.mount(container);
                            document.body.insertBefore(container, document.body.firstChild);
                            setCookie("NewUserDialogDate" + accountsetStore.userInfo?.userSn, new Date().toLocaleDateString(), "d1000");
                            resolve(true);
                        } else {
                            resolve(false);
                        }
                    });
                } else {
                    setCookie("isWatchedNewUserDialog" + accountsetStore.userInfo?.userSn, "true", "d1000");
                    if (getCookie("NewUserDialogDate" + accountsetStore.userInfo?.userSn) != new Date().toLocaleDateString()) {
                        resolve(false);
                    } else {
                        resolve(true);
                    }
                }
            });
        } else {
            resolve(false);
        }
    });
};

const tryShowAdvertisement = (): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
        if (
            !window.isErp &&
            !window.isProSystem &&
            getCookie("pcAdvertisementShowed") != new Date().getDate().toString() &&
            !useThirdPartInfoStoreHook().isThirdPart
        ) {
            setCookie("pcAdvertisementShowed", new Date().getDate().toString(), "d1");
            request({
                url: "/api/Advertisement",
                params: { typeId: 2020 },
                method: "get",
            }).then((res: IResponseModel<{ adId: number; title: string; adUrl: string; adLink: string }>) => {
                if (res.state === 1000 && res.data.adId != 0) {
                    const capp = createApp(AdvertisementDialog, {
                        adId: res.data.adId,
                        adUrl: res.data.adUrl,
                        adLink: res.data.adLink,
                    });
                    const container = document.createElement("div");
                    capp.use(ElementPlus);
                    capp.mount(container);
                    document.body.insertBefore(container, document.body.firstChild);
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        } else {
            resolve(false);
        }
    });
};

export const tryShowAutoDialog = () => {
    const dialogPromiseArray: Array<() => Promise<boolean>> = [
        tryShowNewProUserDialog,
        tryShowNewFunctionDialog,
        tryShowUserScanDialog,
        tryShowReNewProDialog,
        tryShowAdvertisement,
    ];
    let index = 0;
    const tryOne = () => {
        if (!dialogPromiseArray[index]) return;
        dialogPromiseArray[index]().then((r) => {
            if (!r) {
                index++;
                tryOne();
            }
        });
    };
    tryOne();
};
