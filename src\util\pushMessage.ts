import { request, type IResponseModel } from "@/util/service";
import dayjs from "dayjs";
import { useAccountSetStoreHook } from "@/store/modules/accountSet";
const accountsetStore = useAccountSetStoreHook();
export function pushLoginMessage() {
    const sendLoginMessageFlag = localStorage.getItem("sendLoginMessage-" + accountsetStore.userInfo?.userSn);
    if (sendLoginMessageFlag !== dayjs().format("YYYY-MM-DD")) {
        request({
            url: "/api/AppMessage/PushLoginMessage",
            method: "post",
        }).then((res: IResponseModel<boolean>) => {
            if (res.data) {
                localStorage.setItem("sendLoginMessage-" + accountsetStore.userInfo?.userSn, dayjs().format("YYYY-MM-DD"));
            }
        });
    }
}
