<template>
    <div class="main-top main-tool-bar space-between">
        <div class="main-tool-left">
            <el-date-picker v-model="startDate" type="date" style="width: 132px" :teleported="false" value-format="YYYY-MM-DD" />
            <span class="ml-10 mr-10 float-l">至</span>
            <el-date-picker v-model="endDate" type="date" style="width: 132px" :teleported="false" value-format="YYYY-MM-DD" />
            <a class="button solid-button float-l ml-10" @click="getBillAnalysisData">查询</a>
        </div>
        <div class="main-tool-right">
            <RefreshButton></RefreshButton>
        </div>
    </div>
    <div class="main-center">
        <Table 
            :data="tableData" 
            :columns="columns" 
            :loading="loading" 
            :scrollbarShow="true"
            :tableName="setModule"
        ></Table>
    </div>
</template>

<script setup lang="ts">
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import { firstDayOfMonth, lastDayOfMonth } from "../utils";
import { getUrlSearchParams } from "@/util/url";
import { request } from "@/util/service";
import { ref, watch, onMounted } from "vue";
import { ElNotify } from "@/util/notify";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "ScmVoucherMain";
const props = defineProps({
    scmAsid: {
        type: Number,
        default: 0,
    },
    scmProductType: {
        type: Number,
        default: 0,
    },
});

const startDate = ref(firstDayOfMonth());
const endDate = ref(lastDayOfMonth());
const loading = ref(false);
const columns = ref<Array<IColumnProps>>([
    { label: "单据类型", prop: "billRootTypeText", align: "left", headerAlign: "left", minWidth: 268, width: getColumnWidth(setModule, "billRootTypeText") },
    { label: "进销存单据数", prop: "billCount", align: "left", headerAlign: "left", minWidth: 270, width: getColumnWidth(setModule, "billCount") },
    { label: "关联凭证数", prop: "genVoucherCount", align: "left", headerAlign: "left", minWidth: 270, width: getColumnWidth(setModule, "genVoucherCount") },
    { label: "未生成凭证", prop: "unGenVoucherCount", align: "left", headerAlign: "left", minWidth: 270, resizable: false },
]);

const tableData = ref([]);

function getBillAnalysisData() {
    if (startDate.value === "" || startDate.value === null) {
        ElNotify({
            type: "warning",
            message: "查询起始日期不能为空",
        });
        return false;
    }
    if (endDate.value === "" || endDate.value === null) {
        ElNotify({
            type: "warning",
            message: "查询结束日期不能为空",
        });
        return false;
    }
    if (startDate.value > endDate.value) {
        ElNotify({
            type: "warning",
            message: "查询起始日期不能大于结束日期",
        });
        return false;
    }
    loading.value = true;
    const data = {
        startDate: startDate.value,
        endDate: endDate.value,
        scmProductType: props.scmProductType,
        scmAsid: props.scmAsid,
    };
    request({
        url: `/api/BillVoucherAnalysis/List?` + getUrlSearchParams(data),
        // headers: {
        //     scmProductType: props.scmProductType,
        //     scmAsid: props.scmAsid,
        // },
    }).then((res: any) => {
        if (res.state === 1000) {
            tableData.value = res.data;
        }
        loading.value = false;
    });
}

onMounted(() => {
    getBillAnalysisData();
});

defineExpose({
    getBillAnalysisData,
});
</script>

<style scoped lang="less">
.calendarInput {
    width: 130px;
}
:deep(.el-date-editor) {
    & .el-input__prefix {
        position: absolute;
        right: 0;
    }
    & .el-input__suffix-inner {
        position: absolute;
        right: 30px;
        top: 9px;
    }
}
</style>
