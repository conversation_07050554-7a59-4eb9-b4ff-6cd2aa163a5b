<template>
    <div class="carry-over-boxs">
        <div class="stepTitle" :class="dealWithType === 'noNeedDoNothing' ? 'no-data' : ''">
            <span>{{ asTitle }}</span>
        </div>
        <div class="tip" v-show="dealWithType === 'noNeedDoNothing'">
            <span>亲！本期不需要结转{{ asTheme }}。</span>
        </div>
        <div class="check-voucher-head" v-show="dealWithType === 'checkOutIncomeList'">
            <div class="check-voucher-title" style="width: 226px">摘要</div>
            <div class="check-voucher-title" style="width: 446px">科目</div>
            <div class="check-voucher-title" style="width: 104px; text-align: right">借方金额</div>
            <div class="check-voucher-title" style="width: 104px; text-align: right">贷方金额</div>
        </div>
        <div
            class="check-voucher-list"
            v-show="dealWithType === 'checkOutIncomeList'"
            v-for="(listTotalItem, listTotalIndex) in allData"
            :key="listTotalIndex"
        >
            <div
                class="voucher-list-item"
                v-for="(listItem, index) in listTotalItem.vouchers"
                :key="index"
                @dblclick="editCarryOver(listItem.vid)"
            >
                <div class="voucher-list-head">
                    <span class="voucher-date float-l">{{ "日期：" + listItem.vdate.split("T")[0] }}</span>
                    <span class="voucher-num float-l ml-20">{{ "凭证字号：" + listItem.vgname + "-" + listItem.vnum }}</span>
                    <a class="link hover-display float-r ml-20" @click="() => deleteCArryOver(listItem.vid, listItem.pid, listTotalIndex)"
                        >删除</a
                    >
                    <a class="link hover-display float-r ml-20" @click="() => editCarryOver(listItem.vid)">修改</a>
                </div>
                <div class="voucher-list-lines">
                    <div class="voucher-list-line" v-for="item in listItem.voucherLines" :key="item.veid">
                        <div style="width: 226px">{{ resetDescription(item.description) }}</div>
                        <div style="width: 444px">{{ item.asubname }}</div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(item.debit) }}</div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(item.credit) }}</div>
                    </div>
                    <div class="voucher-list-line">
                        <div style="width: 226px">合计</div>
                        <div style="width: 444px">
                            {{ digitUppercase(getTotalMoney(listItem.voucherLines, "debit")) }}
                        </div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(getTotalMoney(listItem.voucherLines, "debit")) }}</div>
                        <div style="width: 104px; text-align: right">{{ formatMoney(getTotalMoney(listItem.voucherLines, "credit")) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="check-box-tool-bar">
            <a class="button mr-10" @click="carryOverLast">上一步</a>
            <a class="button solid-button mr-10" v-show="dealWithType === 'checkOutIncomeList'" @click="newVoucher">新增凭证</a>
            <a class="solid-button" @click="carryOverNext">下一步</a>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { formatMoney } from "@/util/format";
import { getTotalMoney, resetDescription, getVoucherIndex } from "../utils";
import { digitUppercase } from "@/util/format";

import type { ICheckResultItem } from "../tpyes";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";

const props = defineProps<{
    resetTotalMoney: Function;
}>();

const asTitle = computed(() => {
    const accountingStandard = useAccountSetStore().accountSet?.accountingStandard as number;
    if (accountingStandard === AccountStandard.FarmerCooperativeStandard) {
        return "第 2 步 结转盈余-结转未分配盈余";
    } else if (accountingStandard === AccountStandard.UnionStandard) {
        return "第 2 步 工会资金结转结余、财政拨款结转结余";
    } else if (accountingStandard === AccountStandard.VillageCollectiveEconomyStandard) {
        return "第 2 步 结转盈余-结转未分配收益";
    } else {
        return "第 2 步 结转损益-结转未分配利润";
    }
});

const asTheme = useAccountSetStore().accountSet?.accountingStandard === 4 ? "盈余" : "损益";
const allData = ref<ICheckResultItem[]>([]);
const dealWithType = ref<"checkOutIncomeList" | "noNeedDoNothing">("checkOutIncomeList");

const emit = defineEmits(["carryOverLast", "newVoucher", "carryOverNext", "editCarryOver", "deleteCarryOver"]);
const carryOverLast = () => {
    emit("carryOverLast", isNormalCome.value);
    isNormalCome.value = true;
};
const newVoucher = () => {
    const length = allData.value.length;
    const vgid = allData.value[length - 1]?.vouchers[allData.value[length - 1]?.vouchers.length - 1]?.vgid || 1010;
    emit("newVoucher", vgid);
};
const carryOverNext = () => emit("carryOverNext");
const editCarryOver = (VID: number) => emit("editCarryOver", VID);
const deleteCArryOver = (VID: number, pid: number, firseIndex: number, callback:() => void = () => {}) => {
    request({ url: "/api/Voucher?pId=" + pid + "&vId=" + VID }).then((res: any) => {
        if (res.state != 1000 || !res.data) {
            ElNotify({ type: "warning", message: "删除失败" });
            return;
        }
        if (res.data.approveStatus == 1) {
            ElNotify({ type: "warning", message: "亲！已审核凭证不能进行删除" });
            return;
        }
        ElConfirm("亲，确认要删除吗?").then((r: any) => {
            if (r) {
                request({ url: "/api/Voucher?pId=" + pid + "&vId=" + VID, method: "delete" }).then((res: any) => {
                    if (res.state != 1000 || !res.data) return;
                    props.resetTotalMoney();
                    ElNotify({ type: "success", message: "删除成功" });
                    allData.value[firseIndex].vouchers = allData.value[firseIndex].vouchers.filter((item) => item.vid != VID);
                    callback()
                });
            }
        });
    });
};
const getCarryOverIncomeVoucherIndex = (vid: number, pid: number) => {
    return getVoucherIndex(allData.value, vid, pid);
};
const isNormalCome = ref(true);
const setData = (loadingData: ICheckResultItem[]) => {
    allData.value = loadingData;
};
const setDealWithType = (type: "checkOutIncomeList" | "noNeedDoNothing", normal = true) => {
    dealWithType.value = type;
    isNormalCome.value = normal;
};

defineExpose({ setData, setDealWithType, deleteCArryOver, getCarryOverIncomeVoucherIndex });
</script>

<style lang="less" scoped>
.carry-over-boxs {
    box-sizing: border-box;
    width: 1000px;
    margin: 0 auto;
    > .tip {
        margin-top: 40px;
        padding-left: 130px;
        text-align: left;
        height: 288px;
        span {
            color: var(--font-color);
            font-size: var(--h2);
            line-height: 25px;
        }
    }
    .stepTitle {
        margin: 0px 20px;
        color: var(--font-color);
        font-size: 24px;
        line-height: 30px;
        text-align: left;
        padding: 20px 0 10px;
        &.no-data {
            padding-top: 60px;
            padding-left: 110px;
        }
    }
    .check-box-tool-bar {
        margin-top: 20px;
        text-align: center;
        margin-bottom: 40px;
    }
    .check-voucher-head {
        width: 980px;
        margin: 0 auto;
        background-color: var(--table-title-color);
        height: 37px;
        .check-voucher-title {
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 37px;
            font-weight: bold;
            float: left;
            padding-left: 20px;
            text-align: left;
        }
    }
    .check-voucher-list {
        margin-top: 10px;
        min-height: 321px;
        .voucher-list-item {
            box-sizing: border-box;
            width: 980px;
            margin: 10px auto 0;
            border: 1px solid #bcbcbc;
            &:hover {
                border-color: var(--main-color);
                & > .voucher-list-head {
                    & > a.hover-display {
                        display: inline-block;
                    }
                }
            }
            .voucher-list-head {
                color: #404040;
                font-size: 12px;
                height: 39px;
                position: relative;
                padding: 0 20px;
                & > span {
                    display: inline-block;
                    color: #7f7f7f;
                    font-size: var(--h5);
                    line-height: 17px;
                    margin-top: 11px;
                }
                .hover-display {
                    margin-top: 11px;
                    line-height: 17px;
                    display: none;
                    font-size: var(--h5);
                }
            }
            .voucher-list-lines {
                color: #404040;
                font-size: 12px;
                text-align: left;
                .voucher-list-line {
                    display: flex;
                    align-items: center;
                    color: var(--font-color);
                    font-size: var(--h5);
                    line-height: 17px;
                    padding: 8px 20px 7px 0px;
                    cursor: pointer;
                    border-top: 1px solid #f0f0f0;
                    &:first-child {
                        border-top-color: #bcbcbc;
                    }
                    & > div {
                        padding-left: 20px;
                        display: inline-block;
                        word-break: break-all;
                        vertical-align: top;
                    }
                    &:hover {
                        background-color: var(--table-hover-color);
                    }
                }
            }
        }
    }
}
</style>
