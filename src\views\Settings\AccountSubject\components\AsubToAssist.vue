<template>
    <div class="panel window" style="display: block;position: absolute; width: 428px; top: 100px; left: 507px; z-index: 929">
        <div class="panel-header panel-header-noborder window-header" style="width: 428px">
            <div class="panel-title" style="">
                <div class="pop-title">
                    明细科目转辅助核算
                    <div
                        class="pop-help-tip"
                        @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/handle?subMenuId=110109')"
                        v-show="!isHideBarcode"
                    >
                        <img src="@/assets/Settings/question.png" />
                        <div class="pop-help-tip-expend">
                            <a class="pop-help-tip-content">点击查看帮助</a>
                            <div class="pop-help-tip-icon"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-tool">
                <a class="panel-tool-close" @click="closeAsubToAssist"></a></div>
        </div>
        <div id="asubToAssist" title="" style="width: 428px" class="panel-body panel-body-noborder window-body">
            <div class="asub-to-assist-main">
                <div class="asub-to-assist-info">
                    请选择需要转换的科目：(1.只能选非末级科目；2.该科目下的所有明细科目将全部转为辅助核算)
                </div>
                <div class="asub-to-assist-select">
                    科目：
                    <div class="jqtransform jqtransformdone">
                        <select
                            id="selAsubToAssistAsub"
                            style="width: 280px"
                            v-model="asubNum"
                            tabindex="-1"
                            class="select2-hidden-accessible"
                            aria-hidden="true"
                        >
                        <option v-for="item in props.asubList" value="item.value" :key="(item as IList).value" >{{ (item as IList).text }}</option>
                            </select>
                    </div>
                </div>
                <div class="asub-to-assist-info">请选择该科目需要设置的辅助核算：</div>
                <div class="asub-to-assist-select">
                    辅助核算：
                    <div class="jqtransform jqtransformdone">
                        <select
                            id="selAsubToAssistAssist"
                            style="width: 280px"
                            v-model="assistNum"
                            tabindex="-1"
                            class="select2-hidden-accessible"
                            aria-hidden="true">
                        <option v-for="item in props.assistList" value="item.value" :key="(item as IList).value" >{{ (item as IList).text }}</option>
                        </select>
                    </div>
                </div>
                <div class="asub-to-assist-tip">
                    <img src="@/assets/Settings/tips.png" />科目的下级含有数量、外币、辅助核算时，不支持转换哦~
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button mr-20" @click="checkAsubToAssist">确定</a>
                <a class="button" @click="closeAsubToAssist">取消</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { globalWindowOpen } from "@/util/url";
import {ref} from "vue";

interface  IList{
    value:string,
    text:string,
}
const props = defineProps({
    asubList:{
        type:Array<IList>,
        default:()=>[],
    },
    assistList:{
        type:Array<IList>,
        default:()=>[],
    },

})
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const asubNum = ref(props.asubList[0])
const assistNum = ref(props.assistList[0])

const emits = defineEmits(['closeAsubToAssist','checkAsubToAssist'])
function closeAsubToAssist(){
    emits('closeAsubToAssist')
}
function checkAsubToAssist(){
    emits('checkAsubToAssist',asubNum.value)
}
</script>

<style scoped lang="less">
.window {
    background: var(--white);
    padding: 0;
    border: 1px solid var(--border-color);
    box-shadow: 0px 2px 2px 0px rgba(173, 187, 200, 0.19);
    overflow: visible;
    & .window-header {
    height: 52px;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding: 0;
    & .panel-title {
    color: var(--font-color);
    font-size: var(--h3);
    line-height: 52px;
    text-align: center;
    height: 52px;
    & .pop-title {
    display: flex;
    justify-content: center;
    font-weight: bold;
    & .pop-help-tip {
    cursor: pointer;
    position: relative;
    margin-left: 3px;
    & img {
    width: 16px;
    // line-height: 30;
}
    & .pop-help-tip-expend {
    position: absolute;
    left: -79.5px;
    top: -40px;
    padding-top: 6px;
    width: 105px;
    text-align: center;
    visibility: hidden;
    opacity: 0;
    transition: var(--transition-time);
    z-index: 10;
}


}
}
}
    & .panel-tool {
    position: absolute;
    top: 20px;
    bottom: 20px;
    right: 20px;
    margin: 0;
    height: 20px;
    width: 20px;
    &  .panel-tool-close {
    width: 12px;
    height: 12px;
    background: url(@/assets/Settings/close.png) no-repeat;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 1;
}
}
}
    & #asubToAssist {
    text-align: center;
    & .asub-to-assist-main {
    text-align: left;
    display: inline-block;
    padding: 20px 30px;
    color: var(--font-color);
    font-size: var(--font-size);
    line-height: var(--line-height);
    & .asub-to-assist-info {
    font-size: 15px;
    margin-bottom: 10px;
}
    & .asub-to-assist-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 15px;
    margin-bottom: 10px;
}
   & .asub-to-assist-tip {
    font-size: var(--h5);
    color: #AAAAAA;
    display: flex;
    align-items: center;
    & img {
    width: 14px;
    margin-right: 5px;
}
}
}
}
    & .buttons {
    padding: 10px 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
}
}
</style>
