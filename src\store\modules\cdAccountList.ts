import { ref } from "vue";
import { defineStore } from "pinia";
import { getGlobalToken } from "@/util/baseInfo";
import store from "@/store";
import { type IBankAccountItem, getCDAccountApi } from "@/views/Cashier/CDAccount/utils";

export const useCDAccountStore = defineStore("cdAccount", () => {
    const cdAccountList = ref<IBankAccountItem[]>([]);

    const getCDAccountAllList = () => {
        return new Promise<IBankAccountItem[]>((resolve, reject) => {
            const globalToken = getGlobalToken();
            if (globalToken === "") {
                reject("token为空");
            } else {
                Promise.all([getCDAccountApi(1010), getCDAccountApi(1020)])
                    .then((res: any) => {
                        if (res[0].state !== 1000 || res[1].state !== 1000) {
                            reject(res[0].msg || res[1].msg);
                        }
                        const data = [...res[0].data, ...res[1].data];
                        cdAccountList.value = [...res[0].data, ...res[1].data];
                        resolve(data);
                    })
                    .catch((error) => {
                        reject(error);
                    });
            }
        });
    };

    return { cdAccountList, getCDAccountAllList };
});

/** 在setup外使用 */
export function useCDAccountStoreHook() {
    return useCDAccountStore(store);
}
