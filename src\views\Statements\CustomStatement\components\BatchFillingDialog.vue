<template>
    <el-dialog v-model="fillCellsDialog" :title="`填充单元格-${selectedStartCell}`" destroy-on-close width="1142px" class="fill-cells-dialog custom-confirm">
        <div class="create-statement-content-container">
            <CreateTemplate
                from="filling"
                @fillCellsCancel="fillCellsDialog = false"
                :selectedStartCell="selectedStartCell"
                @goGenerate="fillCellsDialog = false"
            >
            </CreateTemplate>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import CreateTemplate from "./CreateTemplate.vue";
const emits = defineEmits(["update:modelValue"]);
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
    selectedStartCell: {
        type: String,
        default: "",
    },
});

const fillCellsDialog = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
</script>

<style lang="less" scoped>
.create-statement-content-container {
    :deep(.create-statement-content) {
        .create-statement-content-center {
            height: auto !important;
        }
    }
}
</style>
