<template>
    <div class="change-content" style="width: 1000px; padding: 0px; margin: 0 auto">
        <div class="title">{{ props.changeTitle }}</div>
        <div style="border: 1px solid var(--slot-title-color); margin: 32px 0 10px">
            <table class="change-tb">
                <tr>
                    <td colspan="4" class="tb-hr">基本信息</td>
                </tr>
                <tr>
                    <td class="tb-title" style="width: 174px; text-align: right">资产编号：</td>
                    <td id="ztxg_bh" class="tb-field">{{ props.changeData.fa_num }}</td>
                    <td class="tb-title" style="width: 84px">资产类别：</td>
                    <td id="ztxg_lb" class="tb-field">
                        <Tooltip :content="props.changeData.fa_type_name" :max-width="326">{{ props.changeData.fa_type_name }}</Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">资产名称：</td>
                    <td id="ztxg_mc" class="tb-field">
                        <Tooltip :content="props.changeData.fa_name" :max-width="326">{{ props.changeData.fa_name }} </Tooltip>
                    </td>
                    <td class="tb-title">资产型号：</td>
                    <td id="ztxg_xh" class="tb-field">
                        <Tooltip :content="props.changeData.fa_model" :max-width="326">{{ props.changeData.fa_model }} </Tooltip>
                    </td>
                </tr>
            </table>
            <div class="line"></div>
            <div class="change-content">
                <div class="tb-hr">变更内容</div>
                <div class="change-table">
                    <div class="change-title">调整前：</div>
                    <Table :data="beforeTable" :columns="beforeColumns" :tableName="setModule" @save-header-dragend="handleColumns">
                        <template #department_id>
                            <el-table-column
                                label="使用部门"
                                min-width="274"
                                align="center"
                                header-align="center"
                                prop="department_id"
                                :width="getColumnWidth(setModule, 'department_id')">
                                <template #default="scope">
                                    <Select v-model="scope.row.department_id" :filterable="true" :disabled="true">
                                        <ElOption v-for="item in departmentList" :key="item.id" :value="item.id" :label="item.name">
                                            <span>{{ item.name }}</span>
                                        </ElOption>
                                    </Select>
                                </template>
                            </el-table-column>
                        </template>
                        <template #ratio>
                            <el-table-column
                                label="分摊比例"
                                min-width="200"
                                align="center"
                                header-align="center"
                                prop="ratio"
                                :width="getColumnWidth(setModule, 'ratio')">
                                <template #default="scope">
                                    <div class="cell-flex">
                                        <el-input style="width: 86%" v-model="scope.row.ratio" :disabled="true" />
                                        <span style="padding-left: 2px">%</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #asub>
                            <el-table-column
                                :label="props.changeData.fa_property === 0 ? '折旧费用科目' : '摊销费用科目'"
                                min-width="272"
                                align="center"
                                header-align="center"
                                prop="asub_id"
                                :resizable="false">
                                <template #default="scope">
                                    <SelectV2
                                        :class="'visibleSelect' + scope.$index"
                                        :options="costAsubAmList"
                                        :disabled="true"
                                        :placeholder="' '"
                                        v-model="scope.row.asub_id"
                                        @change="(value) => clickCostAsub(value, scope.$index)"
                                        :showMoreFilled="!!beforeTable[scope.$index].asub_aae"
                                        :iconClearRight="-24"
                                        @expandAssit="
                                            (label) =>
                                                 expandAssit(props.changeData.fa_property === 0 ? '折旧费用科目' : '摊销费用科目',label, beforeTable[scope.$index].asub_aae, beforeTable[scope.$index].asub_id)"
                                        >
                                    </SelectV2>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                    <div class="change-title">调整后：</div>
                    <Table
                        :data="afterTable"
                        :columns="afterColumns"
                        :show-overflow-tooltip="true"
                        :tooltip-options="{ effect: 'light', placement: 'top' }"
                        :hasAddSub="true"
                        :customSubShowIndex="0"
                        :tableName="setModule"
                        @save-header-dragend="handleColumns">
                        <template #department_id>
                            <el-table-column
                                label="使用部门"
                                min-width="274"
                                align="center"
                                header-align="center"
                                prop="department_id"
                                :width="getColumnWidth(setModule, 'department_id')">
                                <template #default="scope">
                                    <Select
                                        v-model="scope.row.department_id"
                                        :filterable="true"
                                        :fit-input-width="true"
                                        :filter-method="departFilterMethod">
                                        <ElOption v-for="item in showdepartmentList" :key="item.id" :value="item.id" :label="item.name">
                                            <span>{{ item.name }}</span>
                                        </ElOption>
                                    </Select>
                                </template>
                            </el-table-column>
                        </template>
                        <template #ratio>
                            <el-table-column
                                label="分摊比例"
                                min-width="200"
                                align="center"
                                header-align="center"
                                prop="ratio"
                                :show-overflow-tooltip="false"
                                :width="getColumnWidth(setModule, 'ratio')">
                                <template #default="scope">
                                    <div class="cell-flex">
                                        <el-input
                                            style="width: 86%"
                                            v-model="scope.row.ratio"
                                            @input="(val) => ratioInput(val, scope.$index)" />
                                        <span style="padding-left: 2px">%</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #asub>
                            <el-table-column
                                :label="props.changeData.fa_property === 0 ? '折旧费用科目' : '摊销费用科目'"
                                min-width="272"
                                align="center"
                                header-align="center"
                                prop="asub_id"
                                :resizable="false">
                                <template #default="scope">
                                    <SelectV2
                                        :class="'visibleSelect' + scope.$index"
                                        :options="costAsubAmList"
                                        :filterable="true"
                                        :placeholder="' '"
                                        v-model="scope.row.asub_id"
                                        @clickOption="(value) => clickCostAsub(value, scope.$index)">
                                    </SelectV2>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="buttons" style="margin-top: 22px; padding-bottom: 40px; text-align: center">
                <a class="button solid-button" style="margin: 0 5px" @click="savedata">保存</a>
                <a class="button" style="margin: 0 5px" @click="changeCancle">取消</a>
            </div>
        </div>
    </div>
    <AuxiliaryDialog
        v-model="auxiliaryDialogShow"
        :title="auxiliaryTitle"
        :asubName="auxiliaryAsubName"
        :aacode="auxiliaryCode"
        :departmentId="department"
        :aaAllowNull="auxiliaryAllowNull"
        :aaReadonly="auxiliaryReadonly"
        :aaId="auxiliaryId"
        @setAsubWithAAE="setAsubWithAAE"></AuxiliaryDialog>
</template>

<script setup lang="ts">
import Tooltip from "@/components/Tooltip/index.vue";
import Table from "@/components/Table/index.vue";
import Select from "@/components/Select/index.vue";
import SelectV2 from "@/components/AsubSelect/index.vue";
import ElOption from "@/components/Option/index.vue";
import AuxiliaryDialog from "./AuxiliaryDialog.vue";
import type { IDepartment, ISelectItem, IAccountSubjectList } from "../types";
import type { Option } from "@/components/SelectCheckbox/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IAccountSubjectModel } from "@/api/accountSubject.js";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { ref, watchEffect, toRef, computed, watch } from "vue";
import { getGlobalLodash } from "@/util/lodash";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const setModule = "AssetsDepartmentChange";
const _ = getGlobalLodash();

const props = defineProps({
    changeData: {
        type: Object,
        default: () => {
            return {};
        },
    },
    changeTitle: {
        type: String,
        default: "",
    },
    pid: {
        type: Number,
        default: 0,
    },
});
const emits = defineEmits(["changeCancle", "saveData"]);

const asubRef = ref<any>([]);
const accountSetStore = useAccountSetStore();
const accountingStandard = accountSetStore.accountSet!.accountingStandard as number;
const dataValue = ref();
const asubValue = ref("");
const department = computed(() => {
    return props.changeData.faAmortizations?.map((item: any) => item.department_id);
});
const beforeTable = computed(() => {
    if (props.changeData.faAmortizations) {
        return _.cloneDeep(
            props.changeData.faAmortizations.map((item: any) =>
                Object.assign({}, item, {
                    ratio: Number(item.ratio) == 100 ? 100 : _.round(item.ratio * 100, 2),
                    asub_id: item.asub_id + "",
                })
            )
        );
    }
    return [];
});
const afterTable = ref();
watch(
    () => props.changeData.faAmortizations,
    (val) => {
        afterTable.value = val?.map((item: any) =>
            Object.assign({}, item, { ratio: Number(item.ratio) == 100 ? 100 : _.round(item.ratio * 100, 2), asub_id: item.asub_id + "" })
        );
    },
    { immediate: true }
);
const beforeColumns = ref<Array<IColumnProps>>([]);
beforeColumns.value = calColumns();
const afterColumns = ref<Array<IColumnProps>>([]);
afterColumns.value = calColumns();
function calColumns() {
    return [{ slot: "department_id" }, { slot: "ratio" }, { slot: "asub" }];
}
function handleColumns() {
    beforeColumns.value = calColumns();
    afterColumns.value = calColumns();
}

function ratioInput(value: string, index: number) {
    let ratio = value.replace(".", "");
    const regex = /^(100|[1-9][0-9]?)$/;
    afterTable.value[index].ratio = ratio.match(regex) ? ratio.match(regex)![0] : "";
}

const auxiliaryDialogShow = ref(false);
const auxiliaryTitle = ref("");
const auxiliaryAsubName = ref("");
const auxiliaryCode = ref<string[]>([]);
const auxiliaryAllowNull = ref<string[]>([]);
const auxiliaryReadonly = ref(false);
const auxiliaryId = ref();
const costAsubIndex = ref(0);
const clickAsubId = ref();
function expandAssit(title: string, asubName: string, id: string, value: any) {
    auxiliaryDialogShow.value = true;
    auxiliaryTitle.value = title;
    auxiliaryAsubName.value = asubName;
    auxiliaryId.value = id.split(",");
    auxiliaryCode.value = id.split(",").map((item) => {
        if(item.includes("-")){
            return item.replace("-", "");
        }else{
            return useAssistingAccountingStore().getAssistingAccountingModel(Number(item))!.aatype + ""
        }
    });
    auxiliaryReadonly.value = true;
}
function clickCostAsub(costValue: string, index: number) {
    const asubInfo = costAsubList.value!.find((item) => item.asubId == costValue) as IAccountSubjectList;
    auxiliaryReadonly.value = false;
    clickAsubOption(
        [-1, 0].includes(props.changeData.fa_property) ? "折旧费用科目" : "摊销费用科目",
        asubInfo.asubName,
        asubInfo.aatypes,
        asubInfo.asubId,
        index,
        asubInfo.aatypesAllowNull
    );
}
function clickAsubOption(title: string, name: string, aaTypes: string, value: any, costIndex: number, aatypesAllowNull: string) {
    if (!aaTypes) {
        afterTable.value[costIndex].asub_aae = "";
        return;
    }
    auxiliaryId.value = [];
    clickAsubId.value = value;
    costAsubIndex.value = costIndex ? costIndex : 0;
    auxiliaryTitle.value = title;
    auxiliaryAsubName.value = name;
    auxiliaryCode.value = aaTypes.includes(",")
        ? aaTypes.split(",").map((item) => {
              return item.split("_")[0];
          })
        : [aaTypes.split("_")[0]];
    auxiliaryAllowNull.value = auxiliaryCode.value.map((item) => {
        return aatypesAllowNull?.split(",").includes(item) ? "1" : "0";
    });
    auxiliaryId.value = afterTable.value[costAsubIndex.value].asub_aae ? afterTable.value[costAsubIndex.value].asub_aae.split(",") : [];
    auxiliaryDialogShow.value = true;
}
function setAsubWithAAE(params: any) {
    afterTable.value[costAsubIndex.value].asub_aae = params.map((item: any) => item.id).join(",");
    afterTable.value[costAsubIndex.value].asub_id = clickAsubId.value;
}

const asubList = ref(new Array<{ asubType: number; asubList: Array<IAccountSubjectList> }>());
const costAsubList = ref<IAccountSubjectList[]>();
const costAsubAmList = ref<any[]>([]);

watchEffect(() => {
    const accountSubject = useAccountSubjectStore().accountSubjectList;
    asubList.value = [];
    asubList.value.push({
        asubType: 0,
        asubList: accountSubject
            .filter((asub) => asub.status === 0 && asub.isLeafNode)
            .map((asub) => ({ ...asub, asubId: asub.asubId + "" })),
    });
    let asubGroup: any = {};
    accountSubject.forEach((asub) => {
        if (!asubGroup[asub.asubType]) {
            asubGroup[asub.asubType] = new Array<IAccountSubjectList>();
            asubList.value.push({ asubType: asub.asubType, asubList: asubGroup[asub.asubType] });
        }
        if (asub.status === 0 && asub.isLeafNode) {
            asubGroup[asub.asubType].push(asub);
        }
    });
    let asubType;
    switch (accountingStandard) {
        case AccountStandard.FolkComapnyStandard:
            asubType = [1, 9];
            break;
        case AccountStandard.LittleCompanyStandard:
        case AccountStandard.CompanyStandard:
        case AccountStandard.FarmerCooperativeStandard:
        case AccountStandard.FarmerCooperativeStandard2023:
        case AccountStandard.VillageCollectiveEconomyStandard:
            asubType = [1, 5, 6];
            break;
        case AccountStandard.UnionStandard:
            asubType = [1, 7];
            break;
    }
    costAsubList.value = asubType?.reduce((prev: IAccountSubjectList[], item: number) => {
        let list = asubList.value.filter((i) => i.asubType === item)[0].asubList;
        if (item === 1) {
            if (props.changeData.fa_property === 0) {
                if (accountingStandard === 2 || accountingStandard === 1) {
                    list = list.filter((i) => !i.asubCode.startsWith("1602"));
                } else if (accountingStandard === 3) {
                    list = list.filter((i) => !i.asubCode.startsWith("1502"));
                } else if (accountingStandard === 4 || accountingStandard === 5) {
                    list = list.filter((i) => !i.asubCode.startsWith("152"));
                } else if (accountingStandard === 6) {
                    list = list.filter((i) => !i.asubCode.startsWith("163"));
                } else if (accountingStandard === 7) {
                    list = list.filter((i) => !i.asubCode.startsWith("152"));
                }
            } else if (props.changeData.fa_property === 1) {
                if (accountingStandard === 2 || accountingStandard === 1) {
                    list = list.filter((i) => !i.asubCode.startsWith("1702"));
                }
            }
        }
        list.forEach((item) => {
            costAsubAmList.value.push({ label: item.asubCode + " " + item.asubAAName, value: item.asubId.toString() });
        });
        prev.push(...list);
        return prev;
    }, []);
});

const departmentListStore = toRef(useAssistingAccountingStore(), "departmentList");
const departmentList = ref<Option[]>([]);
watchEffect(() => {
    departmentList.value = departmentListStore.value.reduce((prev: Option[], item: IDepartment) => {
        if (item.aaeid > 0 && item.aaname !== "未录入辅助核算") {
            prev.push({
                id: item.aaeid,
                name: item.aaname,
            });
        }
        return prev;
    }, []);
});

function savedata() {
    if (props.changeData.cost_asub === Number(asubValue.value) && props.changeData.department === Number(dataValue.value)) {
        ElNotify({
            type: "warning",
            message: "亲，您的数据没有变动，不能保存哦！",
        });
        return false;
    }
    let sumRadio = 0;
    let hasNegativeRatio = false;
    let hasEmptyDepartment = false;
    let hasEmptyAsub = false;
    let hasEmptyRatio = false;
    afterTable.value.forEach((item: any) => {
        if (Number(item.ratio) < 0) hasNegativeRatio = true;
        if (!item.department_id) hasEmptyDepartment = true;
        if (!item.asub_id) hasEmptyAsub = true;
        if (item.department_id && item.asub_id) {
            if (!item.ratio) hasEmptyRatio = true;
            sumRadio += Number(item.ratio);
        }
    });
    if (hasEmptyDepartment) {
        ElNotify({
            type: "warning",
            message: "部门不能为空，请重新分配哦~",
        });
        return;
    }
    if (hasEmptyAsub) {
        ElNotify({
            type: "warning",
            message: "费用科目不能为空，请重新分配哦~",
        });
        return;
    }
    if (hasNegativeRatio) {
        ElNotify({
            type: "warning",
            message: "费用分摊比例不能为负数，请重新分配哦~",
        });
        return;
    }
    if (hasEmptyRatio) {
        ElNotify({
            type: "warning",
            message: "费用分摊比例不能为空，请重新分配哦~",
        });
        return;
    }
    if (sumRadio !== 100) {
        ElNotify({
            type: "warning",
            message: "费用分摊比例之和需为100%，请重新分配哦~",
        });
        return;
    }
    const data = {
        faid: props.changeData.fa_id,
        pid: props.pid,
        vid: 0,
        FAAmortizations: afterTable.value.map((item: any) => Object.assign({}, item, { ratio: item.ratio / 100 })),
    };
    request({
        url: `/api/FAChange/DepartmentChange`,
        method: "post",
        data,
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            emits("changeCancle",true);
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存失败！",
            });
        }
    });
}
function changeCancle() {
    emits("changeCancle");
}

const showdepartmentList = ref<Array<Option>>([]);
watchEffect(() => {
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value));
});
function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value, "name");
}
</script>

<style scoped lang="less">
.tb-hr {
    padding: 20px 20px 15px 20px;
    color: var(--font-color);
    font-size: var(--h3);
    line-height: 22px;
    font-weight: bold;
    text-align: left;
}

.change-table {
    padding-left: 100px;
    padding-right: 150px;
    .change-title {
        color: #101010;
        font-size: 16px;
        text-align: left;
        padding: 6px 0px;
    }
    :deep(.more-filled) {
        background-color: var(--el-disabled-bg-color);
    }

    :deep(.el-table td.el-table__cell) {
        .cell.el-tooltip{
            height: 94%;
            position: relative;
            top: 1px;
        }
        .el-select-v2,.el-select,.cell-flex,.el-select-v2__wrapper {
            width: calc(100% - 2px);
        }
    }
    :deep(.custom-table tbody tr td .cell ) {
        padding: 0 6px 0 8px;
        input[type="text"] {
            border: none;

        }
    }
    :deep(.el-select-v2__placeholder) {
        text-align: left;
    }
    :deep(.el-select-v2__wrapper.is-disabled) {
        background-color: #f5f7fa;
        .el-select-v2__placeholder {
            color: var(--input-disabled-font-color)
        }
    }
}

.change-tb {
    & .tb-title {
        text-align: right;
        color: #333333;
        font-size: 14px;
        line-height: 20px;
    }

    & .tb-field {
        padding-top: 6px;
        padding-bottom: 6px;
        font-size: 14px;
        text-align: left;
    }
}

.el-select-dropdown__list {
    max-height: 200px;
}

.el-select-dropdown__item {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;

    :deep(span) {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

.el-select-dropdown__item.hover {
    background-color: var(--main-color) !important;
    color: #fff !important;
}
.cell-flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
