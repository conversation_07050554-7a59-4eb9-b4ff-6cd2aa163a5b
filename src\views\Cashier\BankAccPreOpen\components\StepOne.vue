<template>
    <div class="open-account">
        <div class="title">预约开户</div>
        <div class="open-main-content">
            <TopStep :stepNumber="1" />
            <div class="step-edit">
                <div class="block-title">公司信息</div>
                <div class="block-tip">
                    <img src="@/assets/Cashier/warn.png" />
                    <span>系统会自动同步账套公司信息，请您检查是否准确</span>
                </div>
                <div class="block-main">
                    <el-form :model="companyInfo" label-position="right" :rules="companyInfoRules">
                        <el-row class="isRow">
                            <el-col :span="12">
                                <el-form-item label="公司名称：" :required="true" prop="companyName" label-width="160px">
                                    <el-autocomplete
                                        ref="asNameRef"
                                        @blur="handleCompanyBlur"
                                        v-model="companyInfo.companyName"
                                        :prop="[
                                            {
                                                required: true,
                                                message: '亲，单位名称不能为空',
                                                trigger: ['blur', 'change'],
                                            },
                                        ]"
                                        :fetch-suggestions="querySearch"
                                        :trigger-on-focus="false"
                                        placeholder="请输入完整的单位名称"
                                        style="width: 238px"
                                        @select="selectName"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="统一社会信用代码：" :required="true" prop="socialCreditCode" label-width="137px">
                                    <el-input v-model="companyInfo.socialCreditCode" style="width: 240px"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">法人信息</div>
                <div class="block-main">
                    <el-form :model="companyInfo" label-position="right" :rules="companyInfoRules">
                        <el-row class="isRow">
                            <el-col :span="12">
                                <el-form-item label="法人姓名：" :required="true" prop="legalPersonName" label-width="160px">
                                    <el-input v-model="companyInfo.legalPersonName" style="width: 240px" @blur="handleNameBlur"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="法人手机号码：" :required="true" prop="legalPhone" label-width="109px">
                                    <el-input v-model="companyInfo.legalPhone" style="width: 240px"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
                <div class="line"></div>
                <div class="block-title">预约业务</div>
                <div class="block-main">
                    <el-form :model="companyInfo" label-position="right" label-width="190px">
                        <el-row class="isRow">
                            <el-form-item label="选择账户类型：" :required="true" prop="accountType">
                                <el-radio-group v-model="companyInfo.accountType" class="ml-4">
                                    <el-radio :label="1">已有基本户</el-radio>
                                    <el-radio :label="0">没有基本户</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </div>
            </div>
            <div class="buttons">
                <a class="button" @click="back">取消</a>
                <a class="button ml-28 solid-button" @click="toStepTwo">下一步</a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import TopStep from "./TopStep.vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getCompanyDetailApi, type ICompanyInfo } from "@/api/getCompanyList";
import { getCompanyList } from "@/util/getCompanyList";
import { checkLegalPersonName } from "../utils";
import { IBaseCompanyInfo } from "../types";

const emit = defineEmits<{
    (e: "back"): void;
    (e: "cancelPreOpen"): void;
    (e: "nextStep"): void;
}>();
//返回提示
const back = () => {
    companyInfo.value.legalPersonName = "";
    companyInfo.value.rightLegalPersonName = "";
    companyInfo.value.legalPhone = "";
    emit("cancelPreOpen");
};

const asNameRef = ref();
const queryParams = reactive({
    isFromDb: false,
    name: "",
    data: [] as ICompanyInfo[],
});
const querySearch = (queryString: string, cb: any) => {
    getCompanyList(1010, queryString, cb, queryParams);
};
function selectName(item: any) {
    companyInfo.value.companyName = item.value;
    companyInfo.value.socialCreditCode = item.creditCode;
    companyInfo.value.legalPersonName = companyInfo.value.rightLegalPersonName = item.legalPersonName;
    getCompanyDetailApi(1010, decodeURIComponent(item.value));
}

const companyInfo = ref<IBaseCompanyInfo>(new IBaseCompanyInfo());
function reset() {
    companyInfo.value = new IBaseCompanyInfo();
}
const handleCompanyBlur = (event: any) => {
    getCompanyDetailApi(1010, decodeURIComponent(event.target.value)).then((res: any) => {
        if (res.state === 1000) {
            companyInfo.value.rightLegalPersonName = res.data?.legalPersonName ?? "";
        }
    });
};
//隐藏了所有的校验规则
const companyInfoRules = ref({
    companyName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
    socialCreditCode: [{ required: true, message: "请输入统一社会信用代码", trigger: ["change"] }],
    legalPersonName: [{ required: true, message: "请输入法人姓名", trigger: "blur" }],
    legalPhone: [{ required: true, message: "请输入法人手机号", trigger: "blur" }],
});
const isCanToNext = ref(true);
const handleNameBlur = () => {
    //点击下一步失焦防止出现2条失败提示
    if (!checkLegalPersonName(companyInfo.value.legalPersonName, companyInfo.value.rightLegalPersonName)) {
        isCanToNext.value = false;
    }
    const timer = setTimeout(() => {
        isCanToNext.value = true;
        clearTimeout(timer);
    }, 200);
};
const toStepTwo = () => {
    if (!isCanToNext.value) return;
    if (!checkCompanyInfo()) return;
    if (!checkLegalPersonName(companyInfo.value.legalPersonName, companyInfo.value.rightLegalPersonName)) return;
    emit("nextStep");
};

const initStepOneBaseInfo = (data: any) => {
    //从暂存信息中获取数据，再请求一次正确的法人信息
    companyInfo.value.companyName = data.companyName ?? "";
    companyInfo.value.socialCreditCode = data.unifiedNumber ?? "";
    companyInfo.value.legalPersonName = data.legalName ?? "";
    companyInfo.value.legalPhone = data.legalMobile ?? "";
    companyInfo.value.accountType = data.accountType ?? 0;
    getCompanyDetailApi(1010, decodeURIComponent(companyInfo.value.companyName))
        .then((res: any) => {
            if (res.state === 1000) {
                companyInfo.value.rightLegalPersonName = res.data?.legalPersonName ?? "";
            }
        })
        .catch(() => {
            companyInfo.value.rightLegalPersonName = "";
        });
};

const getAccountData = () => {
    request({
        url: `/api/AccountSet/Info`,
        method: "get",
    }).then((res: any) => {
        if (res.state === 1000) {
            companyInfo.value.companyName = res.data.asName;
            companyInfo.value.socialCreditCode = res.data.unifiedNumber;
            const taxadId = res.data.taxadId;
            getCompanyDetailApi(1010, decodeURIComponent(companyInfo.value.companyName)).then((res: any) => {
                if (res.state === 1000) {
                    companyInfo.value.legalPersonName = companyInfo.value.rightLegalPersonName = res.data?.legalPersonName ?? "";
                }
            });
            //taxadId和获取开户省市id可能存在对应不上，存一下报税地区名字 自动带入开户省份
            if (taxadId !== 0) {
                request({ url: window.jtaxHost + "/api/TaxDeclare/GetAreaList", method: "post" }).then(
                    (res: IResponseModel<{ taxAreaId: number; taxAreaName: string }[]>) => {
                        if (res.state === 1000) {
                            const areaList = res.data;
                            companyInfo.value.taxProvinceName = areaList.find((item) => item.taxAreaId === taxadId)?.taxAreaName ?? "";
                            companyInfo.value.taxProvinceCode = taxadId;
                        }
                    }
                );
            }
        }
    });
};

const checkCompanyInfo = () => {
    if (companyInfo.value.companyName === "") {
        ElNotify({
            message: "请输入公司名称",
            type: "warning",
        });
        return false;
    }
    if (companyInfo.value.socialCreditCode === "") {
        ElNotify({
            message: "请输入统一社会信用代码",
            type: "warning",
        });
        return false;
    }
    if (companyInfo.value.legalPersonName === "") {
        ElNotify({
            message: "请输入法人姓名",
            type: "warning",
        });
        return false;
    }
    if (companyInfo.value.legalPhone === "" || !/^1\d{10}$/.test(companyInfo.value.legalPhone)) {
        ElNotify({
            message: "请输入正确的法人手机号码",
            type: "warning",
        });
        return false;
    }
    return true;
};

defineExpose({
    initStepOneBaseInfo,
    getAccountData,
    companyInfo,
    reset,
});
</script>
<style scoped lang="less">
@import "@/style/Cashier/BankAccPreOpen.less";
</style>
