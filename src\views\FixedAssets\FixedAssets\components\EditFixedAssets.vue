<template>
    <div class="edit-asset">
        <div class="title">编辑资产</div>
        <div class="divEdit">
            <div class="block-title">
                基本信息<span id="createtime">录入日期：{{ formatDate(createdDate) }}{{ addForm.faType }}</span>
            </div>
            <div class="block-main">
                <el-form label-position="left" :rules="rules" ref="formRefInfo">
                    <el-row class="isRow">
                        <el-form-item label="资产类别：" prop="FaType" :required="true">
                            <Select 
                                v-model="addForm.faType" 
                                :teleported="false" 
                                :input-text="faTypeName" 
                                @change="AssetClassChange"
                                :filterable="true"
                                :filter-method="faTypeFilterMethod"
                            >
                                <ElOption
                                    v-for="item in showfaTypeList"
                                    :value="item.value"
                                    :label="item.label"
                                    :key="item.value"
                                >
                                </ElOption>
                            </Select>
                        </el-form-item>
                        <el-form-item label="资产编号：" prop="FaNum" :required="true">
                            <Tooltip :content="addForm.faNum" :isInput="true">
                                <el-input v-model="addForm.faNum" :disabled="isCanEdit"></el-input>
                            </Tooltip>
                        </el-form-item>
                        <el-form-item label="资产名称：" prop="FaName" :required="true">
                            <div v-if="faNameTextareaShow">
                                <el-input
                                    v-model="addForm.faName"
                                    ref="FaNumRef"
                                    @blur="inputTypeBlur('FaNumRef3')"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    @input="limitInputLength(addForm.faName, '资产名称')"
                                    @focus="inputTypeFocus()"
                                    type="textarea"
                                    maxlength="256"
                                    resize="none"
                                >
                                </el-input>
                            </div>
                            <Tooltip :content="addForm.faName" :isInput="true" v-else>
                                <el-input @focus="inputTypeFocus(3)" ref="FaNumRef" v-model="addForm.faName"></el-input>
                            </Tooltip>
                        </el-form-item>
                    </el-row>
                    <el-row class="isRow">
                        <el-form-item label="规格型号：" class="form-label-pl-10">
                            <div v-if="faModelTextareaShow">
                                <el-input
                                    v-model="addForm.faModel"
                                    ref="FaNumRef"
                                    @blur="inputTypeBlur('FaNumRef5')"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    type="textarea"
                                    maxlength="256"
                                    @input="limitInputLength(addForm.faModel, '规格型号')"
                                    @focus="inputTypeFocus()"
                                    resize="none"
                                    style="z-index: 1001"
                                ></el-input>
                            </div>
                            <Tooltip :content="addForm.faModel" :isInput="true" v-else>
                                <el-input @focus="inputTypeFocus(5)" ref="FaNumRef" v-model="addForm.faModel"></el-input>
                            </Tooltip>
                        </el-form-item>
                        <el-form-item class="form-label-pl-10">
                            <template #label>
                                <component :is="renderDateContent('录入期间：', () => emits('goDepreciationTab'))" />
                            </template>
                            <el-input v-model="entryPeriod" :disabled="true"></el-input>
                        </el-form-item>
                        <el-form-item prop="StartedDate" :required="true">
                            <template #label>
                                <component :is="renderDateContent('开始使用日期：', () => emits('goDepreciationTab'))" />
                            </template>
                            <el-date-picker
                                :disabled="isCanEdit"
                                style="width: 160px; height: 28px"
                                v-model="addForm.startedDate"
                                type="date"
                                placeholder=" "
                                value-format="YYYY-MM-DD"
                                size="small"
                                :disabled-date="disabledDateEnd"
                                clearable
                            />
                        </el-form-item>
                    </el-row>
                    <el-row class="isRow">
                        <el-form-item label="使用部门：" :required="true">
                            <SelectCheckbox
                                :disabled="isCanEdit"
                                :options="departmentList"
                                :isShowAllInfo="true"
                                v-model:selected-list="addForm.department"
                                :bottomText="hasPermissionDepartment() ? '新增部门' : ''"
                                @handleClick="clickAddDepartment"
                                @change="changeDepartmentOpt"
                            ></SelectCheckbox>
                        </el-form-item>
                        <el-form-item label="增加方式：" v-show="addForm.faProperty !== 2" class="form-label-pl-10">
                            <el-select 
                                v-model="addForm.createdWay" 
                                :teleported="false"
                                :filterable="true"
                                :filter-method="createWayFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showCreateWayList" 
                                    :label="item.label" 
                                    :value="item.value" 
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="供应商：" class="form-label-pl-10">
                            <Select
                                ref="venderRef"
                                v-model="addForm.vendor"
                                :disabled="isCanEdit"
                                :teleported="false"
                                :fitInputWidth="true"
                                :filterable="true"
                                :IconClearRight="'24px'"
                                :clearable="!isCanEdit"
                                :input-text="vendorName"
                                :placeholder="' '"
                                :allow-create="!isErp"
                                :bottom-html="hasPermissionVendor() ? newVendorHtml : ''"
                                @bottom-click="clickAddVendor"
                                @visible-change="handleVenderVisibleChange"
                                :filter-method="vendorFilterMethod"
                            >
                                <ElOption 
                                    v-for="item in showVendorList" 
                                    :key="item.value" 
                                    :value="item.value" 
                                    :label="item.label"
                                > </ElOption>
                            </Select>
                        </el-form-item>
                        <el-form-item v-if="addForm.faProperty === 2" label="使用人：" prop="serPeople" class="form-label-pl-10">
                            <div v-if="usePeopleTextareaShow">
                                <el-input
                                    ref="FaNumRef10"
                                    v-model="addForm.usePeople"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    type="textarea"
                                    maxlength="256"
                                    @input="limitInputLength(addForm.usePeople, '使用人')"
                                    @focus="inputTypeFocus(10)"
                                    @blur="inputTypeBlur('FaNumRef10')"
                                    resize="none"
                                    style="z-index: 1001"
                                >
                                </el-input>
                            </div>
                            <Tooltip :content="addForm.usePeople" :isInput="true" v-else>
                                <el-input
                                    ref="FaNumRef10"
                                    v-model="addForm.usePeople"
                                    @focus="inputTypeFocus(10)"
                                ></el-input>
                            </Tooltip>
                        </el-form-item>
                    </el-row>
                    <el-row class="isRow">
                        <el-form-item v-if="addForm.faProperty !== 2" label="使用人：" prop="serPeople" class="form-label-pl-10">
                            <div v-if="usePeopleTextareaShow">
                                <el-input
                                    ref="FaNumRef10"
                                    v-model="addForm.usePeople"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    type="textarea"
                                    maxlength="256"
                                    @input="limitInputLength(addForm.usePeople, '使用人')"
                                    @focus="inputTypeFocus(10)"
                                    @blur="inputTypeBlur('FaNumRef10')"
                                    resize="none"
                                    style="z-index: 1001"
                                >
                                </el-input>
                            </div>
                            <Tooltip :content="addForm.usePeople" :isInput="true" v-else>
                                <el-input
                                    ref="FaNumRef10"
                                    v-model="addForm.usePeople"
                                    @focus="inputTypeFocus(10)"
                                ></el-input>
                            </Tooltip>
                        </el-form-item>
                        <el-form-item label="存放位置：" prop="location" class="non-required">
                            <div v-if="locationTextareaShow">
                                <el-input
                                    ref="FaNumRef11"
                                    v-model="addForm.location"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    type="textarea"
                                    maxlength="256"
                                    @input="limitInputLength(addForm.location, '存放位置')"
                                    @focus="inputTypeFocus(11)"
                                    @blur="inputTypeBlur('FaNumRef11')"
                                    resize="none"
                                    style="z-index: 1001"
                                >
                                </el-input>
                            </div>
                            <Tooltip :content="addForm.location" :isInput="true" v-else>
                                <el-input @focus="inputTypeFocus(11)" ref="FaNumRef11" v-model="addForm.location"></el-input>
                            </Tooltip>
                        </el-form-item>
                    </el-row>
                </el-form>
            </div>
            <div class="line"></div>
            <div class="block-title">折旧方式</div>
            <div class="block-main">
                <el-form>
                    <el-row class="isRow">
                        <el-form-item :label="addForm.faProperty === 0 ? '折旧方法：' : '摊销方法：'" :required="true">
                            <el-select 
                                v-model="addForm.depreciationType" 
                                :teleported="false" 
                                :disabled="isCanEdit"
                                :filterable="true"
                                :filter-method="depreciationTypeFilterMethod"
                            >
                                <el-option 
                                    v-for="item in showDepreciationTypeList" 
                                    :label="item.label" 
                                    :value="Number(item.value)" 
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item
                            :label="addForm.faProperty === 0 ? '录入当期是否折旧：' : '录入当期是否摊销：'"
                            :required="true"
                            label-width="172px"
                        >
                            <el-radio-group v-model="addForm.depreciationNow" :disabled="isdepreciatedRadioFlag || isCanEdit">
                                <el-radio :label="1" size="large">是</el-radio>
                                <el-radio :label="0" size="large">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-row>
                    <template v-if="addForm.faProperty === 2 || (addForm.faProperty === 1 && [3, 4, 5].includes(accountingStandard))">
                        <el-row class="isRow">
                            <el-form-item label="资产科目：" :required="true">
                                <Select
                                    ref="faAsubRef"
                                    v-model="addForm.faAsub"
                                    :teleported="false"
                                    :disabled="isCanEdit"
                                    :input-text="faAsubName"
                                    :showMoreFilled="addForm.asubAAERelation.fa_aae"
                                    :IconClearRight="'10px'"
                                    @expandAssit="expandAssit('资产科目', faAsubName || '', addForm.asubAAERelation.fa_aae, addForm.faAsub)"
                                    @focus="() => getFixedAssetsAsubList()"
                                    @click="clickAsub"
                                    :filterable="true"
                                    :filter-method="faAsubFilterMethod"
                                >
                                    <ElOption
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in showfaAsubList"
                                        :key="item.value"
                                        @click="clickAsubOption('资产科目', item.label, item.type, item.value)"
                                    >
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item label="资产处置科目：" :required="true" v-if="addForm.faProperty === 1">
                                <Select
                                    ref="disposalAsubRef"
                                    v-model="addForm.disposalAsub"
                                    :teleported="false"
                                    style="width: 160px"
                                    :input-text="disposalAsubName"
                                    :disabled="isCanEdit"
                                    :showMoreFilled="addForm.asubAAERelation.disposal_aae"
                                    :IconClearRight="'10px'"
                                    @expandAssit="
                                        expandAssit(
                                            '资产处置科目',
                                            disposalAsubName || '',
                                            addForm.asubAAERelation.disposal_aae,
                                            addForm.disposalAsub
                                        )
                                    "
                                    @focus="() => getDisposalAsubList()"
                                    @click="clickAsub"
                                    :filterable="true"
                                    :filter-method="disAsubFilterMethod"
                                >
                                    <ElOption 
                                        :label="item.label" 
                                        :value="item.value" 
                                        v-for="item in showdisAsubList" 
                                        :key="item.value"
                                        @click="clickAsubOption('资产处置科目', item.label, item.type, item.value)"
                                    >
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item label="摊销费用科目：" :required="true" class="jtzj">
                                <Select
                                    ref="costAsubRef"
                                    v-model="addForm.costAsub"
                                    :disabled="isCanEdit || addForm.department.length > 1"
                                    :filterable="true"
                                    :fitInputWidth="true"
                                    :placeholder="' '"
                                    :showMoreFilled="addForm.department.length === 1 ? addForm.amortizations[0].asub_aae :''"
                                    :IconClearRight="'10px'"
                                    @change="changeCostAsub"
                                    :filter-method="costAsubFilterMethod"
                                    @expandAssit="expandAssit('摊销费用科目', costAsubName || '', addForm.amortizations[0].asub_aae, addForm.costAsub)"
                                >
                                    <ElOption
                                        v-for="item in showcostAsubList"
                                        :key="item.asubId"
                                        :value="item.asubId + ''"
                                        :label="item.asubCode + ' ' + item.asubAAName"
                                        @click="clickAsubOption('摊销费用科目', item.asubName, item.aatypes, item.asubId, 0, item.aatypesAllowNull)"
                                    >
                                        <span>{{ item.asubCode + " " + item.asubAAName }}</span>
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item class="apportion form-label-pl-10">
                                <el-tooltip v-model:visible="amortizationTips" effect="light" placement="top">
                                    <template #content>
                                        <span>多部门才可进行费用分摊哦~</span>
                                    </template>
                                    <el-checkbox
                                        v-model="isAmortization"
                                        :disabled="addForm.department.length < 2 || isCanEdit"
                                        @mouseenter="enterAmortization"
                                        @mouseleave="leaveAmortization"
                                        >分摊</el-checkbox
                                    >
                                </el-tooltip>
                            </el-form-item>
                        </el-row>
                    </template>
                    <template v-else>
                        <el-row class="isRow">
                            <el-form-item label="资产科目：" :required="true">
                                <Select
                                    ref="faAsubRef"
                                    v-model="addForm.faAsub"
                                    :teleported="false"
                                    :disabled="isCanEdit"
                                    :input-text="faAsubName"
                                    :showMoreFilled="addForm.asubAAERelation.fa_aae"
                                    :IconClearRight="'10px'"
                                    @expandAssit="expandAssit('资产科目', faAsubName || '', addForm.asubAAERelation.fa_aae, addForm.faAsub)"
                                    @focus="() => getFixedAssetsAsubList()"
                                    @click="clickAsub"
                                    :filterable="true"
                                    :filter-method="faAsubFilterMethod"
                                >
                                    <ElOption
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in showfaAsubList"
                                        :key="item.value"
                                        @click="clickAsubOption('资产科目', item.label, item.type, item.value)"
                                    >
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item :label="addForm.faProperty === 0 ? '累计折旧科目：' : '累计摊销科目'" :required="true">
                                <Select
                                    ref="deprecationAsubRef"
                                    v-model="addForm.depreciationAsub"
                                    :teleported="false"
                                    :disabled="isCanEdit"
                                    :input-text="depreciationAsubName"
                                    :showMoreFilled="addForm.asubAAERelation.depreciation_aae"
                                    :IconClearRight="'10px'"
                                    @expandAssit="
                                        expandAssit(
                                            addForm.faProperty === 0 ? '累计折旧科目' : '累计摊销科目',
                                            depreciationAsubName ?? '',
                                            addForm.asubAAERelation.depreciation_aae,
                                            addForm.depreciationAsub
                                        )
                                    "
                                    @focus="() => getDeprecationAsubList()"
                                    @click="clickAsub"
                                    :filterable="true"
                                    :filter-method="deAsubFilterMethod"
                                >
                                    <ElOption
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in showdeAsubList"
                                        :key="item.value"
                                        @click="
                                            clickAsubOption(
                                                addForm.faProperty === 0 ? '累计折旧科目' : '累计摊销科目',
                                                item.label,
                                                item.type,
                                                item.value
                                            )
                                        "
                                    >
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item label="资产处置科目：" :required="true">
                                <Select
                                    ref="disposalAsubRef"
                                    v-model="addForm.disposalAsub"
                                    :teleported="false"
                                    style="width: 160px"
                                    :input-text="disposalAsubName"
                                    :disabled="isCanEdit"
                                    :showMoreFilled="addForm.asubAAERelation.disposal_aae"
                                    :IconClearRight="'10px'"
                                    @expandAssit="
                                        expandAssit(
                                            '资产处置科目',
                                            disposalAsubName || '',
                                            addForm.asubAAERelation.disposal_aae,
                                            addForm.disposalAsub
                                        )
                                    "
                                    @focus="() => getDisposalAsubList()"
                                    @click="clickAsub"
                                    :filterable="true"
                                    :filter-method="disAsubFilterMethod"
                                >
                                    <ElOption
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in showdisAsubList"
                                        :key="item.value"
                                        @click="clickAsubOption('资产处置科目', item.label, item.type, item.value)"
                                    >
                                    </ElOption>
                                </Select>
                            </el-form-item>
                        </el-row>
                        <el-row class="isRow">
                            <el-form-item
                                label="资产减值准备科目："
                                v-if="[2, 3].includes(accountingStandard)"
                                :required="true"
                                class="lineHless"
                                @click="clickAsub"
                            >
                                <Select
                                    ref="impairmentProvisionAsubRef"
                                    v-model="addForm.impairmentProvisionAsub"
                                    :teleported="false"
                                    :disabled="isCanEdit"
                                    :input-text="impairmentProvisionName"
                                    @focus="() => getImpairmentProvisionAsubList()"
                                    :filterable="true"
                                    :filter-method="impAsubFilterMethod"
                                    :showMoreFilled="addForm.asubAAERelation.impairment_provision_aae"
                                    :IconClearRight="'10px'"
                                    @expandAssit="
                                        expandAssit(
                                            '资产减值准备科目',
                                            impairmentProvisionName || '',
                                            addForm.asubAAERelation.impairment_provision_aae,
                                            addForm.impairmentProvisionAsub
                                        )
                                    "
                                >
                                    <ElOption
                                        :label="item.label"
                                        :value="item.value"
                                        v-for="item in showimpAsubList"
                                        :key="item.value"
                                        @click="clickAsubOption('资产减值准备科目', item.label, item.type, item.value)"
                                    >
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item
                                :label="addForm.faProperty === 0 ? '折旧费用科目：' : '摊销费用科目：'"
                                :required="true"
                                class="jtzj"
                            >
                                <Select
                                    ref="costAsubRef"
                                    v-model="addForm.costAsub"
                                    :disabled="isCanEdit || addForm.department.length > 1"
                                    :filterable="true"
                                    :fitInputWidth="true"
                                    :showMoreFilled="addForm.department.length === 1? addForm.amortizations[0].asub_aae :''"
                                    :IconClearRight="'10px'"
                                    @change="changeCostAsub"
                                    :filter-method="costAsubFilterMethod"
                                    @expandAssit="expandAssit(addForm.faProperty === 0 ? '折旧费用科目' : '摊销费用科目', costAsubName || '', addForm.amortizations[0].asub_aae, addForm.costAsub)"
                                >
                                    <ElOption
                                        v-for="item in showcostAsubList"
                                        :key="item.asubId"
                                        :value="item.asubId"
                                        :label="item.asubCode + ' ' + item.asubAAName"
                                        @click="
                                            clickAsubOption(
                                                addForm.faProperty === 0 ? '折旧费用科目' : '摊销费用科目',
                                                item.asubName,
                                                item.aatypes,
                                                item.asubId,
                                                0,
                                                item.aatypesAllowNull
                                            )
                                        "
                                    >
                                        <span>{{ item.asubCode + " " + item.asubAAName }}</span>
                                    </ElOption>
                                </Select>
                            </el-form-item>
                            <el-form-item class="apportion form-label-pl-10">
                                <el-tooltip :visible="amortizationTips" effect="light" placement="top">
                                    <template #content>
                                        <span>多部门才可进行费用分摊哦~</span>
                                    </template>
                                    <el-checkbox
                                        v-model="isAmortization"
                                        :disabled="addForm.department.length < 2 || isCanEdit"
                                        @mouseenter="enterAmortization"
                                        @mouseleave="leaveAmortization"
                                        >分摊</el-checkbox
                                    >
                                </el-tooltip>
                            </el-form-item>
                        </el-row>
                    </template>
                </el-form>
                <div class="apportionment-table" v-if="addForm.department.length > 1">
                    <div class="apportionment-title">{{ addForm.faProperty === 0 ? "折旧费用分摊表" : "摊销费用分摊表" }}</div>
                    <Table
                        :data="addForm.amortizations"
                        :columns="amortizationColums"
                        :hasAddSub="!isCanEdit"
                        :customSubtract="true"
                        :show-overflow-tooltip="true"
                        :tooltip-options="{ effect: 'light', placement: 'top' }"
                        :addRowData="{ department_id: '', ratio: '', asub_id: '' }"
                        @handleSubtract="subtractAmortization"
                        @table-add-or-subtract="handleTableAddSub"
                        :tableName="setModule"
                    >
                        <template #department_id>
                            <el-table-column 
                                label="使用部门" 
                                min-width="212" 
                                align="center" 
                                header-align="center" 
                                prop="description" 
                                :width="getColumnWidth(setModule, 'description')"
                            >
                                <template #default="scope">
                                    <Select
                                        :style="{width: '100%'}"
                                        v-model="scope.row.department_id"
                                        :filterable="true"
                                        :fitInputWidth="true"
                                        @change="changeDepartment(scope.row.department_id)"
                                        :disabled="isCanEdit"
                                        :filter-method="departFilterMethod"
                                    >
                                        <ElOption 
                                            v-for="item in showdepartmentList" 
                                            :key="item.id" 
                                            :value="item.id" 
                                            :label="item.name"
                                        ></ElOption>
                                    </Select>
                                </template>
                            </el-table-column>
                        </template>
                        <template #ratio>
                            <el-table-column
                                label="分摊比例"
                                min-width="200"
                                align="center"
                                header-align="center"
                                prop="ratio"
                                :show-overflow-tooltip="false"
                                :width="getColumnWidth(setModule, 'ratio')"
                            >
                                <template #default="scope">
                                    <div class="cell-flex">
                                        <el-input
                                            style="width: 89%"
                                            v-model="scope.row.ratio"
                                            :disabled="isCanEdit"
                                            @input="(val) => ratioInput(val, scope.$index)"
                                        />
                                        <span style="padding-left: 2px">%</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #asub>
                            <el-table-column
                                :label="addForm.faProperty === 0 ? '折旧费用科目' : '摊销费用科目'"
                                min-width="210"
                                align="center"
                                header-align="center"
                                prop="description"
                                :resizable="false"
                            >
                                <template #default="scope">
                                    <SelectV2
                                        :class="'visibleSelect' + scope.$index"
                                        :options="showcostAsubAmList"
                                        :filterable="true"
                                        :disabled="isCanEdit"
                                        :placeholder="' '"
                                        v-model="scope.row.asub_id"
                                        @change="(value) => clickCostAsub(value, scope.$index)"
                                        :toolTipOptions="{ dynamicWidth: true }"
                                         @visible-change="costAsubVisibleChange"
                                        :remote="true"
                                        :showMoreFilled="!!addForm.amortizations[scope.$index].asub_aae"
                                        :iconClearRight="-28"
                                         @expandAssit="(label)=>expandAssit(addForm.faProperty === 0 ? '折旧费用科目' : '摊销费用科目', label || '', addForm.amortizations[scope.$index].asub_aae, addForm.amortizations[scope.$index].asub_id)"
                                    >
                                    </SelectV2>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="line"></div>
            <div class="block-title">资产数据</div>
            <div class="block-main">
                <el-form ref="formRefAsset">
                    <el-row class="isRow">
                        <el-form-item label="资产原值：" :required="true">
                            <el-input v-model="addForm.value" :disabled="isCanEdit" @input="(val) => handleInput(val, 'value')"></el-input>
                        </el-form-item>
                        <el-form-item label="残值率：" :required="true">
                            <el-input v-model="addForm.netSalvageRate" :disabled="isCanEdit">
                                <template #suffix>
                                    <span class="percentsign" :style="isCanEdit ? 'color: var(--weaker-font-color)' : 'color:black'"
                                        >%</span
                                    >
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="预计残值：" class="form-label-pl-10">
                            <el-input v-model="addForm.estimateResidualsRate" :disabled="true"></el-input>
                        </el-form-item>
                    </el-row>
                    <el-row class="isRow">
                        <el-form-item label="预计使用月份：" :required="true">
                            <el-input v-input.integer v-model="addForm.useFullLife" :disabled="isCanEdit"></el-input>
                        </el-form-item>
                        <el-form-item :label="addForm.faProperty === 0 ? '已折旧月份：' : '已摊销月份：'" :required="true">
                            <el-input v-input.integer v-model="addForm.usedLife" :disabled="isCanEdit"></el-input>
                        </el-form-item>
                        <el-form-item label="剩余使用月份：" class="form-label-pl-10">
                            <el-input v-model="addForm.remainingMonth" :disabled="true"></el-input>
                        </el-form-item>
                    </el-row>
                    <el-row
                        class="isRow"
                    >
                        <el-form-item :label="addForm.faProperty === 0 ? '累计折旧：' : '累计摊销：'" :required="true">
                            <Input
                                v-model="addForm.accumlatedDepreciation"
                                :required="true"
                                :disabled="isCanEdit"
                                @input="(val: any) => handleInput(val, 'accumlatedDepreciation')"
                            ></Input>
                        </el-form-item>
                        <el-form-item class="form-label-pl-10" :label="addForm.faProperty === 0 ? '本年累计折旧：' : '本年累计摊销：'">
                            <el-input
                                v-model="addForm.accumlatedDepreciationTY"
                                :disabled="isCanEdit"
                                @input="(val) => handleInput(val, 'accumlatedDepreciationTY')"
                            ></el-input>
                        </el-form-item>
                        <el-form-item class="form-label-pl-10" :label="addForm.faProperty === 0 ? '以前年度累计折旧：' : '以前年度累计摊销：'"> 
                            <el-input v-model="addForm.prevAccumulatedDepreciation" :disabled="true"></el-input>
                        </el-form-item>
                    </el-row>
                    <el-row class="isRow">
                        <el-form-item
                            class="form-label-pl-10"
                            label="减值准备："
                            v-if="
                                (accountingStandard === 3 && addForm.faProperty === 0) ||
                                (accountingStandard === 2 && [0, 1].includes(addForm.faProperty))
                            "
                        >
                            <el-input v-model="addForm.impairmentProvision" prop="impairmentProvision" :disabled="isCanEdit"></el-input>
                        </el-form-item>
                        <el-form-item :label="addForm.faProperty === 0 ? '月折旧额：' : '月摊销额：'" :required="true">
                            <el-input v-model="addForm.monthDepreciationValue" :disabled="true"></el-input>
                        </el-form-item>
                        <el-form-item class="form-label-pl-10" label="进项税额抵扣：" v-if="taxType === 2 && [1, 2, 3].includes(accountingStandard)" prop="inputTax">
                            <el-input v-model="addForm.inputTax" :disabled="isCanEdit"></el-input>
                        </el-form-item>
                        <el-form-item v-if="noteShow() < 2" class="form-label-pl-10" label="净值：" prop="netValue">
                            <el-input v-model="addForm.netValue" :disabled="true"></el-input>
                        </el-form-item>
                        <el-form-item label="备注：" class="form-label-pl-10" v-if="noteShow() < 1">
                            <div v-if="noteTextareaShow">
                                <el-input
                                    v-model="addForm.note"
                                    ref="FaNumRef"
                                    @blur="inputTypeBlur('dadaNumRef8')"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    type="textarea"
                                    maxlength="256"
                                    @input="limitInputLength(addForm.note, '备注')"
                                    @focus="inputTypeFocus(18)"
                                    resize="none"
                                >
                                </el-input>
                            </div>
                            <Tooltip :content="addForm.note" :isInput="true" v-else>
                                <el-input @focus="inputTypeFocus(18)" ref="FaNumRef" v-model="addForm.note"></el-input>
                            </Tooltip>
                        </el-form-item>
                    </el-row>
                    <el-row class="isRow">
                        <el-form-item v-if="noteShow() >= 2" class="form-label-pl-10" label="净值：" prop="netValue">
                            <el-input v-model="addForm.netValue" :disabled="true"></el-input>
                        </el-form-item>
                        <el-form-item v-if="noteShow() >= 1" label="备注：" class="form-label-pl-10" prop="note">
                            <div v-if="noteTextareaShow">
                                <el-input
                                    v-model="addForm.note"
                                    ref="FaNumRef"
                                    @blur="inputTypeBlur('dadaNumRef8')"
                                    :autosize="{ minRows: 1, maxRows: 3.5 }"
                                    type="textarea"
                                    maxlength="256"
                                    @input="limitInputLength(addForm.note, '备注')"
                                    @focus="inputTypeFocus(18)"
                                    resize="none"
                                >
                                </el-input>
                            </div>
                            <Tooltip :content="addForm.note" :isInput="true" v-else>
                                <el-input @focus="inputTypeFocus(18)" ref="FaNumRef" v-model="addForm.note"></el-input>
                            </Tooltip>
                        </el-form-item>
                    </el-row>
                </el-form>
            </div>
            <div class="line"></div>
            <div class="block-title">附件</div>
            <div class="attach-file-center">
                <div v-if="addForm.attachsCount === 0" class="upload-item" @click.stop="getAttachFileList">
                    <div class="upload-item-title"><img src="@/assets/Invoice/upload.png" /></div>
                    <div class="upload-item-tip">单个文件不超过 100M</div>
                </div>
                <template v-else>
                    <a class="link" @click.stop="getAttachFileList">查看附件{{ addForm.attachsCount ? `(${addForm.attachsCount})` : "" }}</a>
                </template>
            </div>
            <div class="buttons" :style="isErp ? 'margin-top:0' : ''">
                <a class="button solid-button mr-10" @click="savedata">保存</a>
                <a class="button" @click="divCancel">取消</a>
            </div>
        </div>
    </div>
    <AddDepartment
        ref="departmentAddRef"
        :isShow="departmentAdd"
        :departmentCode="departmentCode"
        @cancel="() => (departmentAdd = false)"
        @close="() => (departmentAdd = false)"
        @save="saveDepartment"
    />
    <AddVendor
        ref="vendorAddRef"
        :isShow="vendorAdd"
        :vendorCode="vendorCode"
        :codeDisabled="codeDisabled"
        :vendorNameAuto="vendorNameAuto"
        @cancel="() => (vendorAdd = false)"
        @close="() => (vendorAdd = false)"
        @save="saveVendor"
    ></AddVendor>
    <AddAssistingAccountingEntryDialog
        ref="addAssistingAccountingEntryDialogRef"
        :title="title"
        @save-success="erpAAESaveSuccess"
    ></AddAssistingAccountingEntryDialog>
    <AuxiliaryDialog
        v-model="auxiliaryDialogShow"
        :title="auxiliaryTitle"
        :asubName="auxiliaryAsubName"
        :aacode="auxiliaryCode"
        :aaReadonly="auxiliaryReadonly"
        :aaAllowNull="auxiliaryAllowNull"
        :department-id="addForm.department"
        :aaId="auxiliaryId"
        @setAsubWithAAE="setAsubWithAAE"
    ></AuxiliaryDialog>
    <UploadFileDialog :readonly="isCanEdit && attachHasVoucher" ref="uploadFileDialogRef" @save="saveAttachFile" />
</template>

<script setup lang="ts">
import AddAssistingAccountingEntryDialog from "@/components/AddAssistingAccountingEntryDialog/index.vue";
import Table from "@/components/Table/index.vue";
import ElOption from "@/components/Option/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import Input from "@/components/Input/FormatInput/index.vue";
import Select from "@/components/Select/index.vue";
import SelectV2 from "@/components/AsubSelect/index.vue";
import AuxiliaryDialog from "./AuxiliaryDialog.vue";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import AddDepartment from "./AddDepartment.vue";
import AddVendor from "./AddVendor.vue";
import type { IDepartmentAddForm } from "./AddDepartment.vue";
import type { IVendorAddForm } from "./AddVendor.vue";
import type { Option } from "@/components/SelectCheckbox/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { FormRules } from "element-plus";
import type {
    ISelectStrItem,
    IPeriod,
    IDepartment,
    IFixedAssetsType,
    IFAAsubDto,
    IAsubSelect,
    IAccountSubjectList,
} from "../types";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import type { IAssistingAccount } from "@/api/assistingAccounting";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { ref, reactive, watchEffect, computed, watch, nextTick, toRef } from "vue";
import { checkAssetsInput } from "../validator";
import { formatMoney } from "@/util/format";
import { formatDate } from "@/util/date";
import { checkPermission } from "@/util/permission";
import { textareaBottom, checkNeedToastWithBillAndVoucher, getCostAsubList, useTooltip } from "../utils";
import { getGlobalLodash } from "@/util/lodash";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import type { IFileInfo, IGetAttachFileListBack } from "@/components/UploadFileDialog/types";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { createWayList, depreciationTypeList } from "@/views/FixedAssets/utils";

const setModule = "AEFixedAssets";
const { renderDateContent } = useTooltip()

//账套类别
const accountSetStore = useAccountSetStore();
const accountingStandard = accountSetStore.accountSet!.accountingStandard as number;
const taxType = useAccountSetStore().accountSet?.taxType;
const isDataEditType = ref(true);
const props = defineProps<{
    faid: number;
    pid: number;
    editData: any;
    activeName: string;
    periodList: IPeriod[] | undefined;
    maxDate: any;
    samePid: boolean;
}>();
const _ = getGlobalLodash();
const isErp = ref(window.isErp);

function noteShow() {
    let prevContent = 0;
    if ((accountingStandard === 3 && addForm.faProperty === 0) || (accountingStandard === 2 && [0, 1].includes(addForm.faProperty))) {
        prevContent++;
    }
    if (taxType === 2 && [1, 2, 3].includes(accountingStandard)) {
        prevContent++;
    }
    return prevContent;
}

const faNameTextareaShow = ref(false);
const faModelTextareaShow = ref(false);
const usePeopleTextareaShow = ref(false);
const locationTextareaShow = ref(false);
const vendorTextareaShow = ref(false);
const noteTextareaShow = ref(false);
const FaNumRef = ref();
const FaNumRef10 = ref();
const FaNumRef11 = ref();
const formRefInfo = ref();
const formRefAsset = ref();
const inputTypeBlur = (value: string) => {
    switch (value) {
        case "FaNumRef3":
            faNameTextareaShow.value = false;
            break;
        case "FaNumRef5":
            faModelTextareaShow.value = false;
            break;
        case "FaNumRef8":
            vendorTextareaShow.value = false;
            break;
        case "dadaNumRef8":
            noteTextareaShow.value = false;
            break;
        case "FaNumRef10":
            usePeopleTextareaShow.value = false;
            break;
        case "FaNumRef11":
            locationTextareaShow.value = false;
            break;
    }
};

const inputTypeFocus = (num?: number) => {
    textareaBottom(formRefInfo);
    switch (num) {
        case 3:
            faNameTextareaShow.value = true;
            break;
        case 5:
            faModelTextareaShow.value = true;
            break;
        case 8:
            vendorTextareaShow.value = true;
            break;
        case 10:
            locationTextareaShow.value = false;
            usePeopleTextareaShow.value = true;
            break;
        case 11:
            usePeopleTextareaShow.value = false;
            locationTextareaShow.value = true;
            break;
        case 18:
            textareaBottom(formRefAsset);
            noteTextareaShow.value = true;
            break;
    }
    nextTick(() => {
        if (num) {
            getTextareaFocus(num);
        }
    });
};

const getTextareaFocus = (num: number) => {
    switch (num) {
        case 3:
            FaNumRef.value.focus();
            break;
        case 5:
            FaNumRef.value.focus();
            break;
        case 8:
            FaNumRef.value.focus();
            break;
        case 10:
            FaNumRef10.value.focus();
            break;
        case 11:
            FaNumRef11.value.focus();
            break;
        case 18:
            FaNumRef.value.focus();
            break;
    }
};
const createdDate = ref("");

const emits = defineEmits(["divCancel", "goDepreciationTab"]);
const addForm = reactive({
    faNum: "",
    createdPeriod: 8,
    createdWay: 1,
    faName: "",
    faType: "",
    faModel: "",
    startedDate: "",
    department: [0],
    vendor: "",
    usePeople: "",
    location: "",
    depreciationType: 1,
    subjectCode: "",
    depreciationNow: 1,
    faAsub: "",
    depreciationAsub: "",
    disposalAsub: "",
    costAsub: "",
    fa_prepareAcc: "",
    depreciationExpenseAcc: "",
    value: "",
    netValue: "",
    netSalvageRate: "",
    estimateResidualsRate: "",
    useFullLife: "",
    usedLife: "",
    remainingMonth: "",
    accumlatedDepreciation: "0",
    accumlatedDepreciationTY: "0",
    prevAccumulatedDepreciation: "",
    impairmentProvision: "0", // 减值准备
    impairment: "0",
    monthDepreciationValue: 0,
    note: "",
    impairmentProvisionAsub: "0", //减值准备科目id
    faProperty: 0, //资产属性
    fa_asub_name: "",
    disposal_asub_name: "",
    depreciation_asub_name: "",
    cost_asub_name: "",
    impairment_provision_asub_name: "",
    inputTax: "",
    amortizations: [
        {
            department_id: 0,
            ratio: "",
            asub_id: "",
            asub_aae: "",
        },
    ],
    asubAAERelation: {
        fa_aae: "",
        depreciation_aae: "",
        disposal_aae: "",
        impairment_provision_aae: "",
    },
    attachsCount: 0,
    attachFiles: "",
});

let attachParentId = 0;
const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
function getAttachFileList() {
    const params = { faId: props.faid };
    if (attachParentId === 0) {
        request({
            url: "/api/FixedAssets/GetAttachFileList",
            method: "post",
            params,
        }).then((res: IResponseModel<IGetAttachFileListBack>) => {
            if (res.state === 1000 && res.data.result) {
                const list = res.data.data.map((item: any) => {
                    item.relativePath = item.path.replace(/^LemonAcc\/\d{3}\/\d{3}\/\d{3}\//, "");
                    return item;
                });
                attachFiles.value = list;
                attachParentId = res.data.parentId;
                openFileDialog(attachFiles.value, attachParentId);
            } else {
                ElNotify({ type: "warning", message: "出现错误，请稍后重试" });
            }
        });
    } else {
        openFileDialog(attachFiles.value, attachParentId);
    }
}
function openFileDialog(list: IFileInfo[], fileId: number) {
    uploadFileDialogRef.value?.open({ faId: props.faid }, list, fileId);
}
const attachFiles = ref<Array<IFileInfo>>([]);
let isNeedSaveToVoucherForDelete = false;
let isSaving = false;
async function saveAttachFile(_params: any, newFileids: number[], delFileids: number[], fileList: any[]) {
    if (isSaving) return;
    isSaving = true;
    attachFiles.value = fileList.map((f) => {
        return {
            fileId: f.fileId,
            fileName: f.fileName,
            fileSize: f.fileSize,
            fileType: f.fileType,
            relativePath: f.relativePath,
        };
    });
    addForm.attachsCount = fileList.length;
    addForm.attachFiles = fileList.map((item: any) => item.fileId).join(",");
    if (!delFileids.length) {
        isSaving = false;
        return;
    }
    const { faId } = _params;
    const needToast = await checkNeedToastWithBillAndVoucher(faId, delFileids.join(","));
    if (needToast) {
        showDeleteBillOrVoucherConfirm("bill").then((batchDelte: boolean) => {
            isNeedSaveToVoucherForDelete = batchDelte;
            isSaving = false;
        });
    } else {
        isSaving = false;
    }
}

function changeDepartmentOpt(check: boolean, value: any, isAll?: boolean) {
    isAmortization.value = addForm.department.length > 1;
    if (isAll) {
        let amortizations = addForm.department.reduce((prev: any, item: number) => {
            if (addForm.amortizations.findIndex((i) => i.department_id === item) === -1) {
                prev.push({
                    department_id: item,
                    ratio: "",
                    asub_id: "",
                    asub_aae: "",
                });
            }
            return prev;
        }, []);
        addForm.amortizations = addForm.amortizations.concat(amortizations).sort((a, b) => a.department_id - b.department_id);
        return;
    }
    if (check) {
        let lastIndex = addForm.amortizations.reduce((acc, obj, index) => {
            if (obj.department_id === value) {
                return index;
            }
            return acc;
        }, -1);
        // 如果找到了符合条件的对象，则在其后插入新对象
        if (lastIndex !== -1) {
            addForm.amortizations.splice(lastIndex + 1, 0, {
                department_id: value,
                ratio: "",
                asub_id: "",
                asub_aae: "",
            });
        } else {
            if (addForm.department.length === 1) {
                addForm.amortizations.push({
                    department_id: value,
                    ratio: "100",
                    asub_id: addForm.costAsub + "",
                    asub_aae: "",
                });
            } else {
                addForm.amortizations.push({
                    department_id: value,
                    ratio: "",
                    asub_id: "",
                    asub_aae: "",
                });
            }
        }
    } else {
        addForm.amortizations = addForm.amortizations.filter((item) => item.department_id !== value);
    }
}

const isAmortization = ref(false);
const amortizationColums = ref<Array<IColumnProps>>([{ slot: "department_id" }, { slot: "ratio" }, { slot: "asub" }]);
function changeDepartment(val: number) {
    if (val) {
        addForm.department = [...new Set(addForm.amortizations.map((item: any) => item.department_id))] as number[];
    }
}
watch(
    () => isAmortization.value,
    (val) => {
        if (!val) {
            addForm.department = [addForm.amortizations[0].department_id];
            addForm.amortizations = [
                { department_id: addForm.amortizations[0].department_id, ratio: "100", asub_id: props.editData.cost_asub + "", asub_aae: "" },
            ];
            addForm.costAsub = props.editData.faAmortizations?.length > 1 ? "" : String(props.editData.cost_asub);
            if (props.editData.faAmortizations.length > 1) {
                getDefaultCostAsub(true);
            } else {
                addForm.costAsub = String(props.editData.cost_asub);
            }
        } else {
            addForm.costAsub = "";
        }
    }
);
function subtractAmortization(index: number) {
    if (addForm.amortizations[index].department_id) {
        addForm.department = addForm.department.filter((item) => item != addForm.amortizations[index].department_id);
    }
    addForm.amortizations.splice(index, 1);
    if (addForm.amortizations.length < 2) isAmortization.value = false;
}

function handleTableAddSub(data: any) {
    addForm.amortizations = data;
}

const amortizationTips = ref(false);
function enterAmortization() {
    if (addForm.department.length < 2) {
        amortizationTips.value = true;
    }
}
function leaveAmortization() {
    amortizationTips.value = false;
}

const faAsubName = computed(() => {
    return faAsubList.value.find((item) => item.value == addForm.faAsub)?.label;
});
const depreciationAsubName = computed(() => {
    return deprecationAsubList.value.find((item) => item.value == addForm.depreciationAsub)?.label;
});
const disposalAsubName = computed(() => {
    return disposalAsubList.value.find((item) => item.value == addForm.disposalAsub)?.label;
});
const impairmentProvisionName = computed(() => {
    return impairmentProvisionAsubList.value.find((item) => item.value == addForm.impairmentProvisionAsub)?.label;
});
const costAsubName = computed(() => {
    return showcostAsubList.value.find((item) => item.asubId == addForm.costAsub)?.label;
});
const faTypeName = computed(() => {
    return faTypeList.value.find((item) => item.value == addForm.faType)?.label;
});
// const departmentName = computed(() => {
//     return departmentList.value.find((item) => item.value == addForm.department)?.label;
// });
const vendorName = computed(() => {
    return vendorList.value.find((item) => item.value == addForm.vendor)?.label;
});

//部门供应商新增权限
const hasPermissionDepartment = () => {
    if (isErp.value) {
        return checkPermission(["Department-编辑"]);
    } else {
        return checkPermission(["assistingaccount-canedit"]);
    }
};
const hasPermissionVendor = () => {
    if (isErp.value) {
        return checkPermission(["Vendors-编辑"]);
    } else {
        return checkPermission(["assistingaccount-canedit"]);
    }
};

const entryPeriod = ref("");
// 暂存原表格数据，用于回填
const formDataTemp = ref();
// 使用部门下拉框
const departmentAdd = ref(false);
const departmentCode = ref("");
//供应商下拉框
const vendorAdd = ref(false);
const vendorCode = ref("");
const vendorNameAuto = ref("");
const codeDisabled = ref(false);
const newVendorHtml = `<div style="text-align: center; height: 32px; line-height: 32px;">
        <a class="link">
            新增供应商
        </a>
    </div>`;

const addAssistingAccountingEntryDialogRef = ref<InstanceType<typeof AddAssistingAccountingEntryDialog>>();
const erpAAESaveSuccess = (data: any) => {
    if (title.value === "新增部门") {
        useAssistingAccountingStore()
        .getDepartment()
        .then(() => {
            getDepartmentApi("1", data);
        });
    } else if (title.value === "新增供应商") {
        useAssistingAccountingStore().getAssistingAccounting().then(() => {
            getVendorList("1", data);
        });
    } else {
        useAssistingAccountingStore().getAssistingAccounting();
    }
};
const title = ref("添加辅助核算项目");
function clickAddDepartment() {
    if (isErp.value) {
        addAssistingAccountingEntryDialogRef.value?.showAADialog(10004);
        title.value = "新增部门";
    } else {
        // 获取最新的辅助核算编码
        request({
            url: `/api/AssistingAccounting/GetNewAANum?aaType=10004&categoryId=0`,
            method: "post",
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                departmentCode.value = res.data;
                departmentAdd.value = true;
            }
        });
    }
}
function limitInputLength(val: string, name: string, limitLength: number = 256) {
    if (val.length === limitLength) {
        ElNotify({ type: "warning", message: "亲，" + name + `不能超过${limitLength}个字哦~` });
    }
}

function ratioInput(value: string, index: number) {
    let ratio = value.replace(".", "");
    const regex = /^(100|[1-9][0-9]?)$/;
    addForm.amortizations[index].ratio = ratio.match(regex) ? ratio.match(regex)![0] : "";
}

function clickAddVendor() {
    if (isErp.value) {
        addAssistingAccountingEntryDialogRef.value?.showAADialog(10002, autoAddName.value);
        title.value = "新增供应商";
        return; 
    }
    request({
        url: `/api/ErpAssistingAccounting/GetIsEntityNumberChangeable?aaType=10002`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            codeDisabled.value = res.data;
        }
    });
    vendorNameAuto.value = autoAddName.value;
    // 获取最新的辅助核算编码
    let numUrl = isErp.value
        ? `/api/ErpAssistingAccounting/GetNewVendCode?categoryId=10002`
        : `/api/AssistingAccounting/GetNewAANum?aaType=10002&categoryId=0`;
    request({
        url: numUrl,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            vendorCode.value = res.data;
            vendorAdd.value = true;
            if (res.msg) {
                ElNotify({ type: "warning", message: res.msg });
            }
        }
    });
}

function saveDepartment(formData: IDepartmentAddForm) {
    // departmentAddForm
    const data = {
        aaNum: formData.departmentCode,
        aaName: formData.departmentName,
    };
    request({
        url: `/api/AssistingAccounting/Check?aaType=10004`,
        method: "post",
        data,
    }).then((res: IResponseModel<string>) => {
        if (res.data === "N") {
            saveAs(formData);
        } else if (res.data == "Name") {
            ElConfirm("亲，此名称已存在，是否继续保存？").then((r: boolean) => {
                if (r) saveAs(formData);
            });
        } else {
            ElNotify({
                type: "warning",
                message: "部门编号重复，请重新编号！",
            });
        }
    });
}
const vendorAddRef = ref<InstanceType<typeof AddVendor>>();
function saveVendor(formData: IVendorAddForm) {
    if (isErp.value) {
        const data = {
            vendCode: formData.vendorCode,
            vendName: formData.vendorName,
            vendType: formData.vendorType,
            vendConcat: formData.vendorManager,
            vendMobile: formData.vendorManagerPhone,
            VendWechartOrQQ: formData.concatWays,
        };
        request({
            url: `/api/ErpAssistingAccounting/AddVendor`,
            method: "post",
            data,
        }).then((res: IResponseModel<string>) => {
            if (res.state === 1000 && res.data) {
                ElNotify({ message: "亲，保存成功啦", type: "success" });
                useAssistingAccountingStore()
                    .getAssistingAccounting()
                    .then(() => {
                        getVendorList("1", data);
                        vendorAdd.value = false;
                        vendorCode.value = "";
                        vendorAddRef.value?.resetFields();
                    });
            } else {
                ElNotify({ message: res.msg || "保存失败", type: "warning" });
            }
        });
    } else {
        const data = {
            aaNum: formData.vendorCode,
            aaName: formData.vendorName,
            note: formData.note,
        };
        request({
            url: `/api/AssistingAccounting/Check?aaType=10002`,
            method: "post",
            data,
        }).then((res: IResponseModel<string>) => {
            if (res.data === "N") {
                saveVendorInner(data);
            } else if (res.data == "Name") {
                ElConfirm("亲，此名称已存在，是否继续保存？").then((r: boolean) => {
                    if (r) saveVendorInner(data);
                });
            } else {
                ElNotify({
                    type: "warning",
                    message: "供应商编号重复，请重新编号！",
                });
            }
        });
    }
}
function saveVendorInner(formData: any) {
    const entityParams = {
        type: "",
        adLevel0: "",
        adLevel1: "",
        adLevel2: "",
        adLevel3: "",
        address: "",
        contact: "",
        mobilePhone: "",
        taxNumber: "",
        note: formData.note,
    };
    const params = {
        aaNum: formData.aaNum,
        aaName: formData.aaName,
        uscc: "",
        status: 0,
        hasEntity: true,
        ifvoucher: true,
        entity: entityParams,
    };
    // 添加供应商辅助核算
    request({
        url: `/api/AssistingAccounting/Vendor`,
        method: "post",
        data: params,
    }).then(async (res: IResponseModel<string>) => {
        if (res.state === 1000 && res.data) {
            ElNotify({ message: "亲，保存成功啦", type: "success" });
            useAssistingAccountingStore()
                .getAssistingAccounting()
                .then(() => {
                    getVendorList("1", formData);
                    vendorAdd.value = false;
                    vendorCode.value = "";
                    vendorAddRef.value?.resetFields();
                });
        } else {
            ElNotify({ message: res.msg || "保存失败", type: "warning" });
        }
    });
}
const rules = reactive<FormRules>({
    FaNum: [{ required: true, message: "", trigger: "none" }],
    FaName: [{ required: true, message: "", trigger: "none" }],
    FaType: [{ required: true, message: "", trigger: "none" }],
    StartedDate: [{ required: true, message: "", trigger: "none" }],
    department: [{ required: true, message: "", trigger: "none" }],
});
let saveFlag = false;
function savedata() {
    if (saveFlag) return;
    saveFlag = true;
    if (!checkAssetsInput(addForm, accountingStandard, isAmortization.value)) {
        saveFlag = false;
        return;
    }
    if (isAmortization.value) {
        let sumRadio = 0;
        let hasNegativeRatio = false;
        let hasEmptyDepartment = false;
        let hasEmptyAsub = false;
        let hasEmptyRatio = false;
        addForm.amortizations.forEach((item) => {
            if (Number(item.ratio) < 0) hasNegativeRatio = true;
            if (!item.department_id) hasEmptyDepartment = true;
            if (!item.asub_id) hasEmptyAsub = true;
            if (item.department_id && item.asub_id) {
                if (!item.ratio) hasEmptyRatio = true;
                sumRadio += Number(item.ratio);
            }
        });
        if (hasEmptyDepartment) {
            saveFlag = false;
            ElNotify({
                type: "warning",
                message: "部门不能为空，请重新分配哦~",
            });
            return;
        }
        if (hasEmptyAsub) {
            saveFlag = false;
            ElNotify({
                type: "warning",
                message: "费用科目不能为空，请重新分配哦~",
            });
            return;
        }
        if (hasNegativeRatio) {
            saveFlag = false;
            ElNotify({
                type: "warning",
                message: "费用分摊比例不能为负数，请重新分配哦~",
            });
            return;
        }
        if (hasEmptyRatio) {
            saveFlag = false;
            ElNotify({
                type: "warning",
                message: "费用分摊比例不能为空，请重新分配哦~",
            });
            return;
        }
        if (sumRadio !== 100) {
            saveFlag = false;
            ElNotify({
                type: "warning",
                message: "费用分摊比例之和需为100%，请重新分配哦~",
            });
            return;
        }
    } else {
        if (!Number(addForm.amortizations[0].ratio)) {
            saveFlag = false;
            ElNotify({
                type: "warning",
                message: "比例不能为空，请重新选择部门哦~",
            });
            return;
        }
    }
    if (addForm.createdPeriod == 0) {
        ElNotify({
            type: "warning",
            message: "亲，出错了，请刷新页面重试或联系客服",
        });
        saveFlag = false;
        return;
    }
    const data = {
        ...addForm,
    };
    if (addForm.faNum === props.editData.fa_num) {
        //进入编辑，未改变资产编号
        saveFlag = false;
        SaveAssetData(data);
        return;
    }
    request({
        //进入编辑，改变资产编号，检查编号是否可用
        url: `/api/FixedAssets/CheckNum?fanum=${encodeURIComponent(addForm.faNum)}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.data === "N") {
                SaveAssetData(data);
            } else {
                if (res.data === "Y1" && props.activeName === "initFixedassets") {
                    ElAlert({
                        message: "亲，资产编号重复了，编号" + addForm.faNum + "已被资产列表使用,是否继续？",
                        modalClass: " "
                    }).then((r) => {
                        if (r) {
                            SaveAssetData(data);
                        }
                    });
                } else {
                    ElAlert({
                        message: "亲，资产编号重复了，是否继续？",
                        modalClass: " "
                    }).then((r) => {
                        if (r) {
                            SaveAssetData(data);
                        }
                    });
                }
            }
        })
        .finally(() => {
            saveFlag = false;
        });
}

function SaveAssetData(data: any) {
    if (Object.prototype.hasOwnProperty.call(data, "department")) {
        delete data.department;
    }
    data.amortizations = data.amortizations.map((item: any) =>
        Object.assign({}, item, { ratio: item.ratio ? item.ratio / 100 : item.ratio })
    );
    data.isNeedSaveToVoucherForAdd = true;
    data.isNeedSaveToVoucherForDelete = isNeedSaveToVoucherForDelete;
    request({
        url:
            props.activeName === "fixedassetsList"
                ? `/api/FixedAssets?faid=${props.faid}&pid=${props.pid}&isDataEditType=${isDataEditType.value}`
                : `/api/FixedAssets/Initial?faid=${props.faid}&pid=${props.pid}&isDataEditType=${isDataEditType.value}`,
        method: "put",
        data: JSON.stringify(data),
        headers: {
            "Content-Type": "application/json",
        },
    }).then((res: IResponseModel<Boolean>) => {
        saveFlag = false;
        if (res.state == 1000 && res.data) {
            ElNotify({
                type: "success",
                message: "保存成功",
            });
            resetEditSlotIsEditting();
            emits("divCancel");
        } else {
            ElNotify({
                type: "warning",
                message: res.msg || "保存失败",
            });
        }
    });
}
function divCancel() {
    resetEditSlotIsEditting();
    addForm.faType = formDataTemp.value ? formDataTemp.value.fa_type : "";
    emits("divCancel");
}

const faTypeList = ref<ISelectStrItem[]>([]);
//资产类别数据
const assetDataList = ref<IFixedAssetsType[]>();
function getFaTypeApi() {
    request({
        url: `/api/FixedAssetsType/List`,
        method: "get",
    }).then((res: IResponseModel<IFixedAssetsType[]>) => {
        if (res.state == 1000) {
            assetDataList.value = res.data;
            const showPart = props.activeName === "initFixedassets" && !props.samePid;
            if (isCanEdit.value || showPart) {
                faTypeList.value = res.data.reduce((prev: ISelectStrItem[], item: IFixedAssetsType) => {
                    if (props.editData.fa_property == item.fa_property) {
                        prev.push({
                            value: item.typeId + "",
                            label: item.typeNum + "-" + item.typeName,
                        });
                    }
                    return prev;
                }, []);
            } else {
                faTypeList.value = res.data.reduce((prev: ISelectStrItem[], item: IFixedAssetsType) => {
                    prev.push({
                        value: item.typeId + "",
                        label: item.typeNum + "-" + item.typeName,
                    });
                    return prev;
                }, []);
            }
        }
    });
}
function init() {
    addForm.faAsub = "";
    addForm.depreciationAsub = "";
    addForm.disposalAsub = "";
    addForm.costAsub = "";
    addForm.impairmentProvisionAsub = "";
}
//资产类别不同选择触发的资产数据
function AssetClassChange(val:string) {
    //计提折旧的 资产类别的改动不会影响折旧率和预计使用月份
    if (!isCanEdit.value) {
        assetDataList.value?.forEach((item: IFixedAssetsType) => {
            if (Number(val) === item.typeId) {
                addForm.netSalvageRate = String(item.netsalvageRate);
                addForm.useFullLife = String(item.serviceMonths);
                if (addForm.faProperty !== item.fa_property) {
                    addForm.faProperty = item.fa_property;
                    init();
                    getFixedAssetsAsubList(true);
                    getDeprecationAsubList(true);
                    getDisposalAsubList(true);
                    getDefaultCostAsub();
                    [AccountStandard.CompanyStandard, AccountStandard.FolkComapnyStandard].includes(accountingStandard) &&
                        getImpairmentProvisionAsubList(true);
                }
                if (item.fa_property === 0 && props.activeName === "fixedassetsList") {
                    addForm.depreciationNow = 0;
                } else {
                    addForm.depreciationNow = 1;
                }
            }
        });
    }
}

//部门
const departmentList = ref<Option[]>([]);
function focusDepartment() {
    const list = useAssistingAccountingStore().departmentList;
    departmentList.value = list.reduce((prev: Option[], item: IDepartment) => {
        if (item.aaeid > 0 && item.aaname !== "未录入辅助核算") {
            prev.push({
                id: item.aaeid,
                name: item.aaname,
            });
        }
        return prev;
    }, []);
}
function getDepartmentApi(flag?: string, addDepartmentData?: any) {
    const list = useAssistingAccountingStore().departmentList;
    departmentList.value = list.reduce((prev: Option[], item: IDepartment) => {
        if (item.aaname !== "未录入辅助核算") {
            prev.push({
                id: item.aaeid,
                name: item.aaname,
            });
        }
        return prev;
    }, []);
    if (flag && addDepartmentData) {
        const selectDepartment = list.find((item: IDepartment) => {
            return isErp.value ? item.aanum == addDepartmentData.departmentCode : item.aanum == addDepartmentData.aaNum;
        });
        addForm.department = selectDepartment
            ? [...addForm.department, selectDepartment.aaeid]
            : props.editData.faAmortizations.map((item: any) => item.department_id);
        addForm.department.length > 1 &&
            (addForm.amortizations = addForm.department.map((item) => {
                return {
                    department_id: item,
                    ratio: "",
                    asub_id: "",
                    asub_aae: "",
                };
            }));
    }
}
//业财环境的供应商
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingList");
const vendorListAll = computed<Array<IAssistingAccount>>(() => {
    return assistingAccountingList.value.reduce((prev: IAssistingAccount[], item: IDepartment) => {
        if (item.aaeid > 0 && item.aaname !== "未录入辅助核算" && item.aatype === 10002) {
            prev.push({ ...item });
        }
        return prev;
    }, []);
});
const vendorList = computed(() => {
    return vendorListAll.value.filter((item) => item.status === 0).map((item) => {
        return {
            value: isErp.value ? item.aaeid.toString() : item.aaname,
            label: item.aaname,
        };
    });
});
function getVendorList(flag?: string, addDepartmentData?: any) {
    const list = useAssistingAccountingStore().assistingAccountingList.filter((item) => item.aatype === 10002);
    if (flag && addDepartmentData) {
        const selectVendor = list.find((item: IDepartment) => {
            return item.aanum == addDepartmentData.aaNum;
        });
        let AddVendorVal = vendorList.value[0].value;
        vendorList.value.forEach((item) => {
            if (item.value > AddVendorVal) {
                AddVendorVal = item.value;
            }
        });
        if (isErp.value) {
            addForm.vendor = selectVendor ? selectVendor.aaeid?.toString() : AddVendorVal;
        } else {
            addForm.vendor = selectVendor ? selectVendor.aaname.toString() : vendorList.value[0].label;
        }
    } else {
        //初始加载会调getVendorList
        const vendor = isErp.value ? props.editData.vendor_id?.toString() : props.editData.vendor;
        addForm.vendor = vendor ? vendor : "";
    }
}

const faAsubRef = ref<InstanceType<typeof Select>>();
const deprecationAsubRef = ref<InstanceType<typeof Select>>();
const disposalAsubRef = ref<InstanceType<typeof Select>>();
const impairmentProvisionAsubRef = ref<InstanceType<typeof Select>>();
const costAsubRef = ref<InstanceType<typeof Select>>();

//点击资产科目、累计折旧科目、资产处置科目、资产减值准备科目时，则没有科目可选择
let isClickAsub = false;
function clickAsub() {
    if (isClickAsub) return;
    isClickAsub = true;
    if (addForm.faType === "") {
        let timer = setTimeout(() => {
            ElNotify({
                type: "warning",
                message: "请先选择资产类别哦~",
            });
            isClickAsub = false;
            clearTimeout(timer);
        }, 0);
        faAsubList.value = [];
        deprecationAsubList.value = [];
        disposalAsubList.value = [];
        impairmentProvisionAsubList.value = [];
    }
}

const auxiliaryDialogShow = ref(false);
const auxiliaryTitle = ref("");
const auxiliaryAsubName = ref("");
const auxiliaryCode = ref<string[]>([]);
const auxiliaryAllowNull = ref<string[]>([]);
const auxiliaryReadonly = ref(false);
const clickAsubId = ref();
const costAsubIndex = ref(0);
const auxiliaryId = ref();
function setAsubWithAAE(params: any) {
    console.log(auxiliaryTitle.value);
    switch (auxiliaryTitle.value) {
        case "资产科目":
            addForm.asubAAERelation.fa_aae = params.map((item: any) => item.id).join(",");
            addForm.faAsub = clickAsubId.value;
            break;
        case "累计折旧科目":
        case "累计摊销科目":
            addForm.asubAAERelation.depreciation_aae = params.map((item: any) => item.id).join(",");
            addForm.depreciationAsub = clickAsubId.value;
            break;
        case "资产处置科目":
            addForm.asubAAERelation.disposal_aae = params.map((item: any) => item.id).join(",");
            addForm.disposalAsub = clickAsubId.value;
            break;
        case "资产减值准备科目":
            addForm.asubAAERelation.impairment_provision_aae = params.map((item: any) => item.id).join(",");
            addForm.impairmentProvisionAsub = clickAsubId.value;
            break;
        case "折旧费用科目":
        case "摊销费用科目":
            !isAmortization.value && (addForm.costAsub = clickAsubId.value);
            addForm.amortizations[costAsubIndex.value].asub_aae = params.map((item: any) => item.id).join(",");
            addForm.amortizations[costAsubIndex.value].asub_id = clickAsubId.value;
            break;
    }
}

function clickCostAsub(costValue: string, index: number) {
    const asubInfo = costAsubList.value!.find((item) => item.asubId == costValue) as IAccountSubjectList;
    clickAsubOption(
        [-1, 0].includes(addForm.faProperty) ? "折旧费用科目" : "摊销费用科目",
        asubInfo.asubName,
        asubInfo.aatypes,
        asubInfo.asubId,
        index,
        asubInfo.aatypesAllowNull
    );
}

function clickAsubOption(title: string, name: string, aaTypes: string, value: any, costIndex?: number, aatypesAllowNull?: string) {
    costAsubIndex.value = costIndex ? costIndex : 0;
    if (!aaTypes) {
        switch (title) {
            case "资产科目":
                addForm.asubAAERelation.fa_aae = "";
                break;
            case "累计折旧科目":
            case "累计摊销科目":
                addForm.asubAAERelation.depreciation_aae = "";
                break;
            case "资产处置科目":
                addForm.asubAAERelation.disposal_aae = "";
                break;
            case "资产减值准备科目":
                addForm.asubAAERelation.impairment_provision_aae = "";
                break;
            case "折旧费用科目":
            case "摊销费用科目":
                addForm.amortizations[costAsubIndex.value].asub_aae = "";
                break;
        }
        return;
    }
    auxiliaryId.value = [];
    clickAsubId.value = value;
    auxiliaryTitle.value = title;
    auxiliaryAsubName.value = name;
    auxiliaryCode.value = aaTypes.includes(",")
        ? aaTypes.split(",").map((item) => {
              return item.split("_")[0];
          })
        : [aaTypes.split("_")[0]];
    if(["折旧费用科目", "摊销费用科目"].includes(title)) {
        auxiliaryAllowNull.value = auxiliaryCode.value.map((item) => {
            return aatypesAllowNull?.split(",").includes(item) ? "1" : "0";
        });
        console.log(aatypesAllowNull,auxiliaryAllowNull.value);
    }else{
        auxiliaryAllowNull.value = aaTypes.includes(",")
        ? aaTypes.split(",").map((item) => {
              return item.split("_")[1];
          })
        : [aaTypes.split("_")[1]];
    }
    switch (auxiliaryTitle.value) {
        case "资产科目":
            faAsubRef.value?.blur();
            auxiliaryId.value = addForm.asubAAERelation.fa_aae.split(",");
            break;
        case "累计折旧科目":
        case "累计摊销科目":
            deprecationAsubRef.value?.blur();
            auxiliaryId.value = addForm.asubAAERelation.depreciation_aae.split(",");
            break;
        case "资产处置科目":
            disposalAsubRef.value?.blur();
            auxiliaryId.value = addForm.asubAAERelation.disposal_aae.split(",");
            break;
        case "资产减值准备科目":
            impairmentProvisionAsubRef.value?.blur();
            auxiliaryId.value = addForm.asubAAERelation.impairment_provision_aae.split(",");
            break;
        case "折旧费用科目":
        case "摊销费用科目":
            costAsubRef.value?.blur();
            auxiliaryId.value = addForm.amortizations[costAsubIndex.value].asub_aae
                ? addForm.amortizations[costAsubIndex.value].asub_aae.split(",")
                : [];
            break;
    }
    auxiliaryDialogShow.value = true;
}

function expandAssit(title: string, asubName: string, id: string, value: any) {
    clickAsubId.value = value;
    auxiliaryDialogShow.value = true;
    auxiliaryTitle.value = title;
    auxiliaryAsubName.value = asubName;
    auxiliaryId.value = id.split(",");
    auxiliaryCode.value = id.split(",").map((item) => {
        if(item.includes("-")){
            return item.replace("-", "");
        }else{
            return useAssistingAccountingStore().getAssistingAccountingModel(Number(item))!.aatype + ""
        }
    });
    auxiliaryReadonly.value = isCanEdit.value;
}

//资产科目
const faAsubList = ref<IAsubSelect[]>([]);
function getFixedAssetsAsubList(flag?: boolean) {
    let asubUrl = "";
    switch (addForm.faProperty) {
        case 0:
            asubUrl = `/api/FixedAssets/GetFixedAssetsAsubList`;
            break;
        case 1:
            asubUrl = `/api/FixedAssets/GetAllLeafIntangibleAsubList`;
            break;
        case 2:
            asubUrl = `/api/FixedAssets/GetAllLeafLongTermDeferredExpensesAsubList`;
            break;
        case undefined:
            return;
    }
    request({
        url: asubUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        faAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
            prev.push({
                value: String(item.asubId),
                label: item.asubName,
                type: item.aatypes,
            });
            return prev;
        }, []);
        if (flag) {
            if (res.data[0].aatypes) {
                addForm.faAsub = "";
            } else {
                addForm.faAsub = faAsubList.value[0].value + "";
            }
        }
    });
}

//累计折旧科目
const deprecationAsubList = ref<IAsubSelect[]>([]);
function getDeprecationAsubList(flag?: boolean) {
    let depreciationUrl = "";
    switch (addForm.faProperty) {
        case 0:
            depreciationUrl = `/api/FixedAssets/GetDeprecationAsubList`;
            break;
        case 1:
            depreciationUrl = `/api/FixedAssets/GetAllLeafAccumulatedAmortizationAsub`;
            break;
    }
    request({
        url: depreciationUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        deprecationAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
            prev.push({
                value: String(item.asubId),
                label: item.asubName,
                type: item.aatypes,
            });
            return prev;
        }, []);
        if (flag) {
            if(res.data.length && !res.data[0].aatypes){
                addForm.depreciationAsub = deprecationAsubList.value[0].value + ""
            }else{
                addForm.depreciationAsub = "";
            }
        }
    });
}

//资产处置科目
const disposalAsubList = ref<IAsubSelect[]>([]);
function getDisposalAsubList(flag?: boolean) {
    let disposalUrl = "";
    switch (addForm.faProperty) {
        case 0:
            disposalUrl = `/api/FixedAssets/GetDisposalAsubList`;
            break;
        case 1:
            disposalUrl = `/api/FixedAssets/GetAllLeafIntangibleDisposalAsub`;
            break;
    }
    request({
        url: disposalUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        disposalAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
            prev.push({
                value: String(item.asubId),
                label: item.asubName,
                type: item.aatypes,
            });
            return prev;
        }, []);
        if (flag) {
            if (res.data[0].aatypes) {
                addForm.disposalAsub = "";
            } else {
                addForm.disposalAsub = disposalAsubList.value[0].value + "";
            }
        }
    });
}

//资产减值准备科目
const impairmentProvisionAsubList = ref<IAsubSelect[]>([]);
function getImpairmentProvisionAsubList(flag?: boolean) {
    let impairmentProvisionUrl = "";
    switch (addForm.faProperty) {
        case 0:
            impairmentProvisionUrl = `/api/FixedAssets/GetImpairmentProvisionAsubList`;
            break;
        case 1:
            impairmentProvisionUrl = `/api/FixedAssets/GetAllLeafIntangibleImpairmentAsub`;
            break;
    }
    request({
        url: impairmentProvisionUrl,
        method: "post",
    }).then((res: IResponseModel<IFAAsubDto[]>) => {
        if (res.state === 1000) {
            impairmentProvisionAsubList.value = res.data.reduce((prev: IAsubSelect[], item: IFAAsubDto) => {
                prev.push({
                    value: item.asubId + "",
                    label: item.asubName,
                    type: item.aatypes,
                });
                return prev;
            }, []);
            if (flag) {
                if(res.data.length && !res.data[0].aatypes){
                    addForm.impairmentProvisionAsub = impairmentProvisionAsubList.value[0].value + "";
                }else{
                    addForm.impairmentProvisionAsub = "";
                }
            }
        }
    });
}

//默认折旧费用科目
function getDefaultCostAsub(ischangeAmortization?: boolean) {
    request({
        url: `/api/FixedAssets/GetDefaultCostAsub?faProperty=${addForm.faProperty}`,
        method: "post",
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            if (!isAmortization.value) {
                addForm.costAsub = res.data.assit ? "" : String(res.data.asubId);
                ischangeAmortization && (addForm.amortizations[0].asub_id = addForm.costAsub);
            }
        }
    });
}
function changeCostAsub(value: string) {
    addForm.amortizations[0].asub_id = value;
}
//折旧费用科目
const costAsubList = ref<IAccountSubjectList[]>();
const costAsubAmList = ref<any[]>([]);

watchEffect(() => {
    const res = getCostAsubList(accountingStandard,addForm.faProperty);
    costAsubList.value = res.costAsubList as IAccountSubjectList[];
    costAsubAmList.value = res.costAsubAmList;
});
function handleInput(value: string, type: string) {
    const regex = /^\d+(?:\.\d{0,2})?/;
    const match = value.match(regex);
    switch (type) {
        case "value":
            addForm.value = match ? match[0] : "";
            break;
        case "accumlatedDepreciation":
            addForm.accumlatedDepreciation = match ? match[0] : "";
            break;
        case "accumlatedDepreciationTY":
            addForm.accumlatedDepreciationTY = match ? match[0] : "";
            break;
    }
}
//是否折旧可编辑
const isCanEdit = ref(false);
// 附件是否可编辑
const attachHasVoucher = ref(false);
async function canEdit() {
    await request({
        url: `/api/FixedAssets/CanEdit?faid=${props.faid}`,
        method: "post",
    }).then((res: IResponseModel<string>) => {
        if (res.state == 1000) {
            isCanEdit.value = false;
            if (res.data != "Y") {
                isCanEdit.value = true;
                attachHasVoucher.value = true;
                if (res.data == "C") {
                    ElNotify({
                        type: "warning",
                        message: "后期存在变更记录，部分字段无法修改！",
                    });
                } else if (res.data == "V") {
                    attachHasVoucher.value = false;
                    ElNotify({
                        type: "warning",
                        message: "已生成凭证，部分字段无法修改！",
                    });
                } else if (res.data == "D") {
                    ElNotify({
                        type: "warning",
                        message: "资产录入当期已计提折旧，部分字段无法修改！",
                    });
                } else if (res.data == "F") {
                    ElNotify({
                        type: "warning",
                        message: "该卡片已折旧，部分字段无法修改！",
                    });
                }
                isDataEditType.value = false;
            } else {
                isDataEditType.value = true;
                attachHasVoucher.value = false;
            }
        }
    }).catch(() => {
        attachHasVoucher.value = false;
    });
}

const disabledDateEnd = (time: Date) => {
    return time > new Date(props.maxDate);
};

watch(
    () => props.editData,
    async (val) => {
        if (val) {
            getVendorList();
            await canEdit();
            addForm.faProperty = props.editData.fa_property;
            Promise.all([
                getFaTypeApi(),
                getDepartmentApi(),
                getFixedAssetsAsubList(),
                getDeprecationAsubList(),
                getDisposalAsubList(),
                [AccountStandard.CompanyStandard, AccountStandard.FolkComapnyStandard].includes(accountingStandard) &&
                    getImpairmentProvisionAsubList(),
            ]).then(() => {
                entryPeriod.value = props.editData.created_period;
                createdDate.value = props.editData.created_date;
                formDataTemp.value = props.editData;
                addForm.faNum = props.editData.fa_num;
                addForm.createdPeriod =
                    props.periodList!.find((item: IPeriod) => item.periodInfo == props.editData.created_period)?.pid || 0;
                addForm.createdWay = props.editData.created_way;
                addForm.faName = props.editData.fa_name;
                addForm.faType = props.editData.fa_type;
                addForm.faModel = props.editData.fa_model;
                addForm.startedDate = props.editData.started_date;
                !window.isErp && (addForm.vendor = props.editData.vendor);
                addForm.usePeople = props.editData.use_people;
                addForm.location = props.editData.location;
                addForm.depreciationNow = props.editData.depreciation_now;
                addForm.depreciationType = props.editData.depreciation_type;
                addForm.faAsub = props.editData.fa_asub + "";
                addForm.depreciationAsub = props.editData.depreciation_asub + "";
                addForm.disposalAsub = props.editData.disposal_asub + "";
                addForm.impairmentProvisionAsub = String(props.editData.impairment_provision_asub);
                addForm.costAsub = props.editData.faAmortizations?.length > 1 ? "" : String(props.editData.cost_asub);
                addForm.value = Number(props.editData.value).toFixed(2);
                addForm.netSalvageRate = Number(props.editData.netsalvage_rate).toFixed(2);
                addForm.estimateResidualsRate = _.round((Number(addForm.value) * Number(addForm.netSalvageRate)) / 100, 2) + "";
                addForm.useFullLife = props.editData.usefullife;
                addForm.usedLife = props.editData.provisionmonth;
                addForm.remainingMonth = props.editData.usefullife - props.editData.provisionmonth + "";
                addForm.accumlatedDepreciation = props.editData.totaldeprecition
                    ? Number(props.editData.totaldeprecition).toFixed(2)
                    : props.editData.totaldeprecition;
                addForm.accumlatedDepreciationTY = props.editData.yeardeprecition
                    ? Number(props.editData.yeardeprecition).toFixed(2)
                    : props.editData.yeardeprecition;
                addForm.prevAccumulatedDepreciation = _.round(Number(props.editData.preyeardeprecition),2).toFixed(2)
                // 减值准备
                addForm.impairmentProvision = props.editData.impairment.toFixed(2);
                addForm.impairment = formatMoney(props.editData.impairment);
                addForm.fa_asub_name = props.editData.fa_asub_name;
                addForm.disposal_asub_name = props.editData.disposal_asub_name;
                addForm.cost_asub_name = props.editData.cost_asub_name;
                addForm.impairment_provision_asub_name = props.editData.impairment_provision_asub_name;
                addForm.depreciation_asub_name = props.editData.depreciation_asub_name;
                addForm.note = props.editData.note;
                addForm.amortizations = props.editData.faAmortizations.map((item: any) =>
                    Object.assign({}, item, { ratio: _.round(item.ratio * 100, 2), asub_id: item.asub_id + "" })
                );
                addForm.department = [...new Set(props.editData.faAmortizations.map((item: any) => item.department_id))] as number[];
                addForm.asubAAERelation = props.editData.faAAEModel;
                isAmortization.value = props.editData.faAmortizations?.length > 1;
                addForm.inputTax = Number(props.editData.input_tax).toFixed(2);
                addForm.attachFiles = props.editData.attachFiles;
                addForm.attachsCount = props.editData.attachsCount;
                addForm.monthDepreciationValue = props.editData.monthdeprecition.toFixed(2);
                isNeedSaveToVoucherForDelete = false;
                attachParentId = 0;
                attachFiles.value.length = 0;
            });
        }
    }
);

//折旧方法变成不折旧的时候 录入当期是否折旧单选框为不折旧 切不可点击
const isdepreciatedRadioFlag = ref(false);
watchEffect(() => {
    isdepreciatedRadioFlag.value = addForm.depreciationType == 0;
});

//当折旧方法变成不折旧的时候 录入当期是否折旧的值为否
watchEffect(() => {
    if (addForm.depreciationType == 0) {
        addForm.depreciationNow = 0;
    }
});

//预计残值
watchEffect(() => {
    if (Object.is(Number(addForm.value) * Number(addForm.netSalvageRate), NaN)) {
        addForm.estimateResidualsRate = "0";
    } else {
        addForm.estimateResidualsRate = _.round((Number(addForm.value) * Number(addForm.netSalvageRate)) / 100, 2) + "";
    }
});

//剩余使用月份
watchEffect(() => {
    if (Object.is(Number(addForm.useFullLife) - Number(addForm.usedLife), NaN)) {
        addForm.remainingMonth = "0";
    } else {
        addForm.remainingMonth = String(Number(addForm.useFullLife) - Number(addForm.usedLife));
    }
});

//以前年度累计折旧
watchEffect(() => {
    if (Object.is(Number(addForm.accumlatedDepreciation) - Number(addForm.accumlatedDepreciationTY), NaN)) {
        addForm.prevAccumulatedDepreciation = "0";
    } else {
        addForm.prevAccumulatedDepreciation = _.round(Number(addForm.accumlatedDepreciation) - Number(addForm.accumlatedDepreciationTY),2).toFixed(2) ;
    }
});

//月折旧额
watchEffect(() => {
    if (Number(addForm.useFullLife) - Number(addForm.usedLife) == 0) {
        addForm.monthDepreciationValue = 0;
    } else {
        if (
            Object.is(
                (Number(addForm.value) * (1 - Number(addForm.netSalvageRate) / 100) -
                    Number(addForm.accumlatedDepreciation) -
                    Number(addForm.impairmentProvision)) /
                    (Number(addForm.useFullLife) - Number(addForm.usedLife)),
                NaN
            )
        ) {
            addForm.monthDepreciationValue = 0;
        } else {
            addForm.monthDepreciationValue = isCanEdit.value ? addForm.monthDepreciationValue :  _.round(
                (Number(addForm.value) * (1 - Number(addForm.netSalvageRate) / 100) -
                    Number(addForm.accumlatedDepreciation) -
                    Number(addForm.impairmentProvision)) /
                    (Number(addForm.useFullLife) - Number(addForm.usedLife)),
                2
            );
        }
    }
});

//净值
watchEffect(() => {
    addForm.netValue = _.round(Number(addForm.value) - Number(addForm.accumlatedDepreciation), 2) + "";
});

//创建新部门
const departmentAddRef = ref<InstanceType<typeof AddDepartment>>();
function saveAs(formData: IDepartmentAddForm) {
    const entity = {
        manager: formData.departmentManager,
        mobilePhone: formData.departmentManagerPhone,
        startDate: formData.departmentEstablishDate,
        endDate: formData.departmentCancelDate,
        note: formData.departmentRemark,
    };
    const departmentData = {
        aaNum: formData.departmentCode,
        aaName: formData.departmentName,
        entity,
        hasEntity: Object.values(entity).some((value) => {
            return value !== null && value !== undefined && value !== "";
        }),
    };
    // 添加部门辅助核算
    request({
        url: `/api/AssistingAccounting/Department`,
        method: "post",
        data: departmentData,
    }).then(async (res: IResponseModel<string>) => {
        if (res.state === 1000 && res.data) {
            useAssistingAccountingStore().getAssistingAccounting();
            useAssistingAccountingStore()
                .getDepartment()
                .then(() => {
                    getDepartmentApi("1", departmentData);
                    departmentAdd.value = false;
                    departmentAddRef.value?.resetFields();
                });
        }
    });
}
//编辑状态处理
let editSlotInit = false;
const editSlotIsEditting = ref(false);
const getEditSlotIsEditting = () => {
    return editSlotIsEditting.value;
};
const resetEditSlotIsEditting = () => {
    editSlotInit = false;
    editSlotIsEditting.value = false;
    document.querySelector(".router-container")!.scrollTop = 0;
};
const venderRef = ref();
function handleVenderVisibleChange() {
    if (!isErp.value || !addForm.vendor) return;
    if (!vendorList.value.some((item) => item.value === props.editData.vendor_id.toString())) {
        const vendor = vendorListAll.value.find((item) => item.aaeid.toString() === addForm.vendor);
        if (vendor) {
            const input = venderRef.value?.getSelect()?.$el?.querySelector("input") as HTMLInputElement;
            nextTick().then(() => {
               input && (input.value = props.editData.vendor);
            })
        }
    }
}
const changeInit = (val: boolean) => {
    nextTick(() => {
        editSlotInit = val;
        handleVenderVisibleChange();
    });
};
watch(
    addForm,
    () => {
        if (!editSlotInit) return;
        editSlotIsEditting.value = true;
    },
    { deep: true }
);
defineExpose({
    getEditSlotIsEditting,
    changeInit,
});

//搜索无数据时，传入新增弹窗内的字段
const autoAddName = ref("");
//模糊搜索
watch(
    () => addForm.faProperty,
    () => {
        createWayList[1].label = addForm.faProperty === 1 ? "内部研发" : "在建工程转入";
        depreciationTypeList[1].label = addForm.faProperty === 0  ? "不折旧" : "不摊销";
    }, 
    {
        immediate: true
    }
);
const showfaTypeList = ref<Array<ISelectStrItem>>([]);
const showCreateWayList = ref<Array<any>>([]);
const showVendorList = ref<Array<any>>([]);
const showDepreciationTypeList = ref<Array<any>>([]);
const showfaAsubList = ref<Array<IAsubSelect>>([]);
const showdeAsubList = ref<Array<IAsubSelect>>([]);
const showdisAsubList = ref<Array<IAsubSelect>>([]);
const showimpAsubList = ref<Array<IAsubSelect>>([]);
const costAsubListAll = ref<Array<any>>([]);
const showcostAsubList = ref<Array<any>>([]);
const showdepartmentList = ref<Array<Option>>([]);
const showcostAsubAmList = ref<Array<any>>([]);
watchEffect(() => { 
    showCreateWayList.value = JSON.parse(JSON.stringify(createWayList));
    showDepreciationTypeList.value = JSON.parse(JSON.stringify(depreciationTypeList)); 
    showfaTypeList.value = JSON.parse(JSON.stringify(faTypeList.value));
    showVendorList.value = JSON.parse(JSON.stringify(vendorList.value)); 
    showfaAsubList.value = JSON.parse(JSON.stringify(faAsubList.value));
    showdeAsubList.value = JSON.parse(JSON.stringify(deprecationAsubList.value));   
    showdisAsubList.value = JSON.parse(JSON.stringify(disposalAsubList.value)); 
    showimpAsubList.value = JSON.parse(JSON.stringify(impairmentProvisionAsubList.value)); 
    showdepartmentList.value = JSON.parse(JSON.stringify(departmentList.value)); 
    showcostAsubAmList.value = JSON.parse(JSON.stringify(costAsubAmList.value));  
});
watchEffect(() => {
    costAsubListAll.value = costAsubList.value!.map((item) => {
        return {
            ...item,
            label: item.asubCode + ' ' + item.asubAAName,
        }
    });
    showcostAsubList.value = JSON.parse(JSON.stringify(costAsubListAll.value));
});
function faTypeFilterMethod(value: string) {
    showfaTypeList.value = commonFilterMethod(value, faTypeList.value, 'label');
}
function createWayFilterMethod(value: string) {
    showCreateWayList.value = commonFilterMethod(value, createWayList, 'label');
}
function vendorFilterMethod(value: string) {
    showVendorList.value = commonFilterMethod(value, vendorList.value, 'label');
    autoAddName.value = showVendorList.value.length === 0 ? value.trim() : "";
}
function depreciationTypeFilterMethod(value: string) {
    showDepreciationTypeList.value = commonFilterMethod(value, depreciationTypeList, 'label');
}
function faAsubFilterMethod(value: string) {
    showfaAsubList.value = commonFilterMethod(value, faAsubList.value, 'label');
}
function deAsubFilterMethod(value: string) {
    showdeAsubList.value = commonFilterMethod(value, deprecationAsubList.value, 'label');
}
function disAsubFilterMethod(value: string) {
    showdisAsubList.value = commonFilterMethod(value, disposalAsubList.value, 'label');
}
function costAsubFilterMethod(value: string) {
    showcostAsubList.value = commonFilterMethod(value, costAsubListAll.value, 'label');
}
function impAsubFilterMethod(value: string) {
    showimpAsubList.value = commonFilterMethod(value, impairmentProvisionAsubList.value, 'label');
}
function departFilterMethod(value: string) {
    showdepartmentList.value = commonFilterMethod(value, departmentList.value, 'name');
}
const costAsubVisibleChange = (visible: boolean) => {
    if (visible) {
        showcostAsubAmList.value = JSON.parse(JSON.stringify(costAsubAmList.value));
    }
}
</script>

<style scoped lang="less">
:deep(.el-textarea__inner) {
    position: absolute;
    top: -32px;
    width: 160px;
    z-index: 1000;
}
.edit-asset {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    & .divEdit {
        background-color: var(--white);
        margin: 0 auto;
        overflow: hidden;

        & .block-title {
            padding: 20px 20px 15px 20px;
            color: var(--font-color);
            font-size: var(--h3);
            line-height: 22px;
            font-weight: bold;
            text-align: left;

            & #createtime {
                color: var(--weaker-font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                font-weight: normal;
                float: right;
            }
        }

        & .block-main {
            & .isRow {
                padding-left: 30px;
                & :deep(.el-form-item) {
                    text-align: left;
                    & .el-form-item__label {
                        width: 138px;
                        padding: 0px;
                        justify-content: flex-start;
                    }
                    &.non-required .el-form-item__label {
                        padding-left: 10px;
                    }
                    & .el-form-item__content {
                        // line-height: 31px;
                        width: 160px;
                        padding-bottom: 5px;
                        padding-right: 25px;
                    }
                }
            }
        }

        & .buttons {
            margin-top: 20px;
            margin-bottom: 40px;
            text-align: center;
            border: 0;
        }

        .el-radio.el-radio--large {
            height: 32px;
        }
    }
}

.apportionment-table {
    width: 624px;
    text-align: left;
    padding-left: 30px;
    padding-bottom: 30px;
    .apportionment-title {
        padding-bottom: 10px;
    }
    :deep(.custom-table tbody tr td .cell input[type="text"]) {
        border: none;
    }
    :deep(.el-select-v2__wrapper) {
        &.is-disabled {
            background-color: var(--el-disabled-bg-color);
            .el-select-v2__placeholder {
                color: var(--el-disabled-color);
            }
        }
        .el-select-v2__placeholder {
            text-align: left;
            width: calc(100% - 22px);
            margin-inline-start:6px;
        }
    }
}

.jtzj {
    & :deep(.el-form-item__content) {
        padding-right: 15px !important;
    }
}

:deep(.el-form-item.is-error .el-input__wrapper) {
    box-shadow: 0 0 0 1px var(--main-color);
}

:deep(.el-form-item__error) {
    display: none;
}

.form-label-pl-10 {
    :deep(.el-form-item__label) {
        padding-left: 10px !important;
    }
    &.apportion {
        :deep(.el-form-item__content) {
            padding-left: 20px;
        }
    }
  
}

:deep(.el-select-dropdown__list) {
    max-height: 200px;
}

:deep(.el-select-dropdown__item) {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word;
    white-space: normal;
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;

    & span {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

:deep(.el-form-item.is-error .el-input__wrapper) {
    box-shadow: 0 0 0 1px #c0c4cc;

    &.is-focus {
        box-shadow: 0 0 0 1px var(--main-color);
    }
}

:deep(.el-input),
:deep(.el-input--small) {
    .el-input__wrapper {
        padding: 1px 10px;
    }

    .el-input__inner {
        font-size: var(--el-form-label-font-size);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

:deep(.el-date-editor) {
    & .el-input__prefix {
        position: absolute;
        right: 0;
    }
    & .el-input__suffix {
        position: absolute;
        right: 20px;
    }
}
.attach-file-center {
    padding-left: 80px;
    width: 100%;
    display: flex;
    box-sizing: border-box;
    .upload-item {
        height: 70px;
        width: 240px;
        border: 1px solid var(--border-color);
        cursor: pointer;
        text-align: left;
        .upload-item-title {
            margin: 10px 0 0 70px;
            img {
                height: 14px;
                width: 14px;
            }
        }
        .upload-item-tip {
            margin: 10px 0 0 60px;
            font-size: 12px;
            color: var(--menu-font-color);
        }
    }
}
.line {
    height: 0.5px;
}
.cell-flex {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
