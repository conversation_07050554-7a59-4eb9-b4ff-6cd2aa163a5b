export interface IAccountSetItem {
    accountingStandard: string;
    appAsId: string;
    asCurrentDate: string;
    asCurrentMonth: string;
    asCurrentYear: string;
    asDeleteBy: string;
    asDeleteDate: string | null;
    asDeleteDay: number;
    asDeleteMonth: number;
    asDeleteYear: number;
    asDestroyBy: string;
    asDestroyDate: string | null;
    asDestroyDay: number;
    asDestroyMonth: number;
    asDestroyYear: number;
    asId: number;
    asIndustry: string;
    asName: string;
    asStartDate: string;
    asStartMonth: string;
    asStartYear: string;
    asubLength: string;
    cashJournal: string;
    checkNeeded: string;
    companyName: string;
    createBy: number;
    createDate: string;
    currentAs: number;
    fixedAsset: string;
    industryName: string;
    isChangedStartDate: boolean;
    isDisabled: boolean;
    lockState: boolean;
    modifyBy: number;
    modifyDate: string;
    needLockPassword: boolean;
    permission: string;
    permissionFunctionCode: string[];
    permissionName: string[];
    phoneList: null;
    showScm: string;
    subAccountingStandard: string;
    taxAdId: number;
    taxNumberS: string;
    taxPayerName: string;
    taxPayerPassword: string;
    taxType: string;
    taxpayerIdentificationNumber: string;
    unifiedNumber: string;
    usedBy: number;
    usedDate: string;
}

export interface ICopyParams {
    copyContent: number;
    periodYear: number;
    periodMonth: number;
    checkCopyFiles: boolean;
}

export interface ICopyAccountSetResult {
    oldAsid: string;
    newAsid: string;
    createdTime: string;
}

export interface ICreateAccountSetResult {
    urlTarget: string;
    newAsid: number;
}

export interface IAccountingStandard {
    value: number;
    text: string;
}

export interface IperiodList {
    [key: number]: Iperiod[];
}

export interface Iperiod {
    asid: number;
    pid: number;
    isActive: boolean;
    year: number;
    sn: number;
    startDate: string;
    endDate: string;
    status: number;
    fastatus: number;
}

export interface IYearList {
    [key: number]: number[];
}

export interface IAccountSetSwitchResult {
    status: number;
    appAsid: string;
}

export interface IAccountSetUpgradeResult {
    needBuySpace: boolean;
    oldAsId: number;
    urlTarget: string;
    newAsId: number;
    copyAsId: number;
    copyAsCreatedDate: string;
}

export interface ITaxArea {
    taxAreaId: number;
    taxAreaName: string;
}

export interface ILoginTable {
    [key :string]: any;
}