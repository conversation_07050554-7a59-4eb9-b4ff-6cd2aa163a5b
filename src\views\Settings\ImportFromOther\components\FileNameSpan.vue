<template>
    <div class="file-name-container">
        <span class="file-name-content">{{ fileNameContent }}</span>
        <span class="file-name-extension">{{ fileNameExtension }}</span>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
    fileName: {
        type: String,
        required: true,
    },
});

const fileNameContent = computed(() => {
    return props.fileName.substring(0, props.fileName.lastIndexOf(".") + 1);
});

const fileNameExtension = computed(() => {
    return props.fileName.substring(props.fileName.lastIndexOf(".") + 1);
});
</script>

<style lang="less" scoped>
.file-name-container {
    display: flex;

    .file-name-content {
        white-space: nowrap; /* 禁止换行 */
        overflow: hidden; /* 溢出部分隐藏 */
        text-overflow: ellipsis; /* 使用省略号显示溢出部分 */
        max-width: 185px; /* 设置容器宽度，根据实际情况调整 */
    }
    // .file-name-extension {
        // width: 15px;
    // }
}
</style>
