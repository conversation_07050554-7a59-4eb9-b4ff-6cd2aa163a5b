<template>
    <div :class="{ content: true, 'narrow-content': isNarrowContent }">
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <el-select v-model="searchInfo.type" style="width: 86px; margin-right: 10px" @change="handleSearch">
                        <el-option label="月报" value="month"></el-option>
                        <el-option label="季报" value="quarter"></el-option>
                    </el-select>
                    <PaginationPeriodPicker v-model="searchInfo.pid" v-show="searchInfo.type === 'month'"></PaginationPeriodPicker>
                    <PaginationPeriodPicker
                        v-model="searchInfo.quarterId"
                        v-show="searchInfo.type === 'quarter'"
                        :isQuarter="true"
                    ></PaginationPeriodPicker>
                    <span class="ml-10">报表说明</span>
                    <el-popover placement="right-start" :width="750" trigger="hover">
                        <template #reference>
                            <span class="question-icon"> </span>
                        </template>
                        <div class="statement-explain">
                            <div>标准现金流量表只是现金流量报表的另一种编制方式，您可以根据自身业务情况选择使用哪种编制方式哦~</div>
                            <span class="vedio mt-10" @click="handleToHelp">视频帮助</span>
                            <div class="mt-10">三种编制方法说明：</div>
                            <div class="mt-5">
                                <span class="highlight-orange">现金流量表辅助核算法：</span>
                                开启指定科目的现金流辅助核算后，填制凭证时，可对每笔凭证指定对应现金流量项目。优点是数据准确；缺点是操作较为复杂，填制资金类凭证时，都需要选择现金流辅助核算。
                            </div>
                            <div class="mt-5">
                                <span class="highlight-orange">现金流量表公式法：</span>
                                报表数据根据系统预设公式自动生成（如需查看公式，您可以点击报表项目金额栏的“=”符号查看）。优点是操作简单、省时省力；缺点是内置公式无法修改，修改数据需要手动调整报表底稿。
                            </div>
                            <div class="mt-5 mb-5">
                                <span class="highlight-orange">标准现金流量表：</span>
                                根据资金类凭证的非现金及现金等价物科目，预设现金流量的流入流出项目，并自动生成现金流量表。优点是可单独调整每笔凭证的现金流量项目，数据相对准确；缺点是操作较为复杂，需要根据每笔凭证的具体情况判断现金流量项目。
                            </div>
                        </div>
                    </el-popover>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <el-checkbox
                        v-model="searchInfo.isTax"
                        :label="
                            accountingStandard === AccountingStandard.LittleCompanyStandard ? '显示上年累计金额' : '显示上年同期累计金额'
                        "
                        @change="handleTaxSearch"
                    ></el-checkbox>
                    <el-checkbox
                        class="mr-10"
                        v-show="searchInfo.type === 'quarter'"
                        v-model="searchInfo.isFullYear"
                        label="显示本年所有季度"
                        @change="handleFullYearSearch"
                    ></el-checkbox>
                    <a class="button mr-10 large" @click="changeCurrentRoute" v-permission="['cashflowstatement-canedit']">现金流量调整</a>
                    <a class="button mr-10" v-permission="['cashflowstatement-canexport']" @click="handleExport">导出</a>
                    <Dropdown :btnTxt="'打印'" class="mr-10" :downlistWidth="102" v-permission="['cashflowstatement-canprint']">
                        <li @click="handlePrint(0,getSearchParams())">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button" v-permission="['cashflowstatement-canshare']" @click="handleShare">分享</a>
                </div>
            </div>
            <div class="main-center">
                <Table 
                    :loading="loading" 
                    :data="tableData" 
                    :columns="columns" 
                    :cell-class-name="resetCellClassName" 
                    :scrollbar-show="true"
                    :tableName="setModule"
                >
                    <template #lineName>
                        <el-table-column 
                            :show-overflow-tooltip="false" 
                            label="项目" 
                            min-width="400px" 
                            align="left" 
                            header-align="center"
                            prop="lineName"
                            :width="getColumnWidth(setModule, 'lineName')"
                        >
                            <template #default="scope">
                                <span :class="formatLineNameClass(scope.row)">
                                    {{ scope.row.lineName ?? "" }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="标准现金流量表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getSearchParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "StandardCashFlowStatement",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpen, globalWindowOpenPage } from "@/util/url";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { formatMoney } from "@/util/format";
import { hasNumberTitle } from "@/views/Statements/CashFlowStatement/utils";
import { share } from "@/views/Statements/utils";
import { useAccountSetStore } from "@/store/modules/accountSet";
import usePrint from "@/hooks/usePrint";

import { AccountingStandard } from "@/api/accountSet";
import type { IColumnProps } from "@/components/Table/IColumnProps";

import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import Table from "@/components/Table/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "StardartCash";
interface ITableData {
    lineName: string;
    lineNumber: number;
    lineType: number;
    amount: number;
    totalAmount: number;
    qutar1: number;
    qutar2: number;
    qutar3: number;
    qutar4: number;
}

const periodStore = useAccountPeriodStore();
const accountingStandard = useAccountSetStore()!.accountSet!.accountingStandard;

const searchInfo = reactive({
    type: "month",
    isTax: false,
    pid: Number(periodStore.getCurrentPeriod()),
    quarterId: Number(periodStore.getCurrentPeriod()),
    isFullYear: false,
});
const loading = ref(false);
const tableData = ref<Array<ITableData>>([]);
const emptyText = ref("暂无数据");

const columns = ref<Array<IColumnProps>>([]);
const isNarrowContent = ref(true);
function changeColumns() {
    const defaultColumns: Array<IColumnProps> = [
        { slot: "lineName" },
        {
            label: "行次",
            prop: "lineNumber",
            minWidth: 60,
            align: "left",
            headerAlign: "left",
            formatter: function (row: any, column: any, value) {
                if (value === 0) return "";
                return value;
            },
            width: getColumnWidth(setModule, 'lineNumber')
        },
        {
            label: "本年累计金额",
            prop: "totalAmount",
            minWidth: 250,
            align: "right",
            headerAlign: "right",
            formatter: function (row: any, column: any, value: number) {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, 'totalAmount')
        },
    ];
    if (searchInfo.type === "month") {
        columns.value = [
            ...defaultColumns,
            {
                label: searchInfo.isTax
                    ? `上年${accountingStandard === AccountingStandard.LittleCompanyStandard ? "" : "同期"}累计金额`
                    : "本期金额",
                prop: "amount",
                minWidth: 250,
                align: "right",
                headerAlign: "right",
                formatter: function (row: any, column: any, value: number) {
                    return formatMoney(value);
                },
                resizable: false,
            },
        ];
    } else {
        if (!searchInfo.isFullYear) {
            columns.value = [
                ...defaultColumns,
                {
                    label: searchInfo.isTax
                        ? `上年${accountingStandard === AccountingStandard.LittleCompanyStandard ? "" : "同期"}累计金额`
                        : "本季金额",
                    prop: "amount",
                    minWidth: 250,
                    align: "right",
                    headerAlign: "right",
                    formatter: function (row: any, column: any, value: number) {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ];
        } else {
            columns.value = [...defaultColumns, ...getQuarterColumns()];
        }
    }
    isNarrowContent.value = !(searchInfo.type === "quarter" && searchInfo.isFullYear);
}
function getQuarterColumns() {
    const quarterList = ["第一季度", "第二季度", "第三季度", "第四季度"];
    const list: Array<IColumnProps> = quarterList.map((item, index) => {
        return {
            label: item,
            prop: "qutar" + (index + 1),
            minWidth: 120,
            align: "right",
            headerAlign: "right",
            formatter: function (row: any, column: any, value: number) {
                return formatMoney(value);
            },
            width: getColumnWidth(setModule, "qutar" + (index + 1)),
            resizable: (index + 1) !== 4
        };
    });
    return list;
}

const isFullYear = ref(false);
function handleTaxSearch(isTax: any) {
    searchInfo.isTax = !!isTax;
    searchInfo.isTax && (searchInfo.isFullYear = false);
    handleSearch();
}
function handleFullYearSearch(isFullYear: any) {
    searchInfo.isFullYear = !!isFullYear;
    searchInfo.isFullYear && (searchInfo.isTax = false);
    handleSearch();
}
async function handleSearch() {
    updatePrintApi("/api/StandardCashFlowStatement/Print" + (searchInfo.type === "quarter" ? "Quarter" : ""))
    const params = getSearchParams();
    let urlPath = "/api/StandardCashFlowStatement";
    if (searchInfo.type === "quarter") {
        urlPath += "/Quater";
    }
    loading.value = true;
    await request({ url: urlPath, method: "get", params })
        .then((res: IResponseModel<Array<ITableData>>) => {
            isFullYear.value = searchInfo.type === "quarter" && searchInfo.isFullYear;
            changeColumns();
            loading.value = false;
            if (res.state !== 1000) {
                tableData.value = [];
                emptyText.value = "暂无数据";
                ElNotify({ type: "warning", message: res.msg });
                return;
            }
            tableData.value = res.data;
            emptyText.value = "";
        })
        .catch(() => {
            loading.value = false;
            tableData.value = [];
            emptyText.value = "暂无数据";
            ElNotify({ type: "warning", message: "获取数据失败" });
        });
}

const { printDialogVisible, handlePrint, printInfo, otherOptions, updatePrintApi } = usePrint(
    "standardCashFlowStatement",
    "/api/StandardCashFlowStatement/Print" + (searchInfo.type === "quarter" ? "Quarter" : ""),
    {},
    false,
    false,
);
function handleExport() {
    const params = getSearchParams();
    let url = "/api/StandardCashFlowStatement/Export";
    if (searchInfo.type === "quarter") {
        url += "Quarter";
    }
    globalExport(url + "?" + getUrlSearchParams(params));
}
function handleShare() {
    const params = getSearchParams();
    let url = "/api/StandardCashFlowStatement/Share";
    if (searchInfo.type === "quarter") {
        url += "Quarter";
    }
    request({
        url: url + "?" + getUrlSearchParams(params),
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "分享失败，请联系客服或管理员～" });
                return;
            }
            const shareReportHost =
                window.accountSrvHost +
                "/api/WxPay/MakeQRCode.ashx?data=" +
                window.shareReportHost +
                "/ShareReport/" +
                res.data +
                "&CurrentSystemType=1";
            share(shareReportHost);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "分享失败，请联系客服或管理员～" });
        });
}
function getSearchParams() {
    const params = {
        isTax: searchInfo.isTax,
        isFullYear: searchInfo.isFullYear,
        pid: searchInfo.pid,
    };
    if (searchInfo.type === "quarter") {
        params.pid = searchInfo.quarterId;
    }
    return params;
}

function changeCurrentRoute() {
    globalWindowOpenPage("/Statements/CashFlowAdjustment", "现金流量调整");
}
watch([() => searchInfo.pid, () => searchInfo.quarterId], () => {
    handleSearch();
});
async function tryInitBaseData() {
    await request({ url: "/api/StandardCashFlowStatement/TryInitBaseData", method: "post" });
}
onMounted(async () => {
    await tryInitBaseData();
    handleSearch();
});

function formatLineNameClass(row: ITableData) {
    if (row.lineType === 0 || hasNumberTitle(row.lineName) || ["1", "2", "3"].some((item) => row.lineName.trim().startsWith(item))) {
        return "level1";
    } else {
        return "level2";
    }
}

function handleToHelp() {
    globalWindowOpen("https://help.ningmengyun.com/#/jz/videoPlayer?qType=*********");
}

function resetCellClassName(data: { row: any; column: any; rowIndex: number; columnIndex: number }) {
    const { row, column, columnIndex } = data;
    if (columnIndex > 1 && row[column.property] < 0) return "highlight-red";
    return "";
}
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.main-tool-right {
    a.button.large {
        padding: 0 10px;
    }
}
.main-center {
    flex: 1 !important;
    :deep(.custom-table) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .highlight-red {
            .cell {
                color: var(--red);
            }
        }
    }
}
.question-icon {
    display: inline-block;
    height: 16px;
    width: 16px;
    margin-top: 2px;
    margin-left: 2px;
    background-image: url(@/assets/Settings/question.png);
    background-size: 100% 100%;
}
.vedio {
    display: inline-block;
    padding-left: 20px;
    line-height: 16px;
    background-image: url(@/assets/Icons/video.png);
    background-size: 16px 15px;
    background-repeat: no-repeat;
    background-position: left center;
    cursor: pointer;
}
</style>
