<template>
    <div class="content">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="slot-content">
                    <div class="title">收支类别</div>
                    <div class="main-content">
                        <el-tabs v-model="tabName" :class="isErp ? 'erp-content' : ''">
                            <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.name">
                                <div class="main-top main-tool-bar space-between">
                                    <template v-if="tab.name === 10050 ? incomeShow : expendShow">
                                        <div class="main-tool-left">
                                            <span class="mr-10">输入编码或名称：</span>
                                            <form @submit.prevent="handleSubmit">
                                                <el-input
                                                    v-if="tab.name === 10050"
                                                    type="text"
                                                    v-model="searchTextIncome"
                                                    placeholder=" "
                                                    autocomplete="on"
                                                    name="ietypeIncome"
                                                    clearable
                                                />
                                                <el-input
                                                    v-else
                                                    type="text"
                                                    v-model="searchTextExpenditure"
                                                    placeholder=" "
                                                    autocomplete="on"
                                                    name="ietypeExpenditure"
                                                    clearable
                                                />
                                                <button type="submit" class="button solid-button ml-10">查询</button>
                                            </form>
                                        </div>
                                        <div class="main-tool-right">
                                            <a class="button mr-10" @click="handleImport" v-permission="['ietype-canedit']">导入</a>
                                            <a class="button mr-10" @click="handleExport" v-permission="['ietype-canedit']">导出</a>
                                            <a class="solid-button" @click="handleNew(emptyRow)" v-permission="['ietype-canedit']">新增</a>
                                            <RefreshButton></RefreshButton>
                                        </div>
                                    </template>
                                </div>
                                <div class="main-center">
                                    <Table
                                        :data="tab.name === 10050 ? tableDataIncome : tableDataExpend"
                                        :columns="columns"
                                        :loading="loading"
                                        :page-is-show="true"
                                        :layout="tab.name === 10050 ? paginationData.layout : paginationExpend.layout"
                                        :page-sizes="tab.name === 10050 ? paginationData.pageSizes : paginationExpend.pageSizes"
                                        :page-size="tab.name === 10050 ? paginationData.pageSize : paginationExpend.pageSize"
                                        :total="tab.name === 10050 ? paginationData.total : paginationExpend.total"
                                        :currentPage="tab.name === 10050 ? paginationData.currentPage : paginationExpend.currentPage"
                                        :scrollbar-show="true"
                                        @size-change="(size) => sizeChangeHandle(size, tab.name)"
                                        @current-change="(current) => currentPageChangeHandle(current, tab.name)"
                                        @refresh="refreshHandle(tab.name)"
                                        :tableName="setModule"
                                    >
                                        <template #code>
                                            <el-table-column
                                                :label="tab.name === 10050 ? '收入编码' : '支出编码'"
                                                min-width="100px"
                                                align="left"
                                                header-align="left"
                                                prop="code"
                                                :width="getColumnWidth(setModule, 'code')"
                                            >
                                                <template #default="scope">
                                                    <span :class="scope.row.subsubkey > 0 ? 'pl-10' : ''">{{ scope.row.value1 }}</span>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #name>
                                            <el-table-column
                                                :label="tab.name === 10050 ? '收入名称' : '支出名称'"
                                                min-width="180px"
                                                align="left"
                                                header-align="left"
                                                prop="name"
                                                :width="getColumnWidth(setModule, 'name')"
                                            >
                                                <template #default="scope">
                                                    <span :class="scope.row.subsubkey > 0 ? 'pl-10' : ''">{{ scope.row.value2 }}</span>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #matchKey>
                                            <el-table-column
                                                :min-width="cashFlowDisplay ? '250px' : '500px'"
                                                align="left"
                                                header-align="left"
                                                prop="value3"
                                                :width="getColumnWidth(setModule, 'value3')"
                                            >
                                                <template #header>
                                                    <el-popover
                                                        :popper-style="{ fontSize: '12px' }"
                                                        placement="right-start"
                                                        :width="280"
                                                        trigger="hover"
                                                        content="当日记账的摘要中有对应关键字时，可以自动匹配带出收支类别哦~"
                                                    >
                                                        <template #reference>
                                                            <div style="width: 104px">
                                                                智能匹配关键字<img
                                                                    src="@/assets/Cashier/ietype_head_help.png"
                                                                    style="height: 14px; cursor: pointer; margin-left: 7px"
                                                                />
                                                            </div>
                                                        </template>
                                                    </el-popover>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #status>
                                            <el-table-column 
                                                label="启用状态" 
                                                min-width="100px" 
                                                align="center" 
                                                header-align="left"
                                                prop="status"
                                                :width="getColumnWidth(setModule, 'status')"
                                            >
                                                <template #default="scope">
                                                    <el-switch
                                                        v-model="scope.row.num3"
                                                        :active-value="'0'"
                                                        :inactive-value="'1'"
                                                        :before-change="handleBeforeChangeStage"
                                                        @change="handleChangeState($event, scope.row)"
                                                    >
                                                    </el-switch>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <template #operation>
                                            <el-table-column label="操作" min-width="200px" align="left" header-align="left" :resizable="false">
                                                <template #default="scope">
                                                    <a
                                                        class="link"
                                                        v-if="scope.row.subsubkey === '0' && scope.row.num3 === '0'"
                                                        v-permission="['ietype-canedit']"
                                                        @click="handleNew(scope.row)"
                                                    >
                                                        新增
                                                    </a>
                                                    <a class="link" v-permission="['ietype-canedit']" @click="handleEdit(scope.row)">
                                                        编辑
                                                    </a>
                                                    <a
                                                        class="link"
                                                        v-if="!scope.row.haschild"
                                                        v-permission="['ietype-candelete']"
                                                        @click="handleDelete(scope.row)"
                                                    >
                                                        删除
                                                    </a>
                                                </template>
                                            </el-table-column>
                                        </template>
                                    </Table>
                                </div>
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
            </template>
            <template #add>
                <div class="slot-content align-center">
                    <div class="slot-title">{{ tabName === 10050 ? "收入类别" : "支出类别" }}</div>
                    <div class="slot-mini-content">
                        <AddForm
                            ref="addFormRef"
                            :tabName="tabName"
                            :cashFlowDisplay="cashFlowDisplay"
                            :getEditType="getEditType"
                            @save-success="refreshTableData"
                            @cancel="backToMain"
                        />
                    </div>
                </div>
            </template>
        </ContentSlider>
        <ImportSingleFileDialog
            :importTitle="tabName === 10050 ? '收入类别导入' : '支出类别导入'"
            v-model:import-show="importDialogDisplay"
            :importUrl="`/api/IEType/Import?ieType=${tabName}&autoAdd=${autoAdd}`"
            :uploadSuccess="uploadSuccess"
        >
            <template #download>
                1、下载收入类别模板，并按照模板格式进行数据整理<a class="link ml-20" @click="handleDownloadTemplate">下载模板</a>
            </template>
            <template #bottom-tips>
                <div class="import-tip">
                    新增下级收入类别时，当上级收入类别已使用，是否同时新增同名下级类别？
                    <el-radio-group v-model="autoAdd">
                        <el-radio :label="true">是</el-radio>
                        <el-radio :label="false">否</el-radio>
                    </el-radio-group>
                </div>
            </template>
        </ImportSingleFileDialog>
        <DialogTip 
            ref="DialogTipRef"
            @confirm="handleConfirm"
            :tip-title="tipTitle"
            :isLargeTipTitle="true"
            :tipTitleLeft="true"
        >
            <div class="mt-20" style="text-align: left;">
                <el-checkbox v-if="typeStatus === '1'" v-model="notTipClose" label="不再提示" />
                <el-checkbox v-else v-model="notTipOpen" label="不再提示" />
            </div>
        </DialogTip>
    </div>
</template>

<script lang="ts">
export default {
    name: "IEType",
};
</script>
<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, computed, watch } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams, globalExport } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { getNextIECode } from "./utils";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { useAccountSetStore, AccountStandard } from "@/store/modules/accountSet";
import { getGlobalLodash } from "@/util/lodash";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableItem, ISearchBack } from "./types";
import type { IIETypeItem } from "@/views/Cashier/type";

import Table from "@/components/Table/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import AddForm from "./components/AddForm.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { checkPermission } from "@/util/permission";
import { getGlobalToken } from "@/util/baseInfo";
import DialogTip from "@/components/Dialog/DialogTip/index.vue";

const setModule = "IEType";
const isErp = ref(window.isErp);

const addFormRef = ref<InstanceType<typeof AddForm>>();

const cashFlowDisplay = computed(() => {
    const showList = [AccountStandard.CompanyStandard, AccountStandard.LittleCompanyStandard, AccountStandard.FolkComapnyStandard];
    return showList.includes(useAccountSetStore()!.accountSet?.accountingStandard as unknown as AccountStandard);
});

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const {
    paginationData: paginationExpend,
    handleCurrentChange: handleCurrentExpend,
    handleSizeChange: handleSizeExpend,
    handleRerefresh: handleRerefreshExpend,
} = usePagination();
function currentPageChangeHandle(val: number, tabs: 10050 | 10060) {
    tabs === 10050 ? handleCurrentChange(val) : handleCurrentExpend(val);
}
function sizeChangeHandle(val: number, tabs: 10050 | 10060) {
    tabs === 10050 ? handleSizeChange(val) : handleSizeExpend(val);
}
function refreshHandle(tabs: 10050 | 10060) {
    tabs === 10050 ? handleRerefresh() : handleRerefreshExpend();
}

const cashFlowSelectList = computed(() => {
    const list = useAssistingAccountingStore().assistingAccountingList;
    return list.filter((item) => item.aatype === 10007 && item.aaeid !== 31);
});
const columns = computed(() => {
    const baseColumns: Array<IColumnProps> = [
        { slot: "code" },
        { slot: "name" },
        { slot: "matchKey" },
        {
            label: "关联现金流",
            prop: "num2",
            align: "left",
            headerAlign: "left",
            minWidth: 250,
            width: getColumnWidth(setModule, 'num2'),
            formatter(row, column, value) {
                return cashFlowSelectList.value.find((item) => item.aaeid === ~~value)?.aaname || "";
            },
        },
        { slot: "status"},
        { slot: "operation" },
    ];
    if (!cashFlowDisplay.value) baseColumns.splice(3, 1);
    return baseColumns;
});

const handleBeforeChangeStage = () => {
    if (!checkPermission(["ietype-canedit"])) {
        ElNotify({ type: "warning", message: "您没有启用/禁用收支类别的权限，请联系管理员" });
        return false;
    }
    notTipClose.value = localStorage.getItem("hiddenDialogIETypeClose-" + getGlobalToken()) === "true";
    notTipOpen.value = localStorage.getItem("hiddenDialogIETypeOpen-" + getGlobalToken()) === "true";
    return true;
};
const DialogTipRef = ref<InstanceType<typeof DialogTip>>();
const tipTitle = ref("");
const notTipOpen = ref(false);  //开启不在提示
const notTipClose = ref(false); //关闭不在提示
const typeStatus = ref("1"); //记录当前是开启0还是关闭1
let changeStateRow: ITableItem;
const handleChangeState = (value: string, row: ITableItem) => {
    row.num3 = row.num3 === '0' ? '1' : '0';
    changeStateRow = cloneDeep(row);
    const msg = tabName.value === 10050 ? "收入" : "支出";
    typeStatus.value = value;
    if (row.haschild === 1) { //类别有子类别
        if (value === '1' && !notTipClose.value) {
            tipTitle.value = `亲，禁用一级${msg}类别会一并禁用其下级类别，是否确认禁用？`;
            DialogTipRef.value?.showDialog();
            return;
        } 
        if (value === '0' && !notTipOpen.value) {
            tipTitle.value = `亲，启用一级${msg}类别会一并启用其下级类别，是否确认启用？`;
            DialogTipRef.value?.showDialog();
            return;
        }
    }
    updateStatus();
};
const handleConfirm = () => {
    handleNoTipsChange();
    updateStatus();
}
const handleNoTipsChange = () => {
    const key = (typeStatus.value === "1" ? "hiddenDialogIETypeClose-" : "hiddenDialogIETypeOpen-")+ getGlobalToken();
    const val = typeStatus.value === "1" ? notTipClose.value : notTipOpen.value;
    localStorage.setItem(key, String(val));
}
function updateStatus() {
    changeStateRow.num3 = changeStateRow.num3 === '0' ? '1' : '0';
    let params = {
        ieType: tabName.value,
        ieId: Number(changeStateRow.subkey),
        state: Number(changeStateRow.num3),
    };
    request({ 
        url: "/api/IEType/ChangeState", 
        method: "post",
        params: params,
    }).then((res: IResponseModel<string>) => {
        if (res.state == 1000 && res.data) {
            ElNotify({
                type: "success",
                message: (changeStateRow.num3 === "0" ? "启用" : "禁用") + "成功",
            });
            refreshTableData();
        } else {
            ElNotify({ type: "warning", message: res.msg || "操作失败了，请联系侧边栏客服" });
        }
    });
}

const currentSlot = ref("main");
const slots = ["main", "add"];
const loading = ref(false);

const tabName = ref<10050 | 10060>(10050);
const tabs: Array<{ label: string; name: 10050 | 10060 }> = [
    { label: "收入", name: 10050 },
    { label: "支出", name: 10060 },
];

const incomeShow = ref(true);
const expendShow = ref(true);
function handleSubmit(event: Event) {
    event.preventDefault();
    if (tabName.value === 10050) {
        incomeShow.value = false;
        nextTick().then(() => {
            incomeShow.value = true;
        });
        searchText1.value = searchTextIncome.value;
        paginationData.currentPage === 1 ? getTableList() : (paginationData.currentPage = 1);
    } else {
        expendShow.value = false;
        nextTick().then(() => {
            expendShow.value = true;
        });
        searchText2.value = searchTextExpenditure.value;
        paginationExpend.currentPage === 1 ? getTableList() : (paginationExpend.currentPage = 1);
    }
}

const searchText1 = ref("");
const searchText2 = ref("");
const searchTextIncome = ref("");
const searchTextExpenditure = ref("");
const tableDataIncome = ref<ITableItem[]>([]);
const tableDataExpend = ref<ITableItem[]>([]);
async function getTableList(type?: 10050 | 10060) {
    const ieType = type || tabName.value;
    const params = {
        ieType,
        ieName: ieType === 10050 ? searchText1.value.trim() : searchText2.value.trim(),
        pageIndex: ieType === 10050 ? paginationData.currentPage : paginationExpend.currentPage,
        pageSize: ieType === 10050 ? paginationData.pageSize : paginationExpend.pageSize,
    };
    loading.value = true;
    await request({ url: "/api/IEType/PagingList?" + getUrlSearchParams(params) })
        .then((res: IResponseModel<ISearchBack>) => {
            loading.value = false;
            if (res.state === 1000) {
                if (ieType === 10050) {
                    tableDataIncome.value = res.data.data;
                    paginationData.total = res.data.total;
                } else {
                    tableDataExpend.value = res.data.data;
                    paginationExpend.total = res.data.total;
                }
            }
        })
        .catch(() => {
            loading.value = false;
        });
}

let type: "" | "Edit" | "New" = "";
function getEditType() {
    return type;
}
const emptyRow = ref({
    as_id: "",
    mainkey: "",
    subkey: "",
    value1: "",
    value2: "",
    value3: "",
    description: "",
    created_by: "",
    created_date: "",
    modified_by: "",
    modified_date: "",
    subsubkey: "0",
    haschild: 0,
    num1: "",
    num2: "",
    num3: "0",
});
const { cloneDeep, isEqual } = getGlobalLodash();
function handleNew(empty: ITableItem) {
    const row = cloneDeep(empty);
    type = "New";
    if (row.subkey) {
        request({
            url: "/api/IEType/PreCheckAdd?parentIeId=" + Number(row.subkey),
            method: "post",
        }).then((res: IResponseModel<string>) => {
            if (res.data === "ALERT") {
                ElConfirm(`上级收支类别已使用，保存后，将同时新增同名下级类别 "${row.value2 + "1"}"代替, 您确定继续新增吗？`).then(
                    (r: boolean) => {
                        r && handleNewAdd(row.subkey, row.value2);
                    }
                );
            } else if (res.data === "SUCCESS") {
                handleNewAdd(row.subkey, row.value2);
            }
        });
    } else {
        handleNewAdd(row.subkey);
    }
}
function handleNewAdd(val: string, name?: string) {
    getNextIECode(tabName.value.toString()).then((res) => {
        addFormRef.value?.editSearchInfo({ ie_no: res, ie_parentid: Number(val) }, name);
        currentSlot.value = "add";
    });
}
function handleEdit(row: ITableItem) {
    type = "Edit";
    const params = {
        ie_id: row.subkey,
        ie_no: row.value1,
        ie_name: row.value2,
        ie_keyword: row.value3,
        cashFlow: ~~row.num2 === 0 ? "" : (~~row.num2).toString(),
        state: Number(row.num3),
    };
    if (row.subsubkey === "0") {
        addFormRef.value?.editSearchInfo(params);
    } else {
        const parentName = getParentName(row.subsubkey);
        addFormRef.value?.editSearchInfo(params, parentName);
    }
    currentSlot.value = "add";
}
function getParentName(parentId: string) {
    const parent = allIETypeList.value.find((item) => item.subkey === parentId);
    return parent ? parent.value2.replace(/^(收-|支-)/, "") : "全部类别";
}
function handleDelete(row: ITableItem) {
    let result = ref<ITableItem[]>([]);
    if (Number(row.subsubkey) > 0) {
        if (row.mainkey === "10050") {
            result.value = tableDataIncome.value.filter((item) => item.subkey === row.subsubkey);
        } else {
            result.value = tableDataExpend.value.filter((item) => item.subkey === row.subsubkey);
        }
    }
    ElConfirm("亲，确认要删除吗?").then((r: any) => {
        if (r) {
            request({
                url: "/api/IEType/PreCheckDel?ieid=" + row.subkey,
                method: "post",
            })
                .then((res: IResponseModel<string>) => {
                    if (res.data === "ALERT") {
                        return ElConfirm(
                            `该收支类别已有数据，删除后，本类别的数据将转入上级类别 "${result.value[0].value1}${result.value[0].value2}"，您确定继续删除吗?`
                        );
                    } else {
                        return Promise.resolve(true);
                    }
                })
                .then((confirmResult: boolean) => {
                    confirmResult && handleDeleteRequest(row.subkey);
                });
        }
    });
}
function handleDeleteRequest(ieId: string) {
    request({ url: "/api/IEType?ieId=" + ieId, method: "delete" })
        .then((res: any) => {
            if (res.state == 1000) {
                if (res.data === "Success") {
                    ElNotify({ type: "success", message: "删除成功" });
                    const pagination = tabName.value === 10050 ? paginationData : paginationExpend;
                    const length = tabName.value === 10050 ? tableDataIncome.value.length : tableDataExpend.value.length;
                    if (length === 1) {
                        pagination.currentPage > 1 ? (pagination.currentPage = pagination.currentPage - 1) : getTableList();
                    } else {
                        getTableList();
                    }
                    getAllIEType();
                } else if (res.data === "USE") {
                    ElNotify({ type: "warning", message: "此类别已在日记账或票据中使用，无法删除哦！" });
                } else if (res.data == "LAST") {
                    const typeName = tabName.value === 10050 ? "收入" : "支出";
                    ElNotify({ type: "warning", message: "至少保留一个" + typeName + "类别哦！" });
                } else {
                    ElNotify({ type: "warning", message: "删除失败了，请联系侧边栏客服" });
                }
            } else {
                ElNotify({ type: "warning", message: "删除失败" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "删除失败" });
        });
}

const allIETypeList = ref<Array<IIETypeItem>>([]);
function getAllIEType() {
    request({ url: "/api/IEType/List" }).then((res: IResponseModel<{ rows: Array<IIETypeItem> }>) => {
        if (res.state === 1000) {
            allIETypeList.value = res.data.rows;
        }
    });
}
function refreshTableData() {
    window.dispatchEvent(new CustomEvent("reloadIETypeList", { detail: { type: "", journalType: "" } }));
    getTableList();
    getAllIEType();
    backToMain();
}
function backToMain() {
    currentSlot.value = "main";
    addFormRef.value?.resetForm();
    type = "";
}
async function handleInit() {
    await getTableList(10050);
    await getTableList(10060);
    getAllIEType();
}
async function reloadIETypeList(e: any) {
    if (useRouterArrayStoreHook().routerArray.find((item) => item.title.includes("收支类别"))) {
        const event: CustomEvent = e;
        if (event.detail.type === "" && event.detail.journalType === "") return;
        if (event.detail.type === "10050" || event.detail.type === "10060") {
            const ieType = Number(event.detail.type) as 10050 | 10060;
            loading.value = true;
            await getTableList(ieType);
            getAllIEType();
            loading.value = false;
        }
    }
}

const importDialogDisplay = ref(false);
const autoAdd = ref(false);
function handleImport() {
    importDialogDisplay.value = true;
}

function handleExport() {
    const ieName = tabName.value === 10050 ? searchText1.value.trim() : searchText2.value.trim();
    globalExport(`/api/IEType/Export?ieType=${tabName.value}&ieName=${ieName}`);
}
function handleDownloadTemplate() {
    globalExport("/api/IEType/ExportImportTemplate?ieType=" + tabName.value);
}
function uploadSuccess(res: IResponseModel<{ result: string; error: string } | null>) {
    if (res.state !== 1000 || !res.data || res.data.result !== "Success") {
        ElNotify({ type: "warning", message: res.msg || "导入失败" });
        return;
    }
    ElNotify({ type: "success", message: "导入成功" });
    importDialogDisplay.value = false;
    getTableList();
}

onMounted(() => {
    handleInit();
    window.addEventListener("reloadIETypeList", reloadIETypeList);
});
onUnmounted(() => {
    window.removeEventListener("reloadIETypeList", reloadIETypeList);
});

watch(
    [
        () => paginationData.currentPage,
        () => paginationData.pageSize,
        () => paginationData.refreshFlag,
        () => paginationExpend.pageSize,
        () => paginationExpend.currentPage,
        () => paginationExpend.refreshFlag,
    ],
    () => {
        getTableList();
    }
);
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
.main-top {
    :deep(.el-input) {
        width: 150px;
        height: 32px;
    }
}
.main-center {
    :deep(.el-table) {
        .el-scrollbar__view {
            min-height: 441px;
        }
        .el-popper.is-light {
            width: auto;
            text-align: left;
        }
    }
}
.slot-content {
    height: 100%;
    .slot-mini-content {
        width: 1000px;
        margin-top: 32px;
        border: 1px solid var(--slot-title-color);
    }
    :deep(.el-tabs) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .el-tabs__content {
            flex: 1;
        }
        .el-tab-pane {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        &.erp-content {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .el-tabs__content {
                flex: 1;
                display: flex;
                flex-direction: column;
                .main-center {
                    flex: 1;
                    .el-tab-pane {
                        height: 100%;
                        .el-scrollbar__view {
                            min-height: 0px;
                        }
                    }
                }
            }
        }
    }
}
.import-tip {
    margin-top: 20px;
    margin-left: 40px;
    padding-left: 20px;
    background-image: url("@/assets/Icons/warn.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    background-position: 0 2px;
}
</style>
