<template>
    <div class="autoBankInfo" v-show="!isHideBarcode">
        <span>如有疑问，请</span>
        <span class="consulting link">
            咨询客服
            <img src="@/assets/Cashier/yinqihulianzaixianzixun.png" alt="咨询客服" v-show="!isProSystem && !isErp" />
            <img src="@/assets/Cashier/yinqihulianzaixianzixun_pro.jpg" alt="咨询客服" v-show="isProSystem" />
            <img src="@/assets/Cashier/yinqihulianzaixianzixun_erp.jpg" alt="咨询客服" v-show="isErp" />
        </span>
        <span>，咨询时间：周一至周日 8:30-12:00,13:30-20:30</span>
    </div>
    <div class="report-img" v-show="!isHideBarcode">
        <img src="@/assets/Icons/top-img.png" />
    </div>
</template>
<script setup lang="ts">
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { ref } from "vue";

const isProSystem = ref(window.isProSystem);
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
</script>
