import { useUserInfoStore } from "@/store/modules/userInfo"
import { getLocalStorage, setLocalStorage } from "@/utils/localStorageOperate"
import { getAppasid } from "@/utils/url"
import dayjs from "dayjs"

const userInfoStore = useUserInfoStore()

export const setDialogNotMindLocalStorage = (dialogId: string) => {
  const userId = userInfoStore.userInfo?.userId || ""
  const asId = getAppasid()
  const yearMonth = dayjs().format("YYYYMM")
  const localStorageKey = `${dialogId}_${userId}_${asId}_${yearMonth}`
  setLocalStorage(localStorageKey, "1")
}

export const getDialogNotMindLocalStorage = (dialogId: string) => {
  const userId = userInfoStore.userInfo?.userId || ""
  const asId = getAppasid()
  const yearMonth = dayjs().format("YYYYMM")
  const localStorageKey = `${dialogId}_${userId}_${asId}_${yearMonth}`
  return getLocalStorage(localStorageKey)
}

// 辅助函数：计算字段总和
export const sumField = (rows: any[], fieldName: string): string => {
  return rows
    .reduce((sum: number, row: any) => {
      const value = parseFloat(row[fieldName] || "0")
      return sum + value
    }, 0)
    .toFixed(2)
}
