export type SortType = 1 | 0 | -1;

export function tableHeaderCustomSort(event: MouseEvent) {
    // descending 降序 ascending 升序
    const sortItems = document.querySelectorAll(".header-caret");
    if (!sortItems || sortItems.length === 0) return false;
    const dom = event.target as HTMLElement;
    const index = Array.from(sortItems).indexOf(dom);
    let sortType: SortType = 0;
    for (let i = 0; i < sortItems.length; i++) {
        if (i === index) continue;
        sortItems[i].classList.remove("ascending");
        sortItems[i].classList.remove("descending");
    }
    if (!dom.classList.contains("ascending") && !dom.classList.contains("descending")) {
        dom.classList.add("descending");
        sortType = -1;
    } else if (dom.classList.contains("descending")) {
        dom.classList.remove("descending");
        dom.classList.add("ascending");
        sortType = 1;
    } else {
        dom.classList.remove("ascending");
        dom.classList.remove("descending");
        sortType = 0;
    }
    return sortType;
}
