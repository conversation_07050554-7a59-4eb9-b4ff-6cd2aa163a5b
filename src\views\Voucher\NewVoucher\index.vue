<template>
    <div class="content" :class="{ 'zoom-out': zoomState === 'out', 'zoom-in': zoomState === 'in' }" ref="contentRef">
        <div class="title">{{ vgName }}</div>
        <div class="main-content">
            <div class="main-center">
                <Voucher
                    v-model:queryParams="voucherQueryParams"
                    @zoom="zoomCallback"
                    @load-success="voucherLoadSuccess"
                    @voucher-changed="voucherChanged"
                    @reset-v-date-and-v-num="resetVDateAndVNum"
                    ref="voucher"
                    :edited="edited"
                    :new-voucher="newVoucher"
                    @save="saveVoucher('save')"
                    @saveAndNew="saveVoucher('saveAndAdd')"
                    :load-voucher-template="loadVoucherTemplate"
                    :load-voucher-draft="loadVoucherDraft"
                    :showCancel="pageType === 'newVoucher' ? false : true"
                    :cancelTxt="getCanEditVoucher() ? '取消' : '返回'"
                    @back="cancel" 
                    @delete-voucher="deleteVoucher"
                    :showKeysTip="pageType === 'newVoucher'"
                    :showSwitchBtn="true"
                    :switchInfo="switchInfo"
                    @preVoucher="loadVoucherSwitchInfo(1)"
                    @nextVoucher="loadVoucherSwitchInfo(2)"
                    :module-permission="props.fcode"
                ></Voucher>
            </div>
        </div>
        <EasyRecommendDialog ref="EasyRecommendDialogRef"></EasyRecommendDialog>
    </div>
</template>
<style lang="less" scoped>
@import "@/style/SelfAdaption.less";

.content {
    min-width: calc(1000px + 108px);

    .main-content {
        width: auto;
        height: auto;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        // height: calc(var(--voucher-min-height) + 1px + 68px);
        // height: ~"max(calc(var(--voucher-min-height) + 1px + 68px), calc(100vh - var(--content-padding-bottom) - var(--title-height)))";

        .main-top {
            position: relative;
            z-index: 2;
            height: 80px;
            align-self: stretch;
            padding: 0;
            display: flex;
            align-items: flex-start;
            padding: 20px 54px;
            align-items: center;
            height: auto;

            .main-tool-left,
            .main-tool-right {
                display: flex;
                align-items: center;
                position: relative;
                z-index: 1;
            }
            .main-tool-middle.hide-client-header {
                flex:1;
                height: 30px;
                background-color: red;
                -webkit-app-region: drag ;
                -webkit-user-select: none ;
            }
            .main-tool-left {
                margin-top: 30px;
                margin-top: 0;

                .dropdown-button {
                    margin-left: 10px;

                    :deep(li) {
                        text-align: left;
                    }
                }
            }

            .main-tool-right {
                margin-top: 40px;
                margin-top: 0;

                .prev-btn,
                .next-btn {
                    height: 17px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    .btn-text {
                        padding: 0 10px;
                        line-height: 16px;
                    }

                    .btn-icon {
                        width: 17px;
                        height: 17px;
                        background-image: url(/src/assets/Voucher/prev.png);
                        background-repeat: no-repeat;
                        background-size: 100%;
                    }
                }

                .prev-btn {
                    margin-left: 24px;
                    .btn-icon {
                        background-image: url("@/assets/voucher/prev.png");
                    }

                    &:hover {
                        color: var(--main-color);
                        .btn-icon {
                            background-image: url("@/assets/voucher/prev-hover.png");
                        }
                    }

                    &.disabled {
                        color: #999;
                        .btn-icon {
                            background-image: url("@/assets/voucher/prev-disabled.png");
                        }
                    }
                }

                .next-btn {
                    margin-left: 20px;
                    .btn-icon {
                        background-image: url("@/assets/voucher/next.png");
                    }
                    .btn-text {
                        padding-right: 0;
                    }

                    &:hover {
                        color: var(--main-color);
                        .btn-icon {
                            background-image: url("@/assets/voucher/next-hover.png");
                        }
                    }

                    &.disabled {
                        color: #999;
                        .btn-icon {
                            background-image: url("@/assets/voucher/next-disabled.png");
                        }
                    }
                }
            }

            .main-tool-center {
                font-weight: 600;
                color: var(--font-color);
                line-height: 22px;
                font-size: var(--h3);
                padding-top: 5px;
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                bottom: 0;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .line {
            background-color: var(--title-split-line);
            align-self: stretch;
        }

        .main-center {
            padding: 0;
            // height: 0;
            // flex: 1;
            overflow: initial;
        }
    }

    &.zoom-in {
        .main-content {
            .main-top {
                width: 1050px;
                align-self: center;
            }
        }
    }
}

.voucher-settings-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .voucher-settings-content {
        padding: 40px 60px;
        display: flex;
        flex-direction: column;

        .voucher-settings-item {
            display: flex;
            align-items: center;

            .item-title {
                width: 200px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
            }

            & + .voucher-settings-item {
                margin-top: 16px;
            }
        }
    }

    .buttons {
        padding: 10px;
        text-align: center;
        border-top: 1px solid var(--border-color);
    }
}

body[erp] {
    .content {
        .main-content {
            .main-top {
                padding: 20px 74px;

                .main-tool-right {
                    .keyboard-block {
                        .keyboard-icon {
                            background: none;
                        }

                        .keyboard-content {
                            color: var(--main-color);
                        }

                        &:hover {
                            .keyboard-content {
                                color: var(--main-color);
                            }
                        }
                    }

                    .next-btn:not(.disabled) {
                        &:hover .btn-icon {
                            background-image: url("@/assets/voucher/next-hover-erp.png");
                        }
                    }

                    .prev-btn:not(.disabled) {
                        &:hover .btn-icon {
                            background-image: url("@/assets/voucher/prev-hover-erp.png");
                        }
                    }
                }
            }
        }
    }
}

body[erp] .custom-confirm .el-dialog__body .buttons {
    display: flex;
    justify-content: flex-end;
}
</style>
<script lang="ts">
export default {
    name: "NewVoucher",
};
</script>
<script setup lang="ts">
import { onDeactivated, onMounted, onUnmounted, ref, watch, nextTick, reactive, computed } from "vue";
import Voucher from "@/components/Voucher/index.vue";
import { deleteVoucherQuickKeycode, setVoucherQuickKeycode, handleCheckHasDialog } from "./utils";
import {
    DataVoucherQueryParams,
    EditVoucherQueryParams,
    LoadVoucherDraftQueryParams,
    LoadVoucherTemplateQueryParams,
    NewVoucherQueryParams,
    VoucherModel,
    VoucherSaveModel,
    VoucherSwitchInfoModel,
    DraftSaveParams,
    VoucherSaveParams,
    VoucherQueryParams,
    VoucherAttachFileModel,
    CopyVoucherQueryParams,
    OffsetVoucherQueryParams,
} from "@/components/Voucher/types";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getGlobalToken } from "@/util/baseInfo";
import { ElConfirm, ElAlert } from "@/util/confirm";
import { useRoute, useRouter } from "vue-router";
import { closeCurrentTab, globalWindowOpenPage, reloadPeriodInfo } from "@/util/url";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useVoucherGroupStoreHook } from "@/store/modules/voucherGroup";
import { getGlobalLodash } from "@/util/lodash";
import { onActivated } from "vue";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import EasyRecommendDialog  from "../components/EasyRecommendDialog/index.vue";
import { erpNotifyTypeEnum, erpNotify } from "@/util/erpUtils";

const isErp = ref(window.isErp)
const routerArrayStore = useRouterArrayStoreHook();
const periodStore = useAccountPeriodStore();
const route = useRoute();
const router = useRouter();
const pageType = ref<"voucherPage" | "newVoucher" | "errorPage">();
const voucherQueryParams = ref<VoucherQueryParams>();
const lastVoucherDate = ref("");
const currentFullPath = ref(route.fullPath);
const currentPath = ref(route.path);

const _ = getGlobalLodash();
const props = defineProps({
    pid: {
        type: Number,
        default: 0,
    },
    vid: {
        type: Number,
        default: 0,
    },
    fcode: {
        type: String,
        default: "",
    },
    from: {
        type: String,
        default: "",
    },
    isTemp: {
        type: Boolean,
        default: false,
    },
});
const voucher = ref<InstanceType<typeof Voucher>>();
function initPageState() {
    if (props.pid && props.vid) {
        pageType.value = "voucherPage";
        voucherQueryParams.value = new EditVoucherQueryParams(Number(props.pid), Number(props.vid));
        voucher.value?.setIsTempVoucher(props.isTemp);
        watch(
            () => `${props.pid}-${props.vid}`,
            () => {
                voucherQueryParams.value = new EditVoucherQueryParams(Number(props.pid), Number(props.vid));
                voucher.value?.setIsTempVoucher(props.isTemp);
            }
        );
    } else {
        pageType.value = "newVoucher";
        initVoucher();
    }
}
function initVoucher() {
    let queryParams: VoucherQueryParams;
    if (route.query.vtid) {
        queryParams = new LoadVoucherTemplateQueryParams(Number(route.query.vtid));
    } else if (route.query.vdid && route.query.pid) {
        queryParams = new LoadVoucherDraftQueryParams(Number(route.query.pid), Number(route.query.vdid));
    } else if (route.query.vcid && route.query.pid) {
        queryParams = new CopyVoucherQueryParams(Number(route.query.pid), Number(route.query.vcid));
    } else if (route.query.vrid && route.query.pid) {
        queryParams = new OffsetVoucherQueryParams(Number(route.query.pid), Number(route.query.vrid));
    } else {
        queryParams = new NewVoucherQueryParams();
    }
    if (route.query.fileIds) {
        request({
            url: "/api/Voucher/GetVoucherAttachFileModelList",
            method: "post",
            params: { attachFileIds: route.query.fileIds },
        }).then((res: IResponseModel<Array<VoucherAttachFileModel>>) => {
            if (res.state === 1000) {
                if (queryParams instanceof NewVoucherQueryParams) {
                    (queryParams as NewVoucherQueryParams).attachFileIds = route.query.fileIds?.toString() || "";
                    (queryParams as NewVoucherQueryParams).attachFiles = res.data;
                }
                if (queryParams instanceof LoadVoucherTemplateQueryParams) {
                    (queryParams as LoadVoucherTemplateQueryParams).attachFileIds = route.query.fileIds?.toString() || "";
                    (queryParams as LoadVoucherTemplateQueryParams).attachFiles = res.data;
                }
            }
            voucherQueryParams.value = queryParams;
            // 清除 queryParams 对象引用，释放内存
            queryParams = null as any;
        });
    } else {
        voucherQueryParams.value = queryParams;
        queryParams = null as any;
    }
}
// 返回跳转前的页面
const returnPreviousPage = ref(false);
let hasConfirmDialog = false;
onActivated(() => {
    let currentPath = currentFullPath.value;
    if(isErp.value && currentPath.includes("&jxcst")){
        let params = currentPath.split('?')[1].split('&');
        let newParams = params.filter(param => !param.startsWith("jxcst="));
        currentPath = currentPath.split('?')[0] + "?" + newParams.join('&');
    }
    if (pageType.value === "newVoucher") {
        if (route.fullPath !== currentPath.toString()) {
            if (!edited.value) {
                initVoucher();
                currentFullPath.value = route.fullPath;
            } else {
                if(!hasConfirmDialog){
                    hasConfirmDialog = true;
                    ElConfirm(
                    "<div style='text-align: left; margin: 0 -10px;'>您之前编辑的凭证还没有保存<br/>点击'编辑原凭证'则可继续编辑原凭证<br>点击'进入新凭证'则原凭证将不会保存并进入新凭证页面</div>",
                    false,
                    () => {
                        // 点右上角关闭回到原页面
                        returnPreviousPage.value = true;
                        if(isErp.value){
                            //业财暂时关闭，后续讨论通信如何实行
                            // erpNotify(erpNotifyTypeEnum.erpBack,{back:true})
                        }else{
                            history.back();
                        }
                    },
                    "提示",
                    {
                        confirmButtonText: "编辑原凭证",
                        cancelButtonText: "进入新凭证",
                    },
                    undefined,
                    undefined,
                    undefined,
                    true
                ).then((r) => {
                    if (r) {
                        routerArrayStore.replaceCurrentRouter(currentFullPath.value);
                    } else {
                        if (returnPreviousPage.value) {
                            returnPreviousPage.value = false;
                        } else {
                            initVoucher();
                            voucher.value?.closeDialog();
                        }
                        currentFullPath.value = route.fullPath;
                    }
                    hasConfirmDialog = false;

                });
                }
            }
        }
        handleSetSave();
    }
});
const contentRef = ref<HTMLElement>();
function handleSetSave() {
    setVoucherQuickKeycode({
        save: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            voucher.value?.handleSave();
        },
        saveAndAdd: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            saveVoucher("saveAndAdd");
        },
        newVoucher: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            voucher.value?.defaultNewVoucher();
        },
        lastVoucher: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            if (cantDelete.value) return;
            if (switchInfo.value.hasPrev === false) {
                ElNotify({ type: "warning", message: "亲，没有上一张凭证了~" });
                return;
            }
            loadVoucherSwitchInfo(1, false);
        },
        nextVoucher: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            if (cantDelete.value) return;
            if (switchInfo.value.hasNext === false) {
                ElNotify({ type: "warning", message: "亲，没有下一张凭证了~" });
                return;
            }
            loadVoucherSwitchInfo(2, false);
        },
        openTempDialog: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            voucher.value?.showVoucherTemplates();
        },
        deleteVoucher: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            if (cantDelete.value) return;
            voucher.value?.deleteVoucher();
        },
        saveAsTemp: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            voucher.value?.saveAsTemplate();
        },
        print: () => {
            if (handleCheckHasDialog(contentRef.value)) return;
            voucher.value?.showPrintVoucherDialog();
        },
    });
}
onDeactivated(() => {
    if (pageType.value === "newVoucher") {
        deleteVoucherQuickKeycode();
    }
});
const leaveValidator = routerArrayStore.registerLeaveValidator(route.path, () => {
    if (edited.value) {
        if (!confirm("您确认要离开吗？系统可能不会保存您的更改~")) {
            return false;
        }
    }
    return true;
});
onMounted(() => {
    initPageState();
    if (pageType.value === "newVoucher") {
        handleSetSave();
        if (localStorage.getItem(getGlobalToken() + "-voucherDraft")) {
            var voucherModel = JSON.parse(localStorage.getItem(getGlobalToken() + "-voucherDraft") || "{}") as VoucherModel;
            ElAlert({ message: "您之前编辑的凭证草稿还没有保存，您想继续编辑该凭证吗？" }).then((r) => {
                if (r) {
                    voucherQueryParams.value = new DataVoucherQueryParams(voucherModel.voucherLines);
                }
                localStorage.removeItem(getGlobalToken() + "-voucherDraft");
            });
        }
        startSaveDraft();
    }
});
onUnmounted(() => {
    if (draftTimer !== null) {
        clearInterval(draftTimer);
    }
    leaveValidator.dispose();
    if (pageType.value === "newVoucher") {
        deleteVoucherQuickKeycode();
    }
});
const zoomState = ref<"in" | "out">("in");
const zoomCallback = ref((_zoomState: "in" | "out"): void => {
    zoomState.value = _zoomState;
});
const switchInfo = ref(new VoucherSwitchInfoModel());
const edited = ref(false);
const saving = ref(false);
const voucherGroupStore = useVoucherGroupStoreHook();
const vgName = ref("记账凭证");
let draftTimer: any = null;
const EasyRecommendDialogRef = ref<InstanceType<typeof EasyRecommendDialog>>();
function saveVoucher(saveType: "saveAndAdd" | "save") {
    if (!getCanSaveVoucher()) {
        return;
    }
    saving.value = true;
    voucher.value?.saveVoucher(
        new VoucherSaveParams(1010, (res: IResponseModel<VoucherSaveModel>) => {
            saving.value = false;
            if (res.state === 1000) {
                const voucherModel = voucher.value?.getVoucherModel();
                lastVoucherDate.value = res.data.vdate || "";
                // 新增凭证pid需要同步更新
                useAccountPeriodStore().changePeriods(res.data.pid.toString());
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({
                        message: "亲，保存成功啦！",
                        type: "success",
                    });
                }
                EasyRecommendDialogRef.value?.setDialog(res.data.pid,res.data.vid);
                
                communication();
                if (saveType === "save") {
                    voucherQueryParams.value = new EditVoucherQueryParams(res.data.pid, res.data.vid);
                } else if (saveType === "saveAndAdd") {
                    edited.value = false;
                    newVoucher();
                }
                reloadPeriodInfo();
                periodStore.getPeriods();
                voucher.value?.getVoucherDescription();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
        })
    );
}

function communication() {
    router.beforeEach((to, from, next) => {
        window.dispatchEvent(new CustomEvent((to.name as string) + "VoucherChange"));
        next();
    });
    window.dispatchEvent(new CustomEvent("reloadVoucherList"));
}

const cantDelete = ref(false);
function deleteVoucher() {
    if (cantDelete.value) {
        return;
    }
    cantDelete.value = true;
    let voucherModel = voucher.value?.getVoucherModel();
    if (voucherModel) {
        ElAlert({ message: "亲，确认要删除吗" }).then((r) => {
            if (r) {
                request({ url: "/api/Voucher?pId=" + voucherModel?.pid + "&vId=" + voucherModel?.vid, method: "delete" }).then(
                    (res: IResponseModel<boolean>) => {
                        if (res.data) {
                            communication();
                            ElAlert({ message: "亲，删除成功啦！请刷新页面查看最新数据", hideCancel: true }).then(() => {
                                switch (pageType.value) {
                                    case "voucherPage":
                                        cancel();
                                        break;
                                    case "newVoucher":
                                        if (switchInfo.value.hasNext) {
                                            loadVoucherSwitchInfo(2);
                                        } else if (switchInfo.value.hasPrev) {
                                            loadVoucherSwitchInfo(1);
                                        } else {
                                            loadVoucherSwitchInfo(0);
                                        }
                                        cantDelete.value = false;
                                        break;
                                }
                            });
                        } else {
                            cantDelete.value = false;
                            ElNotify({
                                message: res.msg || "亲，删除失败啦！请联系侧边栏客服！",
                                type: "warning",
                            });
                        }
                    }
                ).catch(() => {
                    cantDelete.value = false;
                });
            } else {
                cantDelete.value = false;
            }
        });
    } else {
        cantDelete.value = false;
    }
}
function voucherLoadSuccess(voucherModel: VoucherModel) {
    if (voucherQueryParams.value instanceof NewVoucherQueryParams || voucherQueryParams.value instanceof EditVoucherQueryParams) {
        edited.value = false;
    } else {
        edited.value = true;
        // voucherChanged();
    }
    if ((voucherModel.pid === 0 && voucherModel.vid === 0) || pageType.value === "voucherPage") {
        loadVoucherSwitchInfo(0);
    }
    if (voucherQueryParams.value instanceof NewVoucherQueryParams) {
        voucher.value?.focusFirstLine();
    }
}

let loading = false;
let isComfirm = false;
function loadVoucherSwitchInfo(operation: 0 | 1 | 2, loadingTip = true) {
    if ((operation === 1 && switchInfo.value.hasPrev === false) || (operation === 2 && switchInfo.value.hasNext === false)) {
        return;
    }
    if (loading && operation !== 0) {
        loadingTip && ElNotify({ message: "正在切换，请稍后点击~", type: "warning" });
        return;
    }
    if (isComfirm) return;
    isComfirm = true;
    new Promise<void>((resolve, reject) => {
        if (edited.value && operation !== 0) {
            ElAlert({ message: "亲，您已经修改了凭证，是否要保存？" }).then((r) => {
                isComfirm = false;
                if (r) {
                    saveVoucher("save");
                    reject();
                } else {
                    resolve();
                }
            });
        } else {
            isComfirm = false;
            resolve();
        }
    }).then(() => {
        loading = true;
        let voucherModel = voucher.value?.getVoucherModel();
        voucher.value && (voucher.value.warningRowIndex = -1);
        request({
            url: "/api/Voucher/GetVoucherSwitchInfo",
            method: "post",
            data: {
                pid: voucherModel?.pid === 0 ? "" : voucherModel?.pid,
                date: voucherModel?.vdate,
                vgId: voucherModel?.vgId,
                vnum: voucherModel?.vnum,
                operation: operation,
                isNewVoucher: true,
                voucherListQueryParams: {},
            },
        }).then((res: IResponseModel<VoucherSwitchInfoModel>) => {
            _.debounce(() => {
                loading = false;
            }, 1000)();
            if (res.state === 1000) {
                if (operation !== 0) {
                    voucherQueryParams.value = new EditVoucherQueryParams(res.data.pid, res.data.vid);
                }
                switchInfo.value = res.data;
            }
        });
    });
}

function newVoucher() {
    new Promise<void>((resolve, reject) => {
        if (edited.value) {
            ElAlert({ message: "亲，您已经修改了凭证，是否要保存？" }).then((r) => {
                if (r) {
                    saveVoucher("save");
                    reject();
                } else {
                    resolve();
                }
            });
        } else {
            resolve();
        }
    }).then(() => {
        const queryParams = new NewVoucherQueryParams();
        queryParams.vgId = voucher.value?.getVoucherModel().vgId || 0;
        queryParams.vnum = voucher.value?.getVoucherModel().vnum || 0;
        queryParams.lastVoucherDate = lastVoucherDate.value;

        voucherQueryParams.value = queryParams;
    });
}
function loadVoucherTemplate(vtId: number) {
    const newParams = new LoadVoucherTemplateQueryParams(vtId);
    if (route.query.fileIds) {
        if (voucherQueryParams.value instanceof NewVoucherQueryParams) {
            newParams.attachFileIds = (voucherQueryParams.value as NewVoucherQueryParams).attachFileIds;
            newParams.attachFiles = (voucherQueryParams.value as NewVoucherQueryParams).attachFiles;
        }
        if (voucherQueryParams.value instanceof LoadVoucherTemplateQueryParams) {
            newParams.attachFileIds = (voucherQueryParams.value as LoadVoucherTemplateQueryParams).attachFileIds;
            newParams.attachFiles = (voucherQueryParams.value as LoadVoucherTemplateQueryParams).attachFiles;
        }
    }
    voucherQueryParams.value = newParams;
    ElNotify({
        message: "亲，加载成功啦！",
        type: "success",
    });
}

function loadVoucherDraft(pid: number, vid: number) {
    voucherQueryParams.value = new LoadVoucherDraftQueryParams(pid, vid);
    ElNotify({
        message: "亲，加载成功啦！",
        type: "success",
    });
}
let voucherReadonly = ref(false);
function voucherChanged(readonly: boolean) {
    edited.value = true;
    voucherReadonly.value = readonly;
}

function getCanSaveVoucher(): boolean {
    if (saving.value) {
        return false;
    }
    return true;
}

function getCanEditVoucher(): boolean {
    let voucherModel = voucher.value?.getVoucherModel();
    if (voucherModel) {
        return voucherModel.pstatus !== 3 && voucherModel?.approveStatus !== 1;
    } else {
        return false;
    }
}

function startSaveDraft() {
    draftTimer = setInterval(() => {
        if (edited.value) {
            console.log("保存草稿");
            voucher.value?.saveVoucher(
                new DraftSaveParams((voucherModel: VoucherModel) => {
                    localStorage.setItem(getGlobalToken() + "-voucherDraft", JSON.stringify(voucherModel));
                }),
                true
            );
        } else {
            localStorage.removeItem(getGlobalToken() + "-voucherDraft");
        }
    }, 1000);
}

function cancel() {
    useFullScreenStore().changeFullScreenStage(false);
    voucher.value && (voucher.value.warningRowIndex = -1);
    if (window.isErp) {
        closeCurrentTab();
    } else {
        closeCurrentTab();
        cantDelete.value = false;
        nextTick(()=>{
            if (props.from === "salesinvoice") {
                globalWindowOpenPage("/Invoice/SalesInvoice?from=voucherPage&r=" + Math.random(), "销项发票");
                return;
            }
            if (props.from === "purchaseinvoice") {
                globalWindowOpenPage("/Invoice/PurchaseInvoice?from=voucherPage&r=" + Math.random(), "进项发票");
                return;
            }
            if (props.from === "expenseReceipts") {
                globalWindowOpenPage("/Invoice/ExpenseBill?from=voucherPage&r=" + Math.random(), "费用单据");
                return;
            }
            if (props.from === "cashjournal") {
                globalWindowOpenPage("/Cashier/CashJournal?from=voucherPage&r=" + Math.random(), "现金日记账");
                return;
            }
            if (props.from === "depositjournal") {
                globalWindowOpenPage("/Cashier/DepositJournal?from=voucherPage&r=" + Math.random(), "银行日记账");
                return;
            }
            if (props.from === "journal") {
                globalWindowOpenPage("/AccountBooks/Journal", "序时账");
                return;
            }
            if (props.from === "transfer") {
                globalWindowOpenPage("/Cashier/Transfer?from=voucherPage&r=" + Math.random(), "内部转账");
                return;
            }
            window.history.back();
        })
    }
}

function resetVDateAndVNum() {
    if (!window.isErp) {
        return;
    }
    if (props.fcode === "scmvoucher-cancreatevoucher" || props.fcode === "invoice-output-cancreatevoucher") {
        pageType.value = "errorPage";
    }
}

function getEditedState() {
    return edited.value;
}

watch(
    () => voucher.value?.getVoucherModel().vgId,
    (vgId) => {
        const vg = voucherGroupStore.voucherGroupList.find((item) => item.id === vgId);
        if (vg) {
            vgName.value = vg.title;
        } else {
            vgName.value = "记账凭证";
        }
    }
);
watch(edited, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
defineExpose({
    getEditedState,
});
</script>
