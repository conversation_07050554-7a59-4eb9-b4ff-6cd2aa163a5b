import { useUserStoreHook } from "@/store/modules/user";
import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios";
import { getGlobalToken } from "./baseInfo";
import { getCookie } from "./cookie";
import { isLemonClient } from "./lmClient";
import { isInWxWork } from "./wxwork";
import { appendAACompanyId, setTopLocationhref } from "./url";
import { toLogout } from "./erpUtils";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";

// 不需要加appasid的请求
const requestWithoutAppasid = ["QyWxHasDeptFollowUser", "api/ImportFromYun", "api/ImportFromOther", "WechatBindScaned", "Websites/InvoiceConfig", "jxc_api/Backup"];
// 判断是否需要加appasid
const isNeedAppasid = (url: string) => {
    return requestWithoutAppasid.every((item) => url.indexOf(item) === -1);
};
const requestWithoutCorpId = ["/wb/enter/getScmServiceInfo"];

//创建请求实例
function createService() {
    const service = axios.create();
    service.interceptors.request.use(
        (config) => {
            config.withCredentials = true;
            if (config.url?.startsWith("/api/")) {
                config.headers.appasid = getGlobalToken();
                config.baseURL = window.jApiHost;
            }
            const url = config.url;
            if (url?.toLowerCase().indexOf("appasid=") == -1 && isNeedAppasid(url)) {
                const token = getGlobalToken();
                if (token) {
                    if (url.indexOf("?") > 0) {
                        config.url = url + "&appasid=" + token;
                    } else {
                        config.url = url + "?appasid=" + token;
                    }
                }
            }
            config.url = appendAACompanyId(config.url ?? "");
            if (config.url.indexOf(window.eHost) === 0) {
                if (config.url.indexOf("?") === -1) {
                    config.url += "?r=" + Math.random();
                } else {
                    config.url += "&r=" + Math.random();
                }
                if (isInWxWork() && requestWithoutCorpId.every((item) => config.url?.indexOf(item) === -1)) {
                    config.url += "&corpid=" + getCookie("corpId") + "&suitid=" + window.wxworkSuitId + "&wxworkUserId=" + getCookie("wxworkUserId");
                }
            }

            if (
                config.data &&
                config.headers["Content-Type"] &&
                (config.headers["Content-Type"] as string).toLowerCase().indexOf("multipart/form-data") >= 0 &&
                config.data instanceof FormData
            ) {
                // 解决文件有变更再上传会报错的问题。因为是一个trick，处理起来有些别扭先统一在request里处理
                const tempData = new FormData();
                const inputs = document.querySelectorAll('input[type="file"]');

                config.data.forEach((data: any, index: any) => {
                    if (data instanceof File) {
                        for (let i = 0; i < inputs.length; i++) {
                            const input = inputs[i] as HTMLInputElement;
                            if ((input.files as FileList)[0] === data) {
                                const newElement = input.cloneNode(true);
                                const file: File = ((newElement as HTMLInputElement).files as FileList)[0];
                                tempData.append(index, file);
                                return;
                            }
                        }
                    }

                    tempData.append(index, data);
                });

                config.data = tempData;
            }

            return config;
        },
        //发送失败
        (error) => Promise.reject(error)
    );
    //响应拦截
    service.interceptors.response.use(
        (response) => {
            if (response.headers["content-disposition"] && typeof response.data !== "string") {
                response.data["content-disposition"] = response.headers["content-disposition"];
            }
            const route = response.config.url?.split("?")[0] || "";
            if (route === "/api/Voucher" && ["post", "put", "delete"].includes(response.config.method || "")) {
                dispatchReloadAsubAmountEvent();
            }
            return response.data;
        },
        (error) => {
            if (error.response) {
                const userStore = useUserStoreHook();
                if (error.response.status === 401) {
                    //获取账套信息出错，请您重新登录或询问账套管理员
                    userStore.logoutDialog();
                } else if (error.response.status === 403) {
                    //您的登录状态已过期，请您重新登录
                    userStore.logoutDialog();
                } else if (error.response.status === 410 && error.response.headers.pragma === "no-cache") {
                    //账号在其他地方登录提示
                    if (window.isErp && error.response.statusText === "limit") {
                        toLogout(error.status.toString());
                    } else if (!window.isErp) {
                        if (window.location.href.indexOf(window.location.origin + "/410") > -1) return;
                        const url = window.location.origin + "/410";
                        if (isLemonClient()) window.location.href = url;
                        else setTopLocationhref(url);
                    }
                } else {
                    return Promise.reject(error);
                }
            } else {
                return Promise.reject(error);
            }
        }
    );
    return service;
}

/** 创建请求方法 */
function createRequestFunction(service: AxiosInstance) {
    return function (config: AxiosRequestConfig) {
        const configDefault = {
            //数据过多，请求需两三分钟
            // timeout: 60000,
            baseURL: window.jHost,
            data: {},
        };
        return service<IResponseModel<any>, IResponseModel<any>>(Object.assign(configDefault, config));
    };
}

/** 用于网络请求的实例 */
export const service = createService();
/** 用于网络请求的方法 */
export const request = createRequestFunction(service);

export interface IResponseModel<T> {
    state: 1000 | 2000 | 9999;
    subState: number;
    data: T;
    msg: string;
}
