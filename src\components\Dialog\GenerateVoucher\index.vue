<template>
    <el-dialog
        modal-class="modal-class"
        v-model="dialogShow"
        class="custom-confirm dialogDrag voucher-template-dialog"
        title="生成凭证规则"
        center
        :width="dialogWidth"
        destroy-on-close
    >
        <div class="voucher-template-box" v-dialogDrag ref="boundaryRef" :style="isMaxHeight ? 'max-height:350px;overflow-y: auto' : ''">
            <div class="vouher-template-detial">
                <div class="voucher-template-top">
                    <div class="voucher-templat-name">
                        <slot></slot>
                    </div>
                    <div class="voucher-group">
                        <span>凭证字：</span>
                        <el-select 
                            v-model="formData.vgId" 
                            :teleported="false"
                            :filterable="true"
                            :filter-method="voucherGroupFilterMethod"
                        >
                            <el-option 
                                v-for="item in showVoucherGroup" 
                                :value="item.id" 
                                :key="item.id" 
                                :label="item.name"
                            ></el-option>
                        </el-select>
                    </div>
                </div>
                <div
                    class="voucher-template-main"
                    @mouseleave="mouseEnterTable = false"
                    @mouseenter="mouseEnterTable = true"
                >
                    <Table
                        :columns="columns"
                        :data="formData.voucherTemplateLines"
                        :max-height="isErp ? '264' : 'auto'"
                        cell-class-name="row-click-show-others"
                        :hasAddSub="true && mouseEnterTable"
                        add-sub-field="index"
                        :continuousClick="true"
                        :not-delete-row="notDeleteRow"
                        :notDeleteNotifyText="notDeleteNotifyText"
                        :add-row-data="defaultLine"
                        :customAdd="customAdd"
                        :customSubtract="customSubtract"
                        :firstRowNotShow="firstRowNotShow"
                        :lastRowNotShow="lastRowNotShow"
                        :lockLine="formData.voucherTemplateLines.findIndex((item) => item.lockAsub)"
                        :show-overflow-tooltip="true"
                        empty-text="暂无数据"
                        @table-add-or-subtract="handleTableAddSub"
                        @handleAdd="handleAdd"
                        @handleSubtract="handleSubtract"
                        :tableName="setModule"
                    >
                        <template #asubId>
                            <el-table-column 
                                label="会计科目" 
                                min-width="270px" 
                                align="left" 
                                header-align="left" 
                                prop="asubCode"
                                :width="getColumnWidth(setModule, 'asubCode')"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 94%" @click="() => rowClick(scope)">
                                        <div class="click-show-item select-v2">
                                            <div v-if="formData.voucherTemplateLines[scope.$index]?.lockAsub" style="margin-left: 11px">
                                                <span>{{ getAsubName(formData.voucherTemplateLines[scope.$index].asubId) }}</span>
                                            </div>
                                            <SelectV2 
                                                v-else
                                                :class=" 'visibleSelect' +scope.$index "
                                                :options="asubIdList" 
                                                :filterable="true"  
                                                v-model="formData.voucherTemplateLines[scope.$index].asubId" 
                                                @change="(val) => asubChange(scope, val)" 
                                                @focus="() => asubFocus(scope.$index)"
                                                @visible-change="handleVisibleChange"
                                            >
                                            </SelectV2>
                                            <!-- <FilterCustomSelect
                                                :fit-input-width="true"
                                                v-model="formData.voucherTemplateLines[scope.$index].asubId"
                                                placeholder=" "
                                                :filterable="true"
                                                :class="notShow ? 'icon-not-show' : ''"
                                                v-else
                                                @change="(val) => asubChange(scope, val)"
                                                @focus="() => asubFocus(scope.$index)"
                                                ref="asubSelectRef"
                                                popper-class="asub-select-popper"
                                                :popper-options="{
                                                    modifiers: [
                                                        {
                                                            name: 'eventListeners',
                                                            options: {
                                                                scroll: false,
                                                            },
                                                        },
                                                    ],
                                                }"
                                                :filter-method="handleAsubFilter"
                                            >
                                                <Option v-for="opt in asubIdList" :key="opt.value" :label="opt.label" :value="opt.value" />
                                            </FilterCustomSelect> -->
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #direction>
                            <el-table-column 
                                label="方向" 
                                min-width="80px" 
                                align="left" 
                                header-align="left" 
                                prop="credit"  
                                :show-overflow-tooltip="false"
                                :width="getColumnWidth(setModule, 'credit')" 
                                :resizable="setModule !== 'CashGenerateVoucherDialog'"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 100%" @click="() => rowClick(scope)">
                                        <div class="click-show-item select-complete">
                                            <el-select v-model="formData.voucherTemplateLines[scope.$index].direction" placeholder="">
                                                <el-option :value="1" label="借" />
                                                <el-option :value="2" label="贷" />
                                            </el-select>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #InvoicevalueType>
                            <el-table-column 
                                label="取值" 
                                min-width="180px" 
                                align="left" 
                                header-align="left" 
                                prop="valueType" 
                                :resizable="false"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 94%" @click="() => rowClick(scope)">
                                        <div class="click-show-item select-complete">
                                            <el-select 
                                                v-model="formData.voucherTemplateLines[scope.$index].valueType" 
                                                placeholder=" "
                                                :filterable="true"
                                                :filter-method="invoiceTemplateValueFilterMethod"
                                            >
                                                <el-option 
                                                    v-for="item in showInvoiceTemplateValueTypeList" 
                                                    :value="item.value" 
                                                    :label="item.label" 
                                                    :key="item.value"
                                                />
                                            </el-select>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #salaryValueType>
                            <el-table-column 
                                label="取值" 
                                min-width="180px" 
                                align="left" 
                                header-align="left" 
                                prop="valueType" 
                                :resizable="false"
                            >
                                <template #default="scope">
                                    <div style="width: 100%; height: 94%" @click="() => rowClick(scope)">
                                        <template>
                                            <span>{{ scope.row.asubCode }}</span>
                                        </template>
                                        <div class="click-show-item select-complete">
                                            <el-select
                                                v-model="formData.voucherTemplateLines[scope.$index].valueId"
                                                placeholder=" "
                                                :fit-input-width="true"
                                                :filterable="true"
                                                :filter-method="tempalteValueTypeFilterMethod"
                                            >
                                                <Option
                                                    v-for="item in showTempalteValueTypeList"
                                                    :key="(item as any).value"
                                                    :value="(item as any).value"
                                                    :label="(item as any).label"
                                                />
                                            </el-select>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
                <div class="buttons" style="border-top: 1px solid var(--border-color)">
                    <a class="button solid-button" @click="throttleVoucherSaving">确定</a>
                    <a class="button ml-10" @click="cancelDialog">取消</a>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, type PropType, toRef, watch, watchEffect } from "vue";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IAccountSubjectModel } from "@/api/accountSubject";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import type { IVoucherGroup } from "@/api/voucherGroup";
import {
    VoucherTemplateSaveModel,
    VoucherTemplateLineSaveModel,
    createVoucherTemplate,
    updateVoucherTemplate,
    VoucherTemplateLogicEnum,
} from "@/api/voucherTemplate";
import type { IVoucherTemplateModel, IVoucherTempalteLine } from "./types";
import SelectV2 from "@/components/AsubSelect/index.vue";
import Option from "@/components/Option/index.vue";
import { nextTick } from "vue";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
import FilterCustomSelect from "@/views/Statements/components/EditEquation/FilterCustomSelect.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";

const isErp = ref(window.isErp);
const boundaryRef = ref<Element>();
const mouseEnterTable = ref(false);
const props = defineProps({
    voucherDialogIsShow: {
        type: Boolean,
        default: false,
    },
    columns: {
        type: Array<IColumnProps>,
        default: () => {},
    },
    dialogWidth: {
        type: String,
        default: "820px",
    },
    tempalteValueTypeList: {
        type: Array,
        default: () => [],
    },
    preCheck: {
        type: Function,
        default: () => false,
    },
    defaultLine: {
        type: Object as PropType<IVoucherTempalteLine>,
        default: () => {
            return {
                asubId: "",
                direction: 1,
            };
        },
    },
    autoAddNextLine: {
        type: Boolean,
        default: true,
    },
    notShow: {
        type: Boolean,
        default: true,
    },
    minRowCount: {
        type: Number,
        default: 2,
    },
    customAdd: {
        type: Boolean,
        default: false,
    },
    customSubtract: {
        type: Boolean,
        default: false,
    },
    notDeleteRow: {
        type: Number,
        default: 0,
    },
    notDeleteNotifyText: {
        type: String,
        default: "",
    },
    isMaxHeight: {
        type: Boolean,
        default: true,
    },
    firstRowNotShow: {
        type: Boolean,
        default: false,
    },
    lastRowNotShow: {
        type: Boolean,
        default: false,
    },
    customNotify: {
        type: Boolean,
        default: false,
    },
    setModule: {
        type: String,
        default: "",
    }
});
const voucherGroupStore = useVoucherGroupStore();
const emit = defineEmits(["voucherSave", "update:voucherDialogIsShow", "handleDelete", "handleAddVoucher"]);
const voucherGroup = toRef(voucherGroupStore, "voucherGroupList");
const dialogShow = computed({
    get() {
        return props.voucherDialogIsShow;
    },
    set(value) {
        emit("update:voucherDialogIsShow", value);
    },
});

const formData = ref<IVoucherTemplateModel>({
    vgId: 0,
    vtId: 0,
    voucherTemplateLines: [
        // {
        //     asubId: "",
        //     direction: 1,
        // },
    ],
});

// 已删除科目数组
const deletedRowIdList = ref<any>([]);

// 获取科目列表
const asubCodeList = ref<any[]>([]);
const asubIdList = ref<any[]>([]);
const accountSubjectStore = useAccountSubjectStore();
const asubList = ref<IAccountSubjectModel[]>([]);
const accountSubject = window.isErp
    ? toRef(accountSubjectStore, "accountSubjectList")
    : toRef(accountSubjectStore, "accountSubjectListWithoutDisabled");
function getAccountSubject() {
    asubCodeList.value = [];
    asubIdList.value = [];
    asubList.value = accountSubject.value;
    accountSubject.value.forEach((item) => {
        asubCodeList.value.push({ label: item.asubCode + " " + item.asubAAName, value: item.asubCode });
        asubIdList.value.push({ label: item.asubCode + "-" + item.asubAAName, value: item.asubId.toString() });
    });

}
getAccountSubject();
function handleAsubFilter(v: string) {
    asubIdList.value = accountSubject.value
        .filter(
            (item: IAccountSubjectModel, index: number) =>
                item.asubCode.startsWith(v.trim()) 
                || item.asubAAName.includes(v.trim()) 
                || item.acronym.includes(v.trim()
            )
        )
        .map((list) => {
            return { label: list.asubCode + "-" + list.asubAAName, value: list.asubId.toString() };
        });
}
function initEditForm(data: IVoucherTemplateModel, editType: "edit" | "new" = "edit") {
    formData.value = data;
    let noneAsubIdIndex = -1;
    formData.value.voucherTemplateLines.forEach((item: any, index: number) => {
        item.index = index;
        if (!getAsubName(item.asubId)) {
            item.asubId = "";
            noneAsubIdIndex = index;
        }
    });
    if (editType === "new") {
        formData.value.vgId = voucherGroup.value.find((item) => item.isDefault)!.id;
        return;
    }
    if (noneAsubIdIndex > -1) {
        ElConfirm(`亲，第${noneAsubIdIndex + 1}行，请选择科目！`, true, () => {}, "提示", undefined, false, 9999);
    }
}
const getAsubName = (asubId: string) => {
    const asubdIdInt = parseInt(asubId);
    const asub = asubList.value.find((item) => item.asubId == asubdIdInt);
    return asub ? asub.asubCode + "-" + asub.asubAAName : "";
};

function handleTableAddSub(data: any) {
    formData.value.voucherTemplateLines = data;
}
function handleAdd(index: number) {
    formData.value.voucherTemplateLines.push({ ...props.defaultLine, index: index + 1 });
    emit("handleAddVoucher", index);
}
function handleSubtract(index: number) {
    emit("handleDelete", formData.value.voucherTemplateLines.length, index);
}
let currentRowIndex = ref(-1);
const rowClick = (scope: any) => {
    currentRowIndex.value = scope.$index;
};

const asubFocus = (index: number) => {
    if (!props.autoAddNextLine) return;
    //如果是最后一行，增加一行
    if (index === formData.value.voucherTemplateLines.length - 1) {
        formData.value.voucherTemplateLines.push({ ...props.defaultLine, index: index + 1 });
    }
};

const asubSelectRef = ref();
const defaultOpenAsubSelect = () => {
    nextTick().then(() => {
        asubSelectRef.value?.focus();
    });
};
const currentIndex = ref(-1);
const asubChange = (scope: any, value: string) => {
    const index = scope.$index;
    const asubId = parseInt(value);
    const asub = asubList.value.find((item) => item.asubId === asubId);
    if (asub) {
        let logicEnum: VoucherTemplateLogicEnum;
        if (asub.isLeafNode) {
            if (asub.assistingAccounting === 0) {
                logicEnum = VoucherTemplateLogicEnum.TotalAsub;
            } else {
                logicEnum = VoucherTemplateLogicEnum.AssisAsub;
            }
        } else {
            logicEnum = VoucherTemplateLogicEnum.DetailAsub;
        }
        formData.value.voucherTemplateLines[index].logicEnum = logicEnum;
    }
    currentIndex.value = scope.$index;
};

const handleVisibleChange = (visible:boolean) => {
    let div = document.querySelector(`.visibleSelect${currentIndex.value}`);
    if (!visible) {
        div?.classList.add('noVisible');
    } else {
        div?.classList.remove('noVisible');
    }
}

const voucherSaving = () => {
    if (!props.preCheck()) {
        return;
    }

    if (formData.value.voucherTemplateLines.some((item) => !item.asubId) && !props.customNotify) {
        let rowIndex = formData.value.voucherTemplateLines.findIndex((item) => !item.asubId);
        let hasNonEmptyAfter = false;
        if (rowIndex > -1) {
            hasNonEmptyAfter = formData.value.voucherTemplateLines.slice(rowIndex + 1).some((item) => item.asubId);
            let list = formData.value.voucherTemplateLines.slice(0, rowIndex);
            if (hasNonEmptyAfter || list.length < props.minRowCount) {
                ElNotify({
                    message: `亲，第${(rowIndex as number) + 1}行,请选择科目！`,
                    type: "warning",
                });
                return;
            }
        }
    }
    if (formData.value.voucherTemplateLines.some((item) => !item.asubId) && props.customNotify) {
        ElNotify({
            message: `请选择会计科目！`,
            type: "warning",
        });
        return;
    }

    if (formData.value.voucherTemplateLines.length < props.minRowCount) {
        ElNotify({
            message: `亲，凭证模板不能少于${props.minRowCount}行哦！`,
            type: "warning",
        });
        return;
    }

    const firstDeriction = formData.value.voucherTemplateLines[0].direction;
    if (formData.value.voucherTemplateLines.every((item) => item.direction === firstDeriction)) {
        ElNotify({
            message: "亲，凭证模板必须有借有贷！",
            type: "warning",
        });
        return;
    }
    emit("voucherSave", formData.value, deletedRowIdList.value);
    // 重置删除项数组
    deletedRowIdList.value = [];
};
let timer = ref(0);
function throttleVoucherSaving() {
    if (timer.value) {
        return;
    }
    voucherSaving();
    timer.value = setTimeout(() => {
        timer.value = 0;
    }, 2000);
}
const cancelDialog = () => {
    dialogShow.value = false;
};

interface saveVoucherTempalteParams {
    vtType: number;
    vtName: string;
}

const saveVoucherTemplate = (params: saveVoucherTempalteParams) => {
    const saveData = buildSaveData(params);
    if (saveData.vtId !== 0) {
        return new Promise<number>((resolve, reject) => {
            updateVoucherTemplate(saveData).then((res) => {
                if (res.state === 1000 && res.data) {
                    resolve(saveData.vtId);
                } else {
                    reject({ state: res.state, msg: res.msg });
                }
            });
        });
    } else {
        return new Promise<number>((resolve, reject) => {
            createVoucherTemplate(saveData).then((res) => {
                if (res.state === 1000 && res.data !== 0) {
                    resolve(res.data);
                } else {
                    reject({ state: res.state, msg: res.msg });
                }
            });
        });
    }
};

const buildSaveData = (params: saveVoucherTempalteParams) => {
    const model = new VoucherTemplateSaveModel();
    let rowIndex = formData.value.voucherTemplateLines.findIndex((item) => !item.asubId);
    let voucherTemplateLines = formData.value.voucherTemplateLines.slice(0);
    let hasNonEmptyAfter = false;
    if (rowIndex > -1) {
        hasNonEmptyAfter = formData.value.voucherTemplateLines.slice(rowIndex + 1).some((item) => item.asubId);
        if (!hasNonEmptyAfter && formData.value.voucherTemplateLines.length > props.minRowCount) {
            voucherTemplateLines = voucherTemplateLines.slice(0, rowIndex);
        }
    }
    model.vtId = formData.value.vtId;
    model.vgId = formData.value.vgId;
    model.vtCode = "";
    model.vtName = params.vtName;
    model.vtType = params.vtType;
    model.vtDefault = false;
    model.voucherLines = voucherTemplateLines.map((i) => {
        var line = new VoucherTemplateLineSaveModel();
        line.description = "";
        line.asubId = parseInt(i.asubId);
        line.debit = i.direction == 1 ? 1 : 0;
        line.credit = i.direction == 2 ? 1 : 0;
        line.quantityAccounting = 0;
        line.quantity = 0;
        line.price = 0;
        line.foreigncurrency = 0;
        line.fcId = 1;
        line.fcRate = 0;
        line.fcAmount = 0;
        line.assistingAccounting = 0;
        line.aacode = "";
        line.logicEnum = i.logicEnum ?? 0;
        line.valueType = i.valueType ?? 0;
        return line;
    });
    return model;
};

const handleDeleteRow = (index: number) => {
    const item = formData.value.voucherTemplateLines.splice(index, 1);

    deletedRowIdList.value.push(...item);
};
defineExpose({
    initEditForm,
    saveVoucherTemplate,
    handleDeleteRow,
    defaultOpenAsubSelect,
    buildSaveData,
});

const invoiceTemplateValueTypeList = ref([
    { value: 1010, label: "不含税金额" },
    { value: 1020, label: "税额" },
    { value: 1030, label: "价税合计" },
]);
const showTempalteValueTypeList = ref<Array<any>>([]);
const showVoucherGroup = ref<Array<any>>([]);
const showInvoiceTemplateValueTypeList = ref<Array<any>>([]);
watchEffect(() => { 
    showTempalteValueTypeList.value = JSON.parse(JSON.stringify(props.tempalteValueTypeList));   
    showVoucherGroup.value = JSON.parse(JSON.stringify(voucherGroup.value));   
    showInvoiceTemplateValueTypeList.value = JSON.parse(JSON.stringify(invoiceTemplateValueTypeList.value));   
});
function tempalteValueTypeFilterMethod(value: string) {
    showTempalteValueTypeList.value = commonFilterMethod(value, props.tempalteValueTypeList, 'label');
}
function voucherGroupFilterMethod(value: string) {
    showVoucherGroup.value = commonFilterMethod(value, voucherGroup.value, 'name');
}
function invoiceTemplateValueFilterMethod(value: string) {
    showInvoiceTemplateValueTypeList.value = commonFilterMethod(value, invoiceTemplateValueTypeList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";

.voucher-template-main {
    :deep(.el-input__wrapper) {
        box-shadow: none;
    }
}

.voucher-template-box {
    // max-height: 350px;
    // overflow-y: scroll;
    .vouher-template-detial {
        color: #404040;
        font-size: 12px;
        text-align: left;

        .voucher-template-top {
            padding: 14px 24px 10px;
            height: 30px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;

            .voucher-group {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                margin-right: 2px;
                .detail-el-select(85px);
            }
        }

        .voucher-template-main {
            margin-bottom: 12px;
            padding: 0 24px;

            :deep(.row-click-show-others) {
                .cell {
                    width: 100%;
                    height: 100%;
                    line-height: 40px;
                    padding: 0;

                    > div {
                        padding: 0 12px;
                        box-sizing: border-box;
                        position: relative;

                        .click-show-item {
                            position: absolute;
                            top: 4px;
                            left: 4px;
                            width: calc(100% - 8px);
                            height: calc(100% - 6px);
                            display: flex;
                            align-items: center;

                            & > input {
                                .detail-original-input(100%, 100%);
                            }
                            &.select-complete {
                                .el-input {
                                    &.is-focus {
                                        .el-input__wrapper {
                                            box-shadow: 0 0 0 1px var(--main-color) inset !important;
                                        }
                                    }
                                    .el-input__wrapper {
                                        box-shadow: unset !important;
                                        &:hover {
                                            box-shadow: 0 0 0 1px var(--main-color) inset !important;
                                        }
                                    }
                                }
                            }

                            .el-select {
                                width: 100%;
                                height: 100%;

                                &.icon-not-show {
                                    .el-icon {
                                        display: none;
                                    }
                                    .icon_clear {
                                        right: 32px !important;
                                    }
                                }

                                .select-trigger {
                                    line-height: 28px;

                                    .el-input {
                                        height: 30px;

                                        .el-input__inner {
                                            border: none;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .balance-panel {
            .balance-panel-title {
                font-size: var(--h3);
                color: var(--font-color);
                line-height: 22px;
                font-weight: 500;
                padding: 12px;
                border-bottom: 1px solid #edefee;
                background-color: #f8f8f8;
                border-radius: 4px 4px 0 0;
            }

            .balance-value-rate-container {
                padding-right: 20px;
                padding-top: 40px;
                padding-bottom: 40px;
                text-align: center;
                font-size: 0;
                background-color: #f8f8f8;
                border-radius: 0 0 4px 4px;

                span {
                    font-size: var(--font-size);
                }

                > input {
                    .detail-original-input(140px, 28px);

                    &::-webkit-outer-spin-button,
                    &::-webkit-inner-spin-button {
                        -webkit-appearance: none !important;
                    }
                    -moz-appearance: textfield;
                    &:focus,
                    &:hover {
                        -moz-appearance: number-input;
                    }
                }
            }

            .balance-panel-tips {
                padding-left: 220px;
                padding-top: 32px;
                padding-bottom: 20px;

                .tip-sub-title {
                    margin-top: 12px;
                    font-size: var(--h5);
                    color: #777777;
                    line-height: 10px;
                }
            }

            .voucher-line-equation-top {
                height: 50px;
                padding: 0 12px;
                background-color: #f8f8f8;

                > .asub-name {
                    height: 28px;
                    width: 165px;
                    margin-top: 11px;
                    position: relative;

                    :deep(.el-select) {
                        width: 100% !important;
                        height: 100% !important;
                    }

                    :deep(.asub-img) {
                        top: 5px;
                        right: 5px;
                    }
                }

                .txt {
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 50px;
                    margin-right: 2px;
                }

                .operator {
                    .detail-el-select(75px, 28px);
                    margin-top: 11px;
                }

                .balance {
                    .detail-el-select(105px, 28px);
                    margin-top: 11px;
                }
            }

            .voucher-line-equation-main {
                padding: 10px;
                padding-top: 0;
                background-color: #f8f8f8;
            }
        }

        .custom-precheck-btns {
            padding: 10px 0;
            // margin-top: 18px;
            text-align: center;
            border-top: 1px solid var(--border-color);
            text-align: center;
        }
    }
}

.el-select-dropdown__list {
    max-height: 200px;
    // overflow-y: auto;
}

.el-select-dropdown__item {
    width: 100%;
    height: auto;
    font-size: var(--el-font-size-base);
    padding: 6px 6px 6px 8px;
    line-height: 16px;
    position: relative;
    word-wrap: break-word !important;
    white-space: normal !important;
    color: var(--el-text-color-regular);
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    & span {
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 设置最多显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

:deep(.el-select-v2) {
    .el-select-v2__wrapper.is-focused {
        .el-select-v2__placeholder {
            color: var(--border-color);
        } 
    } 
    &.noVisible {
        .el-select-v2__wrapper.is-focused {
            .el-select-v2__placeholder {
                color: var(--font-color);
            }  
        }
    }
    .el-select-v2__placeholder {
        font-size: var(--table-body-font-size);
    }
}
body[erp] {
    .table.paging-hide {
        :deep(.el-table) {
            border-bottom: none;
            .el-table__body-wrapper {
                z-index: 2;
                border-bottom: 1px solid var(--el-border-color-lighter);
            }
        }
    }
}

</style>
<style lang="less">
.voucher-template-main{
    .custom-table tbody tr td .cell {
        input{
          border:0 !important;
        }
    }
}
</style>
