import type { InjectionK<PERSON> } from "vue";
import type { ISubjectTree } from "./util";
import type { Ref } from "vue"; // Fixed problem 1
export const updateAsubCodeKey: InjectionKey<undefined | ((value: string, name?: string) => void)> = Symbol();
export const popoverHandleCloseKey: InjectionKey<undefined | (() => void)> = Symbol();
export const initAsubTreeKey: InjectionKey<Ref<Array<ISubjectTree>> | undefined> = Symbol(); // Fixed problem 2
export const stopPopoverCloseKey: InjectionKey<undefined | (() => void)> = Symbol();
