<template>
    <div class="content" ref="contentRef">
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content" :class="isErp ? 'erp' : ''" :style="isErp ? 'overflow-y:auto' : ''">
                    <div class="list-show-container overlay-container" v-show="showBox === 'voucherList'">
                        <div class="title">凭证列表</div>
                        <MainTopBox
                            ref="mainTopBoxRef"
                            :periodDateList="periodDateList"
                            :maxSearchDate="maxSearchDate"
                            :checkNeeded="checkNeeded"
                            :getSelectedvoucherList="getSelectedvoucherList"
                            :table-data="voucherList"
                            :paginationData="paginationData"
                            :voucherGroupList="voucherGroupList"
                            v-model:columnType="columnType"
                            @open-recyclebin="openRecyclebin"
                            @delete-vouchers-batch="deleteVouchersBatch"
                            @copy-vouchers-batch="copyVouchersBatch"
                            @openSortDialog="openSortDialog"
                            @handle-search="handleLoadData"
                            @load-data="getTableList"
                            @handle-init-search="handleInitSearch"
                            @import-success="successImport"
                            @batchOperate="handleOpenBatchOperateDialog"
                            @sortVoucherNum="handleSortVoucherNum"
                            @changeVoucherNum="handleChangeVoucherNum"
                            @combineVoucher="handleOpenCombineDialog"
                        />
                        <div class="main-center">
                            <VoucherListItem
                                ref="voucherListRef"
                                :columnType="columnType"
                                :tableData="voucherList"
                                @handle-print="handlePrint"
                                @handle-delete="handleDelete"
                                @handle-check="handleCheck"
                                @handle-edit="handleEdit"
                                @handle-insert="handleInsert"
                                @handle-copy="handleCopy"
                                @handle-red="handleRed"
                                @handle-open-file-center="handleOpenFileCenter"
                                @handleSaveAsTemp="openSaveAsVoucherTempDialog"
                            >
                            </VoucherListItem>
                            <div class="table-pagination bottom" :class="{ base: !isErp }">
                                <TablePagination
                                    :page-sizes="paginationData.pageSizes"
                                    :page-size="paginationData.pageSize"
                                    :total="paginationData.total"
                                    :current-page="paginationData.currentPage"
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    @refresh="handleRerefresh"
                                ></TablePagination>
                            </div>
                        </div>
                    </div>
                    <div class="list-show-container" v-show="showBox === 'voucherRecyclebinList'">
                        <div class="title">凭证回收站</div>
                        <div class="main-top main-tool-bar space-between split-line">
                            <div class="main-tool-left"></div>
                            <div class="main-tool-right">
                                <a class="button mr-10" @click="closeRecyclebin">返回</a>
                                <a class="button mr-10" v-permission="['voucher-candelete']" @click="clearVoucherRecyclebin">清空回收站</a>
                                <a class="button" v-permission="['voucher-candelete']" @click="deleteRecyclebinVouchers">批量删除</a>
                                <RefreshButton></RefreshButton>
                            </div>
                        </div>
                        <div class="main-center">
                            <VoucherListItem
                                ref="voucherRecyclebinListRef"
                                :columnType="columnType"
                                :tableData="voucherRecyclebinList"
                                list-type="VoucherRecyclebinList"
                                @handle-delete-recyclebin="handleDeleteRecyclebin"
                                @handle-restore="handleRestore"
                            >
                            </VoucherListItem>
                            <div class="table-pagination">
                                <TablePagination
                                    :page-sizes="paginationData.pageSizes"
                                    :page-size="paginationDataRecyclebin.pageSize"
                                    :total="paginationDataRecyclebin.total"
                                    :current-page="paginationDataRecyclebin.currentPage"
                                    @size-change="handleSizeChangeRecyclebin"
                                    @current-change="handleCurrentChangeRecyclebin"
                                    @refresh="openRecyclebin"
                                >
                                </TablePagination>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #voucher>
                <div class="slot-content">
                    <div class="title">{{ getVoucherTitle(voucher?.getVoucherModel().vgId ?? 1010) }}</div>
                    <div class="slot-mini-content">
                        <div class="main-center">
                            <Voucher
                                ref="voucher"
                                v-model:query-params="voucherQueryParams"
                                @zoom="zoomCallback"
                                @load-success="voucherLoadSuccess"
                                @voucher-changed="voucherChanged"
                                :showSwitchBtn="['copy', 'insert', 'offset', 'edit', 'check'].indexOf(voucherType) !== -1"
                                :showCancel="true"
                                @back="backToMain"
                                :edited="edited"
                                @save="saveVoucher"
                                @delete-voucher="deleteVoucher"
                                :showKeysTip="['copy', 'insert', 'offset', 'edit', 'check'].indexOf(voucherType) !== -1"
                                :switchInfo="switchInfo"
                                @preVoucher="loadVoucherSwitchInfo(1)"
                                @nextVoucher="loadVoucherSwitchInfo(2)"
                            ></Voucher>
                        </div>
                    </div>
                </div>
            </template>
        </ContentSlider>
    </div>
    <el-dialog v-model="sortVoucherShow" title="整理凭证" center width="510px" class="custom-confirm dialogDrag">
        <div class="sort-voucherno-content" v-dialogDrag>
            <div class="sort-voucherno-main">
                <div class="options-form">
                    <div class="item">
                        <div class="item-title">整理期间：</div>
                        <div class="item-content">
                            <Select v-model="sortVoucherInfo.pid" placeholder=" " :teleported="false">
                                <el-option
                                    v-for="item in periodDateOriginList"
                                    :key="item.pid"
                                    :label="item.year + '年' + item.sn + '月'"
                                    :value="item.pid"
                                />
                            </Select>
                        </div>
                    </div>
                    <div class="item">
                        <div class="item-title">凭证字：</div>
                        <div class="item-content">
                            <el-select v-model="sortVoucherInfo.vgid" placeholder=" " :teleported="false">
                                <el-option value="" label="全部" />
                                <el-option v-for="item in voucherGroupList" :key="item.id" :label="item.title" :value="item.id + ''" />
                            </el-select>
                        </div>
                    </div>
                    <div class="item">
                        <el-radio-group v-model="sortVoucherInfo.sortType">
                            <el-radio label="0">按凭证号重新顺次编号</el-radio>
                            <el-radio label="1">按凭证日期重新顺次编号</el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="break-nums-border" v-show="sortBreakNumsShow">
                    <div class="break-nums">
                        <img src="@/assets/Settings/warn.png" alt="" class="tip-icon" />
                        <div class="break-nums-content" v-html="breakNumsListStr"></div>
                    </div>
                </div>
            </div>
            <div class="buttons">
                <a class="button mr-10" @click="() => (sortVoucherShow = false)">取消</a>
                <a class="button solid-button" @click="confirmSort">确认</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="divCopyShow" center title="复制凭证" width="440px" class="custom-confirm dialogDrag">
        <div class="copy-container" v-dialogDrag>
            <div class="copy-content">
                <table cellpadding="0" cellspacing="0">
                    <tr>
                        <td>凭证字：</td>
                        <td class="voucherGroup">
                            <el-select v-model="copyBatchVoucherGroup" :teleported="false">
                                <el-option v-for="item in voucherGroupList" :key="item.id" :value="item.id" :label="item.title" />
                            </el-select>
                        </td>
                    </tr>
                    <tr>
                        <td>凭证日期：</td>
                        <td class="voucherDate">
                            <el-date-picker
                                :disabled-date="disabledDate"
                                v-model="copyBatchVoucherDate"
                                type="date"
                                :clearable="true"
                                value-format="YYYY-MM-DD"
                            />
                        </td>
                    </tr>
                </table>
            </div>
            <div class="buttons" style="border-top: 1px solid var(--border-color)">
                <a class="button solid-button" @click="copyConfirm">复制</a>
                <a class="button" :class="{ 'ml-10': !isErp }" @click="() => (divCopyShow = false)">取消</a>
            </div>
        </div>
    </el-dialog>
    <el-dialog v-model="clearVouchersDialogShow" title="提示" center width="400px" class="custom-confirm dialogDrag">
        <div class="dialog-content" v-dialogDrag>
            <div class="dialog-content-body">清空凭证将删除所有的凭证行数据、关联的附件、备注等。确定要清空凭证吗？</div>
            <div class="buttons" style="border-top: 1px solid var(--border-color)">
                <a
                    class="button solid-button"
                    @click="
                        clearVoucher();
                        clearVouchersDialogShow = false;
                    "
                    >清空</a
                >
                <a class="button ml-10" @click="clearVouchersDialogShow = false">取消</a>
            </div>
        </div>
    </el-dialog>
    <LoadVoucherTemplate @on-select="loadVoucherTemplate" ref="loadVoucherTemplateDialog"></LoadVoucherTemplate>
    <SaveVoucherTemplate
        @save-voucher-template="saveVoucherTemplate"
        ref="saveVoucherTemplateDialog"
        @closed="handleResetTempInfo"
    ></SaveVoucherTemplate>
    <LoadVoucherDraft @on-select="loadVoucherDraft" ref="loadVoucherDraftDialog"></LoadVoucherDraft>
    <UploadFileDialog ref="uploadFileDialogRef" :readonly="fileCenterReadonly" @save="saveAttachfiles" />
    <ConfirmWithoutGlobal
        v-model="confirmWithoutGlobalVisible"
        :show-close="false"
        confirmButtonText="编辑原凭证"
        cancelButtonText="进入新页面"
        :cancel-click="reLoadCurrentPage"
    >
        <div style="text-align: left; margin: 0 -30px">
            您之前编辑的凭证还没有保存<br />点击'编辑原凭证'则可继续编辑原凭证<br />点击'进入新页面'则原凭证将不会保存并进入凭证列表页面
        </div>
    </ConfirmWithoutGlobal>
    <DialogBatchOperate @load-data="handleRefreshByChangeCreatedBy" ref="dialogBatchOperateRef" />
    <DialogSortVoucher ref="dialogSortVoucherRef" @load-data="getTableList" />
    <DialogChangeVoucherNum ref="dialogChangeVoucherNumRef" @load-data="getTableList" />
    <DialogHandleRed ref="dialogHandleRedRef" @confirmOffset="confirmOffset" @closed="handleResetOffset" />
    <EasyRecommendDialog ref="EasyRecommendDialogRef"></EasyRecommendDialog>
    <DialogVoucherCombine ref="dialogVoucherCombineRef" @combine="handleCombine" />
</template>

<script lang="ts">
export default {
    name: "VoucherList",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, watchEffect, nextTick, toRef, onMounted, onUnmounted, onActivated, onDeactivated, computed, inject } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { ElConfirm } from "@/util/confirm";
import { getUrlSearchParams, reloadPeriodInfo, tryClearCustomUrlParams } from "@/util/url";
import { usePagination } from "@/hooks/usePagination";
import { formatNumberRange } from "./utils";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getDatesMax } from "./utils";
import { getMonthStart, getMonthEnd } from "@/util/format";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { goToScrollTop } from "@/util/common";
import { PeriodStatus } from "@/api/period";
import {
    EditVoucherQueryParams,
    InsertVoucherQueryParams,
    CopyVoucherQueryParams,
    OffsetVoucherQueryParams,
    VoucherQueryParams,
    NewVoucherQueryParams,
    VoucherSaveParams,
    VoucherSaveModel,
    VoucherTemplateSaveParams,
    LoadVoucherTemplateQueryParams,
    LoadVoucherDraftQueryParams,
    RecyclebinVoucherQueryParams,
    VoucherModel,
    VoucherAttachFileModel,
    VoucherSwitchInfoModel,
    DataVoucherQueryParams,
} from "@/components/Voucher/types";
import { getGlobalToken } from "@/util/baseInfo";
import { useLoading } from "@/hooks/useLoading";
import { getCurrentPeriodApi, type IPeriod } from "@/api/period";
import { dayjs } from "element-plus";
import type { IVoucherRecyclebin, IVoucherModel, ISearchParams, IVoucherLine, ICombineInfo, IMergeBack } from "./types";
import { VoucherTemplateSaveModel, VoucherTemplateLineSaveModel, createVoucherTemplate } from "@/api/voucherTemplate";
import type { IFileInfo } from "@/components/UploadFileDialog/types";
import type { IVoucherListItem } from "../components/VoucherListItem/types";

import ContentSlider from "@/components/ContentSlider/index.vue";
import LoadVoucherTemplate from "@/views/Voucher/components/LoadVoucherTemplate/index.vue";
import SaveVoucherTemplate from "@/views/Voucher/components/SaveVoucherTemplate/index.vue";
import LoadVoucherDraft from "@/views/Voucher/components/LoadVoucherDraft/index.vue";
import UploadFileDialog from "@/components/UploadFileDialog/index.vue";
import Voucher from "@/components/Voucher/index.vue";
import MainTopBox from "./components/MainTopBox.vue";
import VoucherListItem from "../components/VoucherListItem/index.vue";
import TablePagination from "@/components/Table/TablePagination.vue";
import Select from "@/components/Select/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import { checkPermission } from "@/util/permission";
import { getGlobalLodash } from "@/util/lodash";
import { useRouterArrayStoreHook } from "@/store/modules/routerArray";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { deleteVoucherQuickKeycode, setVoucherQuickKeycode, handleCheckHasDialog } from "../NewVoucher/utils";
import ConfirmWithoutGlobal from "@/components/ConfirmWithoutGlobal/index.vue";
import { useFullScreenStore } from "@/store/modules/fullScreen";
import DialogBatchOperate from "./components/DialogBatchOperate.vue";
import DialogHandleRed from "./components/DialogHandleRed.vue";
import DialogSortVoucher from "./components/DialogSortVoucher.vue";
import DialogChangeVoucherNum from "./components/DialogChangeVoucherNum.vue";
import { dispatchReloadAsubAmountEvent } from "@/components/Voucher/utils";
import { AttachFileCategory } from "@/views/ERecord/types";
import { showDeleteBillOrVoucherConfirm } from "@/components/UploadFileDialog/utils";
import { pageScrollKey } from "@/symbols";
import EasyRecommendDialog  from "../components/EasyRecommendDialog/index.vue";
import DialogVoucherCombine from "./components/DialogVoucherCombine.vue";
const EasyRecommendDialogRef = ref<InstanceType<typeof EasyRecommendDialog>>();
const confirmWithoutGlobalVisible = ref(false);
const _ = getGlobalLodash();
const periodStore = useAccountPeriodStore();
interface IBreakNumsList {
    [key: number]: number[];
}

const route = useRoute();
const accountSet = useAccountSetStore().accountSet;
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const {
    paginationData: paginationDataRecyclebin,
    handleCurrentChange: handleCurrentChangeRecyclebin,
    handleSizeChange: handleSizeChangeRecyclebin,
} = usePagination();
const clearVouchersDialogShow = ref(false);

const uploadFileDialogRef = ref<InstanceType<typeof UploadFileDialog>>();
const loadVoucherTemplateDialog = ref<InstanceType<typeof LoadVoucherTemplate>>();
const saveVoucherTemplateDialog = ref<InstanceType<typeof SaveVoucherTemplate>>();
const loadVoucherDraftDialog = ref<InstanceType<typeof LoadVoucherDraft>>();
const voucherListRef = ref<InstanceType<typeof VoucherListItem>>();
const voucherRecyclebinListRef = ref<InstanceType<typeof VoucherListItem>>();
const mainTopBoxRef = ref<InstanceType<typeof MainTopBox>>();
const dialogBatchOperateRef = ref<InstanceType<typeof DialogBatchOperate>>();
const dialogSortVoucherRef = ref<InstanceType<typeof DialogSortVoucher>>();
const dialogChangeVoucherNumRef = ref<InstanceType<typeof DialogChangeVoucherNum>>();
const dialogVoucherCombineRef = ref<InstanceType<typeof DialogVoucherCombine>>();

const isErp = ref(window.isErp);
const copyBatchVoucherGroup = ref(useVoucherGroupStore()?.defaultVgId || 1010);
const copyBatchVoucherDate = ref("");
const divCopyShow = ref(false);
const checkNeeded = ref(false);
const columnType = ref(0);
const voucherList = ref<IVoucherListItem[]>([]);
const periodDateList = ref<IPeriod[]>([]);
const periodDateOriginList = ref<IPeriod[]>([]);
const voucherRecyclebinList = ref<IVoucherListItem[]>([]);
const voucherGroupList = toRef(useVoucherGroupStore(), "voucherGroupList");
const sortVoucherShow = ref(false);
const showBox = ref("voucherList");
const breakNumsList = ref<IBreakNumsList>({});
const sortBreakNumsShow = ref(false);
const breakNumsListStr = ref("");
const maxSearchDate = ref("");
const fileCenterReadonly = ref(false);
let needLoad = false;
const sortVoucherInfo = reactive({
    pid: periodStore.period.endPid,
    sortType: "0",
    vgid: "",
});

watchEffect(() => {
    const cachePeriodList = _.cloneDeep(periodStore.periodList);
    const periodList = cachePeriodList.reverse();
    periodDateList.value = periodList;
    periodDateOriginList.value = periodList.filter((item: any) => item.status !== PeriodStatus.CheckOut);
});

const getSearchPeriod = () => request({ url: "/api/Period/List" });

const accountSetListener = toRef(useAccountSetStore(), "accountSet");
watch(
    () => accountSetListener.value?.checkNeeded,
    (newVal) => {
        checkNeeded.value = newVal === 1;
    }
);

const handleInit = async () => {
    checkNeeded.value = accountSet?.checkNeeded === 1;
};
handleInit();
const handleInitSearch = (needCheck?: boolean) => {
    currentSlot.value = "main";
    loadingCount = 0;
    handleLoadData(!!needCheck);
};

const fileCenterInfo = reactive({
    pid: 0,
    vid: 0,
});
const handleOpenFileCenter = (pid: number, vid: number) => {
    fileCenterInfo.pid = pid;
    fileCenterInfo.vid = vid;
    request({ url: "/api/Voucher/WithoutVoucherEntry?pId=" + pid + "&vId=" + vid }).then((res: any) => {
        if (res.state !== 1000) return;
        fileCenterReadonly.value = res.data.approveStatus === 1 || res.data.pstatus === 3 || !checkPermission(["voucher-canedit"]);
        const { startDate, endDate } = mainTopBoxRef.value?.getSearchParams() as ISearchParams;
        uploadFileDialogRef.value?.changeERecordSearchDate(startDate, endDate);
        uploadFileDialogRef.value?.open(
            {
                eRecordSearchDate: { startDate: getMonthStart(new Date(res.data.vdate)), endDate: getMonthEnd(new Date(res.data.vdate)) },
                fileCategory: AttachFileCategory.Voucher,
            },
            res.data.attachFiles,
            10001
        );
    });
};
let isVoucherExecute = true;
let tempVoucherLines: Array<IVoucherLine> = [];
function openSaveAsVoucherTempDialog(voucherLines: Array<IVoucherLine>) {
    tempVoucherLines = [...voucherLines];
    isVoucherExecute = false;
    saveVoucherTemplateDialog.value?.showSaveVoucherTemplateDialog();
}
function handleResetTempInfo() {
    tempVoucherLines.length = 0;
    isVoucherExecute = true;
}
async function saveAttachfiles(params: any, newFileids: Number[], delFileids: Number[], fileList: IFileInfo[]) {
    const attachFiles = fileList.map((f) => {
        let model = new VoucherAttachFileModel();
        model.fileId = f.fileId;
        model.fileName = f.fileName;
        model.fileSize = f.fileSize;
        model.relativePath = f.relativePath;
        model.fileType = f.fileType;
        return model;
    });
    const fileIds = attachFiles.map((f) => f.fileId).join(",");
    const pid = fileCenterInfo.pid;
    const vid = fileCenterInfo.vid;
    const item = voucherList.value.find((item) => item.vid === vid && item.pid === pid) as IVoucherListItem;
    let newFileLength = item.attachments + newFileids.length - delFileids.length;
    if (newFileLength < 0) {
        newFileLength = 0;
    }
    if (!delFileids.length) {
        saveAttachfileFn(pid, vid, fileIds, newFileLength);
        return;
    }
    const needToast = await checkNeedToastWithBillAndVoucher(pid, vid, delFileids.join(","));
    if (!needToast) {
        saveAttachfileFn(pid, vid, fileIds, newFileLength);
        return;
    }
    showDeleteBillOrVoucherConfirm("voucher").then((batchDelete: boolean) => {
        saveAttachfileFn(pid, vid, fileIds, newFileLength, batchDelete);
    });
}
function saveAttachfileFn(pid: number, vid: number, fileIds: string, newFileLength: number, isNeedSaveToOther = false) {
    request({ url: "/api/Voucher?pId=" + pid + "&vId=" + vid }).then((res: any) => {
        if (res.state !== 1000) return;
        const result: VoucherModel = res.data;
        const params = {
            pid: result.pid,
            vid: result.vid,
            vgId: result.vgId,
            vnum: result.vnum,
            attachments: newFileLength,
            note: result.note,
            vtype: result.vtype,
            vdate: result.vdate,
            fileIds: fileIds,
            autoVNum: true,
            voucherLines: result.voucherLines,
            isNeedSaveToOther,
        };
        request({ url: "/api/Voucher", method: "put", data: params }).then((r: any) => {
            if (r.state == 1000) {
                // 不需要再次加载数据  只需要改变凭证的附件数量
                const index = voucherList.value.findIndex((item) => item.vid === vid);
                const item = voucherList.value[index];
                if (index !== -1) {
                    const beforeList = voucherList.value.slice(0, index);
                    const afterList = voucherList.value.slice(index + 1);
                    voucherList.value = [...beforeList, { ...item, attachments: newFileLength }, ...afterList];
                }
            }
        });
    });
}
async function checkNeedToastWithBillAndVoucher(pid: number, vid: number, delFileids: string) {
    return await request({
        url: "/api/Voucher/GetNeedSaveToOther",
        method: "post",
        params: { pid, vid, delFileids },
    })
        .then((res: IResponseModel<boolean>) => {
            if (res.state !== 1000) return false;
            return res.data;
        })
        .catch(() => {
            return false;
        });
}

// 凭证相关
const switchInfo = ref(new VoucherSwitchInfoModel());
const voucherType = ref<"check" | "edit" | "copy" | "insert" | "offset" | "" | "restore">("");
const edited = ref(false);
const saving = ref(false);
const slots = ["main", "voucher"];
const currentSlot = ref("main");
const voucherQueryParams = ref<VoucherQueryParams>(new VoucherQueryParams());
const voucher = ref<InstanceType<typeof Voucher>>();
const zoomState = ref<"in" | "out">("in");
const zoomCallback = ref((_zoomState: "in" | "out"): void => {
    zoomState.value = _zoomState;
});
let loading = false;
function loadVoucherSwitchInfo(operation: 0 | 1 | 2, loadingTip = true) {
    if ((operation === 1 && switchInfo.value.hasPrev === false) || (operation === 2 && switchInfo.value.hasNext === false)) {
        return;
    }
    voucher.value && (voucher.value.warningRowIndex = -1);
    if (loading && operation !== 0) {
        loadingTip && ElNotify({ message: "正在切换，请稍后点击~", type: "warning" });
        return;
    }
    loading = true;
    let voucherModel = voucher.value?.getVoucherModel();
    request({
        url: "/api/Voucher/GetVoucherSwitchInfo",
        method: "post",
        data: {
            pid: voucherModel?.pid === 0 ? "" : voucherModel?.pid,
            date: voucherModel?.vdate,
            vgId: voucherModel?.vgId,
            vnum: voucherModel?.vnum,
            operation: operation,
            isNewVoucher: false,
            voucherListQueryParams: currentSearchParams,
        },
    })
        .then((res: any) => {
            loading = false;
            const data = res as IResponseModel<VoucherSwitchInfoModel>;
            if (data.state === 1000) {
                if (operation !== 0) {
                    handleEdit(data.data.vid, data.data.pid);
                }
                switchInfo.value = data.data;
            }
        })
        .catch(() => {
            loading = false;
        });
}
function voucherLoadSuccess() {
    if (
        voucherQueryParams.value instanceof NewVoucherQueryParams ||
        voucherQueryParams.value instanceof EditVoucherQueryParams ||
        voucherQueryParams.value instanceof InsertVoucherQueryParams
    ) {
        edited.value = false;
    } else {
        edited.value = true;
    }
    loadVoucherSwitchInfo(0);
    currentSlot.value = "voucher";
    const scrollTimeout = setTimeout(() => {
        document.querySelector(".router-container")!.scrollTop = 0;
        clearTimeout(scrollTimeout);
    });
    if (voucherQueryParams.value instanceof EditVoucherQueryParams) {
        const voucherModel = voucher.value?.getVoucherModel();
        if (voucherModel && (voucherModel.approveStatus === 1 || voucherModel.pstatus === 3)) {
            voucherType.value = "check";
        } else {
            voucherType.value = "edit";
        }
    }
}
let voucherReadonly = ref(false);
function voucherChanged(readonly: boolean) {
    edited.value = true;
    voucherReadonly.value = readonly;
}
const routerArrayStore = useRouterArrayStoreHook();
const leaveValidator = routerArrayStore.registerLeaveValidator(route.path, () => {
    if (edited.value) {
        if (!confirm("您确认要离开吗？系统可能不会保存您的更改~")) {
            return false;
        }
    }
    return true;
});
const currentPath = ref(route.path);
watch(edited, (newVal) => {
    routerArrayStore.changeRouterEditting(currentPath.value, newVal);
});
onUnmounted(() => {
    leaveValidator.dispose();
});
function handleEdit(vid: number, pid: number, vstatus = 0) {
    voucherType.value = "edit";
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
    voucher.value?.setIsTempVoucher(vstatus === 1);
}
function handleCheck(vid: number, pid: number, vstatus = 0) {
    voucherType.value = "check";
    voucherQueryParams.value = new EditVoucherQueryParams(pid, vid);
    voucher.value?.setIsTempVoucher(vstatus === 1);
}
const currentOffsetInfo = { vid: 0, pid: 0 };
function handleResetOffset() {
    currentOffsetInfo.vid = 0;
    currentOffsetInfo.pid = 0;
}
function confirmOffset() {
    handleRed(currentOffsetInfo.vid, currentOffsetInfo.pid);
}
const dialogHandleRedRef = ref<InstanceType<typeof DialogHandleRed>>();
function handleRed(vid: number, pid: number, voucherModel?: IVoucherListItem) {
    currentOffsetInfo.vid = vid;
    currentOffsetInfo.pid = pid;
    if (voucherModel && voucherModel.rf.length > 0) {
        dialogHandleRedRef.value?.handleShowRedVoucher(voucherModel.rf);
        return;
    }
    voucherType.value = "offset";
    voucherQueryParams.value = new OffsetVoucherQueryParams(pid, vid);
}
async function handleRefreshByChangeCreatedBy(refresh: boolean) {
    if (refresh) {
        const userNameList = await mainTopBoxRef.value?.getUserNameList();
        const { prepareBy = "" } = mainTopBoxRef.value?.getSearchParams() as ISearchParams;
        if (userNameList?.findIndex((item) => item === prepareBy) === -1) {
            mainTopBoxRef.value?.setSearchParams({ prepareBy: "" });
        }
    }
    getTableList();
}
function handleSortVoucherNum() {
    const { startPId = 0, endPId, startDate = "", endDate, selectorType = "0" } = mainTopBoxRef.value?.getSearchParams() || {};
    if (selectorType === "0") {
        if (startPId !== endPId) {
            ElNotify({ type: "warning", message: "不支持跨月进行凭证号排序，请重新筛选后再试哦~" });
            return false;
        }
    } else {
        if (dayjs(startDate).format("YYYYMM") !== dayjs(endDate).format("YYYYMM")) {
            ElNotify({ type: "warning", message: "不支持跨月进行凭证号排序，请重新筛选后再试哦~" });
            return false;
        }
    }
    if (voucherList.value.length === 0) {
        ElNotify({ type: "warning", message: "所选月份没有凭证数据，无需排序哦~" });
        return false;
    }
    if (voucherList.value.length === 1) {
        ElNotify({ type: "warning", message: "所选月份至少要有2张凭证才能进行排序哦~" });
        return false;
    }
    if (voucherList.value[0].status === PeriodStatus.CheckOut) {
        ElNotify({ type: "warning", message: "已结账期间不支持凭证号排序哦~" });
        return false;
    }
    if (mainTopBoxRef.value?.checkHasBreakNum()) {
        ElNotify({ type: "warning", message: "请先整理所选期间断号再进行凭证号排序哦~" });
        return false;
    }
    const [year, month] = dayjs(startDate).format("YYYY-MM").split("-");
    const datePid = periodDateList.value.find((item) => item.year.toString() === year && item.sn === Number(month))?.pid || 0;
    const pid = selectorType === "0" ? startPId : datePid;
    dialogSortVoucherRef.value?.handleOpenDialog(pid);
}
function checkCanChangeOrCombine(type: "change" | "combine") {
    const chooseList: IVoucherModel[] = voucherListRef.value?.getSelectedVoucherList() || [];
    const tipTheme = type === "change" ? "凭证号调整" : "凭证合并";
    if (chooseList.length === 0) {
        const message = type === "change" ? "请先勾选需要调整的凭证哦~" : "请先勾选需要合并的凭证哦~";
        ElNotify({ type: "warning", message });
        return false;
    }
    const firstItem = chooseList[0];
    let moreMonth = false;
    let moreVgName = false;
    let hasCheck = false;
    for (let i = 0; i < chooseList.length; i++) {
        if (firstItem.pid !== chooseList[i].pid) {
            moreMonth = true;
            break;
        }
        if (firstItem.vgName !== chooseList[i].vgName) {
            moreVgName = true;
            break;
        }
        if (type === "change") continue;
        if (chooseList[i].approveStatus === 1) {
            hasCheck = true;
            break;
        }
    }
    if (moreMonth) {
        ElNotify({ type: "warning", message: `不支持跨月进行${tipTheme}，请重新选择后再试哦~` });
        return false;
    }
    if (firstItem.status === PeriodStatus.CheckOut) {
        ElNotify({ type: "warning", message: `已结账期间不支持${tipTheme}哦~` });
        return false;
    }
    if (hasCheck && type === "combine") {
        ElNotify({ type: "warning", message: "已审核的凭证不支持凭证合并哦~" });
        return false;
    }
    if (mainTopBoxRef.value?.checkHasBreakNum() && type === "change") {
        ElNotify({ type: "warning", message: "请先整理所选期间断号再进行凭证号调整哦~" });
        return false;
    }
    if (moreVgName) {
        ElNotify({ type: "warning", message: "请选择相同凭证字的凭证哦~" });
        return false;
    }
    return true;
}
function handleChangeVoucherNum() {
    const chooseList: IVoucherModel[] = voucherListRef.value?.getSelectedVoucherList() || [];
    if (!checkCanChangeOrCombine("change")) return;
    const firstItem = chooseList[0];
    const { year, sn } = periodDateList.value.find((item) => item.pid === firstItem.pid) || { year: 0, sn: 0 };
    dialogChangeVoucherNumRef.value?.handleOpenDialog(chooseList, year, sn);
}
function handleOpenCombineDialog() {
    if (!checkPermission(["voucher-candelete"])) {
        ElNotify({ type: "warning", message: "您没有凭证删除权限，无法操作合并凭证哦~" });
        return;
    }
    const chooseList: IVoucherModel[] = voucherListRef.value?.getSelectedVoucherList() || [];
    if (!checkCanChangeOrCombine("combine")) return;
    if (chooseList.length < 2) {
        ElNotify({ type: "warning", message: "至少需要勾选2张凭证才可以操作合并哦~" });
        return;
    }
    dialogVoucherCombineRef.value?.handleOpenDialog(chooseList);
}
let isPrewiew = false;
function handleCombine(combineInfo: ICombineInfo) {
    if (isPrewiew) return;
    isPrewiew = true;
    const pid = combineInfo.selectList[0].pid;
    const vids = combineInfo.selectList.map((item) => item.vid);
    const data = { pid, vids, debitAsubMerge: combineInfo.debit, creditAsubMerge: combineInfo.credit }
    request({ url: "/api/Voucher/GetMergeVoucherPreview", method: "post", data }).then((res: IResponseModel<IMergeBack>) => {
        isPrewiew = false;
        if (res.state !== 1000) return;
        if (res.data.voucherLines.length === 0) {
            ElNotify({ type: "warning", message: "凭证合并失败！合并后所有科目金额相互抵消，没有凭证行；请重新选择凭证进行合并哦~" });
            return;
        }
        let totalDebit = 0;
        let totalCredit = 0;
        const maxAmount = 999999999.99;
        for (let i = 0; i < res.data.voucherLines.length; i++) {
            const voucherLine = res.data.voucherLines[i];
            totalDebit += voucherLine.debit;
            totalCredit += voucherLine.credit;
            const debit = Math.abs(voucherLine.debit);
            const credit = Math.abs(voucherLine.credit);
            if (debit > maxAmount || credit > maxAmount || totalDebit > maxAmount || totalCredit > maxAmount) {
                ElNotify({ type: "warning", message: "凭证合并失败！合并后科目金额或合计金额超过亿位，请重新选择凭证进行合并哦~" });
                return;
            }
        }
        voucherType.value = "edit";
        const params = new DataVoucherQueryParams(res.data.voucherLines);
        params.vnum = res.data.vnum;
        params.vdate = res.data.vdate;
        params.vgId = res.data.vgId;
        params.isCombine = true;
        params.pid = pid;
        params.vids = vids;
        params.attachFileIds = res.data.attachFileIds;
        params.attachFiles = res.data.attachFiles;
        params.note = res.data.note;
        params.attachments = res.data.attachments;
        voucherQueryParams.value = params;
    }).catch(() => {
        isPrewiew = false;
    })
}

let currentInsertData = ref({ vid: 0, pid: 0 });
function handleInsert(vid: number, pid: number) {
    currentInsertData.value = { vid: vid, pid: pid };
    voucherType.value = "insert";
    voucherQueryParams.value = new InsertVoucherQueryParams(pid, vid);
}

function handleCopy(vid: number, pid: number) {
    voucherType.value = "copy";
    voucherQueryParams.value = new CopyVoucherQueryParams(pid, vid);
}
let rids = 0;
function handleRestore(rid: number) {
    rids = rid;
    voucherType.value = "restore";
    voucherQueryParams.value = new RecyclebinVoucherQueryParams(rid);
}
function clearVoucher() {
    voucher.value?.clearVoucher();
}
function saveVoucher() {
    if (!getCanSaveVoucher()) {
        return;
    }
    saving.value = true;
    voucher.value?.saveVoucher(
        new VoucherSaveParams(1010, (res: IResponseModel<VoucherSaveModel>) => {
            needLoad = false;
            saving.value = false;
            if (res.state === 1000) {
                const voucherModel = voucher.value?.getVoucherModel();
                useAccountPeriodStore().changePeriods(res.data.pid.toString());
                edited.value = false;
                if (res.data.vnum !== voucherModel?.vnum) {
                    ElNotify({
                        message: "保存成功！" + voucherModel?.vnum + "号凭证号已经存在，已为您更新为" + res.data.vnum + "号凭证~",
                        type: "success",
                    });
                } else {
                    ElNotify({
                        message: "亲，保存成功啦！",
                        type: "success",
                    });
                }
                if (voucherType.value === "edit" || voucherType.value === "insert" || voucherType.value === "copy") {
                    EasyRecommendDialogRef.value?.setDialog(res.data.pid, res.data.vid);
                    voucherQueryParams.value = new EditVoucherQueryParams(res.data.pid, res.data.vid);
                    return;
                }
                if (voucherType.value === "restore") {
                    deleteRecyclebinApi(rids).then((res: any) => {
                        if (res.state != 1000) return;
                        getVoucherRecyclebinList();
                        saving.value = false;
                        currentSlot.value = "main";
                    });
                    return;
                }
                getTableList();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
        })
    );
}
function getCanSaveVoucher(): boolean {
    if (!checkPermission(["voucher-canedit"])) {
        return false;
    }
    if (!edited.value) {
        return false;
    }
    if (saving.value) {
        return false;
    }
    return true;
}
async function backToMain() {
    voucher.value?.removeEventListener();
    edited.value = false;
    const _s = mainTopBoxRef.value?.getSearchParams().startPId as number;
    const _e = mainTopBoxRef.value?.getSearchParams().endPId as number;
    await mainTopBoxRef.value?.changePeriodData(_s, _e);
    needLoad = true;
    if (needLoad) {
        if (voucherType.value === "restore") {
            currentSlot.value = "main";
            useFullScreenStore().changeFullScreenStage(false);
            openRecyclebin();
            const timer1 = setTimeout(() => {
                voucherType.value = "";
                clearTimeout(timer1);
            }, 300);
            return;
        }

        getSearchPeriod().then((res: any) => {
            if (res.state != 1000) return;
            periodDateList.value = res.data;
            getTableList(false, true);
            currentSlot.value = "main";
            useFullScreenStore().changeFullScreenStage(false);
            const timer2 = setTimeout(() => {
                voucherType.value = "";
                clearTimeout(timer2);
            }, 300);
        });
        return;
    }
    getSearchPeriod().then((res: any) => {
        if (res.state != 1000) return;
        periodDateList.value = res.data;
        currentSlot.value = "main";
        useFullScreenStore().changeFullScreenStage(false);
        const timer4 = setTimeout(() => {
            voucherType.value = "";
            clearTimeout(timer4);
        }, 300);
    });
}
function loadVoucherTemplate(vtId: number) {
    voucherQueryParams.value = new LoadVoucherTemplateQueryParams(vtId, getOriginEditType());
}
function saveVoucherTemplate(vtId: number, vtName: string, saveAmount: boolean) {
    if (saving.value) {
        return;
    }
    saving.value = true;
    if (!isVoucherExecute) {
        const params = new VoucherTemplateSaveModel();
        params.vtType = vtId;
        params.vtName = vtName;
        params.voucherLines = tempVoucherLines.map((i) => {
            const newValues: any = {};
            newValues.direction = i.debit ? 1 : i.credit ? 2 : 0;
            if (!saveAmount) {
                newValues.credit = 0;
                newValues.debit = 0;
            }
            return new VoucherTemplateLineSaveModel({
                ...i,
                ...newValues,
            });
        });
        createVoucherTemplate(params)
            .then((res: IResponseModel<number>) => {
                saving.value = false;
                if (res.state === 1000 && res.data !== 0) {
                    saveVoucherTemplateDialog.value?.close();
                    ElNotify({ type: "success", message: "亲，保存成功啦！" });
                } else {
                    ElNotify({ type: "warning", message: res.msg || "保存失败" });
                }
            })
            .catch(() => {
                saving.value = false;
                ElNotify({ type: "warning", message: "保存失败" });
            });
        return;
    }
    voucher.value?.saveVoucher(
        new VoucherTemplateSaveParams(vtId, vtName, (res: IResponseModel<VoucherTemplateSaveModel>) => {
            if (res.state === 1000) {
                ElNotify({
                    message: "亲，保存成功啦！",
                    type: "success",
                });
                saveVoucherTemplateDialog.value?.close();
            } else if (res.state === 2000) {
                if (res.subState !== -1) {
                    ElNotify({
                        message: res.msg,
                        type: "warning",
                    });
                }
            } else if (res.state === 9999) {
                ElNotify({
                    message: "保存失败",
                    type: "warning",
                });
            }
            saving.value = false;
        })
    );
}
function loadVoucherDraft(pid: number, vid: number) {
    voucherQueryParams.value = new LoadVoucherDraftQueryParams(pid, vid, getOriginEditType());
}

function getOriginEditType() {
    if (voucherQueryParams.value instanceof InsertVoucherQueryParams) {
        return 2;
    } else if (voucherQueryParams.value instanceof CopyVoucherQueryParams) {
        return 3;
    } else if (voucherQueryParams.value instanceof LoadVoucherTemplateQueryParams) {
        return voucherQueryParams.value.originEditType;
    } else if (voucherQueryParams.value instanceof LoadVoucherDraftQueryParams) {
        return voucherQueryParams.value.originEditType;
    } else {
        return 1;
    }
}

let canSearch = true;
let needWarning = true;
let currentSearchParams: ISearchParams;
function checkCreateDateIsLicit() {
    const { startCreateTime, endCreateTime } = route.query;
    if (loadingCount !== 1 || !startCreateTime || !endCreateTime) return false;
    const day_s = dayjs(startCreateTime  as string);
    const day_e = dayjs(endCreateTime as string);
    if (!day_s.isValid() || !day_e.isValid()) return false;
    if (day_s.isAfter(day_e)) return false;
    return true;
}
const getTableList = (scrollToTop = false, debug = false) => {
    voucherListRef.value?.clearSelectedVoucherList();
    debug && voucherListRef.value?.cacheLastScrollTop();
    voucherList.value = [];
    voucherListRef.value?.setInitEnd(false);
    const searchParams = mainTopBoxRef.value?.getSearchParams() as ISearchParams;
    window.localStorage.setItem(
        "voucherList-searchParams-" + getGlobalToken(),
        JSON.stringify({ sortColumn: searchParams.sortColumn, sortType: searchParams.sortType })
    );
    const params: any = { ...searchParams, pageIndex: paginationData.currentPage, pageSize: paginationData.pageSize };
    const { startCreateTime, endCreateTime } = route.query;
    if (checkCreateDateIsLicit()) {
        params.vStartCreateDate = startCreateTime;
        params.vEndCreateDate = endCreateTime;
        params.isUseCreateDate = true;
    }
    request({ url: "/api/Voucher/PagingList?" + getUrlSearchParams(params) })
        .then((res: any) => {
            if (res.state != 1000) return;
            isErp.value && res.msg && ElNotify({ type: "warning", message: res.msg });
            const data: IVoucherModel[] = res.data.data;
            debug && voucherListRef.value?.debugScroll();
            voucherList.value = data.map((item) => {
                return {
                    ...item,
                    checked: false,
                };
            });
            if (loadingCount === 1) {
                if (needCheck) {
                    voucherListRef.value?.setSelectList(voucherList.value);
                }
                const sortVNums = route.query.sortVNums;
                if (sortVNums) {
                    sortVoucherInfo.pid = Number(route.query.CheckOutP);
                    openSortDialog();
                }
            }
            paginationData.total = res.data.count;
            if (needWarning) {
                mainTopBoxRef.value?.checkBreakNum();
            }
        })
        .finally(() => {
            canSearch = true;
            mainTopBoxRef.value?.setPeriodInfo(params.selectorType as "0" | "1");
            currentSlot.value = "main";
            // window.localStorage.setItem("VoucherListSize", params.pageSize + "");
            needWarning = true;
            scrollToTop && goToScrollTop();
            currentSearchParams = searchParams;
            reloadPeriodInfo();
            voucherListRef.value?.setInitEnd(true);
            if (loadingCount === 1) {
                tryClearCustomUrlParams(route);
            }
        });
};

let loadingCount = 0;
let needCheck = false;
const handleLoadData = (check?: boolean) => {
    needCheck = check ? true : false;
    paginationData.currentPage === 1 ? handleSearch() : ((paginationData.currentPage = 1), setTimeout(() => (needWarning = true), 0));
};
const handleSearch = () => {
    if (!canSearch) return;
    canSearch = false;
    loadingCount++;
    getTableList();
};
function deleteVoucher() {
    const voucherModel = voucher.value?.getVoucherModel();
    if (voucherModel) {
        if (voucherModel.pid && voucherModel.vid) {
            handleDelete(voucherModel.vid, voucherModel.pid);
        }
    }
}
const getVoucherRecyclebinListApi = () => {
    const params = {
        pageIndex: paginationDataRecyclebin.currentPage,
        pageSize: paginationDataRecyclebin.pageSize,
    };
    if (paginationDataRecyclebin.currentPage === 0) {
        params.pageIndex = 1;
    }
    // window.localStorage.setItem("VoucherListSize", params.pageSize + "");
    return request({ url: "/api/VoucherRecyclebin/PagingList?" + getUrlSearchParams(params) });
};
const getVoucherRecyclebinList = () => {
    voucherRecyclebinListRef.value?.clearSelectedVoucherList();
    handleLoadingRecyclebinList();
};
interface IVoucherRecyclebinBack {
    data: IVoucherRecyclebin[];
    count: number;
}
const handleLoadingRecyclebinList = () => {
    voucherRecyclebinListRef.value?.setInitEnd(false);
    getVoucherRecyclebinListApi().then((res: IResponseModel<IVoucherRecyclebinBack>) => {
        if (res.state != 1000) return;
        const data: IVoucherRecyclebin[] = res.data.data;
        if (data.length === 0) {
            paginationDataRecyclebin.currentPage = 0;
        } else if (paginationDataRecyclebin.currentPage === 0) {
            paginationDataRecyclebin.currentPage = 1;
        }
        voucherRecyclebinList.value = data.map((item) => {
            return {
                ...item.voucherModel,
                checked: false,
                rid: item.rid,
            };
        });
        voucherRecyclebinListRef.value?.setInitEnd(true);
        paginationDataRecyclebin.total = res.data.count;
    });
};
const openRecyclebin = () => {
    getVoucherRecyclebinList();
    showBox.value = "voucherRecyclebinList";
};
const closeRecyclebin = () => {
    const callback = () => {
        showBox.value = "voucherList";
        nextTick(() => {
            getTableList();
        });
    };
    initPeriodInfo(callback);
};
const deleteRecyclebinVouchers = () => {
    const voucherList = voucherRecyclebinListRef.value?.getSelectedVoucherList() || [];
    if (voucherList.length === 0) {
        ElNotify({ type: "warning", message: "亲，请先勾选凭证。" });
        return;
    }
    const rIds = voucherList.map((item: any) => item.rid);
    handleDeleteRecyclebin(rIds);
};
const clearVoucherRecyclebin = () => {
    ElConfirm("亲，确定要清空回收站吗？").then((r: any) => {
        if (r) {
            request({ url: "/api/VoucherRecyclebin/Clear", method: "delete" }).then((res: any) => {
                if (res.state != 1000 || res.data != true) {
                    ElNotify({ type: "warning", message: "清空失败" });
                    return;
                }
                ElNotify({ type: "success", message: "清空成功" });
                getVoucherRecyclebinList();
            });
        }
    });
};

// 批量删除
let canBatchDelete = true;
const deleteVouchersBatch = () => {
    if (!canBatchDelete) {
        ElNotify({ type: "warning", message: "亲，正在删除中，请稍后再试~" });
        return;
    }
    const chooseList: IVoucherModel[] = voucherListRef.value?.getSelectedVoucherList() || [];
    if (chooseList.length === 0) {
        ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！", showClose: true });
        return;
    }
    const queryParamsList = chooseList.map((item) => {
        return {
            vid: item.vid,
            pid: item.pid,
        };
    });
    ElConfirm("亲，确认要删除凭证吗？").then((r: any) => {
        if (r) {
            useLoading().enterLoading("努力加载中，请稍后...");
            request({
                url: "/api/Voucher/Batch",
                method: "delete",
                headers: { "content-type": "application/json" },
                data: JSON.stringify(queryParamsList),
            })
                .then((r: any) => {
                    useLoading().quitLoading();
                    if (r.state !== 1000) {
                        ElNotify({ type: "warning", message: r.msg || "亲，删除失败啦！请联系侧边栏客服！" });
                        return;
                    }
                    const result = r.data;
                    if (result != undefined) {
                        if (result.isOvertime === true) {
                            ElNotify({ type: "warning", message: "数据太多啦，服务器太忙了，请10分钟后分批删除哦~" });
                        } else {
                            dispatchReloadAsubAmountEvent();
                            ElConfirm(
                                "成功：" +
                                    result.batchOperationResult.success +
                                    "，跳过：" +
                                    result.batchOperationResult.jump +
                                    "（已经审核、已经结账的凭证，将会被跳过！）",
                                true
                            );
                            const length = result.batchOperationResult.success;
                            const tableLength = voucherList.value.length;
                            const callback = () => {
                                if (length === tableLength) {
                                    paginationData.currentPage === 1
                                        ? getTableList(false, true)
                                        : (paginationData.currentPage--, setTimeout(() => (needWarning = true), 0));
                                } else {
                                    getTableList(false, true);
                                }
                            };
                            initPeriodInfo(callback);
                        }
                    } else {
                        ElNotify({ type: "warning", message: "亲，删除失败啦！请联系侧边栏客服！" });
                    }
                })
                .catch(() => {
                    useLoading().quitLoading();
                })
                .finally(() => {
                    canBatchDelete = true;
                });
        }
    });
};
const copyVoucherList = ref<{ pid: number; vid: number }[]>([]);
const copyConfirm = () => {
    if (!copyBatchVoucherDate.value) {
        ElNotify({ type: "warning", message: "亲，凭证日期不能为空！" });
        return;
    }
    if (copyVoucherList.value.length > 50) {
        ElNotify({ type: "warning", message: "亲，批量复制不能超过50张凭证" });
        return;
    }
    useLoading().enterLoading("努力加载中，请稍后...");
    request({
        url: "/api/Voucher/BatchCopyVoucher?vDate=" + copyBatchVoucherDate.value + "&vgId=" + copyBatchVoucherGroup.value,
        method: "post",
        headers: { "content-type": "application/json" },
        data: copyVoucherList.value,
    })
        .then(async (r: any) => {
            useLoading().quitLoading();
            if (r.state != 1000) {
                ElNotify({ type: "warning", message: r.msg || "复制失败" });
                return;
            }
            const result = r.data;
            if (result != undefined) {
                if (result.IsOvertime === true) {
                    ElNotify({ type: "warning", message: "数据太多啦，服务器太忙了，请10分钟后分批删除哦~" });
                } else {
                    divCopyShow.value = false;
                    dispatchReloadAsubAmountEvent();
                    ElConfirm(
                        "成功：" +
                            result.success +
                            "，失败：" +
                            (result.faild + result.jump) +
                            "（" +
                            (window.isErp ? "暂存凭证、" : "") +
                            "科目已停用或辅助核算已停用的凭证，将会被跳过！）",
                        true
                    );
                    const _s = mainTopBoxRef.value?.getSearchParams().startPId as number;
                    const _e = mainTopBoxRef.value?.getSearchParams().endPId as number;
                    await mainTopBoxRef.value?.changePeriodData(_s, _e);
                    getTableList();
                }
            } else {
                ElNotify({ type: "warning", message: "亲，复制失败啦！请联系侧边栏客服！" });
            }
        })
        .catch(() => {
            useLoading().quitLoading();
        });
};
const copyVouchersBatch = () => {
    const voucherList: IVoucherModel[] = voucherListRef.value?.getSelectedVoucherList() || [];
    if (voucherList.length === 0) {
        ElNotify({ type: "warning", message: "亲，您还没有选择凭证哦！" });
        return;
    }
    if (voucherList.length === 1) {
        handleCopy(voucherList[0].vid, voucherList[0].pid);
        return;
    }
    divCopyShow.value = true;
    const dateList = voucherList.map((item) => {
        return item.vdate;
    });
    const maxDate = getDatesMax(dateList);
    copyBatchVoucherDate.value = maxDate;
    const queryParamsList = voucherList.map((item) => {
        return {
            vid: item.vid,
            pid: item.pid,
        };
    });
    copyVoucherList.value = queryParamsList;
};
const openSortDialog = () => {
    const params = {
        pId: sortVoucherInfo.pid,
        vgId: sortVoucherInfo.vgid,
    };
    request({ url: "/api/Voucher/GetAllBreakNum?" + getUrlSearchParams(params), method: "post" })
        .then((r: any) => {
            if (r.state === 1000) {
                breakNumsList.value = r.data;
                const str = getBreakNumStr(breakNumsList.value);
                breakNumsListStr.value = str;
                sortBreakNumsShow.value = breakNumsListStr.value.length > 0;
                sortVoucherShow.value = true;
            } else {
                ElNotify({ type: "warning", message: "亲，获取断号信息失败，请联系侧边栏客服！" });
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "亲，获取断号信息失败，请联系侧边栏客服！" });
        });
};
const confirmSort = () => {
    sortVoucherShow.value = false;
    ElConfirm("此操作不可逆，凭证将重新编号，确认继续吗？").then((r: any) => {
        r ? handleSort() : (sortVoucherShow.value = true);
    });
};
const handleSort = () => {
    const params = {
        sortType: sortVoucherInfo.sortType,
        pId: sortVoucherInfo.pid,
        vgId: sortVoucherInfo.vgid || 0,
    };
    request({ url: "/api/Voucher/SortVoucher?" + getUrlSearchParams(params), method: "post" }).then((res: any) => {
        if (res.state != 1000) {
            ElNotify({ type: "warning", message: "亲，整理失败了，请联系侧边栏客服！" });
            return;
        }
        ElNotify({ type: "success", message: "亲，整理成功啦！" });
        handleSearch();
    });
};
const handlePrint = (vid: string | number, pid: string | number) => {
    mainTopBoxRef.value?.printVoucherItem(vid, pid);
};
const deleteRecyclebinApi = (rid: number | string) => {
    return request({ url: "/api/VoucherRecyclebin?rIds=" + rid, method: "delete" });
};
const deleteRecyclebinApi2 = (rid: number[]) => {
    return request({ 
        url: "/api/VoucherRecyclebin/Batch", 
        method: "delete",
        data: rid,
    });
};
const handleDeleteRecyclebin = (rid: number[]) => {
    ElConfirm("亲，确定要删除吗？").then((r: any) => {
        if (r) {
            deleteRecyclebinApi2(rid)
                .then((res: any) => {
                    if (res.state != 1000 || res.data != true) {
                        ElNotify({ type: "warning", message: "删除失败" });
                        return;
                    }
                    ElNotify({ type: "success", message: "删除成功" });
                    getVoucherRecyclebinList();
                })
                .catch(() => {
                    ElNotify({ type: "warning", message: "亲，删除失败了，请联系侧边栏客服！" });
                });
        }
    });
};
let cantDelete = false;
const handleDelete = (vid: string | number, pid: string | number) => {
    if (cantDelete) return;
    cantDelete = true;
    if (periodList.value.find((z) => z.pid == pid)?.status === 3) {
        ElNotify({ type: "warning", message: "亲，凭证所在的期间已结账，请先在期末结转反结账到对应期间！" });
        cantDelete = false;
        return;
    }
    ElConfirm("亲，确认要删除吗？").then((r: any) => {
        if (r) {
            request({ url: "/api/Voucher?pId=" + pid + "&vId=" + vid, method: "delete" })
                .then((r: any) => {
                    cantDelete = false;
                    if (r.state != 1000 || r.data != true) {
                        ElNotify({ type: "warning", message: r.msg || "亲，删除失败了！" });
                        return;
                    }
                    ElNotify({ type: "success", message: "亲，删除成功啦！" });
                    const callback = () => {
                        if (voucherList.value.length === 1) {
                            paginationData.currentPage === 1
                                ? getTableList(false, true)
                                : (paginationData.currentPage--, setTimeout(() => (needWarning = true), 0));
                        } else {
                            getTableList(false, true);
                        }
                    };
                    initPeriodInfo(callback);
                })
                .catch(() => {
                    cantDelete = false;
                    ElNotify({ type: "warning", message: "亲，删除失败了，请联系侧边栏客服！" });
                });
        } else {
            cantDelete = false;
        }
    });
};
const successImport = () => {
    const callback = () => {
        paginationData.currentPage === 1 ? getTableList() : ((paginationData.currentPage = 1), setTimeout(() => (needWarning = true), 0));
    };
    initPeriodInfo(callback);
};
const initPeriodInfo = (callback: Function) => {
    getSearchPeriod().then((res: IResponseModel<IPeriod[]>) => {
        if (res.state === 1000) {
            const copyPeriodList: IPeriod[] = JSON.parse(JSON.stringify(periodDateList.value));
            periodDateList.value = res.data;
            const pList = periodDateList.value.map((item) => item.pid);
            const maxPid = Math.max(...pList);
            getCurrentPeriodApi().then(async (r: IResponseModel<IPeriod>) => {
                if (r.state !== 1000) {
                    ElNotify({ type: "warning", message: "亲，获取期间信息失败，请联系侧边栏客服！" });
                    return;
                }
                const currentP = r.data.pid;
                const _s = mainTopBoxRef.value?.getSearchParams().startPId as number;
                const _e = mainTopBoxRef.value?.getSearchParams().endPId as number;
                const loadHandle = () => {
                    paginationData.currentPage === 1
                        ? getTableList()
                        : ((paginationData.currentPage = 1), setTimeout(() => (needWarning = true), 0));
                    currentSlot.value = "main";
                    showBox.value = "voucherList";
                };
                if (copyPeriodList.findIndex((item) => item.pid === currentP) === -1) {
                    // 当前期间需要重置
                    mainTopBoxRef.value?.changePeriodInfo(currentP, currentP);
                    await mainTopBoxRef.value?.changePeriodData(currentP, currentP);
                    // 直接去到新的区间，并且需要检查段号
                    loadHandle();
                } else {
                    const start = _s < currentP ? _s : currentP;
                    const end = Math.min(_e, currentP, maxPid);
                    mainTopBoxRef.value?.changePeriodInfo(start, end);
                    await mainTopBoxRef.value?.changePeriodData(start, end);
                    // 直接去到新的区间，并且需要检查段号
                    callback();
                }
            });
        }
    });
};
function handleOpenBatchOperateDialog() {
    const selectedList = voucherListRef.value?.getSelectedVoucherList() || [];
    dialogBatchOperateRef.value?.handleOpenDialog(selectedList);
}

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    needWarning = false;
    voucherListRef.value?.setScrollTop(); // 切换页面滚动到顶部
    getTableList();
});
watch([() => paginationDataRecyclebin.currentPage, () => paginationDataRecyclebin.pageSize], () => {
    voucherRecyclebinListRef.value?.setScrollTop(); // 切换页面滚动到顶部
    openRecyclebin();
});
watch(
    () => sortVoucherInfo.pid,
    () => {
        if (!sortVoucherShow.value) return;
        openSortDialog();
    }
);
watch(
    () => sortVoucherInfo.vgid,
    () => {
        if (!sortVoucherShow.value) return;
        openSortDialog();
    }
);

const getSelectedvoucherList = () => voucherListRef.value?.getSelectedVoucherList() || [];
const getBreakNumStr = (breakNumsList: IBreakNumsList) => {
    const keyList = Object.keys(breakNumsList).map((key) => Number(key));
    let breakNumsListStr = "";
    keyList.forEach((key) => {
        if (breakNumsList[key].length > 0) {
            const type = voucherGroupList.value.find((item) => item.id === key)?.name ?? "";
            const breakNumStr = formatNumberRange(breakNumsList[key])
                .map((item) => type + item)
                .join("，");
            breakNumsListStr +=
                "<div>" +
                type +
                "字共有" +
                breakNumsList[key].length +
                "个凭证断号：" +
                '<span class="highlight-orange">' +
                breakNumStr +
                "</span>" +
                "</div>";
        }
    });
    return breakNumsListStr;
};
const getVoucherTitle = (vgId: number) => {
    return voucherGroupList.value.find((item) => item.id === vgId)?.title ?? "记账凭证";
};

const asStartDate = useAccountSetStore().accountSet?.asStartDate ?? "";
const periodList = computed(() => useAccountPeriodStore()?.periodList);
const accountSetMinDate = new Date(
    periodList.value.find((p) => p.status === PeriodStatus.HasVoucher || p.status === PeriodStatus.ChangeOut)?.startDate || ""
);
function disabledDate(time: Date) {
    const accountStartDate = dayjs(asStartDate).valueOf();
    const minDate = dayjs(accountSetMinDate).valueOf();
    const limitDate = Math.max(accountStartDate, minDate);
    return time.getTime() < limitDate || disabledDateEnd(time);
}
function disabledDateEnd(time: Date) {
    const now = new Date();
    now.setFullYear(now.getFullYear() + 5);
    if (time.getTime() >= accountSetMinDate.getTime() && time.getTime() < now.getTime()) {
        return false;
    } else {
        return true;
    }
}
const routeQueryParams = ref<any>(null);
onBeforeRouteLeave((to, from, next) => {
    routeQueryParams.value = from.query;
    next();
});
const routerArray = toRef(routerArrayStore, "routerArray");
const reLoadCurrentPage = () => {
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if (currentRouterModel) {
        if (currentRouterModel.stop) return;
        routerArrayStore.refreshRouter(currentRouterModel!.path);
        currentRouterModel.stop = true;
    }
};
onActivated(() => {
    handleSetSave();
    const thisRouteQueryParams = route.query;
    const isEqualRouterParams = _.isEqual(thisRouteQueryParams, routeQueryParams.value);
    if (!isEqualRouterParams && routeQueryParams.value !== null) {
        if (currentSlot.value === "voucher" && edited.value) {
            confirmWithoutGlobalVisible.value = true;
        } else {
            reLoadCurrentPage();
        }
    }
});
const contentRef = ref<HTMLElement>();
function handleSetSave() {
    setVoucherQuickKeycode({
        save: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                voucher.value?.handleSave();
            }
        },
        saveAndAdd: () => {},
        newVoucher: () => {},
        lastVoucher: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                if (cantDelete) return;
                if (switchInfo.value.hasPrev === false) {
                    ElNotify({ type: "warning", message: "亲，没有上一张凭证了~" });
                    return;
                }
                loadVoucherSwitchInfo(1, false);
            }
        },
        nextVoucher: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                if (cantDelete) return;
                if (switchInfo.value.hasNext === false) {
                    ElNotify({ type: "warning", message: "亲，没有下一张凭证了~" });
                    return;
                }
                loadVoucherSwitchInfo(2, false);
            }
        },
        openTempDialog: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                voucher.value?.showVoucherTemplates();
            }
        },
        deleteVoucher: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                if (cantDelete) return;
                voucher.value?.deleteVoucher();
            }
        },
        saveAsTemp: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                voucher.value?.saveAsTemplate();
            }
        },
        print: () => {
            if (currentSlot.value === "voucher") {
                if (handleCheckHasDialog(contentRef.value)) return;
                voucher.value?.showPrintVoucherDialog();
            }
        },
    });
}
onDeactivated(() => {
    deleteVoucherQuickKeycode();
});
function getTableListListener(v:any) {
    if(v.detail && v.detail.type === "approve" && currentSlot.value === "voucher") return;
    getTableList();
}
onMounted(() => {
    handleSetSave();
    // Lmtips:这两个是不是同一个就行？？？
    window.addEventListener("reloadVoucherList", getTableListListener);
    window.addEventListener("modifyaccountSubject", getTableListListener);
    const currentRouterModel = routerArray.value.find((item) => item.alive);
    if ((currentRouterModel as any)?.stop + "" !== "undefined") {
        setTimeout(() => {
            delete (currentRouterModel as any).stop;
        });
    }
});
onUnmounted(() => {
    deleteVoucherQuickKeycode();
    window.removeEventListener("reloadVoucherList", getTableListListener);
    window.removeEventListener("modifyaccountSubject", getTableListListener);
    routeQueryParams.value = null;
});
const pageScroll = inject(pageScrollKey);
watch(showBox, () => {
    voucherListRef.value?.setScrollTop();
    voucherRecyclebinListRef.value?.setScrollTop();
    pageScroll && pageScroll(false, () => {});
})
</script>

<style lang="less" scoped>
@import "@/style/Voucher/VoucherList.less";
@import "@/style/SelfAdaption.less";
@import "@/style/Functions.less";

.content {
    min-width: calc(1000px + 108px);
    height: 100%;

    .main-content {
        min-width: calc(1000px + 108px);
        height: 100%;
        .list-show-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            .main-center {
                flex: 1;
                display: flex;
                flex-direction: column;
                :deep(.voucher-list-container) {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    &.empty-container {
                        justify-content: center;
                    }

                    .dv-no-data {
                        flex: 1;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                }
            }
            // &.overlay-container{
            //     :deep(.el-overlay-dialog){
            //         overflow:visible ;
            //         top:-20px !important;
            //         bottom:5px !important;
            //     }
            // }
        }

        &.erp {
            .main-center {
                padding: 0 20px 20px;
                box-sizing: border-box;
                overflow: hidden;
            }
        }
    }

    .slot-content {
        min-width: calc(1000px + 108px);

        .slot-mini-content {
            width: 100%;
            // height: calc(var(--voucher-min-height) + 1px + 48px);
            // height: ~"max(calc(var(--voucher-min-height) + 1px + 48px), calc(100vh - var(--content-padding-bottom) - var(--title-height)))";

            .main-tool-bar {
                padding-left: 54px;
                padding-right: 54px;
                align-self: center;

                &.out {
                    width: calc(100% - 128px);
                }

                &.in {
                    width: 1050px;
                }

                .main-tool-right {
                    .prev-btn,
                    .next-btn {
                        height: 17px;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        .btn-text {
                            padding: 0 10px;
                            line-height: 16px;
                        }

                        .btn-icon {
                            width: 17px;
                            height: 17px;
                            background-image: url(/src/assets/Voucher/prev.png);
                            background-repeat: no-repeat;
                            background-size: 100%;
                        }
                    }

                    .prev-btn {
                        margin-left: 24px;
                        .btn-icon {
                            background-image: url("@/assets/voucher/prev.png");
                        }

                        &:hover {
                            color: var(--main-color);
                            .btn-icon {
                                background-image: url("@/assets/voucher/prev-hover.png");
                            }
                        }

                        &.disabled {
                            color: #999;
                            .btn-icon {
                                background-image: url("@/assets/voucher/prev-disabled.png");
                            }
                        }
                    }

                    .next-btn {
                        margin-left: 20px;
                        .btn-icon {
                            background-image: url("@/assets/voucher/next.png");
                        }
                        .btn-text {
                            padding-right: 0;
                        }

                        &:hover {
                            color: var(--main-color);
                            .btn-icon {
                                background-image: url("@/assets/voucher/next-hover.png");
                            }
                        }

                        &.disabled {
                            color: #999;
                            .btn-icon {
                                background-image: url("@/assets/voucher/next-disabled.png");
                            }
                        }
                    }
                }
            }
        }
    }
}

.copy-container {
    .copy-content {
        table {
            margin: 20px auto 0;

            tr td {
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: var(--line-height);
                padding-bottom: 16px;

                &.voucherGroup {
                    .detail-el-select(180px, 30px);
                }

                &.voucherDate {
                    .detail-el-date-picker(180px, 30px);
                }
            }
        }
    }
}

.table-pagination {
    height: 42px;
    display: flex;
    align-items: center;

    .datagrid-pager {
        border: none;
        flex: 1;
    }

    &.base {
        padding: 0 10px;
        &.top {
            border: none;
        }
        &.bottom {
            border: none;
        }
    }
}
body[erp] {
    .content {
        .slot-content {
            .slot-mini-content {
                .main-tool-bar {
                    padding-left: 74px;
                    padding-right: 74px;

                    &.out {
                        width: calc(100% - 148px);
                    }

                    .main-tool-right {
                        .next-btn {
                            &:hover .btn-icon {
                                background-image: url("@/assets/voucher/next-hover-erp.png");
                            }
                        }

                        .prev-btn {
                            &:hover .btn-icon {
                                background-image: url("@/assets/voucher/prev-hover-erp.png");
                            }
                        }
                    }
                }
            }
        }
        .main-content .list-show-container .main-center {
            :deep(.voucher-list-container) {
                margin-top: 15px;
            }
        }
    }
}
</style>
