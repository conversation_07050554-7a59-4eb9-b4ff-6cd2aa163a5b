<template>
  <a
    class="button"
    :class="{ 'solid-button': isSolidButton }"
    @click="handleClick">
    <slot />
  </a>
</template>
<script setup lang="ts">
  import { useBasicInfoStore } from "@/store/modules/basicInfo"
  import { ElNotify } from "@/utils/notify"
  const { basicInfo } = storeToRefs(useBasicInfoStore())

  const props = withDefaults(
    defineProps<{
      isSolidButton?: boolean
    }>(),
    {
      isSolidButton: false,
    },
  )

  const emits = defineEmits<{ (e: "click"): void }>()

  function handleClick() {
    if (!basicInfo.value.initCompleted) {
      ElNotify({
        type: "warning",
        message: "正在进行税务初始化，请稍后再试~",
      })
      return
    }
    emits("click")
  }
</script>
