[{"functionXh": "1b9fce98b9f04eff867abf41b969f2c5", "formXh": null, "functionDm": "substr", "functionMc": "substr", "functionType": "GLOBAL", "functionBody": "function (value, startIndex, endIndex) {\r\n      return String.prototype.substr.call(value, startIndex, endIndex);\r\n    }", "description": null, "dependencies": null}, {"functionXh": "27118326006d3829667a400ad23d5d98", "formXh": null, "functionDm": "String", "functionMc": "String", "functionType": "GLOBAL", "functionBody": "function (value) {\r\n      return String(value);\r\n    }", "description": null, "dependencies": null}, {"functionXh": "27226c864bac7454a8504f8edb15d95b", "formXh": null, "functionDm": "Boolean", "functionMc": "Boolean", "functionType": "GLOBAL", "functionBody": "function (value) {\r\n      return Boolean(value);\r\n    }", "description": null, "dependencies": null}, {"functionXh": "2c7317cc06f5437e87efc2acac028506", "formXh": null, "functionDm": "validIDCardNo", "functionMc": "validIDCardNo", "functionType": "GLOBAL", "functionBody": "function validIDCardNo(val) {\n    // 是否为身份证 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X\n    var isCardNo = function (val) {\n        return /(^\\d{15}$)|(^\\d{17}(\\d|X)$)/.test(val);\n    };\n\n    // 15位转18位身份证号\n    var changeFivteenToEighteen = function (card) {\n        if (card.length == '15') {\n            var arrInt = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];\n            var arrCh = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];\n            var cardTemp = 0;\n            // FIXME: 长度15是否一律按19xx处理\n            card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6);\n            for (var i = 0; i < 17; i++) {\n                cardTemp += card.substr(i, 1) * arrInt[i];\n            }\n            card += arrCh[cardTemp % 11];\n            return card;\n        }\n        return card;\n    };\n\n    // 出生日期码校验\n    var checkDate = function (val) {\n        var pattern = /^(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)$/;\n        if (pattern.test(val)) {\n            var year = val.substring(0, 4);\n            var month = val.substring(4, 6);\n            var date = val.substring(6, 8);\n            var date2 = new Date(year + '-' + month + '-' + date);\n            if (date2 && date2.getMonth() == parseInt(month) - 1) {\n                return true;\n            }\n        }\n        return false;\n    };\n\n    // 校验码校验\n    var checkCode = function (val) {\n        var p = /^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/;\n        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];\n        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];\n        var code = val.substring(17);\n        if (p.test(val)) {\n            var sum = 0;\n            for (var i = 0; i < 17; i++) {\n                sum += val[i] * factor[i];\n            }\n            if (parity[sum % 11] == code.toUpperCase()) {\n                return true;\n            }\n        }\n        return false;\n    };\n\n    // 省级地址码校验\n    var checkProv = function (val) {\n        var pattern = /^[1-9][0-9]/;\n        var provs = {\n            11: '北京',\n            12: '天津',\n            13: '河北',\n            14: '山西',\n            15: '内蒙古',\n            21: '辽宁',\n            22: '吉林',\n            23: '黑龙江 ',\n            31: '上海',\n            32: '江苏',\n            33: '浙江',\n            34: '安徽',\n            35: '福建',\n            36: '江西',\n            37: '山东',\n            41: '河南',\n            42: '湖北 ',\n            43: '湖南',\n            44: '广东',\n            45: '广西',\n            46: '海南',\n            50: '重庆',\n            51: '四川',\n            52: '贵州',\n            53: '云南',\n            54: '西藏 ',\n            61: '陕西',\n            62: '甘肃',\n            63: '青海',\n            64: '宁夏',\n            65: '新疆',\n            71: '台湾',\n            81: '香港',\n            82: '澳门',\n            83: '台湾',\n        };\n        if (pattern.test(val)) {\n            if (provs[val]) {\n                return true;\n            }\n        }\n        return false;\n    };\n    if (!isCardNo(val)) {\n        return false;\n    }\n\n    if (val.length === 15) {\n        return validIDCardNo(changeFivteenToEighteen(val));\n    }\n\n    if (checkCode(val)) {\n        var date = val.substring(6, 14);\n\n        if (checkDate(date)) {\n            if (checkProv(val.substring(0, 2))) {\n                return true;\n            }\n        }\n    }\n\n    return false;\n}", "description": null, "dependencies": null}, {"functionXh": "38fa9974d25d46d8aef7147f2499da51", "formXh": null, "functionDm": "max", "functionMc": "max", "functionType": "GLOBAL", "functionBody": "function () {\n      return Math.max.apply(this, arguments);\n    }", "description": null, "dependencies": null}, {"functionXh": "3c6b46d62e114f25acecd8ab84643c15", "formXh": null, "functionDm": "min", "functionMc": "min", "functionType": "GLOBAL", "functionBody": "function () {\n      return Math.min.apply(this, arguments);\n    }", "description": null, "dependencies": null}, {"functionXh": "3c756b7a056144ce959b4fba49fde3b4", "formXh": null, "functionDm": "sumList", "functionMc": "sumList", "functionType": "GLOBAL", "functionBody": "function (list, key, start, end, sortKey) {\r\n      if(start === undefined){\r\n        start = 0;\r\n      }\r\n      if(end === undefined){\r\n        end = (list || []).length - 1;\r\n      }\r\n      if(sortKey != undefined && typeof sortKey == \"string\"){\r\n        var ret = [];\r\n        for (var i = list.length-1; i >= 0; i--) {\r\n          ret[list[i][sortKey]] = list[i];\r\n        }\r\n        list = ret;\r\n      }\r\n      for (var i = end, sum = 0; i >= start; i--) {\r\n        sum += Number(list[i][key]);\r\n      }\r\n      return sum;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "3c756b7a056144ce959b4fba49fde3b5", "formXh": null, "functionDm": "isInteger", "functionMc": "isInteger", "functionType": "GLOBAL", "functionBody": "function isInteger(value){\r\n      if(Math && Math.isInteger){\r\n        return Math.isInteger(value)\r\n      }\r\n      return (typeof value == \"number\" || typeof value == \"string\") && value % 1 ===0\r\n    }", "description": null, "dependencies": null}, {"functionXh": "60a236bc586b4c8bb4fa13135805baaa", "formXh": null, "functionDm": "round", "functionMc": "round", "functionType": "GLOBAL", "functionBody": "function(value,decimal){var addZero=function(changeNum,origin,s){var index=changeNum.indexOf(\".\");if(index<0&&s>0){changeNum=changeNum+\".\";for(var i=0;i<s;i++){changeNum=changeNum+\"0\"}}else{index=changeNum.length-index;for(var j=0;j<(s-index)+1;j++){changeNum=changeNum+\"0\"}}if(origin<0&&Number(changeNum)!==0){if(Number(s)>0){return\"-\"+changeNum}else{return -changeNum}}else{return changeNum}};var sentificToStr=function(origin){var mtc=(origin+\"\").match(/(\\d)(?:\\.(\\d*))?e([+-]\\d+)/);if(mtc){var num=mtc[1]+(mtc[2]||\"\");if(Number(mtc[3])>0){for(var mm=0;mm<Number(mtc[3])-(mtc[2]||\"\").length;mm++){num+=\"0\"}}else{var num1=\"0.\";for(var mm=0;mm<-Number(mtc[3])-1;mm++){num1+=\"0\"}num=num1+num}origin=num}return origin.toString()};var digitLength=function(num){var eSplit=num.toString().split(/[eE]/);var len=(eSplit[0].split(\".\")[1]||\"\").length-(+(eSplit[1]||0));return len>0?len:0};var getNumberFullLen=function(origin){return origin.toString().replace(\".\",\"\").replace(/^0+/,\"\").length};var strip=function(origin,precision,targetDigit){var defaultPrecision=16;if(Number(origin.toPrecision(defaultPrecision).toString().replace(\".\",\"\").replace(/^0+/,\"\"))>Number.MAX_SAFE_INTEGER){defaultPrecision=15}if(Number(origin.toString().replace(\".\",\"\").replace(/^0+/,\"\"))>Number.MAX_SAFE_INTEGER&&Number(origin.toString().substr(origin.toString().length-1))<5){defaultPrecision=getNumberFullLen(origin)-2}precision=precision||defaultPrecision;if(getNumberFullLen(origin)<precision){return origin}var strippedNum=parseFloat(origin.toPrecision(precision));if(getNumberFullLen(strippedNum)===precision&&targetDigit+6<digitLength(strippedNum)){return strip(strippedNum,precision-1,targetDigit)}else{return strippedNum}};var _nativeToFixed=function(origin,s){var e,changeNum,index,i,j;if(origin<0){e=-origin}else{e=origin}var digitLen=digitLength(e);if(digitLen===s&&s>0){return addZero(sentificToStr(e.toString()),origin,s)}else{if(digitLen<s&&s>0){changeNum=e}else{changeNum=strip(e,undefined,s)}}changeNum=sentificToStr(changeNum);var zeros=\"\";if(((changeNum+\"\").split(\".\")[1]||\"\").length<s){zeros=new Array(s+1-((changeNum+\"\").split(\".\")[1]||\"\").length).join(\"0\")}var changeNumPowStr=(changeNum+\"\").split(\".\")[0]+(((changeNum+\"\").split(\".\")[1]||\"\").substr(0,s)+zeros)+\".\"+(zeros?\"\":(((changeNum+\"\").split(\".\")[1]||\"\").substr(s)));var changeNumPow=Number(changeNumPowStr)+0.5;changeNumPow=sentificToStr(changeNumPow);if(digitLength(changeNumPow)>1){changeNumPow=strip(Number(changeNumPow),undefined,1)}zeros=\"\";if((changeNumPow+\"\").split(\".\")[0].length-1<s){zeros=new Array(s+1-(changeNumPow+\"\").split(\".\")[0].length).join(\"0\")}changeNumPowStr=(zeros?\"0\":(changeNumPow+\"\").split(\".\")[0].substr(0,(changeNumPow+\"\").split(\".\")[0].length-s))+\".\"+zeros+(changeNumPow+\"\").split(\".\")[0].substr((zeros?0:(changeNumPow+\"\").split(\".\")[0].length-s));changeNum=(sentificToStr(Number(changeNumPowStr))).toString();return addZero(changeNum,origin,s)};return parseFloat(_nativeToFixed(Number(value),decimal))};", "description": null, "dependencies": null}, {"functionXh": "77c0aff825dbb4b8c94c4221d1c6d12a", "formXh": null, "functionDm": "doCatch", "functionMc": "doCatch", "functionType": "GLOBAL", "functionBody": "function (expression, defaultValue) {\r\n      var r = typeof defaultValue == \"undefined\" ? 0: defaultValue;\r\n      try{\r\n        r = engineJs.custom(engineJs.content,expression) || r;\r\n      }catch(e){\r\n        return r;\r\n      }\r\n      return r;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "7f3c852cba2a4ed1a8dfb6f8160f5e27", "formXh": null, "functionDm": "abs", "functionMc": "abs", "functionType": "GLOBAL", "functionBody": "function (value) {\n      return Math.abs(value);\n    }", "description": null, "dependencies": null}, {"functionXh": "81e82f42dc3f019b6ad9e22ae97f72fb", "formXh": null, "functionDm": "rangeExec", "functionMc": "rangeExec", "functionType": "GLOBAL", "functionBody": "function (start,end, expression, xhKey) {\r\n      var ret = [];\r\n      var listPath = null;\r\n      var xhMap = {};\r\n      if(typeof end == \"string\"){\r\n         xhKey = expression;\r\n        expression = end;\r\n        end = undefined;\r\n      }\r\n      if(xhKey){\r\n        listPath = expression.match(/(ywbw\\.[^\\ ]*?)\\[\\*\\]/)[1];\r\n        var list = engineJs.custom(engineJs.content,listPath);\r\n        for (var k = 0; k < list.length; k++) {\r\n          xhMap[list[k][xhKey]] = k;\r\n        }\r\n      }\r\n      var getIndex = function(xhKey,xhValue){\r\n        return xhMap[xhValue]\r\n      }\r\n      var run = function(i){\r\n        return engineJs.custom(engineJs.content,expression.replace(/\\[\\*\\]/gi,\"[\"+i+\"]\").replace(/(['\"])(\\*)\\1/g,\"$1\"+i+\"$1\"))\r\n      }\r\n      if(end){\r\n        for(var i = start;i<end;i++){\r\n          ret[i] = run(xhK<PERSON>?getIndex(xhK<PERSON>,i):i);\r\n        }\r\n      }else if(typeof start == \"object\" && start.length){\r\n        for(var i = 0;i<start.length;i++){\r\n          ret[start[i]] =run(xhKey?getIndex(xhKey,start[i]):start[i]);\r\n        }\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "904454a324354d2dbdf520d850dcf412", "formXh": null, "functionDm": "getObjFromList", "functionMc": "getObjFromList", "functionType": "GLOBAL", "functionBody": "function (list, key, value) {\r\n      for(var i=0;i<list.length;i++){\r\n        if(typeof list[i][key] == \"number\"){\r\n          if(list[i][key] === Number(value)){\r\n            return list[i];\r\n          }\r\n        }else{\r\n          if(list[i][key] === value){\r\n            return list[i];\r\n          }\r\n        }\r\n        \r\n      }\r\n      return null;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "99df2146e4ac47989faa09b27dc9b002", "formXh": null, "functionDm": "<PERSON><PERSON><PERSON><PERSON>", "functionMc": "<PERSON><PERSON><PERSON><PERSON>", "functionType": "GLOBAL", "functionBody": "function (value) {\n      return value.length;\n    }", "description": null, "dependencies": null}, {"functionXh": "a7598c5c55964b7789835109043daa8d", "formXh": null, "functionDm": "getIf", "functionMc": "getIf", "functionType": "GLOBAL", "functionBody": "function (condition, trueVal, falseVal) {\r\n      if(arguments.length<=2){\r\n        falseVal = true;\r\n      }\r\n      // falseVal = falseVal === undefined?true:falseVal\r\n      return condition ? trueVal : falseVal;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "b2ee912b91d69b435159c7c3f6df7f5f", "formXh": null, "functionDm": "Number", "functionMc": "Number", "functionType": "GLOBAL", "functionBody": "function (value) {\r\n      return Number(value) || 0;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "bd4f997ba19147f293373817244a881b", "formXh": null, "functionDm": "count<PERSON><PERSON><PERSON>", "functionMc": "count<PERSON><PERSON><PERSON>", "functionType": "GLOBAL", "functionBody": "function countChars(str,unit) {\n  var length = 0;\n  for (var i = 0; i < str.length; i++) {\n    var charCode = str.charCodeAt(i);\n    length += (charCode >= 0x4E00 && charCode <= 0x9FFF) || charCode > 0xFF00 ? unit : 1;\n  }\n  return length;\n}", "description": null, "dependencies": null}, {"functionXh": "cbe0396fee5842c98666a1eeead2392c", "formXh": null, "functionDm": "sum", "functionMc": "sum", "functionType": "GLOBAL", "functionBody": "function () {\n      for (var i = arguments.length - 1, sum = 0; i >= 0; i--) {\n        sum += arguments[i];\n      }\n      return sum;\n    }", "description": null, "dependencies": null}, {"functionXh": "e06046e1b90a47ceacaf81e482e39f49", "formXh": null, "functionDm": "customGet_checkObjExist", "functionMc": "customGet_checkObjExist", "functionType": "GLOBAL", "functionBody": "function customGet_checkObjExist(key, funcName, params) {\n  if (!this.ywbw[key] && funcName) {\n    this.ywbw[key] = this[funcName].call(this, params);\n  }\n  return true;\n}", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc001", "formXh": null, "functionDm": "execExpressionWithDep", "functionMc": "execExpressionWithDep", "functionType": "GLOBAL", "functionBody": "function execExpressionWithDep(expression, dep) {\n  return new Function('with(this){return '.concat(expression.replace(/ /gi, ''), '}')).call(this);\n}", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc819", "formXh": null, "functionDm": "trim", "functionMc": "trim", "functionType": "GLOBAL", "functionBody": "function (value) {\n      return value.replace(/^s+/, \"\");\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc820", "formXh": null, "functionDm": "checkRepeat", "functionMc": "checkRepeat", "functionType": "GLOBAL", "functionBody": "function checkRepeat(list) {\r\n      var o = {},\r\n        i;\r\n    \r\n      for (\r\n        var _len = arguments.length,\r\n          keys = new Array(_len > 1 ? _len - 1 : 0),\r\n          _key = 1;\r\n        _key < _len;\r\n        _key++\r\n      ) {\r\n        keys[_key - 1] = arguments[_key];\r\n      }\r\n    \r\n      for (i = 0; i < list.length; i++) {\r\n        var ret = \"\";\r\n    \r\n        for (var j = 0; j < keys.length; j++) {\r\n          if(list[i][keys[j]]){ret += list[i][keys[j]];}\r\n        }\r\n        if(!ret)continue;\r\n        if (o[ret]) return true;\r\n        o[ret] = true;\r\n      }\r\n    \r\n      return false;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc821", "formXh": null, "functionDm": "judgeTime", "functionMc": "judgeTime", "functionType": "GLOBAL", "functionBody": "function judgeTime(time1, time2) {\n      var expression =\n        arguments.length > 2 && arguments[2] !== undefined\n          ? arguments[2]\n          : \"==\";\n      var format =\n        arguments.length > 3 && arguments[3] !== undefined\n          ? arguments[3]\n          : \"yyyy-MM-dd\";\n\n      return eval(\n        this.parseDate(time1, format).getTime() +\n          expression +\n          this.parseDate(time2, format).getTime()\n      );\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc827", "formXh": null, "functionDm": "parseDate", "functionMc": "parseDate", "functionType": "GLOBAL", "functionBody": "function parseDate(str, fmt) {\r\n      fmt = fmt || \"yyyy-MM-dd\";\r\n      var obj = {\r\n        y: 0,\r\n        M: 1,\r\n        d: 0,\r\n        H: 0,\r\n        h: 0,\r\n        m: 0,\r\n        s: 0,\r\n        S: 0,\r\n      };\r\n      fmt.replace(\r\n        /([^yMdHmsS]*?)(([yMdHmsS])\\3*)([^yMdHmsS]*?)/g,\r\n        function (m, $1, $2, $3, $4, idx, old) {\r\n          str = str.replace(\r\n            new RegExp($1 + \"(\\\\d{\" + $2.length + \"})\" + $4),\r\n            function (_m, _$1) {\r\n              obj[$3] = parseInt(_$1);\r\n              return \"\";\r\n            }\r\n          );\r\n          return \"\";\r\n        }\r\n      );\r\n      obj.M--; // 月份是从0开始的，所以要减去1\r\n\r\n      var date = new Date(obj.y, obj.M, obj.d, obj.H, obj.m, obj.s);\r\n      if (obj.S !== 0) date.setMilliseconds(obj.S); // 如果设置了毫秒\r\n\r\n      return date;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc828", "formXh": null, "functionDm": "checkTimeByNSQXDM", "functionMc": "checkTimeByNSQXDM", "functionType": "GLOBAL", "functionBody": "function checkTimeByNSQXDM(time1, time2, nsqxdm, format) {\n      format = format || \"yyyy-MM-dd\";\n      nsqxdm = Number(nsqxdm);\n      // 06 月 08季 09 半年 10 年 11 次\n      time1 = this.parseDate(time1, format);\n      time2 = this.parseDate(time2, format);\n      var sameDay = time1.getTime() == time2.getTime();\n      var sameYear = time1.getFullYear() == time2.getFullYear();\n      if (nsqxdm == 11 && sameDay) return true;\n      if (\n        nsqxdm == 10 &&\n        sameYear &&\n        (Math.abs(time1.getTime() - time2.getTime()) / (24 * 3600 * 1000) ==\n          365 ||\n          Math.abs(time1.getTime() - time2.getTime()) / (24 * 3600 * 1000) ==\n            366)\n      )\n        return true;\n      if (\n        nsqxdm == 9 &&\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 5 && \n        [0,6].indexOf(time1.getMonth()) != -1\n      )\n        return true;\n      if (\n        nsqxdm == 8 &&\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 2 &&\n        [0,3,6,9].indexOf(time1.getMonth()) != -1\n      )\n        return true;\n      if (\n        nsqxdm == 6 &&\n        sameYear &&\n        Math.abs(time1.getMonth() - time2.getMonth()) == 0\n      )\n        return true;\n      return false;\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc829", "formXh": null, "functionDm": "checkTime", "functionMc": "checkTime", "functionType": "GLOBAL", "functionBody": "function checkTime(time1, format) {\r\n      format = format || \"yyyy-MM-dd\";\r\n      var map = {\r\n        \"yyyy-MM-dd\":/^20(((([248][048])|([13579][26]))\\-(((0[13578]|1[02])\\-([0-2][0-9]|3[01]))|((0[469]|11)\\-([0-2][0-9]|30))|(02\\-([0-2][0-9]))))|((([248][1-35-79])|([13579][013-57-9]))\\-(((0[13578]|1[02])\\-([0-2][0-9]|3[01]))|((0[469]|11)\\-([0-2][0-9]|30))|(02\\-(((0|1)[0-9])|(2[0-8]))))))$/\r\n      }\r\n\r\n      if(!map[format]){return false};\r\n      return map[format].test(time1)\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc830", "formXh": null, "functionDm": "inTimeRange", "functionMc": "inTimeRange", "functionType": "GLOBAL", "functionBody": "function inTimeRange(time1, time2, time3, format) {\n      format = format || \"yyyy-MM-dd\";\n      time1 = this.parseDate(time1, format);\n      time2 = this.parseDate(time2, format);\n      time3 = this.parseDate(time3, format);\n      time1 = time1.getTime();\n      time2 = time2.getTime();\n      time3 = time3.getTime();\n\n      return (\n        (time3 >= time1 && time3 <= time2) || (time3 >= time2 && time3 <= time1)\n      );\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc831", "formXh": null, "functionDm": "getListFromList", "functionMc": "getListFromList", "functionType": "GLOBAL", "functionBody": "function (list, key, values) {\r\n      if(!Array.isArray(values)){\r\n        values = [values];\r\n      }\r\n      var ret = [];\r\n      var m = {};\r\n      for(var i=0;i<values.length;i++){\r\n        m[values[i]] = true;\r\n      }\r\n      for(var i=0;i<list.length;i++){\r\n        if(m[list[i][key]]){\r\n          ret.push(list[i])\r\n        }\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc832", "formXh": null, "functionDm": "inArray", "functionMc": "inArray", "functionType": "GLOBAL", "functionBody": "function (list, value) {\r\n      for(var i=0;i<list.length;i++){\r\n        if(list[i] === value){\r\n          return true\r\n        }\r\n      }\r\n      return false;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc833", "formXh": null, "functionDm": "getMonthByTime", "functionMc": "getMonthByTime", "functionType": "GLOBAL", "functionBody": "function (time, format) {\n      format = format || \"yyyy-MM-dd\";\n      time = this.parseDate(time,format);\n      return time.getMonth()+1;\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc834", "formXh": null, "functionDm": "getIndexFromList", "functionMc": "getIndexFromList", "functionType": "GLOBAL", "functionBody": "function getIndexFromList(list, key, values) {\r\n      var ret = [];\r\n      var m = {};\r\n      for(var i=0;i<list.length;i++){\r\n        m[list[i][key]] = i;\r\n      }\r\n      for(var i=0;i<values.length;i++){\r\n        ret.push(m[values[i]]);\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc835", "formXh": null, "functionDm": "getListFromListByKey", "functionMc": "getListFromListByKey", "functionType": "GLOBAL", "functionBody": "function (list, key) {\r\n      var ret = [];\r\n      for(var i=0;i<list.length;i++){\r\n        if(list[i] && list[i][key]){\r\n          ret.push(list[i][key]);\r\n        }\r\n      }\r\n      return ret;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc920", "formXh": null, "functionDm": "checkFbzlMustUploaded", "functionMc": "checkFbzlMustUploaded", "functionType": "GLOBAL", "functionBody": "function checkFbzlMustUploaded(xmlRoot){\r\n      var fbzl = this.doCatch(\"hd.fbzlxx.fbzl\",[]);\r\n      var fbzlVO = this.doCatch(xmlRoot+\".fbzlContent.fbzlVO\",[]);\r\n      for (var i = 0; i < fbzl.length; i++) {\r\n        if(fbzl[i].bslxDm === \"1\"){\r\n          for (var j = 0; j < fbzlVO.length; j++) {\r\n            if(fbzlVO[j].fbzlDm === fbzl[i].fbzlDm){\r\n              if(fbzlVO[j].yscbz == \"Y\" )return true;\r\n              return fbzlVO[j].files && fbzlVO[j].files.length > 0 || false\r\n            }\r\n          }\r\n        }\r\n      }\r\n      return false\r\n    }", "description": null, "dependencies": null}, {"functionXh": "e102205d31f04defbfb455a63c7fc921", "formXh": null, "functionDm": "checkFbzlUploaded", "functionMc": "checkFbzlUploaded", "functionType": "GLOBAL", "functionBody": "function checkFbzlUploaded(fbzlDmArr, condition, validType, xmlRoot){\r\n      var hd = this.hd;\r\n      validType = validType || \"one\";\r\n      if(!condition)return true;\r\n      var sbzlxlcode = hd.sbzlxlcode;\r\n      var m = {};\r\n      var fbzlVO = this.doCatch(xmlRoot+\".fbzlContent.fbzlVO\",[]);\r\n      var runList = [];\r\n      var passCount = 0;\r\n      for (var i = 0; i < fbzlDmArr.length; i++) {\r\n        m[sbzlxlcode + fbzlDmArr[i]] = true;\r\n      }\r\n      for (var i = 0; i < fbzlVO.length; i++) {\r\n        if(m[fbzlVO[i].fbzlDm]){\r\n          runList.push(fbzlVO[i]);\r\n        }\r\n      }\r\n      for (var i = 0; i < runList.length; i++) {\r\n        if(runList[i].yscbz == \"Y\" ){\r\n          passCount ++;\r\n        }else if(runList[i].files && runList[i].files.length > 0 || false){\r\n          passCount ++;\r\n        }\r\n        if(validType == \"one\" && passCount)return true;\r\n      }\r\n      return passCount == fbzlDmArr.length;\r\n    }", "description": null, "dependencies": null}, {"functionXh": "01832d9207994eb29325bcbc925e206e", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb5jycyjmxm", "functionMc": "customGet_fb5jycyjmxm", "functionType": "diy", "functionBody": "function customGet_fb5jycyjmxm() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].zsxmDm === '10109'){\n\t\t\tif(VOs[i].ssjmxzDm === '0007013612' || VOs[i].ssjmxzDm === '0007013613' || VOs[i].ssjmxzDm === '0007013610' || VOs[i].ssjmxzDm === '0007013611') {\n\t\t\t A = 1;\n\t\t\t}\n            }\n\t\t\t if (VOs[i].zsxmDm === '30203'){\n\t\t\tif(VOs[i].ssjmxzDm === '0061013610' || VOs[i].ssjmxzDm === '0061013611' || VOs[i].ssjmxzDm === '0061013608' || VOs[i].ssjmxzDm === '0061013609') {\n\t\t\t A = 1;\n\t\t\t}\n            }\n\t\t\t if (VOs[i].zsxmDm === '30216'){\n\t\t\tif(VOs[i].ssjmxzDm === '0099013603' || VOs[i].ssjmxzDm === '0099013604' || VOs[i].ssjmxzDm === '0099013601' || VOs[i].ssjmxzDm === '0099013602') {\n\t\t\t A = 1;\n\t\t\t}\n            }\n        }\n    } catch (e) { }\n    return A;\n}", "description": "附表5就业创业减免项目", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\"]"}, {"functionXh": "047cdc5f73e944449fb5e1890d39e323", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_tysbtstx", "functionMc": "customGet_tysbtstx", "functionType": "diy", "functionBody": "function customGet_tysbtstx() {\n  var inDate = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\nvar sytysbxejmqy = this.fzxx.bqxx.sytysbxejmqy;\nvar gtgsh = this.fzxx.bqxx.gtgsh;\nvar result = '';\nvar jmb = 0;\nvar fb5 = 0;\nvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\nvar VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\nfor (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011804' || VOs[i].ssjmxzDm === '0061011804' || VOs[i].ssjmxzDm === '0099011802'){\n              fb5 = 1;\n      break;\n          }\n      }\n for (var i = 0; i < VOs1.length; i++) {\n          if (VOs1[i].hmc.substr(0, 10) === '0001011814'){\n              jmb = 1;\n      break;\n          }\n      }\nif (inDate && gtgsh === 'N' && sytysbxejmqy === 'Y' && (jmb === 1 || fb5 === 1)) {\n  result = 'tsxt';\n}\nreturn result;\n}", "description": "退役士兵提示性提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "07cfc6a7c72245ca8ca4bde3acdd2ff2", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_6", "functionMc": "customGet_fb1lcsftx_6", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_6() {\n  return this.customGet_fb1lcsftx('6') === 0;\n}", "description": "根据二维表行序号判断附表1第6行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','6').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "0a4ab21a963e4d8eb942a564eb82354d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx", "functionMc": "customGet_zzsfjsbqjxsemx", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx(xh) {\n    var ywbw = this.ywbw;\n    var fzxx = this.fzxx;\n    var bool = fzxx.bqxx.ckscqy==='N'&&fzxx.bqxx.ckwmqy==='N'&&fzxx.bqxx.fdqqy==='N';\n    var fs = ywbw.sbZzsYbnsrFb2Bqjxsemx[xh-1].fs === 0;\n    var je = ywbw.sbZzsYbnsrFb2Bqjxsemx[xh-1].je === 0;\n    var se = ywbw.sbZzsYbnsrFb2Bqjxsemx[xh-1].se === 0;\n    return bool ? fs && je && se : true;\n}", "description": "增值税及附加税费申报表附列资料（二）待抵扣进项税额 第【25】-【33】行，仅辅导期一般纳税人或出口退税企业可填写", "dependencies": "[]"}, {"functionXh": "0acb0deedda644ed9cf464ead1faebb0", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_JmbSksbYtxx", "functionMc": "customGet_JmbSksbYtxx", "functionType": "diy", "functionBody": "function customGet_JmbSksbYtxx() {\n  var A = 'ytbcz';\n  var VOs = this.ytxx.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm || [];\n  for (var i = 0; i < VOs.length; i++) {\n      if (VOs[i].hmc.substr(0, 10) === '0001129914'){\n          A = VOs[i].bqfse;\n      }\n  }\n  return A;\n}", "description": "判断是否发生税控设备服务费，返回ytbcz表示未发生，否则表示发生", "dependencies": "[]"}, {"functionXh": "0f5639fe4dd1472db4b85df25633d22d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_15", "functionMc": "customGet_fb1lcsftx_15", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_15() {\n  return this.customGet_fb1lcsftx('15') === 0;\n}", "description": "根据二维表行序号判断附表1第15行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','15').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "10bc41424160473487408d0c6782030a", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLslfJmxzDm", "functionMc": "customGet_fjsLslfJmxzDm", "functionType": "diy", "functionBody": "function customGet_fjsLslfJmxzDm(arr) {\n    var index = arr[0];\n    var fjsxxForm = this.ywbw.sbFjsf.sbFjsfQtxx;\n    var obj = this.ywbw.sbFjsf.sbFjsfMx[index];\n    var A = fjsxxForm.bqsfsyxgmyhzc;\n    var B = obj.bqynsfe - obj.jme;\n    var syzt = fjsxxForm.jzzcsyztDm;\n    if (A === 'Y' && B !== 0) {\n        if (obj.zsxmDm === '10109') {\n            if (syzt === '22') {\n                //GTHPHJMXZDMCJS|GTHPHJMSWSXDMCJS|GTHPHJMSWSXMCCJS\n                return '0007049902' || '';\n            } else if (syzt === '21') {\n                //XWQYPHJMXZDMCJS|XWQYPHJMSWSXDMCJS|XWQYPHJMSWSXMCCJS\n                return '0007049903' || '';\n            }\n        } else if (obj.zsxmDm === '30203') {\n            if (syzt === '22') {\n                //GTHPHJMXZDMJYFJ|GTHPHJMSWSXDMJYFJ|GTHPHJMSWSXMCJYFJ\n                return '0061049902' || '';\n            } else if (syzt === '21') {\n                //XWQYPHJMXZDMJYFJ|XWQYPHJMSWSXDMJYFJ|XWQYPHJMSWSXMCJYFJ\n                return '0061049903' || '';\n            }\n        } else if (obj.zsxmDm === '30216') {\n            if (syzt === '22') {\n                //GTHPHJMXZDMDFFJ|GTHPHJMSWSXDMDFFJ|GTHPHJMSWSXMCDFFJ\n                return '0099049902' || '';\n            } else if (syzt === '21') {\n                //XWQYPHJMXZDMDFFJ|XWQYPHJMSWSXDMDFFJ|XWQYPHJMSWSXMCDFFJ\n                return '0099049903' || '';\n            }\n        }\n    }\n    return '';\n}", "description": "六税两费减免性质代码", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.bqsfsyxgmyhzc\",\"ywbw.sbFjsf.sbFjsfQtxx.jzzcsyztDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\"]"}, {"functionXh": "127d28599e7149e3b31492ce600a0c5c", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsMdse", "functionMc": "customGet_fjsMdse", "functionType": "diy", "functionBody": "function customGet_fjsMdse(arr) {\n\tvar index = arr[0];\n\tvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\n\tif (this.ywbw.sbFjsf.sbFjsfMx[index].zsxmDm !== '10109' || this.fzxx.bqxx.dtcjsSlqy === 'N') {\n\t\treturn this.ywbw.sbFjsf.sbFjsfMx[index].zzsmdse;\n\t}else {\n\t\treturn 0;\n\t}\n}", "description": "附表五“增值税免抵税额”列公式", "dependencies": "[]"}, {"functionXh": "13d9259b4bfc4dd68fc279c1ddda87a6", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgybhwjlwjzjtfpbl_Less", "functionMc": "customGet_fzjgybhwjlwjzjtfpbl_Less", "functionType": "diy", "functionBody": "function customGet_fzjgybhwjlwjzjtfpbl_Less(arr,sumSsr ){\n    var index = arr[0];\n    var bl = 0;\n    var zjgybhwjlwjzjtxssr = this.ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtxssr;\n    var sum1 = sumSsr + zjgybhwjlwjzjtxssr;\n    var fzjgybhwjlwjzjtxssr = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[index].fzjgybhwjlwjzjtxssr;\n        if(this.customGet_fzjgsbjnfs_family() === 1&&this.round ( sum1 , 2 ) !== 0){\n            bl = fzjgybhwjlwjzjtxssr / sum1;\n        }else if(this.customGet_fzjgsbjnfs_family() === 2&&this.round ( sum1 , 2 ) !== 0){\n            bl = (1 - this.ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl) * fzjgybhwjlwjzjtxssr / sum1;  \n        }else if(this.customGet_fzjgsbjnfs_family() === 3&&this.round ( sumSsr, 2 ) !== 0){\n            bl = fzjgybhwjlwjzjtxssr / sumSsr; \n        }\n    return bl;\n}", "description": "《汇总纳税企业增值税分配表》分支机构销售收入情况--分支机构一般货物及劳务即征即退--分配比例=分支机构\"一般货物及劳务即征即退销售收入\"÷总机构和所有分支机构的\"一般货物及劳务即征即退销售收入\"。", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwjzjtxssr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtxssr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtfpse\"]"}, {"functionXh": "1424231051254c68b67f77a64bb108f9", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lc", "functionMc": "customGet_fb1lc", "functionType": "diy", "functionBody": "function customGet_fb1lc(ewbhxh) {\n  var fb1lc = ewbhxh;\n  if (this.Number(ewbhxh) === 23) {\n    fb1lc = 3;\n  }\n  else if (this.Number(ewbhxh) === 9) {\n  fb1lc = '9a';\n  }\n  else if (this.Number(ewbhxh) === 22) {\n  fb1lc = '9b';\n  }\n  return fb1lc;\n}", "description": "根据二维表行序号获取附表一栏次", "dependencies": "[\"\"]"}, {"functionXh": "154566dc053a42c6b0234cd2c6d08eaf", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4Row1Col2", "functionMc": "customGet_fb4Row1Col2", "functionType": "diy", "functionBody": "function customGet_fb4Row1Col2() {\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if ((VOs[i].hmc === '0001129914' && VOs[i].swsxDm === 'SXA031900185') || VOs[i].hmc === '0001129914|SXA031900185') {\n                return Number(VOs[i].bqfse);\n            }\n        }\n    } catch (e) { }\n    return this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk, 'ewbhxh', '1').bqfse;\n}", "description": "附表四1_2取数", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqfse\"]"}, {"functionXh": "156e2bd66e7448cda8850a6d51ba4d6a", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_9", "functionMc": "customGet_fb1lcsftx_9", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_9() {\n  return this.customGet_fb1lcsftx('9') === 0;\n}", "description": "根据二维表行序号判断附表1第9行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','9').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "2384fb3759fe4dea98b5b492ffff0c7f", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_32", "functionMc": "customGet_zzsfjsbqjxsemx_32", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_32() {\n    return this.customGet_zzsfjsbqjxsemx(32);\n}\n", "description": "第【32】行仅辅导期一般纳税人或出口退税企业可填写\n", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','32').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','32').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','32').se\"]\n"}, {"functionXh": "242b76c6e17b4b198b41b1280abeb5e3", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_tzqqjjdje", "functionMc": "customGet_tzqqjjdje", "functionType": "diy", "functionBody": "function  customGet_tzqqjjdje() {\n\tvar result = 0;\n\tvar gzsb = '0';\n\tvar nsrSyFscshJjdjzc = this.fzxx.bqxx.nsrSyFscshJjdjzc;\n\tvar inDate = new Date(this.sbsx.skssqq) >= new Date('2019-04-01') && new Date(this.sbsx.skssqz) <= new Date('2023-12-31');\n\tif (this.sbsx.jylxDm){\n\t\tgzsb = this.sbsx.jylxDm;\n\t}\n\tif (inDate && nsrSyFscshJjdjzc !== 'Y' && gzsb === '1'){\n\t\tif((this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').qcye > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').qcye) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqfse > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqfse) ||  (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqzce > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqzce) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqkjjdkjxse > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqkjjdkjxse) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqsjjjdkjxse > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqsjjjdkjxse) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').qmye > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').qmye) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').qcye > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').qcye) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqfse > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqfse) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqzce > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqzce) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqkjjdkjxse > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqkjjdkjxse) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqsjjjdkjxse > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqsjjjdkjxse) || (this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').qmye > this.getObjFromList(this.ytxx.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').qmye)) {\n\t\t\tresult = 1;\n\t\t}\n\t}\n\treturn result;\n}", "description": "“调增前期加计抵减额”提示逻辑，返回0不提示，返回1提示", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').qcye\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqfse\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqzce\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqkjjdkjxse\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqsjjjdkjxse\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').qmye\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').qcye\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqfse\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqzce\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqkjjdkjxse\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqsjjjdkjxse\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').qmye\"]"}, {"functionXh": "274a59c419a941159ab11078adc9c5aa", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_12", "functionMc": "customGet_fb1lcsftx_12", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_12() {\n  return this.customGet_fb1lcsftx('12') === 0;\n}", "description": "根据二维表行序号判断附表1第12行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','12').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "2931b1099a2143d381fae55d650b7826", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_Z", "functionMc": "customGet_Z", "functionType": "diy", "functionBody": "function customGet_Z() {\n    var a = this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '3').ynsejze\n        + this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '3').fcyjse;\n    return Number(a.toFixed(2));\n}", "description": "Z: 主表(第23+28+29栏)【即征即退项目】本月数;", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').ynse<PERSON><PERSON>\", \"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').fcyjse\"]"}, {"functionXh": "2963d12d00014307b83749ff299e2605", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_c1", "functionMc": "customGet_c1", "functionType": "diy", "functionBody": "function customGet_c1() {\n    var a = this.customGet_A() * this.customGet_a1() + this.customGet_B1();\n    var b = this.customGet_A() + this.customGet_B1() + this.customGet_B2();\n    return b === 0 ? 0 : Number((a / b).toFixed(10));\n}", "description": "c1: 一般项目中，(一般计税+简易征收)货劳税额在总税额的比例(货劳+应税服务)，公式为：【A*a1+B1】/【A+B1+B2】;", "dependencies": "[\"customGet_A\", \"customGet_a1\", \"customGet_B1\", \"customGet_B2\"]"}, {"functionXh": "2a07332e86bf4af09d6cb25362142312", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLdtse2", "functionMc": "customGet_fjsLdtse2", "functionType": "diy", "functionBody": "function customGet_fjsLdtse2(arr) {\n  var A = this.customGet_fjsLdtse(arr);\n  return A;\n  }", "description": "由于附表五第四列留抵退税本期扣除额和其他行次的第一列的值也有关系，所以赋值公式写两种：\nrangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].zzsldtse === customGet_fjsLdtse2([*])\", \"*\")\nrangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].zzsldtse === customGet_fjsLdtse1([*])\")\ncustomGet_fjsLdtse1和customGet_fjsLdtse2逻辑一样，只是依赖项互斥，customGet_fjsLdtse1依赖项为第一列，调用增加了一个传参，所以会同时出发1列其他行次的关联规则，所以为了修改其他列次不影响其他行次，拆开写了customGet_fjsLdtse2，依赖项为除了第一列以外的其他节点，只有该行次变化才会触发相关的规则\n", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ybzzs\"]"}, {"functionXh": "2ca133e1023b46829109e19bc800a5a0", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_B2", "functionMc": "customGet_B2", "functionType": "diy", "functionBody": "function customGet_B2() {\n    if (!this.ywbw.sbZzsYbnsrFb1Bqxsqkmx) {\n        return 0;\n    }\n    var result = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '22').kchXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '12').kchXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '13').kchXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '20').kchXxynse\n        - this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '15').kchXxynse\n    return Number(result.toFixed(2));\n}", "description": "B2: 简易征收一般项目中应税服务的销售税额,公式为：附表1【14列(9b+12+13a+13b-15)栏】。", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','22').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','12').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','13').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','20').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','15').kchXxynse\"]"}, {"functionXh": "2f046b5b0f9843efb3192885d4098337", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_delcpyslmx", "functionMc": "customGet_delcpyslmx", "functionType": "diy", "functionBody": "function customGet_delcpyslmx(arr) {\n    var index = arr[0];\n    var obj = this.ywbw.sbZzsYbnsrFbCpygxcsl[index];\n    if (obj.ylxh === null || obj.ylxh === '') {\n\t\tobj.bqgjl = 0;\n\t\tobj.bqcklYsxsl = 0;\n\t\tobj.bqcklYkcsl = 0;\n\t\tobj.qmkcl = 0;\n      return 0;\n    }\n    return obj.qckcl;\n}", "description": "成品油数量明细清空型号后字段值", "dependencies": "[\"ywbw.sbZzsYbnsrFbCpygxcsl[*].ylxh\"]"}, {"functionXh": "30df186333994ab7a4d64b3b2f4daf58", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_hzfpbCheck", "functionMc": "customGet_hzfpbCheck", "functionType": "diy", "functionBody": "function customGet_hzfpbCheck() {\n    var sswrwc = this.Number(this.fzxx.csxx.common.sswrwc);\n    var ybtse = this.ywbw.sbZzsYbnsrFbHznsfp.ybtse;\n    var hzfpbfpse = this.customGet_hzfpbFpse();\n\tvar result = 0;\n    if (sswrwc === 0){\n\t\tif (this.abs(ybtse - hzfpbfpse) > 0){\n\t\t\tresult = 1;\n\t\t}\n\t}else {\n\t\tif (this.abs(ybtse - hzfpbfpse) > 0 && this.abs(ybtse - hzfpbfpse)<= sswrwc){\n\t\t\tresult = 2;\n\t\t}else if (this.abs(ybtse - hzfpbfpse) > sswrwc){\n\t\t\tresult = 3;\n\t\t\t}\n\t}\n\treturn result;\n}", "description": "判断是否在误差范围内，0不存在误差，1不允许存在误差但存在误差，2允许存在误差且误差在允许范围内，3允许存在误差但误差不在允许范围内", "dependencies": "[\"customGet_hzfpbFpse\",\"ywbw.sbZzsYbnsrFbHznsfp.ybtse\",\"customGet_hzfpbzjgFpse\",\"customGet_hzfpbfzjgFpse\"]\n"}, {"functionXh": "31792eb14bb94380ab841b3ce5e466ef", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgysfwfpbl_Less", "functionMc": "customGet_fzjgysfwfpbl_Less", "functionType": "diy", "functionBody": "function customGet_fzjgysfwfpbl_Less(arr,sumSsr ){\n    var index = arr[0];\n    var bl = 0;\n    var zjgysfwxssr = this.ywbw.sbZzsYbnsrFbHznsfp.zjgysfwxssr;\n    var sum1 = sumSsr + zjgysfwxssr;\n    var fzjgysfwxssr = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[index].fzjgysfwxssr;\n        if(this.customGet_fzjgsbjnfs_family() === 1 && this.round ( sum1 , 2 ) !== 0){\n            bl = fzjgysfwxssr / sum1;\n        }else if(this.customGet_fzjgsbjnfs_family() === 2&&this.round ( sum1 , 2 ) !== 0){\n            bl = (1 - this.ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl) * fzjgysfwxssr / sum1; \n        }else if(this.customGet_fzjgsbjnfs_family() === 3&&this.round ( sumSsr, 2 ) !== 0){\n            bl = fzjgysfwxssr / sumSsr;  \n        }\n    return bl;\n}", "description": "《汇总纳税企业增值税分配表》分支机构销售收入情况--分支机构应税服务--分配比例应等于该行分支机构\"应税服务销售收入\"÷总机构和所有分支机构的\"应税服务销售收入。", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwxssr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwxssr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl\"]"}, {"functionXh": "338eee7aa1194306a54be7b619593192", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_11", "functionMc": "customGet_fb1lcsftx_11", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_11() {\n  return this.customGet_fb1lcsftx('11') === 0;\n}", "description": "根据二维表行序号判断附表1第11行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','11').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "35e38484e84643b0a012f1581ecc1b43", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_jmbtysbjmxm", "functionMc": "customGet_jmbtysbjmxm", "functionType": "diy", "functionBody": "function customGet_jmbtysbjmxm() {\n  var A = 0;\n  var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm || [];\n  for (var i = 0; i < VOs.length; i++) {\n      if (VOs[i].hmc.substr(0, 10) === '0001011804' || VOs[i].hmc.substr(0, 10) === '0001011805' || VOs[i].hmc.substr(0, 10) === '0001011808' || VOs[i].hmc.substr(0, 10) === '0001011811' || VOs[i].hmc.substr(0, 10) === '0001011812' || VOs[i].hmc.substr(0, 10) === '0001011813' || VOs[i].hmc.substr(0, 10) === '0001011814'){\n          A = 1;\n      }\n  }\n  return A;\n}", "description": "退役士兵减免事项选择标志", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "37cd7d3c6c5a4149bfdbef8019fc8e5d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_18", "functionMc": "customGet_fb1lcsftx_18", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_18() {\n  return this.customGet_fb1lcsftx('18') === 0;\n}", "description": "根据二维表行序号判断附表1第18行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','18').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "3be08ab6ac3d4de49a0590dadf28ca6d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_7", "functionMc": "customGet_fb1lcsftx_7", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_7() {\n  return this.customGet_fb1lcsftx('7') === 0;\n}", "description": "根据二维表行序号判断附表1第7行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','7').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "3c71db5cf49a497bbeb47cea268a268a", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgysfwjzjtfpbl_Less", "functionMc": "customGet_fzjgysfwjzjtfpbl_Less", "functionType": "diy", "functionBody": "function customGet_fzjgysfwjzjtfpbl_Less(arr,sumSsr ){\n    var index = arr[0];\n    var bl = 0;\n    var zjgysfwjzjtxssr = this.ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtxssr;\n    var sum1 = sumSsr + zjgysfwjzjtxssr;\n    var fzjgysfwjzjtxssr = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[index].fzjgysfwjzjtxssr;\n        if(this.customGet_fzjgsbjnfs_family() === 1&&this.round ( sum1 , 2 ) !== 0){\n            bl = fzjgysfwjzjtxssr / sum1;\n        }else if(this.customGet_fzjgsbjnfs_family() === 2&&this.round ( sum1 , 2 ) !== 0){\n            bl = (1 - this.ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl) * fzjgysfwjzjtxssr / sum1;     \n        }else if(this.customGet_fzjgsbjnfs_family() === 3&&this.round ( sumSsr, 2 ) !== 0){\n            bl = fzjgysfwjzjtxssr / sumSsr; \n        }\n    return bl;\n}", "description": "《汇总纳税企业增值税分配表》分支机构销售收入情况--分支机构应税服务即征即退--分配比例应等于该行分支机构\"应税服务即征即退销售收入\"÷总机构和所有分支机构的\"应税服务即征即退销售收入。", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwjzjtxssr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtxssr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtfpse\"]\n"}, {"functionXh": "3f6ceab28ab24e848a1b70baf61a6382", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_isFb3AllZero", "functionMc": "customGet_isFb3AllZero", "functionType": "diy", "functionBody": "function customGet_isFb3AllZero() {\n  var list = this.ywbw.sbZzsYbnsrFb3Ysfwkcxm || [];\n  var total = 0;\n  var fieldList = ['msxse', 'qcye', 'bqfse', 'bqsjkcje', 'bqykcje', 'qmye'];\n  for (var i = 0, len = list.length; i < len; i += 1) {\n    var item = list[i];\n    for (var j = 0, lenj = fieldList.length; j < lenj; j += 1) {\n      var key = fieldList[j];\n      total += Math.abs(item[key] || 0);\n    }\n  }\n  return total === 0;\n}", "description": "判断附表三是否全为0", "dependencies": "function getDep() {\n  var keys = ['msxse', 'qcye', 'bqfse', 'bqsjkc<PERSON>', 'bqykcje', 'qmye'];\n  var list = [];\n  for (var m = 0, len = keys.length; m < len; m += 1) {\n    for (var n = 0; n < 8; n += 1) {\n      list.push({ xh: n + 1, name: keys[m] });\n    }\n  }\n  var arr = [];\n  for (var i = 0, len = list.length; i < len; i += 1) {\n    var item = list[i];\n    arr.push('getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,\"ewbhxh\",\"' + item.xh + '\").' + item.name + '');\n  }\n  return arr;\n}"}, {"functionXh": "4140b763749d49fa96a212cf410f6c15", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_jmbbqfse", "functionMc": "customGet_jmbbqfse", "functionType": "diy", "functionBody": "function customGet_jmbbqfse(indexArr) {\n  var index = indexArr[0];\n  var ywbw = this.ywbw;\n  var result = 0;\n  var bqfse = ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[index].bqfse;\n  var hmc = ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[index].hmc;\n  if (hmc === '0001129914|SXA031900185') {\n    result = this.getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','1').bqfse;\n  }\n  return Number((result).toFixed(2));\n}", "description": "获取减免表-减税项目-本期发生额", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','1').bqfse\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "470485b0d45344c18833d1b875d42b3b", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_5", "functionMc": "customGet_fb1lcsftx_5", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_5() {\n  return this.customGet_fb1lcsftx('5') === 0;\n}", "description": "根据二维表行序号判断附表1第5行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','5').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "48f71e4feea0471492958bbfdb716dee", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zbsse", "functionMc": "customGet_zbsse", "functionType": "diy", "functionBody": "function customGet_zbsse() {\n  var ywbw = this.ywbw;\n  // 增值税主表第1+3列的第1+5+7+8栏 \n  var zbsse = Number(this.getObjFromList(ywbw.sbZzsYbnsr, 'ewblxh', '1').asysljsxse) +\n      Number(this.getObjFromList(ywbw.sbZzsYbnsr, 'ewblxh', '3').asysljsxse) +\n\n      Number(this.getObjFromList(ywbw.sbZzsYbnsr, 'ewblxh', '1').ajybfjsxse) +\n      Number(this.getObjFromList(ywbw.sbZzsYbnsr, 'ewblxh', '3').ajybfjsxse) +\n\n      Number(this.getObjFromList(ywbw.sbZzsYbnsr, 'ewblxh', '1').mdtbfckxse) +\n\n      Number(this.getObjFromList(ywbw.sbZzsYbnsr, 'ewblxh', '1').msxse);\n      \n  return this.round(zbsse, 2)\n}", "description": "customGet_zbsse", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').asysljsxse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').asysljsxse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').ajybfjsxse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').ajybfjsxse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').mdtbfckxse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').msxse\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','1').bq<PERSON><PERSON><PERSON>\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','2').bqykcje\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','3').bqykcje\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','4').bqykcje\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','5').bqykcje\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','6').bqykcje\",\"getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','6').bqykcje\",\"this.getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','7').bqykcje\",\"this.getObjFromList(ywbw.sbZzsYbnsrFb3Ysfwkcxm,'ewbhxh','8').bqykcje\"]"}, {"functionXh": "5092a9af244542bd8c3b364ebfceccd3", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_getSumJjdj", "functionMc": "customGet_getSumJjdj", "functionType": "diy", "functionBody": "function customGet_getSumJjdj(type,name){\n  var ret = 0;\n  var fzxx = this.fzxx;\n  if(fzxx.tzxx && fzxx.tzxx.ybnsrTzxxJjdjLjfseList) {\n    ret = this.sumList ( this.getListFromList ( fzxx.tzxx.ybnsrTzxxJjdjLjfseList , 'jjdjzcXzjg' , type ) , name );\n  }\n  return ret;\n}", "description": "计算指定加计抵减政策上一期指定节点合计值", "dependencies": "[]"}, {"functionXh": "50d92399028649439cace3b6b2dbe478", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_27", "functionMc": "customGet_zzsfjsbqjxsemx_27", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_27() {\n    return this.customGet_zzsfjsbqjxsemx(27);\n}", "description": "第【27】行仅辅导期一般纳税人或出口退税企业可填写", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','27').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','27').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','27').se\"]\n"}, {"functionXh": "515df73f796440949f5cef46e418511a", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zdrqts", "functionMc": "customGet_zdrqts", "functionType": "diy", "functionBody": "function customGet_zdrqts() {\n        var Date2 = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\n      var Date1 = new Date(this.sbsx.skssqz) <= new Date('2023-12-31');\n      var sydjsyxejmqy = this.fzxx.bqxx.sydjsyxejmqy;\n      var sypkrkxejmqy = this.fzxx.bqxx.sypkrkxejmqy;\n      var gtgsh = this.fzxx.bqxx.gtgsh;\n      var result = '';\n      var jmb1 = 0;\n      var jmb2 = 0;\n   var jmb3 = 0;\n      var fb51 = 0;\n      var fb52 = 0;\n   var fb53 = 0;\n      var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n      var VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n      for (var i = 0; i < VOs.length; i++) {\n        if (VOs[i].ssjmxzDm === '0007013611' || VOs[i].ssjmxzDm === '0061013609' || VOs[i].ssjmxzDm === '0099013602'){\n                    fb51 = 1;\n            break;\n                }\n            }\n      for (var i = 0; i < VOs.length; i++) {\n        if (VOs[i].ssjmxzDm === '0007013610' || VOs[i].ssjmxzDm === '0061013608' || VOs[i].ssjmxzDm === '0099013601'){\n                    fb52 = 1;\n            break;\n                }\n        }\n      for (var i = 0; i < VOs1.length; i++) {\n                if (VOs1[i].hmc.substr(0, 10) === '0001013611'){\n                    jmb1 = 1;\n            break;\n                }\n            }\n      for (var i = 0; i < VOs1.length; i++) {\n          if (VOs1[i].hmc.substr(0, 10) === '0001013610'){\n                    jmb2 = 1;\n            break;\n                }\n            }\n   for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013612' || VOs[i].ssjmxzDm === '0007013613' || VOs[i].ssjmxzDm === '0061013610' || VOs[i].ssjmxzDm === '0061013611' || VOs[i].ssjmxzDm === '0099013603' || VOs[i].ssjmxzDm === '0099013604'){\n                fb53 = 1;\n    break;\n            }\n        }\n   for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001013612' || VOs1[i].hmc.substr(0, 10) === '0001013613'){\n                jmb3 = 1;\n    break;\n            }\n        }\n      if (Date1 && sydjsyxejmqy==='N' && sypkrkxejmqy === 'N' && (this.customGet_jmbjycyjmxm() === 1 || this.customGet_fb5jycyjmxm() === 1)) {\n        result = '请采集《重点群体人员本年度实际工作时间》，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n      }else if (Date2 && sydjsyxejmqy==='N' && gtgsh === 'Y' && (jmb1 === 1 || fb51  === 1) && ((jmb2 !== 1 && fb52 !== 1) || sypkrkxejmqy !== 'N')) {\n        result = '没有采集创业重点群体人员信息表的登记失业半年以上人员，零就业家庭、享受城市低保登记失业人员，毕业年度内高校毕业生从事个体经营人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n      }else if (Date2 && sypkrkxejmqy ==='N' && gtgsh === 'Y' && (jmb2 === 1 || fb52  === 1) && ((jmb1 !== 1 && fb51 !== 1) || sydjsyxejmqy !== 'N')) {\n        result = '没有采集创业重点群体人员信息表的脱贫人口人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n      }else if (Date2 && sydjsyxejmqy==='N' && sypkrkxejmqy === 'N' && gtgsh === 'Y' && (jmb2 === 1 || fb52  === 1) && (jmb1 === 1 || fb51  === 1)) {\n        result = 'all';\n      }else if (Date2 && sydjsyxejmqy==='N' && sypkrkxejmqy === 'N' && gtgsh === 'N' && (jmb3 === 1 || fb53  === 1)) {\n  result = '没有采集企业重点群体人员信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\" >点此采集。</a>';\n   }\n      return result;\n    }", "description": "重点重点群体采集提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "53c88f0eab0246318277e80807d4a884", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjshb_302col5", "functionMc": "customGet_fjshb_302col5", "functionType": "diy", "functionBody": "function customGet_fjshb_302col5(index){\n  var ywbw = this.ywbw;\n\n  if(this.hznsFjsffpbGrid.hznsFjsffpbGridlb[index] && this.hznsFjsffpbGrid.hznsFjsffpbGridlb[index].jsyj > 0){\n    return this.hznsFjsffpbGrid.hznsFjsffpbGridlb[index].jsyj\n  }\n  var obj = this.getObjFromList(ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb,\"fzjgnsrsbh\",ywbw.sbFjsfHznsfjsffpb[index].fzjgnsrsbh);\n  if(obj){\n    return Number(obj.fzjgybhwjlwfpse) + Number(obj.fzjgybhwjlwjzjtfpse) + Number(obj.fzjgysfwfpse) + Number(obj.fzjgysfwjzjtfpse)\n  }else{\n    return 0;\n  }\n}", "description": "customGet_fjshb_302col5", "dependencies": "[\n  \"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwfpse\",\n  \"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwjzjtfpse\",\n  \"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwfpse\",\n  \"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwjzjtfpse\"\n]"}, {"functionXh": "5c50d577985a44af8955559f45be9686", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4Row1Col4", "functionMc": "customGet_fb4Row1Col4", "functionType": "diy", "functionBody": "function customGet_fb4Row1Col4() {\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if ((VOs[i].hmc === '0001129914' && VOs[i].swsxDm === 'SXA031900185') || VOs[i].hmc === '0001129914|SXA031900185')  {\n                return Number(VOs[i].bqsjdjse);\n            }\n        }\n    } catch (e) { }\n    return this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk, 'ewbhxh', '1').bqsjdjse;\n}", "description": "附表四1_4取数", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse\"]"}, {"functionXh": "5e35e19e6aab43cc99e320992c39f5ea", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_Y2", "functionMc": "customGet_Y2", "functionType": "diy", "functionBody": "function customGet_Y2() {\n    if (!this.ywbw.sbZzsYbnsrFb1Bqxsqkmx) {\n        return 0;\n    }\n    var a = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '15').kchXxynse;\n    return a;\n}", "description": "Y2：简易征收即征即退项目中，服务的销售税额，公式为：附表1【14列15栏】", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','15').kchXxynse\"]"}, {"functionXh": "5e5ca308d4024b08ada0b7a1be5bb422", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgysfwfpse_Less", "functionMc": "customGet_fzjgysfwfpse_Less", "functionType": "diy", "functionBody": "function customGet_fzjgysfwfpse_Less(arr){\n    var index = arr[0];\n    var se = 0;\n    var abc = this.customGet_A() * ( 1 - this.customGet_a1() ) + this.customGet_B2() - this.customGet_C() * ( 1 - this.customGet_c1() );\n    var fzjgysfwfpbl = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[index].fzjgysfwfpbl;\n    if(this.customGet_fzjgsbjnfs_family() === 3){\n        se = abc * (1 - this.ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl) * fzjgysfwfpbl;\n    }else{\n        se = abc * fzjgysfwfpbl;\n    }\n    return se;\n}", "description": "分支机构应税服务的分配税额应等于[]", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwfpbl\",\"ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl\"]\n"}, {"functionXh": "5f19e90187564d3d835a7ff19a43d101", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_hzfpbFpse", "functionMc": "customGet_hzfpbFpse", "functionType": "diy", "functionBody": "function customGet_hzfpbFpse() {\n\tvar zjgfpse=this.customGet_hzfpbzjgFpse();\n\tvar fzjgfpse=this.customGet_hzfpbfzjgFpse();\n\treturn this.round(zjgfpse + fzjgfpse, 2);\n}", "description": "汇总纳税分配表分配税额合计", "dependencies": "[\"customGet_hzfpbfzjgFpse\",\"customGet_hzfpbzjgFpse\"]"}, {"functionXh": "64eec924c3004e8783de3b8b5d526207", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_22", "functionMc": "customGet_fb1lcsftx_22", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_22() {\n  return this.customGet_fb1lcsftx('22') === 0;\n}", "description": "根据二维表行序号判断附表1第9b行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','22').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "697bc592a7184de2af88e9f4572838ab", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "<PERSON><PERSON><PERSON><PERSON>", "functionMc": "<PERSON><PERSON><PERSON><PERSON>", "functionType": "diy", "functionBody": "function doWhile(start, end, expression, xhKey) {\n  var listPath = null;\n  var xhMap = {};\n  if (typeof end === \"string\") {\n    xhKey = expression;\n    expression = end;\n    end = undefined;\n  }\n  if (xhKey) {\n    listPath = expression.match(/(ywbwDto\\.[^\\ ]*?)\\[\\*\\]/)[1];\n    var list = engineJs.custom(engineJs.content, listPath);\n    for (var k = 0; k < list.length; k++) {\n      xhMap[list[k][xhKey]] = k;\n    }\n  }\n  var getIndex = function (xhKey, xhValue) {\n    return xhMap[xhValue];\n  };\n  var run = function (i) {\n    return engineJs.custom(\n      engineJs.content,\n      expression\n        .replace(/\\[\\*\\]/gi, \"[\"+i+\"]\")\n        .replace(/(['\"])(\\*)\\1/g, \"$1\"+i+\"$1\")\n    );\n  };\n  if (end) {\n    for (var i = start; i < end; i++) {\n      if (run(xhK<PERSON> ? getIndex(xh<PERSON><PERSON>, i) : i)) {\n        return true;\n      }\n    }\n  } else if (typeof start === \"object\" && start.length) {\n    for (var i = 0; i < start.length; i++) {\n      if (run(xhKey ? getIndex(xhKey, start[i]) : start[i])) {\n        return true;\n      }\n    }\n  }\n  return false;\n}", "description": "循环判断直至满足条件", "dependencies": "[]"}, {"functionXh": "6bf80d69974e44ed98c9213d44969078", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsldtse", "functionMc": "customGet_fjsldtse", "functionType": "diy", "functionBody": "function customGet_fjsldtse() {\n  var res = 0;\n  var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n  for (var i = 0; i < VOs.length; i++) {\n        if (VOs[i].zsxmDm === '30203'){\n            res = VOs[i].zzsldtse;\n            break;\n                }\n            }\n  return res;\n}", "description": "获取附加税留底退税额", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].zzsldtse\"]"}, {"functionXh": "75b13ce9abe640c38f689fecbfb18af4", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_delcpymx", "functionMc": "customGet_delcpymx", "functionType": "diy", "functionBody": "function customGet_delcpymx(arr) {\n    var index = arr[0];\n    var obj = this.ywbw.sbZzsYbnsrFbCpygxcqk[index];\n    if (obj.ypxh === null || obj.ypxh === '') {\n\t\tobj.qckcDcsl = 0;\n\t\tobj.qckcZgsl = 0;\n\t\tobj.bqcklYkcsl = 0;\n\t\tobj.bqrkZgsl = 0;\n\t\tobj.bqrkDcsl = 0;\n\t\tobj.bqckYsbfsl = 0;\n\t\tobj.bqckFysbfZysl = 0;\n\t\tobj.bqckFysbfDcsl = 0;\n\t\tobj.qmkcZgsl = 0;\n\t\tobj.qmkcDcsl = 0;\n      return 0;\n    }\n    return obj.qckcZgje;\n}", "description": "成品油明细，清空油品型号后单元格", "dependencies": "[\"ywbw.sbZzsYbnsrFbCpygxcqk[*].ypxh\"]"}, {"functionXh": "7a07e0d7bb444a3fa4ab2cdd00b2c56e", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_ybnsrFjsCol10", "functionMc": "customGet_ybnsrFjsCol10", "functionType": "diy", "functionBody": "function customGet_ybnsrFjsCol10(indexArr) {\n    var index = indexArr[0];\n    var ywbw = this.ywbw;\n    var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm || '';\n    if (zsxmDm === '10109') {\n        return 0;\n    }\n    var A = ywbw.sbFjsf.sbFjsfQtxx.bqsfsycjyhxqyjzzc;\n    var B = this.round(ywbw.sbFjsf.sbFjsfQtxx.dqxztze * 0.3,2) + ywbw.sbFjsf.sbFjsfQtxx.sqldkdmje;\n    var fjsxxGridlb1 = this.getObjFromList(ywbw.sbFjsf.sbFjsfMx,'zsxmDm','30203');\n    var fjsxxGridlb2 = this.getObjFromList(ywbw.sbFjsf.sbFjsfMx,'zsxmDm','30216');\n    var C = this.round((fjsxxGridlb1.bqynsfe - fjsxxGridlb1.jme - fjsxxGridlb1.phjmse) + (fjsxxGridlb2.bqynsfe - fjsxxGridlb2.jme - fjsxxGridlb2.phjmse),2);\n    var D = this.round(ywbw.sbFjsf.sbFjsfMx[index].bqynsfe - ywbw.sbFjsf.sbFjsfMx[index].jme - ywbw.sbFjsf.sbFjsfMx[index].phjmse,2);\n    if (A === \"N\") {\n        return 0;\n    }\n    if (A === \"Y\" && B < 0) {\n        if (zsxmDm === \"30216\") {\n            return this.round((B - fjsxxGridlb1.bqcjrhxqydmje),2);\n        }\n        return this.round((B *(ywbw.sbFjsf.sbFjsfMx[index].sl1 /(fjsxxGridlb1.sl1 + fjsxxGridlb2.sl1))),2);\n    }\n    if (A === \"Y\" && B >= 0 && B - C >= 0) {\n        return D;\n    }\n    if (A === \"Y\" && B >= 0 && B - C < 0) {\n        if (zsxmDm === \"30216\") {\n            return this.round(B - fjsxxGridlb1.bqcjrhxqydmje,2);\n        }\n        return this.round((B *(ywbw.sbFjsf.sbFjsfMx[index].sl1 /(fjsxxGridlb1.sl1 + fjsxxGridlb2.sl1))),2);\n    }\n    return 0;\n}", "description": "customGet_ybnsrFjsCol10", "dependencies": "[\n    \"ywbw.sbFjsf.sbFjsfMx[1].bqynsfe\",\n    \"ywbw.sbFjsf.sbFjsfMx[2].bqynsfe\",\n    \"ywbw.sbFjsf.sbFjsfMx[1].jme\",\n    \"ywbw.sbFjsf.sbFjsfMx[2].jme\",\n    \"ywbw.sbFjsf.sbFjsfMx[1].phjmse\",\n    \"ywbw.sbFjsf.sbFjsfMx[2].phjmse\",\n    \"ywbw.sbFjsf.sbFjsfQtxx.sqldkdmje\",\n    \"ywbw.sbFjsf.sbFjsfQtxx.dqxztze\",\n    \"ywbw.sbFjsf.sbFjsfQtxx.bqsfsycjyhxqyjzzc\"\n]"}, {"functionXh": "7a154e7db2774e86961f98270d5d2911", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_31", "functionMc": "customGet_zzsfjsbqjxsemx_31", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_31() {\n    return this.customGet_zzsfjsbqjxsemx(31);\n}", "description": "第【31】行仅辅导期一般纳税人或出口退税企业可填写\n", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','31').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','21').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','31').se\"]\n"}, {"functionXh": "7aab28a90b8b4f57bf848c65f176a97d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_Jmbckkj_Edit", "functionMc": "customGet_Jmbckkj_Edit", "functionType": "diy", "functionBody": "function customGet_Jmbckkj_Edit() {\n  var A = 1;\n  var fzxx = this.fzxx;\n  var cktsBaqy = fzxx.bqxx.cktsBaqy;\n  var cktsCkdlba = fzxx.bqxx.cktsCkdlba;\n  var cktsKjysfwmsba = fzxx.bqxx.cktsKjysfwmsba;\n  var cktsLljgmszmdstfqy = fzxx.bqxx.cktsLljgmszmdstfqy;\n  if (this.substr(this.nsrxx.zgswjDm,0,5) === '12102' && cktsBaqy === 'N' && cktsCkdlba === 'N' && cktsKjysfwmsba === 'N' && cktsLljgmszmdstfqy === 'N') {\n A = 0;\n  }else if (this.substr(this.nsrxx.zgswjDm,0,3) === '150' && cktsBaqy === 'N') {\n  A = 0;\n}\n  return A;\n}", "description": "判断减免表出口退税和跨境服务是否可编辑，返回0不可编辑，1可编辑", "dependencies": "[]"}, {"functionXh": "7d1e439c6bbb492db996753db323b694", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_XejmjeCheck", "functionMc": "customGet_XejmjeCheck", "functionType": "diy", "functionBody": "function customGet_XejmjeCheck(arr) {\n\tvar index = arr[0];\n\tvar result = 1;\n    var A = this.customGet_jmbzzsxejmje();\n\tvar cjsxejmje = 0;\n\tvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\n\tfor (var i = 0; i < VOs.length; i++){\n\t\tif (VOs[i].zsxmDm === '10109') {\n\t\t\tcjsxejmje = this.round(cjsxejmje + VOs[i].zzsxejmje,2);\n\t\t}\n\t}\n\tif (VOs[index].zsxmDm === '10109' && cjsxejmje !== A) {\n\t\t\tresult = 0;\n\t\t}\n\tif (VOs[index].zsxmDm === '30203' && VOs[index].zzsxejmje !== A) {\n\t\t\tresult = 0;\n\t\t}\n\tif (VOs[index].zsxmDm === '30216' && VOs[index].zzsxejmje !== A) {\n\t\t\tresult = 0;\n\t\t}\n\treturn result;\t\n\t}", "description": "附表五“增值税限额减免金额”列校验,返回1表示通过，0表示不通过", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje\",\"customGet_jmbzzsxejmje\"]"}, {"functionXh": "7f2e7d5bf6e84aa0ade1d16990a1f169", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_33", "functionMc": "customGet_zzsfjsbqjxsemx_33", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_33() {\n    return this.customGet_zzsfjsbqjxsemx(33);\n}", "description": "第【33】行仅辅导期一般纳税人或出口退税企业可填写\n", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','33').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','33').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','33').se\"]\n"}, {"functionXh": "8014db1a2e92420b9d1d3fe084504cb6", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_4", "functionMc": "customGet_fb1lcsftx_4", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_4() {\n  return this.customGet_fb1lcsftx('4') === 0;\n}", "description": "根据二维表行序号判断附表1第4行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','4').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "8042ac0cb78f47dabc7e24f2643d54e2", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_z1", "functionMc": "customGet_z1", "functionType": "diy", "functionBody": "function customGet_z1() {\n    var a = this.customGet_X() * this.customGet_x1() + this.customGet_Y1();\n    var b = this.customGet_X() + this.customGet_Y1() + this.customGet_Y2();\n    return b === 0 ? 0 : Number((a / b).toFixed(10));\n}", "description": "z1: 即征即退项目中，(一般计税+简易征收)货劳税额在总税额的比例(货劳+应税服务)，公式为：【X*x1+Y1】/【X+Y1+Y2】;", "dependencies": "[\"customGet_X\", \"customGet_x1\", \"customGet_Y1\", \"customGet_Y2\"]"}, {"functionXh": "854cf162eab947f0bb559074feb7f661", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_14", "functionMc": "customGet_fb1lcsftx_14", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_14() {\n  return this.customGet_fb1lcsftx('14') === 0;\n}", "description": "根据二维表行序号判断附表1第14行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','14').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "858c3024307e42509abab2b59f3542e0", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_1", "functionMc": "customGet_fb1lcsftx_1", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_1() {\n  return this.customGet_fb1lcsftx('1') === 0;\n}", "description": "根据二维表行序号判断附表1第1行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','1').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "85f0500a725b401f99c5cdbe8a17721c", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsJmxz", "functionMc": "customGet_fjsJmxz", "functionType": "diy", "functionBody": "function customGet_fjsJmxz(indexArr) {\n    var index = indexArr[0];\n    var ywbw = this.ywbw;\n    var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm;\n    var ynse = ywbw.sbFjsf.sbFjsfMx[index].bqynsfe;\n    var xse = this.customGet_zbsse();\n    var ssjmxzDm = ywbw.sbFjsf.sbFjsfMx[index].ssjmxzDm || '';\n    if (this.inArray(['30203', '30216'], zsxmDm)) {\n        if (ynse > 0 && xse <= (this.sbsx.nsqxDm - 5) * 100000) {\n            return zsxmDm === '30203' ? '0061042802' : '0099042802'\n        } else if (this.inArray(['0061042802', '0099042802'], ssjmxzDm)) {\n            return '';\n        }\n    }\n    if (ynse === 0) {\n        return '';\n    } else {\n        return ssjmxzDm;\n    }\n}", "description": "设置附加税减免", "dependencies": "[\"customGet_zbsse\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\"]"}, {"functionXh": "86d432ae1d644b7b9a8a23345ef4f464", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_a1", "functionMc": "customGet_a1", "functionType": "diy", "functionBody": "function customGet_a1() {\n    if (!this.ywbw.sbZzsYbnsrFb1Bqxsqkmx) {\n        return 0;\n    }\n    var a = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '1').hjXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '23').hjXxynse\n        - this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '6').hjXxynse;\n    var b = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '2').kchXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '4').kchXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '5').kchXxynse\n        - this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '7').kchXxynse;\n    return Number((a + b).toFixed(2)) === 0 ? 0 : Number((a / (a + b)).toFixed(10));\n}", "description": "a1：一般计税一般项目中,货劳在总销售收入的比例(货劳+应税服务),公式为：【附表一第10列（1+3-6栏）】÷【附表一第10列（1+3-6栏）+14列（2+4+5-7）栏】", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','1').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','23').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','6').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','2').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','4').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','5').kchXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','7').kchXxynse\"]"}, {"functionXh": "891a253320ee4c8fb2de95128381ba24", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_getjzxqkdmje", "functionMc": "customGet_getjzxqkdmje", "functionType": "diy", "functionBody": "function customGet_getjzxqkdmje() {\n    var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n\tvar dqxztze = this.ywbw.sbFjsf.sbFjsfQtxx.dqxztze;\n\tvar sqldkdmje = this.ywbw.sbFjsf.sbFjsfQtxx.sqldkdmje;\n\tvar result = 0;\n\tvar A = 0;\n    for (var i = 0; i < VOs.length; i++) {\n\tif (VOs[i].zsxmDm === '30203' || VOs[i].zsxmDm === '30216'){\n\tA = A + VOs[i].bqcjrhxqydmje;\n\t}\n\t}\n\tresult = this.round((dqxztze * 0.3) + sqldkdmje - A,2);\n\treturn result;\n}", "description": "获取结转下期可抵免金额", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.dqxztze\",\"ywbw.sbFjsf.sbFjsfQtxx.sqldkdmje\",\"ywbw.sbFjsf.sbFjsfMx[*].bqcjrhxqydmje\"]"}, {"functionXh": "89afd233a72240459b051309d2eabf9a", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLslfJmxzMc", "functionMc": "customGet_fjsLslfJmxzMc", "functionType": "diy", "functionBody": "function customGet_fjsLslfJmxzMc(arr) {\n  var index = arr[0];\n  var fjsxxForm = this.ywbw.sbFjsf.sbFjsfQtxx;\n  var obj = this.ywbw.sbFjsf.sbFjsfMx[index];\n  var A = fjsxxForm.bqsfsyxgmyhzc;\n  var B = obj.bqynsfe- obj.jme;\n  var syzt = fjsxxForm.jzzcsyztDm;\n  var xwqyLslfJmxzList = this.fzxx.kzxx && this.fzxx.kzxx.xwqyLslfJmxzList || [];\n  var mc = '《财政部 国家税务总局关于进一步实施小微企业“六税两费”减免政策的公告》';\n  if (A === 'Y' && B !== 0) {\n      if (obj.zsxmDm === '10109') {\n          if (syzt === '22') {\n              var curItem1 = this.getObjFromList(xwqyLslfJmxzList, 'ssjmxzDm', '0007049902');\n              if (curItem1 && curItem1.ssjmxzmc) {\n                return curItem1.ssjmxzmc;\n              }\n              return '0007049902|SXA031901266|' + mc;\n          } else if (syzt === '21') {\n              var curItem2 = this.getObjFromList(xwqyLslfJmxzList, 'ssjmxzDm', '0007049903');\n              if (curItem2 && curItem2.ssjmxzmc) {\n                return curItem2.ssjmxzmc;\n              }\n              return '0007049903|SXA031901267|' + mc;\n          }\n      } else if (obj.zsxmDm === '30203') {\n          if (syzt === '22') {\n              var curItem3 = this.getObjFromList(xwqyLslfJmxzList, 'ssjmxzDm', '0061049902');\n              if (curItem3 && curItem3.ssjmxzmc) {\n                return curItem3.ssjmxzmc;\n              }\n              return '0061049902|SXA031901272|' + mc;\n          } else if (syzt === '21') {\n              var curItem4 = this.getObjFromList(xwqyLslfJmxzList, 'ssjmxzDm', '0061049903');\n              if (curItem4 && curItem4.ssjmxzmc) {\n                return curItem4.ssjmxzmc;\n              }\n              return '0061049903|SXA031901273|' + mc;\n          }\n      } else if (obj.zsxmDm === '30216') {\n          if (syzt === '22') {\n              var curItem5 = this.getObjFromList(xwqyLslfJmxzList, 'ssjmxzDm', '0099049902');\n              if (curItem5 && curItem5.ssjmxzmc) {\n                return curItem5.ssjmxzmc;\n              }\n              return '0099049902|SXA031901274|' + mc;\n          } else if (syzt === '21') {\n              var curItem6 = this.getObjFromList(xwqyLslfJmxzList, 'ssjmxzDm', '0099049903');\n              if (curItem6 && curItem6.ssjmxzmc) {\n                return curItem6.ssjmxzmc;\n              }\n              return '0099049903|SXA031901275|' + mc;\n          }\n      }\n  }\n  return '';\n}", "description": "六税两费减免名称--前端展示时使用", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.bqsfsyxgmyhzc\",\"ywbw.sbFjsf.sbFjsfQtxx.jzzcsyztDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\"]"}, {"functionXh": "89d3e990357741048fb3960f5ff9ee4c", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4bqtjetstx", "functionMc": "customGet_fb4bqtjetstx", "functionType": "diy", "functionBody": "function customGet_fb4bqtjetstx() {\n\tvar fzxx = this.fzxx;\n\tvar result = '';\n\tvar ywbw = this.ywbw;\n\tvar sfkbj = this.customGet_fb4editable();\n\tvar xzjg = ywbw.sbZzsXzjjdjzcjg.xzjg;\n\tvar bqtje = this.round(this.getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqzce + this.getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqzce,2);\n\tvar Z = this.round(this.getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','13').je - this.getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','21').je - this.getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','22').je,2);\n\tvar syjjdjl = 0;\n\tif (xzjg === 0) {\n\t\tsyjjdjl = fzxx.csxx.jcdlSyjjdjl;\n\t}\n\tif (xzjg === 2) {\n\t\tsyjjdjl = fzxx.csxx.gymjSyjjdjl;\n\t}\n\tif (xzjg === 3) {\n\t\tsyjjdjl = fzxx.csxx.xjzzySyjjdjl;\n\t}\n\tif (sfkbj !== 0 && (xzjg === 0 || xzjg === 2 || xzjg === 3) && fzxx.tzxx.jjdjLjyjts > fzxx.tzxx.jjdjLjtje && fzxx.tzxx.jjdjLjyjts > 0 && Z > 0 && bqtje < this.round(Z * syjjdjl,2)) {\n\t\tresult = 'tstx'\n\t}\n\treturn result;\n\t}", "description": "“本期调减额”提示提醒", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqzce\",\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','7').bqzce\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','13').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','21').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','22').je\",\"ywbw.sbZzsXzjjdjzcjg.xzjg\",\"customGet_fb4editable\"]"}, {"functionXh": "8aa756650fe1454c860e97c571a93518", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLslfJzbl", "functionMc": "customGet_fjsLslfJzbl", "functionType": "diy", "functionBody": "function customGet_fjsLslfJzbl(arr) {\n    var index = arr[0];\n    var fjsxxForm = this.ywbw.sbFjsf.sbFjsfQtxx;\n    var obj = this.ywbw.sbFjsf.sbFjsfMx[index];\n    var A = fjsxxForm.bqsfsyxgmyhzc;\n    var B = obj.bqynsfe - obj.jme;\n    var syzt = fjsxxForm.jzzcsyztDm;\n    if (A === 'Y' && B !== 0) {\n        if (obj.zsxmDm === '10109') {\n            if (syzt === '22') {\n                return Number(0.5 || 0);\n            } else if (syzt === '21') {\n                return Number(0.5 || 0);\n            }\n        } else if (obj.zsxmDm === '30203') {\n            if (syzt === '22') {\n                return Number(0.5 || 0);\n            } else if (syzt === '21') {\n                return Number(0.5 || 0);\n            }\n        } else if (obj.zsxmDm === '30216') {\n            if (syzt === '22') {\n                return Number(0.5 || 0);\n            } else if (syzt === '21') {\n                return Number(0.5 || 0);\n            }\n        }\n    }\n    return 0;\n}", "description": "附加税普惠见证比例", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.bqsfsyxgmyhzc\", \"ywbw.sbFjsf.sbFjsfQtxx.jzzcsyztDm\", \"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\", \"ywbw.sbFjsf.sbFjsfMx[*].jme\"]"}, {"functionXh": "8d0ff260fdc34b26acd634f7b76fa407", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_hzfpbzjgFpse", "functionMc": "customGet_hzfpbzjgFpse", "functionType": "diy", "functionBody": "function customGet_hzfpbzjgFpse() {\n\tvar zjgfpse=this.round(this.ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwfpse + this.ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtfpse + this.ywbw.sbZzsYbnsrFbHznsfp.zjgysfwfpse + this.ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtfpse,2);\n\treturn zjgfpse;\n}", "description": "计算分配表总机构分配税额", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtfpse\"]"}, {"functionXh": "8e7149a4aa6a40e4974c6d9e4bbb3711", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_jmbbqsjdjse", "functionMc": "customGet_jmbbqsjdjse", "functionType": "diy", "functionBody": "function customGet_jmbbqsjdjse(indexArr) {\n  var index = indexArr[0];\n  var ywbw = this.ywbw;\n  var result = 0;\n  var bqfse = ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[index].bqsjdjse;\n  var hmc = ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[index].hmc;\n  if (hmc === '0001129914|SXA031900185') {\n    result = this.getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','1').bqsjdjse;\n  }\n  return Number((result).toFixed(2));\n}", "description": "获取减免表-减税项目-本期实际抵减税额", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','1').bqsjdjse\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "8e8b0baab9764b1d8a779a12e7904324", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_LdtseCheck", "functionMc": "customGet_LdtseCheck", "functionType": "diy", "functionBody": "function customGet_LdtseCheck(arr) {\n var index = arr[0];\n var cjsldtse = 0;\n var yqz = 0;\n var result = 0;\n var minybzzs = 0;\n var cjszero = 0;\n var ywbw = this.ywbw;\n var VOs = ywbw.sbFjsf.sbFjsfMx;\n var moreDate = new Date(this.sbsx.skssqz) > new Date('2022-05-31');\n var A = this.round(ywbw.sbFjsf.sbFjsfQtxx.dqxzldtse + ywbw.sbFjsf.sbFjsfQtxx.sqjcldtse,2);\n var B = this.round(ywbw.sbFjsf.sbFjsfMx[index].zzsxejmje + ywbw.sbFjsf.sbFjsfMx[index].zzsmdse - this.getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').jybfYnse - this.getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').jybfYnse , 2 );\n for (var i = 0; i < VOs.length; i++){\n  if (VOs[i].zsxmDm === '10109') {\n   cjsldtse = this.round(cjsldtse + VOs[i].zzsldtse,2);\n   if (VOs[i].zzsldtse !== 0) {\n\t\tcjszero = 1;\n   }\n  }\n  }\n for (var i = 0; i < VOs.length; i++){\n  if (VOs[i].zsxmDm === '10109') {\n   minybzzs = this.round(minybzzs + VOs[i].ybzzs,2);\n  }\n }\n  for (var i = 0; i < VOs.length; i++){\n  if (VOs[i].zsxmDm !== '10109') {\n   minybzzs = this.min(minybzzs,VOs[i].ybzzs);\n  }\n }\n B = this.round(B + minybzzs,2);\n if (VOs[index].zsxmDm === '10109') {\n   for (var i = 0; i < VOs.length; i++){\n\t\tif (VOs[i].zsxmDm === '10109' && i !== index) {\n\t\tB = this.round(B + VOs[i].zzsxejmje + VOs[i].zzsmdse,2);\n\t\t  }\n\t  }\n  }\n if ( A <=0) {\n\tif (moreDate) {\n\t\tyqz = A;\n\t}else {\n\t\tyqz = 0;\n }\n }else {\n  yqz = this.max(this.min(A,B),0);\n }\n if (moreDate && ((VOs[index].zsxmDm === '10109' && cjsldtse !== yqz) || (VOs[index].zsxmDm === '30203' && VOs[index].zzsldtse !== yqz) || (VOs[index].zsxmDm === '30216' && VOs[index].zzsldtse !== yqz))) {\n\tresult = 1;\n }else if ((VOs[index].zsxmDm === '10109' && cjszero === 1 && !moreDate) || (VOs[index].zsxmDm === '30203' && VOs[index].zzsldtse !== yqz) || (VOs[index].zsxmDm === '30216' && VOs[index].zzsldtse !== yqz)){\n\tresult = 2;\n }\n return result;\n } ", "description": "附表五“留抵退税本期扣除额”列校验,返回0表示通过，1表示属期属期2022-05-31后不通过，2表示属期2022-05-31前不通过", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.dqxzldtse\",\"ywbw.sbFjsf.sbFjsfQtxx.sqjcldtse\",\"ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje\",\"ywbw.sbFjsf.sbFjsfMx[*].zzsmdse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').jybfYnse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').jybfYnse\",\"ywbw.sbFjsf.sbFjsfMx[*].zzsldtse\",\"ywbw.sbFjsf.sbFjsfMx[*].ybzzs\"]"}, {"functionXh": "8e9faed3bb78445aad7a8b22f4448e79", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_syJjdjl", "functionMc": "customGet_syJjdjl", "functionType": "diy", "functionBody": "function customGet_syJjdjl () {\n  var syjjdjl = 0;\n  var xzjg = (this.ywbw.sbZzsXzjjdjzcjg.xzjg || this.ywbw.sbZzsXzjjdjzcjg.xzjg === 0) &&  this.String(this.ywbw.sbZzsXzjjdjzcjg.xzjg);\n  if(xzjg==='0' || (this.fzxx.bqxx.jcdljjdjqy === 'Y' && this.fzxx.bqxx.gymjjjdjqy === 'N' && this.fzxx.bqxx.xjzzyjjdjqy === 'N')) {\n    syjjdjl = this.fzxx.csxx.jcdlSyjjdjl;\n  } else if(xzjg==='2' || (this.fzxx.bqxx.jcdljjdjqy === 'N' && this.fzxx.bqxx.gymjjjdjqy === 'Y' && this.fzxx.bqxx.xjzzyjjdjqy === 'N')) {\n    syjjdjl = this.fzxx.csxx.gymjSyjjdjl;\n  } else if(xzjg==='3' || (this.fzxx.bqxx.jcdljjdjqy === 'N' && this.fzxx.bqxx.gymjjjdjqy === 'N' && this.fzxx.bqxx.xjzzyjjdjqy === 'Y')) {\n    syjjdjl = this.fzxx.csxx.xjzzySyjjdjl;\n  }\n  return Number(syjjdjl)\n}", "description": "获取当前适用加计抵减率:\n1）若附表四填写第6栏弹出框选择了集成电路 或 仅有集成电路（母机和先进制造业都不享受），那么取“集成电路适用加计抵减率”。\n2）若附表四填写第6栏弹出框选择了工业母机 或 仅有工业母机（集成电路和先进制造业都不享受），那么取“工业母机适用加计抵减率”。\n3）若附表四填写第6栏弹出框选择了先进制造业 或 仅有先进制造业（母机和集成电路都不享受），那么取“先进制造业适用加计抵减率”。", "dependencies": "[\"ywbw.sbZzsXzjjdjzcjg.xzjg\"]"}, {"functionXh": "8ef15dda6bf84470bd3276176242939a", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx", "functionMc": "customGet_fb1lcsftx", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx(ewbhxh) {\n  var txbz = 0;\n  if (this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).kjskzzszyfpXse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).kjskzzszyfpXxynse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).kjqtfpXse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).kjqtfpXxynse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).wkjfpXse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).wkjfpXxynse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).xse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).hjXxynse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).jshj !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).ysfwkcxmbqsjkcje !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).kchHsmsxse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).kchXxynse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).nsjctzdxse !== 0 || this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh',ewbhxh).nsjctzXxynse !== 0) {\n    txbz = 1;\n  }\n  return txbz;\n}", "description": "根据二维表行序号判断附表1对应行是否填写", "dependencies": "[\"\"]"}, {"functionXh": "92899262940544d8972ad970453d3fab", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_C", "functionMc": "customGet_C", "functionType": "diy", "functionBody": "function customGet_C() {\n    var result = this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '1').ynsejze\n        + this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '1').fcyjse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '1').ckkjzyjksyjse;\n    return Number(result.toFixed(2));\n}", "description": "C: 主表(第23+28+29栏)【一般项目】本月数;", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').ynse<PERSON><PERSON>\", \"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').fcyjse\", \"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').ckkjzyjksyjse\"]"}, {"functionXh": "975ade53647a42b2b9c5a0181c70ecf5", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsFpbJsyj", "functionMc": "customGet_fjsFpbJsyj", "functionType": "diy", "functionBody": "function customGet_fjsFpbJsyj(nsrsbh) {\n    var obj = this.getObjFromList(this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb, 'fzjgnsrsbh', nsrsbh);\n    if (!obj) {\n        return 0;\n    }\n    return obj.fzjgybhwjlwfpse + obj.fzjgybhwjlwjzjtfpse + obj.fzjgysfwfpse + obj.fzjgysfwjzjtfpse;\n}", "description": "计算附加税分配表中的计税依据", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwjzjtfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwjzjtfpse\"]"}, {"functionXh": "98cad65095c64090ac573af77104d3a5", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_chssqjy", "functionMc": "customGet_chssqjy", "functionType": "diy", "functionBody": "function customGet_chssqjy() {\n  var ywbw = this.ywbw;\n  var A = ywbw.sbFjsf.sbFjsfQtxx.bchssqq;\n  var B = ywbw.sbFjsf.sbFjsfQtxx.bchssqz;\n  A = this.getMonthByTime(A);\n  B = this.getMonthByTime(B);\n  if(A == 1 && [1,3,6,12].indexOf(B) != -1)return true;\n  if(A == 4 && [4,6].indexOf(B) != -1)return true;\n  if(A == 7 && [7,9,12].indexOf(B) != -1)return true;\n  if(A == 10 && [10,12].indexOf(B) != -1)return true;\n  if(A == B)return true;\n  return false;\n}", "description": "customGet_chssqjy", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.bchssqq\",\"ywbw.sbFjsf.sbFjsfQtxx.bchssqz\"]"}, {"functionXh": "99d2d14d878b42e5a8216b2e699a4c74", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgysfwjzjtfpse_Less", "functionMc": "customGet_fzjgysfwjzjtfpse_Less", "functionType": "diy", "functionBody": "function customGet_fzjgysfwjzjtfpse_Less(arr){\nconsole.log(arr,this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb)\n    var index = arr[0];\n    var se = 0;\n    var abc = this.customGet_X() * ( 1 - this.customGet_x1() ) + this.customGet_Y2() - this.customGet_Z() * ( 1 - this.customGet_z1() );\n    var fzjgysfwjzjtfpbl = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[index].fzjgysfwjzjtfpbl;\n    if(this.customGet_fzjgsbjnfs_family() === 3){\n        se = abc * (1 - this.ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl) * fzjgysfwjzjtfpbl;\n    }else{\n        se = abc * fzjgysfwjzjtfpbl;\n    }\n    return se;\n}", "description": "分支机构应税服务即征即退的分配税额应等于【】", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwjzjtfpbl\",\"ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl\"]\n"}, {"functionXh": "99e308595b4d4a6ba42fad03ceaba25d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_2", "functionMc": "customGet_fb1lcsftx_2", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_2() {\n  return this.customGet_fb1lcsftx('2') === 0;\n}", "description": "根据二维表行序号判断附表1第2行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','2').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "9b6f8609af6145d7bb82b8e5c0c419d5", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_23", "functionMc": "customGet_fb1lcsftx_23", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_23() {\n  return this.customGet_fb1lcsftx('23') === 0;\n}", "description": "根据二维表行序号判断附表1第3行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','23').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "9d7a7ed9f49b447aa744b233872a2307", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_28", "functionMc": "customGet_zzsfjsbqjxsemx_28", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_28() {\n    return this.customGet_zzsfjsbqjxsemx(28);\n}", "description": "第【28】行仅辅导期一般纳税人或出口退税企业可填写", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','28').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','28').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','28').se\"]\n"}, {"functionXh": "a4d5d8536ad54ed6bc95b9642d29ab11", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb5tysbjmxm", "functionMc": "customGet_fb5tysbjmxm", "functionType": "diy", "functionBody": "function customGet_fb5tysbjmxm() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].zsxmDm === '10109'){\n\t\t\tif(VOs[i].ssjmxzDm === '0007011804' || VOs[i].ssjmxzDm === '0007011803') {\n\t\t\t A = 1;\n\t\t\t}\n            }\n\t\t\t if (VOs[i].zsxmDm === '30203'){\n\t\t\tif(VOs[i].ssjmxzDm === '0061011804' || VOs[i].ssjmxzDm === '0061011803') {\n\t\t\t A = 1;\n\t\t\t}\n            }\n\t\t\t if (VOs[i].zsxmDm === '30216'){\n\t\t\tif( VOs[i].ssjmxzDm === '0099011802' || VOs[i].ssjmxzDm === '0099011801') {\n\t\t\t A = 1;\n\t\t\t}\n            }\n        }\n    } catch (e) { }\n    return A;\n}", "description": "附表5退役士兵减免项目", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\"]"}, {"functionXh": "a50de23edb244b9398c72c6aac75ef44", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_jmbjycyjmxm", "functionMc": "customGet_jmbjycyjmxm", "functionType": "diy", "functionBody": "function customGet_jmbjycyjmxm() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].hmc.substr(0, 10) === '0001013609' || VOs[i].hmc.substr(0, 10) === '0001013612' || VOs[i].hmc.substr(0, 10) === '0001013607' || VOs[i].hmc.substr(0, 10) === '0001013613' || VOs[i].hmc.substr(0, 10) === '0001013610' || VOs[i].hmc.substr(0, 10) === '0001013611'){\n                A = 1;\n            }\n        }\n    } catch (e) { }\n    return A;\n}", "description": "就业创业证减免事项选择标志", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "a5a4b747d8da403f93225358364614a7", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_jmbzzsxejmje", "functionMc": "customGet_jmbzzsxejmje", "functionType": "diy", "functionBody": "function customGet_jmbzzsxejmje() {\n    var A = 0;\n    try {\n        var VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n        for (var i = 0; i < VOs.length; i++) {\n            if ( VOs[i].hmc.substr(0, 10)  === '0001011814' || VOs[i].hmc.substr(0, 10)  === '0001013612'  || VOs[i].hmc.substr(0, 10)  === '0001013613' || VOs[i].hmc.substr(0, 10)  === '0001013610' || VOs[i].hmc.substr(0, 10)  === '0001013611' || VOs[i].hmc.substr(0, 10)  === '0001011813'){\n                A = A + Number(VOs[i].bqsjdjse);\n            }\n        }\n    } catch (e) { }\n    return Number((A).toFixed(2));\n}", "description": "获取减免表增值税限额减免金额", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]\n"}, {"functionXh": "a6954d6da7774995a7a9e8e390a3a3b6", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_hzfpbfzjgFpse", "functionMc": "customGet_hzfpbfzjgFpse", "functionType": "diy", "functionBody": "function customGet_hzfpbfzjgFpse() {\n\tvar fzjgfpse=this.round(this.round(this.sumList(this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb,'fzjgybhwjlwfpse'), 2) + this.round(this.sumList(this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb,'fzjgybhwjlwjzjtfpse'), 2) + this.round(this.sumList(this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb,'fzjgysfwfpse'), 2) + this.round(this.sumList(this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb,'fzjgysfwjzjtfpse'), 2),2);\n\treturn fzjgfpse;\n}", "description": "计算分配表分支机构分配税额", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwjzjtfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgysfwjzjtfpse\"]"}, {"functionXh": "aee6bf1a96c0439c8abce881ef4d1204", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_Y1", "functionMc": "customGet_Y1", "functionType": "diy", "functionBody": "function customGet_Y1() {\n    if (!this.ywbw.sbZzsYbnsrFb1Bqxsqkmx) {\n        return 0;\n    }\n    var a = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '14').hjXxynse;\n    return a;\n}", "description": "Y1: 简易征收即征即退项目中,货劳在总销售收入的比例(货劳+应税服务),公式为：附表1【第10列14栏】", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','14').hjXxynse\"]"}, {"functionXh": "affb00bb477142dabd29cad2600d6e9b", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4editable", "functionMc": "customGet_fb4editable", "functionType": "diy", "functionBody": "function  customGet_fb4editable() {\n var result = 0;\n var fzxx = this.fzxx;\n if (fzxx.kzxx.sfsscsbqsqbz === 'N' && fzxx.bqxx.yqwsqdjybnsrzgqy !== 'Y' && (fzxx.bqxx.jcdljjdjqy === 'Y' || fzxx.bqxx.zzsjjdjqy === 'Y' || fzxx.bqxx.gymjjjdjqy === 'Y' || fzxx.bqxx.xjzzyjjdjqy === 'Y')) {\n  if (fzxx.bqxx.zzsjzjtqy === 'Y') {\n   result = 2;\n  }else {\n   result = 1;\n  }\n }else if (fzxx.kzxx.sfsscsbqsqbz === 'Y' && fzxx.kzxx.yjjdjzc !== 'N'){\n  if (fzxx.bqxx.zzsjzjtqy === 'Y') {\n   result = 2;\n  }else {\n   result = 1;\n  }\n }\n return result;\n}", "description": "附表4第6、7行的本期发生额、本期调减额的可编辑性，返回1可编辑，0不可编辑", "dependencies": "[]"}, {"functionXh": "b5abb56b1d034e3b9ba04845aea16a1f", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgybhwjlwfpse_Less", "functionMc": "customGet_fzjgybhwjlwfpse_Less", "functionType": "diy", "functionBody": "function customGet_fzjgybhwjlwfpse_Less(arr){\n    var index = arr[0];\n    var se = 0;\n    var abc = this.customGet_A() * this.customGet_a1() + this.customGet_B1() - this.customGet_C() * this.customGet_c1();\n    var fzjgybhwjlwfpbl = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[index].fzjgybhwjlwfpbl;\n    if(this.customGet_fzjgsbjnfs_family() === 3){\n        se = abc * (1 - this.ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl) * fzjgybhwjlwfpbl;\n    }else{\n        se = abc * fzjgybhwjlwfpbl;\n    }\n    return se;\n}", "description": "分支机构一般货物及劳务的分配税额应等于[]", "dependencies": "[\"ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb[*].fzjgybhwjlwfpbl\",\"ywbw.sbZzsYbnsrFbHznsfp.zjggdfpbl\"]\n"}, {"functionXh": "b5f7a19e664b4541bc8671be1f6fba20", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsXejmje", "functionMc": "customGet_fjsXejmje", "functionType": "diy", "functionBody": "function customGet_fjsXejmje(arr) {\n\tvar index = arr[0];\n\tvar VOs = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n\tvar result = 0;\n\tfor (var i = 0; i < VOs.length; i++){\n\t\tif (VOs[i].hmc.substr(0, 10) === '0001011814' || VOs[i].hmc.substr(0, 10) === '0001013612' || VOs[i].hmc.substr(0, 10) === '0001013613' || VOs[i].hmc.substr(0, 10) === '0001013610' || VOs[i].hmc.substr(0, 10) === '0001013611' || VOs[i].hmc.substr(0, 10) === '0001011813') {\n\t\t\tresult = this.round(result + VOs[i].bqsjdjse,2);\n\t\t}\n\t}\n\tif (this.ywbw.sbFjsf.sbFjsfMx[index].zsxmDm !== '10109' || this.fzxx.bqxx.dtcjsSlqy === 'N') {\n\t\treturn result;\n\t}else {\n\t\treturn 0;\n\t}\n}", "description": "获取附加税费增值税限额减免金额", "dependencies": "[\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].bqsjdjse\",\"getObjFromList(ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm,'ewbhxh','1').bqsjdjse\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]\n"}, {"functionXh": "b8b8b99f0cee43b999933f2983d75b45", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_tysbts", "functionMc": "customGet_tysbts", "functionType": "diy", "functionBody": "function customGet_tysbts() {\nvar Date2 = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\nvar Date1 = new Date(this.sbsx.skssqz) <= new Date('2023-12-31');\nvar sytysbxejmqy = this.fzxx.bqxx.sytysbxejmqy;\nvar gtgsh = this.fzxx.bqxx.gtgsh;\nvar result = '';\nvar jmb = 0;\nvar fb5 = 0;\nvar jmb2 = 0;\nvar fb52 = 0;\nvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\nvar VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\nfor (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011803' || VOs[i].ssjmxzDm === '0061011803' || VOs[i].ssjmxzDm === '0099011801'){\n              fb5 = 1;\n      break;\n          }\n      }\n for (var i = 0; i < VOs1.length; i++) {\n          if (VOs1[i].hmc.substr(0, 10) === '0001011813'){\n              jmb = 1;\n      break;\n          }\n      }\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007011804' || VOs[i].ssjmxzDm === '0061011804' || VOs[i].ssjmxzDm === '0099011802'){\n                fb52 = 1;\n    break;\n            }\n        }\n for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001011814'){\n                jmb2 = 1;\n    break;\n            }\n        }\nif (Date1 && sytysbxejmqy === 'N' && (this.customGet_jmbtysbjmxm() === 1 || this.customGet_fb5tysbjmxm() === 1)) {\n  result = '请采集《自主就业退役士兵本年度在企业工作时间表》，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\">点此采集。</a>';\n}else if (Date2 && gtgsh === 'Y' && sytysbxejmqy ==='N' && (jmb === 1 || fb5  === 1)) {\n  result = '没有采集创业自主就业退役士兵信息表信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\">点此采集。</a>';\n}else if (Date2 && gtgsh === 'N' && sytysbxejmqy === 'N' && (jmb2 === 1 || fb52  === 1)) {\n  result = '没有采集企业退役士兵信息，不可选择相关减免，<a href=\"/xxbg/view/zhxxbg/#/zdqthzzjytysbxxcj/index\">点此采集。</a>';\n}\nreturn result;\n}", "description": "退役士兵阻断提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "bc2b0751c6994d3896fad282873ad8fb", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsYbtse", "functionMc": "customGet_fjsYbtse", "functionType": "diy", "functionBody": "function customGet_fjsYbtse(zsxmDm) {\n\tvar VOs = this.ywbw.sbFjsf.sbFjsfMx;\n\tvar cjsybtsfe = 0;\n    var obj = this.getObjFromList(this.ywbw.sbFjsf.sbFjsfMx, 'zsxmDm', zsxmDm);\n    if (!obj) {\n        return 0;\n    }\n\tif (zsxmDm === '10109'){\n\t\tfor (var i = 0; i < VOs.length; i++){\n\t\tif (VOs[i].zsxmDm === '10109') {\n\t\t\tcjsybtsfe = this.round(cjsybtsfe + VOs[i].bqybtse,2);\n\t\t}\n\t}\n\treturn cjsybtsfe;\n\t}else {\n\t\treturn obj.bqybtse;\n\t}  \n}", "description": "一般纳税人主表获取附加税对应征收项目的应补退", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].bqybtse\"]"}, {"functionXh": "be886a22f4b341eb839a7b91c3a1e323", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_29", "functionMc": "customGet_zzsfjsbqjxsemx_29", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_29() {\n    return this.customGet_zzsfjsbqjxsemx(29);\n}", "description": "第【29】行仅辅导期一般纳税人或出口退税企业可填写", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','29').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','29').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','29').se\"]\n"}, {"functionXh": "bf48ddd917724b63b9d389ff7ea2e683", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_isSfzhm", "functionMc": "customGet_isSfzhm", "functionType": "diy", "functionBody": "function customGet_isSfzhm(val) {\n    if (!val) {\n        return true;\n    }\n    var changeFivteenToEighteen = function (card) {\n        if (card.length === 15) {\n            var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8,\n                4, 2);\n            var arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3',\n                '2');\n            var cardTemp = 0,\n                i;\n            card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6);\n            for (i = 0; i < 17; i++) {\n                cardTemp += card.substr(i, 1) * arrInt[i];\n            }\n            card += arrCh[cardTemp % 11];\n            return card;\n        }\n        return card;\n    };\n    var checkParity = function (card) {\n        card = changeFivteenToEighteen(card);\n\n        var len = card.length;\n        if (len === 18) {\n            var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4,\n                2);\n            var arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3',\n                '2');\n            var cardTemp = 0,\n                i, valnum;\n            for (i = 0; i < 17; i++) {\n                cardTemp += card.substr(i, 1) * arrInt[i];\n            }\n            valnum = arrCh[cardTemp % 11];\n            if (valnum === card.substr(17, 1)) {\n                return true;\n            }\n            return false;\n        }\n        return false;\n    };\n    var isCardNo = function (card) {\n        var reg = /(^\\d{15}$)|(^\\d{17}(\\d|X)$)/;\n        if (reg.test(card) === false) {\n            return false;\n        }\n        return true;\n    };\n    if (!checkParity(val)) {\n        return false;\n    }\n    if (!isCardNo(val)) {\n        return false;\n    }\n    return true;\n}", "description": "判断是否为身份证件号码", "dependencies": "[]"}, {"functionXh": "c06d64b1510541e38d1c922b58e31efb", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4Row6bqzce", "functionMc": "customGet_fb4Row6bqzce", "functionType": "diy", "functionBody": "function customGet_fb4Row6bqzce() {\n  var se = 0;\n  var fb2Row13 = Number(this.getObjFromList(this.ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','13').se || 0);\n  var fb2Row21 = Number(this.getObjFromList(this.ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','21').se || 0);\n  var fb2Row22 = Number(this.getObjFromList(this.ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','22').se || 0);\n  var custom_z = Number(this.round(fb2Row13 - fb2Row21 - fb2Row22 ,2));\n  if(this.fzxx.kzxx.sfsscsbqsqbz === 'Y' && this.fzxx.kzxx.yjjdjzc === 'N'){\n\tse = 0;\n  }else if(this.fzxx.bqxx.zzsjzjtqy === 'N' && fb2Row13 > 0 && custom_z > 0  && this.customGet_fb4Row6bqzceEable_nozzsjjdj()) {\n    se = this.min(custom_z * this.customGet_syJjdjl(), Number(this.round(Number(this.fzxx.tzxx.jjdjLjyjts || 0) + this.getObjFromList(this.ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqfse, 2)))\n  }\n  return this.round(se, 2);\n}", "description": "获取附表四第6行第3列“本期调减额”", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','13').se\", \"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','21').se\", \"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','22').se\", \"ywbw.sbZzsXzjjdjzcjg.xzjg\", \"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','6').bqfse\"]\n"}, {"functionXh": "c2cc9f9f986c4ec3b6db812c8305f7f4", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4Row6bqzceEable_nozzsjjdj", "functionMc": "customGet_fb4Row6bqzceEable_nozzsjjdj", "functionType": "diy", "functionBody": "function customGet_fb4Row6bqzceEable_nozzsjjdj() {\n  return (\n    this.fzxx.bqxx.yqwsqdjybnsrzgqy !== 'Y' &&\n    ((this.fzxx.bqxx.jcdljjdjqy === 'Y' && this.fzxx.kzxx.cwgzBsZjyc === 'N') ||\n      this.fzxx.bqxx.zzsjjdjqy === 'Y' ||\n      (this.fzxx.bqxx.gymjjjdjqy === 'Y' && this.fzxx.kzxx.gymjGzBqfseSfkbj === 'Y') ||\n      (this.fzxx.bqxx.xjzzyjjdjqy === 'Y' && this.fzxx.kzxx.xjzzyGzBqfseSfkbj === 'Y')) &&\n    this.fzxx.bqxx.zzsjjdjqy === 'N'\n  );\n}", "description": " 附表四第6行第3列“本期调减额”是否可编辑，且纳税人不享受生产、生活性服务业加计抵减（即未命中“增值税加计抵减企业”）", "dependencies": "[]"}, {"functionXh": "c4f04e5c46774b0d9f7bd3bbc06d8450", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLdtse", "functionMc": "customGet_fjsLdtse", "functionType": "diy", "functionBody": "function customGet_fjsLdtse(arr) {\n var index = arr[0];\n var minybzzs = 0;\n var moreDate = new Date(this.sbsx.skssqz) > new Date('2022-05-31');\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n var A = this.round(this.ywbw.sbFjsf.sbFjsfQtxx.dqxzldtse + this.ywbw.sbFjsf.sbFjsfQtxx.sqjcldtse,2);\n var B = this.round(this.ywbw.sbFjsf.sbFjsfMx[index].zzsxejmje + this.ywbw.sbFjsf.sbFjsfMx[index].zzsmdse - this.getObjFromList(this.ywbw.sbZzsYbnsr,'ewblxh','1').jybfYnse - this.getObjFromList(this.ywbw.sbZzsYbnsr,'ewblxh','3').jybfYnse , 2 );\n for (var i = 0; i < VOs.length; i++){\n  if (VOs[i].zsxmDm === '10109') {\n   minybzzs = this.round(minybzzs + VOs[i].ybzzs,2);\n  }\n }\n for (var i = 0; i < VOs.length; i++){\n  if (VOs[i].zsxmDm !== '10109') {\n   minybzzs = this.min(minybzzs,VOs[i].ybzzs);\n  }\n }\n B = this.round(B + minybzzs,2);\n if ( A <=0 && (this.fzxx.bqxx.dtcjsSlqy === 'N' || this.ywbw.sbFjsf.sbFjsfMx[index].zsxmDm !== '10109')) {\n if (moreDate) {\n\t return A;\n }else {\n\t return 0;\n }\n }else if(this.fzxx.bqxx.dtcjsSlqy === 'N' || this.ywbw.sbFjsf.sbFjsfMx[index].zsxmDm !== '10109'){\n  return this.max(this.min(A,B),0);\n }else {\n  return 0;\n }\n}", "description": "附表五第四列留抵退税本期扣除额赋值函数", "dependencies": "[]"}, {"functionXh": "c69a1997bbae46e28b4461dcab9f7e5b", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_19", "functionMc": "customGet_fb1lcsftx_19", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_19() {\n  return this.customGet_fb1lcsftx('19') === 0;\n}", "description": "根据二维表行序号判断附表1第19行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','19').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "cd1f7088b8864573b6766f2417d2daf3", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zdrqtstx", "functionMc": "customGet_zdrqtstx", "functionType": "diy", "functionBody": "function customGet_zdrqtstx() {\n    var inDate = new Date(this.sbsx.skssqq) >= new Date('2024-01-01');\n var sydjsyxejmqy = this.fzxx.bqxx.sydjsyxejmqy;\n var sypkrkxejmqy = this.fzxx.bqxx.sypkrkxejmqy;\n var gtgsh = this.fzxx.bqxx.gtgsh;\n var result = '';\n var jmb = 0;\n var fb5 = 0;\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n var VOs1 = this.ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm;\n for (var i = 0; i < VOs.length; i++) {\n  if (VOs[i].ssjmxzDm === '0007013612' || VOs[i].ssjmxzDm === '0007013613' || VOs[i].ssjmxzDm === '0061013610' || VOs[i].ssjmxzDm === '0061013611' || VOs[i].ssjmxzDm === '0099013603' || VOs[i].ssjmxzDm === '0099013604'){\n                fb5 = 1;\n    break;\n            }\n        }\n  for (var i = 0; i < VOs1.length; i++) {\n            if (VOs1[i].hmc.substr(0, 10) === '0001013612' || VOs1[i].hmc.substr(0, 10) === '0001013613'){\n                jmb = 1;\n    break;\n            }\n        }\n if (inDate && gtgsh === 'N' && (sydjsyxejmqy==='Y' || sypkrkxejmqy === 'Y') && (jmb === 1 || fb5 === 1)) {\n  result = 'tsxt';\n }\n return result;\n}", "description": "重点人群提示性提示", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbZzsFbZzsjmssbmxb.sbZzsFbZzsjmssbmxbJsxm[*].hmc\"]"}, {"functionXh": "d1e4238247ed47f8b64babdf0fd342cf", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_26", "functionMc": "customGet_zzsfjsbqjxsemx_26", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_26() {\n    return this.customGet_zzsfjsbqjxsemx(26);\n}", "description": "第【26】行仅辅导期一般纳税人或出口退税企业可填写", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','26').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','26').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','26').se\"]"}, {"functionXh": "d1e9e62d69f24a6eb8d70a5fc7a50b40", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLslfSwsxDm", "functionMc": "customGet_fjsLslfSwsxDm", "functionType": "diy", "functionBody": "function customGet_fjsLslfSwsxDm(arr) {\n    var index = arr[0];\n    var fjsxxForm = this.ywbw.sbFjsf.sbFjsfQtxx;\n    var obj = this.ywbw.sbFjsf.sbFjsfMx[index];\n    var A = fjsxxForm.bqsfsyxgmyhzc;\n    var B = obj.bqynsfe - obj.jme;\n    var syzt = fjsxxForm.jzzcsyztDm;\n    if (A === 'Y' && B !== 0) {\n        if (obj.zsxmDm === '10109') {\n            if (syzt === '22') {\n                //GTHPHJMXZDMCJS|GTHPHJMSWSXDMCJS|GTHPHJMSWSXMCCJS\n                return 'SXA031901266' || '';\n            } else if (syzt === '21') {\n                //XWQYPHJMXZDMCJS|XWQYPHJMSWSXDMCJS|XWQYPHJMSWSXMCCJS\n                return 'SXA031901267' || '';\n            }\n        } else if (obj.zsxmDm === '30203') {\n            if (syzt === '22') {\n                //GTHPHJMXZDMJYFJ|GTHPHJMSWSXDMJYFJ|GTHPHJMSWSXMCJYFJ\n                return 'SXA031901272' || '';\n            } else if (syzt === '21') {\n                //XWQYPHJMXZDMJYFJ|XWQYPHJMSWSXDMJYFJ|XWQYPHJMSWSXMCJYFJ\n                return 'SXA031901273' || '';\n            }\n        } else if (obj.zsxmDm === '30216') {\n            if (syzt === '22') {\n                //GTHPHJMXZDMDFFJ|GTHPHJMSWSXDMDFFJ|GTHPHJMSWSXMCDFFJ\n                return 'SXA031901274' || '';\n            } else if (syzt === '21') {\n                //XWQYPHJMXZDMDFFJ|XWQYPHJMSWSXDMDFFJ|XWQYPHJMSWSXMCDFFJ\n                return 'SXA031901275' || '';\n            }\n        }\n    }\n    return '';\n}", "description": "六税两费税务事项代码", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.bqsfsyxgmyhzc\",\"ywbw.sbFjsf.sbFjsfQtxx.jzzcsyztDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\",\"ywbw.sbFjsf.sbFjsfMx[*].jme\"]"}, {"functionXh": "d84c6316b6db4af0b13b01a1c7f8276d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_MdseCheck", "functionMc": "customGet_MdseCheck", "functionType": "diy", "functionBody": "function customGet_MdseCheck(arr) {\n var index = arr[0];\n var cjsmdse = 0;\n var ycjsmdse = 0;\n var yjyfjmdse = 0;\n var ydfjyfjmdse = 0;\n var result = 1;\n var ytxx = this.ytxx;\n var VOs = this.ywbw.sbFjsf.sbFjsfMx;\n for (var i = 0; i < VOs.length; i++){\n  if (VOs[i].zsxmDm === '10109') {\n   cjsmdse = this.round(cjsmdse + VOs[i].zzsmdse,2);\n  }\n  }\n for (var i = 0; i < ytxx.sbFjsf.sbFjsfMx.length; i++){\n  if (VOs[i].zsxmDm === '10109') {\n   ycjsmdse = ytxx.sbFjsf.sbFjsfMx[i].zzsmdse;\n  }\n  if (VOs[i].zsxmDm === '30203') {\n   yjyfjmdse = ytxx.sbFjsf.sbFjsfMx[i].zzsmdse;\n  }\n  if (VOs[i].zsxmDm === '30216') {\n   ydfjyfjmdse =  ytxx.sbFjsf.sbFjsfMx[i].zzsmdse;\n  }\n  }\n if (VOs[index].zsxmDm === '10109' && cjsmdse !== ycjsmdse) {\n   result = 0;\n  }\n if (VOs[index].zsxmDm === '30203' && VOs[index].zzsmdse !== yjyfjmdse) {\n   result = 0;\n  }\n if (VOs[index].zsxmDm === '30216' && VOs[index].zzsmdse !== ydfjyfjmdse) {\n   result = 0;\n  }\n return result;\n}", "description": "附表五“增值税免抵税额”列校验,返回1表示通过，0表示不通过", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].zzsmdse\"]"}, {"functionXh": "d885b4bdfda6432695bce2d6ece05199", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_25", "functionMc": "customGet_zzsfjsbqjxsemx_25", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_25() {\n    return this.customGet_zzsfjsbqjxsemx(25);\n}", "description": "第【25】行仅辅导期一般纳税人或出口退税企业可填写", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','25').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','25').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','25').se\"]"}, {"functionXh": "da90b59b0d08483984afe0db6b8a4c1f", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_B1", "functionMc": "customGet_B1", "functionType": "diy", "functionBody": "function customGet_B1() {\n    if (!this.ywbw.sbZzsYbnsrFb1Bqxsqkmx) {\n        return 0;\n    }\n    var result = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '8').hjXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '9').hjXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '10').hjXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '11').hjXxynse\n        - this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '14').hjXxynse;\n    return Number(result.toFixed(2));\n}", "description": "B1: 简易征收一般项目中货劳的销售税额,公式为：附表1【10列（8+9a+10+11-14）栏】。", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','8').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','9').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','10').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','11').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','14').hjXxynse\"]"}, {"functionXh": "e1338465992b4dff959f7c822d519a77", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_x1", "functionMc": "customGet_x1", "functionType": "diy", "functionBody": "function customGet_x1() {\n    if (!this.ywbw.sbZzsYbnsrFb1Bqxsqkmx) {\n        return 0;\n    }\n    var a = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '6').hjXxynse;\n    var b = this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '6').hjXxynse\n        + this.getObjFromList(this.ywbw.sbZzsYbnsrFb1Bqxsqkmx, 'ewbhxh', '7').kchXxynse;\n    return b === 0 ? 0 : Number((a / b).toFixed(10));\n}", "description": "x1: 一般计税即征即退项目中,货劳在总销售收入的比例(货劳+应税服务),公式为：附表1【第10列6栏）】/【第10列6栏+第14列7栏】", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','6').hjXxynse\", \"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','7').kchXxynse\"]"}, {"functionXh": "e295b12fdf0c4af7bb7a400067bb5227", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_A", "functionMc": "customGet_A", "functionType": "diy", "functionBody": "function customGet_A() {\n    return this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '1').ynse;\n}", "description": "A：主表第19栏【一般项目】本月数，即主表第19栏第1列。", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').ynse\"]"}, {"functionXh": "e4b86e5f697a42c3bb9b6657a06a486c", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "getObjFromListByKeys", "functionMc": "getObjFromListByKeys", "functionType": "diy", "functionBody": "function getObjFromListByKeys(list, obj) {\n  var origin;\n  var result = list;\n  for (var key in obj) {\n    origin = result;\n    result = [];\n    for (var i = 0; i < origin.length; i++) {\n      if (list[i][key] === obj[key]) {\n        result.push(list[i]);\n      }\n    }\n  }\n  return result[0];\n}", "description": "根据多个key过滤列表", "dependencies": "[]"}, {"functionXh": "e78766cb8d9b48929fbc0e6e0d57cb7d", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_17", "functionMc": "customGet_fb1lcsftx_17", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_17() {\n  return this.customGet_fb1lcsftx('17') === 0;\n}", "description": "根据二维表行序号判断附表1第17行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','17').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "e88dbf13a11e47b18c337186f5d1e211", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb1lcsftx_16", "functionMc": "customGet_fb1lcsftx_16", "functionType": "diy", "functionBody": "function customGet_fb1lcsftx_16() {\n  return this.customGet_fb1lcsftx('16') === 0;\n}", "description": "根据二维表行序号判断附表1第16行是否填写\n", "dependencies": "function getDep() {\n  var ylxList = [];\n  var Keys = ['kjskzzszyfpXse', 'kjskzzszyfpXxynse', 'kjqtfpXse', 'kjqtfpXxynse','wkjfpXse','wkjfpXxynse','xse','hjXxynse','jshj','ysfwkcxmbqsjkcje','kchHsmsxse','kchXxynse','nsjctzdxse','nsjctzXxynse'];\n  for (const key of Keys) {\n    ylxList.push(\"getObjFromList(ywbw.sbZzsYbnsrFb1Bqxsqkmx,'ewbhxh','16').\"+ key);\n  }\n  return ylxList;\n}"}, {"functionXh": "e8c50e26fd344d57a9d6acb33fe56847", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsJmse", "functionMc": "customGet_fjsJmse", "functionType": "diy", "functionBody": "function customGet_fjsJmse(indexArr) {\n    var index = indexArr[0];\n    var ywbw = this.ywbw;\n    var zsxmDm = ywbw.sbFjsf.sbFjsfMx[index].zsxmDm;\n    var ynse = ywbw.sbFjsf.sbFjsfMx[index].bqynsfe;\n    var xse = this.customGet_zbsse();\n    var ssjmxzDm = ywbw.sbFjsf.sbFjsfMx[index].ssjmxzDm || '';\n    var swsxDm = ywbw.sbFjsf.sbFjsfMx[index].swsxDm || '';\n    var jme = ywbw.sbFjsf.sbFjsfMx[index].jme || 0;\n    if (this.inArray(['30203', '30216'], zsxmDm)\n        && ((ssjmxzDm === '0061042802' && swsxDm === 'SXA031900783') || (ssjmxzDm === '0099042802' && swsxDm === 'SXA031901054'))\n        && ynse > 0\n        && xse <= (Number(this.sbsx.nsqxDm) - 5) * 100000) {\n        return Number(ynse);\n    }\n    if (!ssjmxzDm) {\n        return 0;\n    } else {\n        return jme;\n    }\n}", "description": "设置附加税减免税额", "dependencies": "[\"ywbw.sbFjsf.sbFjsfMx[*].ssjmxzDm\",\"ywbw.sbFjsf.sbFjsfMx[*].swsxDm\",\"ywbw.sbFjsf.sbFjsfMx[*].bqynsfe\"]"}, {"functionXh": "eacd4dff95b342d7a7c598e854aa3521", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_X", "functionMc": "customGet_X", "functionType": "diy", "functionBody": "function customGet_X() {\n    return this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '3').ynse;\n}", "description": "X：主表第19栏【即征即退】本月数，即主表第19栏第3列。", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').ynse\"]"}, {"functionXh": "ead4d58684eb4d0db0991aa1ddbebbb7", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_ybnsrFjsJsyj", "functionMc": "customGet_ybnsrFjsJsyj", "functionType": "diy", "functionBody": "function customGet_ybnsrFjsJsyj() {\n    var result = 0;\n    if (this.fzxx.bqxx.zzsftjnzjg ==='N') {\n        result = this.round(this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '1').bqybtse + this.getObjFromList(this.ywbw.sbZzsYbnsr, 'ewblxh', '3').bqybtse, 2)\n    } else {\n        result = this.round(this.doCatch('ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwfpse ') + this.doCatch('ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtfpse ') + this.doCatch('ywbw.sbZzsYbnsrFbHznsfp.zjgysfwfpse ') + this.doCatch('ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtfpse '), 2)\n    }\n    return result;\n}", "description": "1，非汇总纳税企业，取当期主税的应补退税额，即取当期一般纳税人中的34栏应补退税额的第1+3列合计数，允许为负数；\n2，汇总纳税企业（存在增值税分配表的情况下），自动取：增值税“汇总纳税企业增值税分配表”的“总机构一般货物及劳务分配税额+总机构一般货物及劳务即征即退分配税额+总机构应税服务分配税额+总机构应税服务即征即退分配税额”的之和", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.jsyjxgxz\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').bqybtse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').bqybtse\",\"ywbw.sbZzsYbnsr\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgybhwjlwjzjtfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwfpse\",\"ywbw.sbZzsYbnsrFbHznsfp.zjgysfwjzjtfpse\"]\n"}, {"functionXh": "efbf391d85a54b18863c46e61ef542e0", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4jjdjejksc", "functionMc": "customGet_fb4jjdjejksc", "functionType": "diy", "functionBody": "function customGet_fb4jjdjejksc() {\n\tvar fzxx = this.fzxx;\n\tvar result = '';\n\tvar ywbw = this.ywbw;\n    var fsc = fzxx.kzxx.sfsscsbqsqbz === 'Y';\n\tvar sfkbj = this.customGet_fb4editable();\n\tif (sfkbj !== 0 && fsc && this.round((fzxx.kzxx.yzcyjtelj + this.getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','8').bqfse),2) > this.round((fzxx.kzxx.yzckjtelj + this.getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','12').se * this.Number (fzxx.kzxx.yzcjjdjl)+1),2)) {\n\t\tif (fzxx.kzxx.yjjdjzc === '0'){\n\t\t\tresult = '集成电路';\n\t\t}\n\t\tif (fzxx.kzxx.yjjdjzc === '2'){\n\t\t\tresult = '工业母机';\n\t\t}\n\t\tif (fzxx.kzxx.yjjdjzc === '3'){\n\t\t\tresult = '先进制造业';\n\t\t}\n\t}\n\treturn result;\n\t}", "description": "累计可填报加计抵减额监控规则\n更正申报首次申报之前属期（SFSSCSBQSQBZ为Y）", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','8').bqfse\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','12').se\",\"customGet_fb4editable\"]"}, {"functionXh": "f110fb2266b44ad0a88b29958993c4a5", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fb4jjdjejkfsc", "functionMc": "customGet_fb4jjdjejkfsc", "functionType": "diy", "functionBody": "function customGet_fb4jjdjejkfsc() {\n var fzxx = this.fzxx;\n var result = '';\n var ywbw = this.ywbw;\n    var issc = fzxx.kzxx.sfsscsbqsqbz === 'Y';\n var sfkbj = this.customGet_fb4editable();\n var jjdjl = 0;\n var yjtelj = 0;\n var kjtesxbyslj = 0;\n if (ywbw.sbZzsXzjjdjzcjg.xzjg === 3 ||(fzxx.bqxx.zzsjjdjqy === 'N' && fzxx.bqxx.jcdljjdjqy === 'N' && fzxx.bqxx.gymjjjdjqy === 'N' && fzxx.bqxx.xjzzyjjdjqy === 'Y')) {\n  jjdjl = fzxx.csxx.xjzzySyjjdjl;\n  result = fzxx.kzxx.nrXjzzyQymdGlsj;\n  yjtelj = this.getObjFromList(fzxx.tzxx.ybnsrTzxxJjdjLjfseList,'jjdjzcXzjg','3').yjtelj;\n  kjtesxbyslj = this.getObjFromList(fzxx.tzxx.ybnsrTzxxJjdjLjfseList,'jjdjzcXzjg','3').kjtesxbyslj;\n }else if (ywbw.sbZzsXzjjdjzcjg.xzjg === 2 ||(fzxx.bqxx.zzsjjdjqy === 'N' && fzxx.bqxx.jcdljjdjqy === 'N' && fzxx.bqxx.gymjjjdjqy === 'Y' && fzxx.bqxx.xjzzyjjdjqy === 'N')) {\n  jjdjl= fzxx.csxx.gymjSyjjdjl;\n  result = fzxx.kzxx.nrGymjQymdGlsj;\n  yjtelj = this.getObjFromList(fzxx.tzxx.ybnsrTzxxJjdjLjfseList,'jjdjzcXzjg','2').yjtelj;\n  kjtesxbyslj = this.getObjFromList(fzxx.tzxx.ybnsrTzxxJjdjLjfseList,'jjdjzcXzjg','2').kjtesxbyslj;\n }else if ((ywbw.sbZzsXzjjdjzcjg.xzjg === 0 ||(fzxx.bqxx.zzsjjdjqy === 'N' && fzxx.bqxx.jcdljjdjqy === 'Y' && fzxx.bqxx.gymjjjdjqy === 'N' && fzxx.bqxx.xjzzyjjdjqy === 'N')) && fzxx.kzxx.sfSctbJcdlJjdjBz === 'N'){\n  jjdjl= fzxx.csxx.jcdlSyjjdjl;\n  result = fzxx.kzxx.nrJcdlQymdGlsj;\n  yjtelj = this.getObjFromList(fzxx.tzxx.ybnsrTzxxJjdjLjfseList,'jjdjzcXzjg','0').yjtelj;\n  kjtesxbyslj = this.getObjFromList(fzxx.tzxx.ybnsrTzxxJjdjLjfseList,'jjdjzcXzjg','0').kjtesxbyslj;\n }\n if(this.round(yjtelj + this.getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','8').bqfse,2) <= this.round(kjtesxbyslj + this.getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','12').se * jjdjl + 1,2) || sfkbj === 0 || issc){\n  result = '';\n }\n return result;\n }", "description": "累计可填报加计抵减额监控规则\n正常申报或更正申报非首次申报之前属期（SFSSCSBQSQBZ为N）", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb4Sedjqk,'ewbhxh','8').bqfse\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','12').se\",\"customGet_fb4editable\"]"}, {"functionXh": "f4534ac241b641de8932e2a1def01bb5", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fjsLdtse1", "functionMc": "customGet_fjsLdtse1", "functionType": "diy", "functionBody": "function customGet_fjsLdtse1(arr) {\n  var A = this.customGet_fjsLdtse(arr);\n  return A;\n  }", "description": "由于附表五第四列留抵退税本期扣除额和其他行次的第一列的值也有关系，所以赋值公式写两种：\nrangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].zzsldtse === customGet_fjsLdtse2([*])\", \"*\")\nrangeExec ( 0 , ywbw.sbFjsf.sbFjsfMx.length ,\"ywbw.sbFjsf.sbFjsfMx[*].zzsldtse === customGet_fjsLdtse1([*])\")\ncustomGet_fjsLdtse1和customGet_fjsLdtse2逻辑一样，只是依赖项互斥，customGet_fjsLdtse1依赖项为第一列，调用增加了一个传参，所以会同时出发1列其他行次的关联规则，所以为了修改其他列次不影响其他行次，拆开写了customGet_fjsLdtse2，依赖项为除了第一列以外的其他节点，只有该行次变化才会触发相关的规则", "dependencies": "[\"ywbw.sbFjsf.sbFjsfQtxx.dqxzldtse\",\"ywbw.sbFjsf.sbFjsfQtxx.sqjcldtse\",\"ywbw.sbFjsf.sbFjsfMx[*].zzsxejmje\",\"ywbw.sbFjsf.sbFjsfMx[*].zzsmdse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','1').jybfYnse\",\"getObjFromList(ywbw.sbZzsYbnsr,'ewblxh','3').jybfYnse\"]"}, {"functionXh": "f6232c5a81a5409c936a467c4d546991", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_fzjgsbjnfs_family", "functionMc": "customGet_fzjgsbjnfs_family", "functionType": "diy", "functionBody": "function customGet_fzjgsbjnfs_family() {\n    var A = 0;\n\tvar B = 0;\n\tvar C = 0;\n\tvar D = 0;\n    try {\n        var VOs = this.ywbw.sbZzsYbnsrFbHznsfp.sbZzsYbnsrFbHznsfpZb;\n        for (var i = 0; i < VOs.length; i++) {\n            if (VOs[i].sbjnfsDm === '3'){\n                A = 1;\n            }\n\t\t\tif (VOs[i].sbjnfsDm === '0'){\n                B = 1;\n            }\n\t\t\tif (VOs[i].sbjnfsDm === '4'){\n                C = 1;\n            }\n        }\n    } catch (e) { }\n\tif (A === 1){\n                D = 1;\n            }\n\tif (A === 0 && B === 1){\n                D =2;\n            }\n\tif (A === 0 && B === 0 && C === 1){\n                D = 3;\n            }\n    return D;\n}", "description": "判断分支机构申报缴纳方式分类：\n分支机构中“申报缴纳方式”存在3时，返回1；\n分支机构中“申报缴纳方式”不存在3，且存在0时，返回2；\n分支机构中“申报缴纳方式”不存在3，且不存在0且存在4时，返回3；", "dependencies": "[]"}, {"functionXh": "fb3ef0137763476b9d3882f61845108f", "formXh": "2843c1e9a25d4c2382680910c11ff2d2", "functionDm": "customGet_zzsfjsbqjxsemx_30", "functionMc": "customGet_zzsfjsbqjxsemx_30", "functionType": "diy", "functionBody": "function customGet_zzsfjsbqjxsemx_30() {\n    return this.customGet_zzsfjsbqjxsemx(30);\n}", "description": "第【30】行仅辅导期一般纳税人或出口退税企业可填写\n", "dependencies": "[\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','30').fs\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','30').je\",\"getObjFromList(ywbw.sbZzsYbnsrFb2Bqjxsemx,'ewbhxh','30').se\"]\n\n"}]