<template>
    <el-dialog v-model="appConfirmVisible" width="557px" center draggable @close="handleClose">
        <template #header>
            <div id="btnAppConfirm">
                <div style="width:120px;border-bottom: 3px solid rgb(68, 180, 73);">
                    <img
                    id="imgAppConfirm"
                    src="@/assets/AppConfirm/appconfirm-selected.png"
                    style="width: 16px; height: 16px; vertical-align: middle; margin-right: 6px; color: #44b449"
                />
                <span id="txtAppConfirm" class="title-text activeConfirm">记账APP验证</span>
                </div>
                
            </div>
        </template>
        <div id="appConfirmDialog">
            <!-- <div id="dialogHeader" class="dialog-header2"> -->
            <!-- <div id="btnAppConfirm" @click="changeConfirmWay(1)" style="float: left; cursor: pointer; height: 48px">
                    <img
                        id="imgAppConfirm"
                        src="@/assets/AppConfirm/appconfirm-selected.png"
                        style="width: 16px; height: 16px; vertical-align: middle; margin-right: 6px"
                    />
                    <span id="txtAppConfirm" class="title-text activeConfirm">记账APP验证</span>
                </div> -->
            <!-- <div
                    id="btnWeworkConfirm"
                    @click="changeConfirmWay(2)"
                    style="float: left; margin-left: 40px; cursor: pointer; height: 48px"
                >
                    <img
                        id="imgWeworkConfirm"
                        src="@/assets/AppConfirm/weworkconfirm.png"
                        style="width: 16px; height: 16px; vertical-align: middle; margin-right: 6px"
                    />
                    <span id="txtWeworkConfirm" class="title-text">企业微信验证</span>
                </div> -->
            <!-- <span
                    class="title-text"
                    id="btnClose"
                    @click="handleClose"
                    style="margin-right: 55px; font-size: 28px; line-height: 44px; cursor: pointer; float: right"
                    >×</span
                > -->
            <!-- <span class="title-text" id="btnClose" onclick="closeDialog()" style="margin-left: 210px; font-size: 28px; line-height: 44px; cursor: pointer">×</span> -->
            <!-- </div> -->
            <!-- <div style="width: 557px; height: 1px; background-color: #edefee; margin-top: 50px; position: absolute"></div> -->
            <div id="unScannedBlock" v-show="unScannedBlock">
                <div class="scanned-title">
                    <img
                        style="width: 10px; margin-right: 5px"
                        src="@/assets/AppConfirm/warning.png"
                    />您正在执行敏感操作，为确保您的账号安全，请使用柠檬云财务App扫码验证身份
                </div>
                <div class="scanned-info">
                    <div>
                        第一步：下载 柠檬云财务APP<a
                            class="link"
                            style="margin-left: 10px"
                            @click="globalWindowOpen('https://www.ningmengyun.com/AppDownload.aspx')"
                            >没有柠檬云财务APP？点此下载</a
                        >
                    </div>
                    <div>第二步：登录 柠檬云财务APP 扫描二维码验证身份</div>
                </div>
                <div class="scanned-content">
                    <div class="qr-example">
                        <img src="@/assets/appConfirm/example.png" style="width: 182px" />
                    </div>
                    <div class="qr-content">
                        <div id="confirmMain" v-if="confirmQrCodeVisible">
                            <div class="confirm-block">
                                <img id="comfirmQrCode" :src="confirmQrCodeSrc" />
                                <img src="@/assets/AppConfirm/logo_qr.png" class="qr-logo" alt="" style="top: 44%" />
                            </div>
                        </div>
                        <div class="expired_tip" id="expired_tip" v-else>
                            二维码过期
                            <a @click="GetQrCode">请点击刷新</a>
                        </div>
                        <div style="margin-top: 5px">登录柠檬云财务APP</div>
                        <div>打开手机扫一扫</div>
                    </div>
                </div>
            </div>
            <div id="unScannedBlockWework" v-show="unScannedBlockWework">
                <div class="scanned-title">
                    <img
                        style="width: 10px; margin-right: 5px"
                        src="@/assets/AppConfirm/warning.png"
                    />您正在执行敏感操作，为确保您的账号安全，请使用企业微信柠檬云财务扫码验证
                </div>
                <div class="scanned-info">
                    <div>
                        第一步：下载 企业微信APP<a
                            class="link"
                            style="margin-left: 10px"
                            href="javascript:void(0);"
                            @click="GetWeworkInstallUrl"
                            >没有企业微信APP？点此下载</a
                        >
                    </div>
                    <div>第二步：在企业微信的工作台登录柠檬云财务</div>
                    <div>第三步：扫描二维码验证身份</div>
                </div>
                <div class="scanned-content">
                    <div class="qr-example" style="float: left; margin-left: 80px">
                        <img src="@/assets/appConfirm/wework-example.png" style="width: 183px; height: 167px" />
                    </div>
                    <div class="qr-content" style="float: left; margin-top: -5px">
                        <div id="confirmMainWework" v-if="comfirmQrCodeWeworkVisible">
                            <div class="confirm-block">
                                <img
                                    id="comfirmQrCodeWework"
                                    src="@/assets/AppConfirm/wework-qrcode.png"
                                    style="width: 111px; height: 111px; margin: 10px 0 0px 0"
                                />
                                <img
                                    src="@/assets/AppConfirm/weworklogo.png"
                                    class="qr-logo"
                                    alt=""
                                    style="top: 44%; width: 24px; height: 24px"
                                />
                            </div>
                        </div>
                        <div class="expired_tip" id="expired_tip_wework" v-else>
                            二维码过期
                            <a @click="GetWeworkQrCode">请点击刷新</a>
                        </div>
                        <div style="margin-top: 10px">登录柠檬云财务后</div>
                        <div>用企业微信扫一扫</div>
                    </div>
                </div>
            </div>
            <div id="scannedBlock" v-show="scannedBlock">
                <img id="scanSuccessful" src="@/assets/AppConfirm/scan-successful.png" />
                <div style="margin-bottom: 65px">请在手机上确认操作</div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import selectedAPPConfirm from "@/assets/appConfirm/appconfirm-selected.png";
import APPConfirm from "@/assets/appConfirm/appConfirm.png";
import selectedWeworkConfirm from "@/assets/appConfirm/weworkconfirm-selected.png";
import WeworkConfirm from "@/assets/appConfirm/weworkconfirm.png";
import { ElNotify } from "@/util/notify";
import { globalWindowOpen } from "@/util/url";
import { request } from "@/util/service";
import { ref, onBeforeUnmount } from "vue";
const props = withDefaults(
    defineProps<{
        operationType: Number;
        close: Function;
        confirm: Function;
        cancel: Function;
        visible?: boolean;
    }>(),
    {
        visible: true,
    }
);

const unScannedBlock = ref(true);
const unScannedBlockWework = ref(false);
const scannedBlock = ref(false);
const appConfirmVisible = ref(props.visible);
const confirmQrCodeVisible = ref(true);
const comfirmQrCodeWeworkVisible = ref(true);
const confirmQrCodeSrc = ref(
    window.accountSrvHost +
        "/api/WxPay/MakeQRCode.ashx?data=https%3A%2F%2Fa.app.qq.com%2Fo%2Fsimple.jsp%3Fpkgname%3Dcom.wta.NewCloudApp.jiuwei117478%26type%3D0%26guid%3D635a5eae-21b1-4f3e-b949-eae4f59d70ad"
);
var polling = false;
let guid = "";
let nextScanState = 1;
const handleClose = () => {
    appConfirmVisible.value = false;
    polling = false;
    clearTimeout(scanTimer);
};

const handleConfirm = () => {
    props.confirm();
    appConfirmVisible.value = false;
};

function GetWeworkInstallUrl() {
    request({
        url: `/api/WxWorkConfirm/GetInstallUrl`,
        method: "post",
    }).then((res: any) => {
        if (res.data.state == 0) {
            globalWindowOpen(res.data.result);
        }
    });
}
//缺少erp环境
function changeConfirmWay(val: number) {
    polling = false;
    if (val == 1) {
        unScannedBlock.value = true;
        unScannedBlockWework.value = false;
        (document.getElementById("imgAppConfirm") as HTMLImageElement).setAttribute("src", selectedAPPConfirm);
        (document.getElementById("txtAppConfirm") as HTMLImageElement).classList.add("activeConfirm");
        // (document.getElementById("imgWeworkConfirm") as HTMLImageElement).setAttribute("src", WeworkConfirm);
        // (document.getElementById("txtWeworkConfirm") as HTMLImageElement).classList.remove("activeConfirm");
        GetQrCode();
    } else {
        unScannedBlock.value = false;
        unScannedBlockWework.value = true;
        (document.getElementById("imgAppConfirm") as HTMLImageElement).setAttribute("src", APPConfirm);
        (document.getElementById("txtAppConfirm") as HTMLImageElement).classList.remove("activeConfirm");
        (document.getElementById("imgWeworkConfirm") as HTMLImageElement).setAttribute("src", selectedWeworkConfirm);
        (document.getElementById("txtWeworkConfirm") as HTMLImageElement).classList.add("activeConfirm");
        GetWeworkQrCode();
    }
}
(function IsWeworkBinding() {
    request({
        url: `/api/WxWorkConfirm/IsBinding`,
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000) {
                if (res.data.data) {
                    //已绑定
                    //记账app隐藏，只余企业微信，执行changeConfirmWay(2)
                    changeConfirmWay(2);
                } else {
                    //未绑定
                    //记账app,企业微信，执行changeConfirmWay(1)
                    changeConfirmWay(1);
                }
            } else {
                ElNotify({
                    type: "error",
                    message: "出现异常，请刷新页面重试或联系系统管理员",
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
})();

let downloadUrl = "https://a.app.qq.com/o/simple.jsp?pkgname=com.wta.NewCloudApp.jiuwei117478";
function GetQrCode() {
    request({
        url: `/api/AppConfirm/GenerateGuid?operationType=` + props.operationType,
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000) {
                confirmQrCodeVisible.value = true;
                unScannedBlock.value = true;
                scannedBlock.value = false;
                confirmQrCodeSrc.value =
                    window.accountSrvHost +
                    "/api/WxPay/MakeQRCode.ashx" +
                    "?data=" +
                    encodeURIComponent(downloadUrl + "&type=0&guid=" + res.data) +
                    "&CurrentSystemType=1";
                guid = res.data;
                nextScanState = 1;
                polling = true;
                scanPolling();
            } else {
                ElNotify({
                    type: "error",
                    message: "出现异常，请刷新页面重试或联系系统管理员",
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
}
let scanTimer: number;
function scanPolling() {
    request({
        url: `/api/AppConfirm/Scanned?guid=` + guid,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data.scanState === nextScanState) {
                scanStateChange();
            } else if (nextScanState === 2 && res.data.scanState === 0) {
                //用户扫描后点击取消，重置二维码状态
                nextScanState = 1;
                unScannedBlock.value = true;
                scannedBlock.value = false;
            }
            if (polling) {
                scanTimer = setTimeout(scanPolling, 1000);
            }
        } else if (res.state === 2000) {
            confirmQrCodeVisible.value = false;
        } else {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        }
    });
}

function scanStateChange() {
    if (nextScanState === 1) {
        unScannedBlock.value = false;
        unScannedBlockWework.value = false;
        scannedBlock.value = true;
    } else if (nextScanState === 2) {
        polling = false;
        clearTimeout(scanTimer);
        handleConfirm();
        // if ($.isFunction(outsideCallBack)) {
        //     outsideCallBack();
        // }
    }
    nextScanState++;
}

//企业微信
function GetWeworkQrCode() {
    request({
        url: `/api/AppConfirm/GenerateGuid?operationType=` + props.operationType,
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000) {
                comfirmQrCodeWeworkVisible.value = true;
                unScannedBlockWework.value = true;
                scannedBlock.value = false;
                guid = res.data;
                nextScanState = 1;
                QueryWeworkBinding();
            } else {
                ElNotify({
                    type: "error",
                    message: "出现异常，请刷新页面重试或联系系统管理员",
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
}

let wxBindTimer: number;
//企业微信绑定
function QueryWeworkBinding() {
    request({
        url: `/api/WxWorkConfirm/IsBinding`,
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000) {
                if (res.data.data) {
                    //已绑定
                    polling = true;
                    QueryWeworkConfirm();
                } else {
                    //未绑定
                    wxBindTimer = setTimeout(QueryWeworkBinding, 1000);
                }
            } else {
                ElNotify({
                    type: "error",
                    message: "出现异常，请刷新页面重试或联系系统管理员",
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
}

let wxConfirmTimer: number;
//企业微信扫描验证
function QueryWeworkConfirm() {
    request({
        url: `/api/WxWorkConfirm?guid=${guid}`,
        method: "post",
    })
        .then((res: any) => {
            if (res.state === 1000) {
                if (res.data.scanState === nextScanState) {
                    scanStateChange();
                } else if (nextScanState === 2 && res.data.scanState === 0) {
                    nextScanState = 1;
                    unScannedBlockWework.value = true;
                    scannedBlock.value = false;
                }
                if (polling) {
                    wxConfirmTimer = setTimeout(QueryWeworkConfirm, 1000);
                }
            } else if (res.state === 2000) {
                comfirmQrCodeWeworkVisible.value = false;
            } else {
                ElNotify({
                    type: "error",
                    message: "出现异常，请刷新页面重试或联系系统管理员",
                });
            }
        })
        .catch(() => {
            ElNotify({
                type: "error",
                message: "出现异常，请刷新页面重试或联系系统管理员",
            });
        });
}
onBeforeUnmount(() => {
    // polling = false;
    clearTimeout(scanTimer);
    clearTimeout(wxBindTimer);
});
</script>

<style scoped lang="less">
@import "@/style/AppConfirm/AppConfirm.less";
</style>
