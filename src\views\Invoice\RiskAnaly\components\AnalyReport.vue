<template>
    <div class="slot-content align-center" v-if="coverData.companyName" style="overflow-y:auto;" @scroll="pageScroll" id="pageScroll">
        <div class="buttons report-button">
            <a class="button" @click="goBack">返回</a>
            <a class="button solid-button ml-10" v-permission="['invoice-analysis-canedit']" @click="exportPdf">导出</a>
        </div>
        <div class="report-line"></div>
        <div class="report-outer">
            <!-- 封面 -->
            <div class="report-warp">
                <img src="@/assets/Invoice/report-bg.png" alt="">
                <div class="report-box">
                    <div class="report-title">发票风险分析报告</div>
                    <div class="report-company">{{ coverData.companyName }}</div>
                    <div class="report-date">
                        <div>报告期：{{ coverData.reportRangeDate }}</div>
                        <div>报告生成时间：{{ coverData.createTime}}</div>
                    </div>
                </div>
                <div class="service-wrap">
                    <div class="service-box">
                        <div class="service-code">
                            <img :src="coverData.qrCode" alt="">
                        </div>
                        <div class="service-txt">
                            <div>声明：</div>
                            <div>本报告仅供根据相关数据进行分析得出，供提示参考。</div>
                            <div>如需要年度财税专家咨询服务（付费），请扫描左侧二维码咨询。</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="page-gap"></div>
            <!-- 目录 -->
            <div class="directory">
                <div class="directory-title">目录</div>
                <div class="directory-subtitle">一、购销发票分析</div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section11')">1.1报告期进销项对比分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section11"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section12')">1.2报告期各税率销售、采购额</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section12"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section13')">1.3报告期上下游发票税额对比分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section13"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section14')">1.4报告期互开发票风险</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section14"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section15')">1.5报告期购销两头在外风险</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section15"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section16')">1.6报告期发票税号及企业名称不匹配风险</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section16"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section17')">1.7报告期销售/采购商品明细</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section17"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section18')">1.8报告期销售/采购商品品类</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section18"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section19')">1.9报告期进项发票中敏感业务发票</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section19"] }}</div>
                </div>
                <div class="directory-subtitle">二、虚开发票风险</div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section21')">2.1报告期红冲、作废分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section21"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section22')">2.2报告期前十红冲发票明细</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section22"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section23')">2.3报告期前十作废发票明细</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section23"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section24')">2.4报告期前十大客户分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section24"] }}</div>
                </div>
                <div class="directory-subtitle">三、采购虚假发票风险</div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section31')">3.1报告期零税额发票分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section31"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section32')">3.2报告期前十大供应商分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section32"] }}</div>
                </div>
                <div class="directory-subtitle">四、销售采购资金分析</div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section41')">4.1报告期销售回款分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section41"] }}</div>
                </div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section42')">4.2报告期采购付款分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section42"] }}</div>
                </div>
                <div class="directory-subtitle">五、报告期销售发票与报表经营收入对比分析</div>
                <div class="directory-theme">
                    <div class="theme-txt" @click="goTo('section51')">5.1报告期销售发票与报表经营收入对比分析</div>
                    <div class="theme-line"></div>
                    <div class="theme-page">{{ directory["#section51"] }}</div>
                </div>
            </div>
            <div class="page-gap"></div>
            <div class="mt-20"></div>
            <!-- 页眉 -->
            <div class="header">
                <div class="header-name">{{ coverData.companyName }}</div>
                <div class="">发票风险分析报告</div>
            </div>
            <!-- 正文 -->
            <div ref="reportRef" id="pages">
                <div class="page-content" id="pages1">
                    <!-- 总结概述 -->
                    <div class="page" id="summary">
                        <div class="page-title"><span>总结概述</span></div>
                        <div class="page-txt">{{ ReportData.summaryData.msg }}</div>
                        <div class="total-risk">
                            <div class="risk-box risk-high">高风险 <span>{{ ReportData.summaryData.highRistCount }}</span></div>
                            <div class="risk-box risk-mid">中风险 <span>{{ ReportData.summaryData.mediumRistCount }}</span></div>
                            <div class="risk-box risk-low">低风险 <span>{{ ReportData.summaryData.lowRistCount }}</span></div>
                        </div>
                    </div>
                    <!-- 一、购销发票分析 -->
                    <div class="page-title first"><span>一、购销发票分析</span></div>
                    <div class="page-blk"  id="section11">
                        <div class="page-subtitle">1.1报告期进销项对比分析</div>
                        <div class="page-txt">销项税/进项税比值分析是针对一般企业，对于外贸企业、初创无销售企业等，该比值可能异常，但该异常不能作为公司经营异常的佐证。</div>
                        <div class="chart-wap border">
                            <div class="chart-title">进销项对比分析图</div>
                            <div id="report-range-sale" style="width: 998px; height: 320px;"></div>
                        </div>
                        <div class="chart-wap table">
                            <div class="chart-table table-col6">
                                <div class="chart-tr head">
                                    <div class="chart-col">时间</div>
                                    <div class="chart-col">销项发票金额（元）</div>
                                    <div class="chart-col">进项发票金额（元）</div>
                                    <div class="chart-col">销项发票税额（元）</div>
                                    <div class="chart-col">进项发票税额（元）</div>
                                    <div class="chart-col">发票计算毛利率(%)</div>
                                </div>
                                <div class="chart-body">
                                    <div 
                                        :class="['chart-tr', {'total' : item.month.indexOf('合计') >= 0}]"    
                                        v-for="(item, index) in TableData11" :key="index"
                                    >
                                        <div class="chart-col">{{ item.month }}</div>
                                        <div class="chart-col">{{ formatMoney(item.salesInvoiceAmount, true, true) }} </div>
                                        <div class="chart-col">{{ formatMoney(item.purchaseInvoiceAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.salesInvoiceTax, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.purchaseInvoiceTax, true, true) }}</div>
                                        <div class="chart-col">{{ item.grossMargin }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData11.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section12">
                        <div class="page-subtitle">1.2报告期各税率销售、采购额</div>
                        <div class="page-txt">由于部分普票无法采集商品明细，为保证数据完整性，获取不到商品明细的发票采用单张发票的平均税率进行计算，若出现正常税率以外的其他税率，原因是单张发票上无法采集商品明细，一般金额影响较小。</div>
                        <div class="chart-wap">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col">时间</div>
                                    <div class="chart-col">税率</div>
                                    <div class="chart-col">销项发票金额(元)</div>
                                    <div class="chart-col">进项发票金额(元)</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr" v-for="(item, index) in TableData12" :key="index">
                                        <div class="chart-col">{{ item.year }}</div>
                                        <div class="chart-col">{{ item.taxRate }} </div>
                                        <div class="chart-col">{{ formatMoney(item.salesInvoiceAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.purchaseInvoiceAmount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData12.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section13">
                        <div :class="['page-subtitle', 
                            {
                                'low': taxUPRisk.ristLevel === 1,
                                'mid': taxUPRisk.ristLevel === 2,
                                'high': taxUPRisk.ristLevel === 3,
                                'risk': taxUPRisk.ristLevel > 0
                            }]"
                        >   1.3报告期上下游发票税额对比分析
                            <span v-if="taxUPRisk.ristLevel > 0">
                                ({{ ristLevelList[taxUPRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据每月进销项税额的比值，若比值偏高或偏低，说明与企业销售采购规模不匹配，可能存在采购虚假发票、虚开发票、延迟开票等风险。</div>
                        <div class="chart-wap">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col">月份</div>
                                    <div class="chart-col">销项税/进项税比值</div>
                                    <div class="chart-col">比值评价</div>
                                    <div class="chart-col">参考范围</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr" v-for="(item, index) in TableData13" :key="index">
                                        <div class="chart-col">{{ item.month }}</div>
                                        <div class="chart-col">{{ item.taxRatio }} </div>
                                        <div class="chart-col">{{ item.ratioEvaluate }}</div>
                                        <div class="chart-col">{{ item.referenceScope }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData13.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="taxUPRisk.msg">{{ taxUPRisk.msg}}</div>
                    </div>
                    <div class="page-blk" id="section14">
                        <div :class="['page-subtitle', 
                            {
                                'low': mutInvoiceRisk.ristLevel === 1,
                                'mid': mutInvoiceRisk.ristLevel === 2,
                                'high': mutInvoiceRisk.ristLevel === 3,
                                'risk': mutInvoiceRisk.ristLevel > 0
                            }]"
                        >
                            1.4报告期互开发票风险 
                            <span v-if="mutInvoiceRisk.ristLevel > 0">
                                ({{ ristLevelList[mutInvoiceRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据上下游互开销项发票额，若上下游企业之间互开销项发票且金额较大；可能存在相互虚开发票、违反《发票管理办法》等风险。</div>
                        <div class="chart-wap">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col">客户/供应商</div>
                                    <div class="chart-col">销售金额（元）</div>
                                    <div class="chart-col">采购金额（元）</div>
                                    <div class="chart-col">小计（元）</div>
                                </div>
                                <div class="chart-body">
                                    <div :class="['chart-tr', {'total' : item.name.indexOf('合计') >= 0}]" 
                                        v-for="(item, index) in TableData14" :key="index"
                                    >
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ formatMoney(item.salesInvoiceAmount, true, true) }} </div>
                                        <div class="chart-col">{{ formatMoney(item.purchaseInvoiceAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.subtotal, true, true) }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData14.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div> 
                        <div class="risk-tip" v-if="mutInvoiceRisk.msg">{{ mutInvoiceRisk.msg }}</div>
                    </div>
                    <div class="page-blk" id="section15">
                        <div class="page-subtitle">1.5报告期购销两头在外风险</div>
                        <div class="page-txt">本条判断依据销售和采购业务在外地的占比，若占比过高，说明企业交易成本大幅上升，违反业务合理性，可能存在采购虚假发票或虚开发票风险。</div>
                        <div class="chart-wap purchase">
                            <div class="chart-table table-col3">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}采购区域情况（金额单位：元）</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">省份</div>
                                        <div class="chart-col">占比（%）</div>
                                        <div class="chart-col">金额（元）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData15" :key="index">
                                        <div class="chart-col">{{ item.province }}</div>
                                        <div class="chart-col">{{ item.ratio }} </div>
                                        <div class="chart-col">{{ formatMoney(item.amount) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData15.length ===0 ">
                                        <div class="chart-col" style="line-height: 200px;">暂无数据</div>
                                    </div>
                                </div> 
                            </div>
                            <div class="wrap-flex-gap all">
                                <div class="chart-table">
                                    <div class="chart-tr head">
                                        <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}采购区域情况</div>
                                    </div>
                                </div>
                                <div id="pillar1" class="pillar-box"></div>
                            </div>
                        </div>
                        <div class="chart-wap salse" style="margin-top: 30px">
                            <div class="chart-table table-col3">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}销售区域情况（金额单位：元）</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">省份</div>
                                        <div class="chart-col">占比（%）</div>
                                        <div class="chart-col">金额（元）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData15Sales" :key="index">
                                        <div class="chart-col">{{ item.province }}</div>
                                        <div class="chart-col">{{ item.ratio }} </div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData15Sales.length ===0 ">
                                        <div class="chart-col" style="line-height: 200px;">暂无数据</div>
                                    </div>
                                </div> 
                            </div>
                            <div class="wrap-flex-gap all">
                                <div class="chart-table">
                                    <div class="chart-tr head">
                                        <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}销售区域情况</div>
                                    </div>
                                </div>
                                <div id="pillar2" class="pillar-box"></div>
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section16">
                        <div class="page-subtitle">1.6报告期发票税号与企业名称不匹配风险</div>
                        <!-- <div class="page-txt">发票税号及企业名称不匹配统计的是取得发票中销方的企业名称与统一社会信用代码不匹配，可通过启用宝进行查验，展示存在不同的企业名称及税号的发票数据。</div> -->
                        <div class="chart-wap">
                            <div class="chart-table table-col6">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}进项发票中公司名称与税号不匹配情况列表</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">序号</div>
                                        <div class="chart-col">发票企业税号</div>
                                        <div class="chart-col">发票企业名称</div>
                                        <div class="chart-col">发票商品名称</div>
                                        <div class="chart-col">发票金额（元）</div>
                                        <div class="chart-col">核实发票的企业名称</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData16" :key="index">
                                        <div class="chart-col">{{ item.num }}</div>
                                        <div class="chart-col">{{ item.taxpayerNumber }} </div>
                                        <div class="chart-col">{{ item.invoiceSupplierName }}</div>
                                        <div class="chart-col">{{ item.goodsName }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                        <div class="chart-col">{{ item.verifySupplierName }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData16.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="TableData16.length > 0">由于部分供应商可能存在改名情况，是否属于“发票抬头名称错误不合规票据”还需自行斟酌判断。</div>
                    </div>
                    <div class="page-blk" id="section17">
                        <div class="page-subtitle">1.7报告期销售/采购商品明细</div>
                        <div class="chart-wap wrap-flex">
                            <div :class="['chart-table sales-detail', {'table-col2': TableData7Keys.length===1, 'table-col3': TableData7Keys.length === 2}]" style="width: 485px;">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">销售商品明细及占比</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">项目</div>
                                        <div class="chart-col" v-for="(item, index) in TableData7Keys" :key="index">{{item}}/占比（%）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData17" :key="index">
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ item.currcentGoodsRatio }} </div>
                                        <div class="chart-col" v-if="item.lastGoodsRatio">{{ item.lastGoodsRatio }}</div>
                                        <div class="chart-col" v-if="item.beforeGoodsRatio">{{ item.beforeGoodsRatio }}</div>
                                    </div> 
                                </div> 
                            </div>
                            <div :class="['chart-table purchase-detail', {'table-col2': TableData7Keys.length===1, 'table-col3': TableData7Keys.length === 2}]" style="width: 485px; margin-left: 30px;">
                                <div class="chart-tr item head">
                                    <div class="chart-col col-title">采购商品明细及占比</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr item bold">
                                        <div class="chart-col">项目</div>
                                        <div class="chart-col" v-for="(item, index) in TableData71Keys" :key="index">{{item}}/占比（%）</div>
                                    </div>
                                    <div class="chart-tr item" v-for="(item, index) in TableData17Pur" :key="index">
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ item.currcentGoodsRatio }} </div>
                                        <div class="chart-col" v-if="item.lastGoodsRatio">{{ item.lastGoodsRatio }}</div>
                                        <div class="chart-col" v-if="item.beforeGoodsRatio">{{ item.beforeGoodsRatio }}</div>
                                    </div> 
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section18">
                        <div class="page-subtitle">1.8报告期销售/采购商品品类</div>
                        <div class="chart-wap wrap-flex">
                            <div :class="['chart-table sales-class', {'table-col2': TableData7Keys.length===1, 'table-col3': TableData7Keys.length === 2}]" style="width: 485px;">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">销售商品品类及占比</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">项目</div>
                                        <div class="chart-col" v-for="(item, index) in TableData72Keys" :key="index">{{item}}/占比（%）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData18" :key="index">
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ item.currcentGoodsRatio }} </div>
                                        <div class="chart-col" v-if="item.lastGoodsRatio">{{ item.lastGoodsRatio }}</div>
                                        <div class="chart-col" v-if="item.beforeGoodsRatio">{{ item.beforeGoodsRatio }}</div>
                                    </div> 
                                </div> 
                            </div>
                            <div :class="['chart-table purchase-class', {'table-col2': TableData7Keys.length===1, 'table-col3': TableData7Keys.length === 2}]" style="width: 485px; margin-left: 30px;">
                                <div class="chart-tr item head">
                                    <div class="chart-col col-title">采购商品品类及占比</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr item bold">
                                        <div class="chart-col">项目</div>
                                        <div class="chart-col" v-for="(item, index) in TableData73Keys" :key="index">{{item}}/占比（%）</div>
                                    </div>
                                    <div class="chart-tr item" v-for="(item, index) in TableData18Pur" :key="index">
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ item.currcentGoodsRatio }} </div>
                                        <div class="chart-col" v-if="item.lastGoodsRatio">{{ item.lastGoodsRatio }}</div>
                                        <div class="chart-col" v-if="item.beforeGoodsRatio">{{ item.beforeGoodsRatio }}</div>
                                    </div> 
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section19">
                        <div class="page-subtitle">1.9报告期进项发票中敏感业务发票</div>
                        <div class="page-txt" v-if="sensitiveWordsMsg">{{ sensitiveWordsMsg }}</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col6">
                                <div class="chart-tr head">
                                    <div class="chart-col">序号</div>
                                    <div class="chart-col">开票日期</div>
                                    <div class="chart-col">发票号码</div>
                                    <div class="chart-col">供应商名称</div>
                                    <div class="chart-col">商品名称</div>
                                    <div class="chart-col">发票金额（元）</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr"  v-for="(item, index) in TableData19.slice(0, 10)" :key="index">
                                        <div class="chart-col">{{ item.num }}</div>
                                        <div class="chart-col">{{ item.invoiceDate }} </div>
                                        <div class="chart-col">{{ item.number }}</div>
                                        <div class="chart-col">{{ item.supplierName }}</div>
                                        <div class="chart-col">{{ item.goodsName }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData19.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page-content" id="pages2">
                    <div class="page-title first"><span>二、虚开发票风险</span></div>
                    <div class="page-blk" id="section21">
                        <div :class="['page-subtitle', 
                            {
                                'low': redInvalidRisk.ristLevel === 1,
                                'mid': redInvalidRisk.ristLevel === 2,
                                'high': redInvalidRisk.ristLevel === 3,
                                'risk': redInvalidRisk.ristLevel > 0
                            }]"
                        >   2.1报告期红冲、作废分析
                            <span v-if="redInvalidRisk.ristLevel > 0">
                                ({{ ristLevelList[redInvalidRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据作废、红冲销项发票金额，若企业经常出现此类发票，说明发票真实性较差，可能存在虚开发票风险。月末作废、红冲销项发票金额较高，说明可能存在虚开发票风险，偷税漏税。</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col6">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}-{{reportRange[reportRange.length-1]}}开票情况</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">月份</div>
                                        <div class="chart-col">有效金额（元）</div>
                                        <div class="chart-col">红冲金额（元）</div>
                                        <div class="chart-col">月末三天红冲金额（元）</div>
                                        <div class="chart-col">作废金额（元）</div>
                                        <div class="chart-col">月末三天作废金额（元）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData21" :key="index">
                                        <div class="chart-col">{{ item.month }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }} </div>
                                        <div class="chart-col">{{ formatMoney(item.redAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.monthLastRedAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.invalidAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.monthLastInvalidAmount, true, true) }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData21.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="redInvalidRisk.msg">{{ redInvalidRisk.msg}}</div>
                    </div>
                    <div class="page-blk" id="section22">
                        <div class="page-subtitle">2.2报告期前十红冲发票明细</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col10">
                                <div class="chart-tr head">
                                    <div class="chart-col">序号</div>
                                    <div class="chart-col">发票类型</div>
                                    <div class="chart-col">发票种类</div>
                                    <div class="chart-col">客户名称</div>
                                    <div class="chart-col">发票代码</div>
                                    <div class="chart-col">发票号码</div>
                                    <div class="chart-col">数电票号码</div>
                                    <div class="chart-col">开票日期</div>
                                    <div class="chart-col">发票金额（元）</div>
                                    <div class="chart-col">发票税额（元）</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr" v-for="(item, index) in TableData22" :key="index">
                                        <div class="chart-col">{{ item.num }}</div>
                                        <div class="chart-col">{{ item.invoiceCategoryDes }} </div>
                                        <div class="chart-col">{{ item.invoiceTypeDes }}</div>
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ item.code }}</div>
                                        <div class="chart-col">{{ item.number }}</div>
                                        <div class="chart-col">{{ item.allElectricNumber }}</div>
                                        <div class="chart-col">{{ item.invoiceDate }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.taxAmount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData22.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section23">
                        <div class="page-subtitle">2.3报告期前十作废发票明细</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col10">
                                <div class="chart-tr head">
                                    <div class="chart-col">序号</div>
                                    <div class="chart-col">发票类型</div>
                                    <div class="chart-col">发票种类</div>
                                    <div class="chart-col">客户名称</div>
                                    <div class="chart-col">发票代码</div>
                                    <div class="chart-col">发票号码</div>
                                    <div class="chart-col">数电票号码</div>
                                    <div class="chart-col">开票日期</div>
                                    <div class="chart-col">发票金额（元）</div>
                                    <div class="chart-col">发票税额（元）</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr" v-for="(item, index) in TableData23" :key="index">
                                        <div class="chart-col">{{ item.num }}</div>
                                        <div class="chart-col">{{ item.invoiceCategoryDes }} </div>
                                        <div class="chart-col">{{ item.invoiceTypeDes }}</div>
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ item.code }}</div>
                                        <div class="chart-col">{{ item.number }}</div>
                                        <div class="chart-col">{{ item.allElectricNumber }}</div>
                                        <div class="chart-col">{{ item.invoiceDate }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.taxAmount, true, true) }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData23.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section24">
                        <div :class="['page-subtitle', 
                            {
                                'low': customerRisk.ristLevel === 1,
                                'mid': customerRisk.ristLevel === 2,
                                'high': customerRisk.ristLevel === 3,
                                'risk': customerRisk.ristLevel > 0
                            }]"
                        >   2.4报告期前十大客户分析
                            <span v-if="customerRisk.ristLevel > 0">
                                ({{ ristLevelList[customerRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据公司客户销售情况，若向关联方企业大额销售，可能存在利润转移，存在构成虚开增值税专用发票罪、逃税罪等风险；若向新公司大额销售，可能存在虚开增值税专用发票、非法出售增值税专用发票的风险；若向社保人数为0的公司大额销售，可能存在虚开增值税专用发票、非法出售增值税专用发票的风险；若前期大额销售的客户后期出现注销或吊销等经营状态异常情况，可能存在虚开增值税专用发票、非法出售增值税专用发票的风险。</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col6">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">202301-202312前十大客户分析</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">客户</div>
                                        <div class="chart-col">发票金额（元）</div>
                                        <div class="chart-col">占比（%）</div>
                                        <div class="chart-col">成立时长（年）</div>
                                        <!-- <div class="chart-col">是否0社保</div> -->
                                        <div class="chart-col">经营状态</div>
                                        <div class="chart-col">登记注册类型</div>
                                    </div>
                                    <div :class="['chart-tr', {'total' : item.name.indexOf('合计') >= 0 }]" v-for="(item, index) in TableData24" :key="index">
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }} </div>
                                        <div class="chart-col">{{ item.ratio }}</div>
                                        <div class="chart-col">{{ item.duration }}</div>
                                        <!-- <div class="chart-col">{{ item.isZeroSocialStaffNum }}</div> -->
                                        <div class="chart-col">{{ item.regStatus }}</div>
                                        <div class="chart-col">{{ item.companyOrgType }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData24.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="customerRisk.msg">{{ customerRisk.msg}}</div>
                    </div>
                </div>
                <div class="page-content" id="pages3">
                    <div class="page-title first"><span>三、采购虚假发票风险</span></div>
                    <div class="page-blk" id="section31">
                        <div :class="['page-subtitle', 
                            {
                                'low': zeroTaxRisk.ristLevel === 1,
                                'mid': zeroTaxRisk.ristLevel === 2,
                                'high': zeroTaxRisk.ristLevel === 3,
                                'risk': zeroTaxRisk.ristLevel > 0
                            }]"
                        >   3.1报告期零税额发票分析
                            <span v-if="zeroTaxRisk.ristLevel > 0">
                                ({{ ristLevelList[zeroTaxRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据公司采购发票情况，若存在大量零税额进项发票，属于开票行为异常，可能存在采购虚假发票、虚开农产品收购发票等风险。</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col3">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}采购情况</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">月份</div>
                                        <div class="chart-col">有效金额（元）</div>
                                        <div class="chart-col">零税额金额（元）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData31" :key="index">
                                        <div class="chart-col">{{ item.month }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }} </div>
                                        <div class="chart-col">{{ formatMoney(item.zeroTaxInvoiceAmount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData31.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="zeroTaxRisk.msg">{{ zeroTaxRisk.msg}}</div>
                    </div>
                    <div class="page-blk" id="section32">
                        <div :class="['page-subtitle', 
                            {
                                'low': supplierRisk.ristLevel === 1,
                                'mid': supplierRisk.ristLevel === 2,
                                'high': supplierRisk.ristLevel === 3,
                                'risk': supplierRisk.ristLevel > 0
                            }]"
                        >   3.2前十大供应商分析
                            <span v-if="supplierRisk.ristLevel > 0">
                                ({{ ristLevelList[supplierRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据公司供应商采购情况，若向关联方企业大额采购，可能存在利润转移，存在构成购买增值税专用发票罪、逃税罪等风险；若向新公司大额采购，向社保人数为0的公司大额采购以及前期大额采购的供应商后期出现注销或吊销等经营状态异常情况，存在构成购买增值税专用发票罪、逃税罪等风险。</div>
                        <div class="chart-wap">
                            <div class="chart-table table-col6">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}前十大供应商分析</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">供应商</div>
                                        <div class="chart-col">发票金额（元）</div>
                                        <div class="chart-col">占比（%）</div>
                                        <div class="chart-col">成立时长（年）</div>
                                        <!-- <div class="chart-col">是否0社保</div> -->
                                        <div class="chart-col">经营状态</div>
                                        <div class="chart-col">登记注册类型</div>
                                    </div>
                                    <div :class="['chart-tr', {'total' : item.name.indexOf('合计') >= 0 }]" v-for="(item, index) in TableData32" :key="index">
                                        <div class="chart-col">{{ item.name }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }} </div>
                                        <div class="chart-col">{{ item.ratio }}</div>
                                        <div class="chart-col">{{ item.duration }}</div>
                                        <!-- <div class="chart-col">{{ item.isZeroSocialStaffNum }}</div> -->
                                        <div class="chart-col">{{ item.regStatus }}</div>
                                        <div class="chart-col">{{ item.companyOrgType }}</div>
                                    </div>
                                    <div class="chart-tr nodata" v-if="TableData32.length ===0 "><div class="chart-col">暂无数据</div></div> 
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="supplierRisk.msg">{{ supplierRisk.msg}}</div>
                    </div>
                </div>
                <div class="page-content" id="pages4">
                    <div class="page-title first"><span>四、销售采购资金分析</span></div>
                    <div class="page-blk" id="section41">
                        <div class="page-subtitle">4.1报告期销售回款分析</div>
                        <div class="page-txt">本条判断依据公司销售回款情况，通过销项发票金额与资金模块日记账客户收入金额对比，若销售回款不及时，可能导致公司面临资金短缺、资金成本增加、供应链中断等风险。</div>
                        <div class="chart-wap">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}前五十销售回款分析</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">序号</div>
                                        <div class="chart-col">客户</div>
                                        <div class="chart-col">销项发票金额（元）</div>
                                        <div class="chart-col">回款金额（元）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData41" :key="index">
                                        <div class="chart-col">{{ item.num }}</div>
                                        <div class="chart-col">{{ item.name }} </div>
                                        <div class="chart-col">{{ formatMoney(item.invoiceAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData41.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                    </div>
                    <div class="page-blk" id="section42">
                        <div class="page-subtitle">4.2报告期采购付款分析</div>
                        <div class="page-txt">本条判断依据公司采购付款情况，通过进项发票金额与资金模块日记账供应商支出金额对比，若存在采购应付金额过多，可能导致公司面临资金短缺、供应链中断、信用等风险，公司短期内面临较大债务偿还压力。</div>
                        <div class="chart-wap">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col col-title">{{reportRange[0]}}~{{reportRange[reportRange.length-1]}}前五十采购付款分析</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr bold">
                                        <div class="chart-col">序号</div>
                                        <div class="chart-col">供应商</div>
                                        <div class="chart-col">进项发票金额（元）</div>
                                        <div class="chart-col">付款金额（元）</div>
                                    </div>
                                    <div class="chart-tr" v-for="(item, index) in TableData42" :key="index">
                                        <div class="chart-col">{{ item.num }}</div>
                                        <div class="chart-col">{{ item.name }} </div>
                                        <div class="chart-col">{{ formatMoney(item.invoiceAmount, true, true) }}</div>
                                        <div class="chart-col">{{ formatMoney(item.amount, true, true) }}</div>
                                    </div> 
                                    <div class="chart-tr nodata" v-if="TableData42.length ===0 "><div class="chart-col">暂无数据</div></div>
                                </div> 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="page-content" id="pages5">
                    <div class="page-title first"><span>五、报告期销售发票与报表经营收入对比分析</span></div>
                    <div class="page-blk" id="section51">
                        <div :class="['page-subtitle', 
                            {
                                'low': incomeCompareRisk.ristLevel === 1,
                                'mid': incomeCompareRisk.ristLevel === 2,
                                'high': incomeCompareRisk.ristLevel === 3,
                                'risk': incomeCompareRisk.ristLevel > 0
                            }]"
                        >   5.1报告期销售发票与报表经营收入对比分析
                            <span v-if="incomeCompareRisk.ristLevel > 0">
                                ({{ ristLevelList[incomeCompareRisk.ristLevel] }})
                            </span>
                        </div>
                        <div class="page-txt">本条判断依据每个月、季、年的销售发票不含税金额与报表的营业收入比较，若销售发票不含税金额低于营业收入，可能存在未开票收入风险；若销售发票不含税金额高于营业收入，可能存在偷税漏税风险。</div>
                        <div class="chart-wap month">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col">时间</div>
                                    <div class="chart-col">销售发票金额（元）</div>
                                    <div class="chart-col">营业收入（元）</div>
                                    <div class="chart-col">销售发票金额/营业收入比值</div>
                                </div>
                                <div class="chart-body">
                                    <div class="chart-tr" :class="{'total' : item.time.indexOf('合计') >= 0 }" v-for="(item, index) in TableData51" :key="index">
                                        <div class="chart-col">{{ item.time }}</div>
                                        <div class="chart-col">{{ formatMoney(item.invoiceAmount, true, true) }} </div>
                                        <div class="chart-col">{{ formatMoney(item.operatingIncome, true, true) }}</div>
                                        <div class="chart-col">{{ item.ratio }}</div>
                                    </div> 
                                </div> 
                            </div>
                        </div>
                        <div class="chart-wap quart">
                            <div class="chart-table">
                                <div class="chart-tr head">
                                    <div class="chart-col">时间</div>
                                    <div class="chart-col">销售发票金额（元）</div>
                                    <div class="chart-col">营业收入（元）</div>
                                    <div class="chart-col">销售发票金额/营业收入比值</div>
                                </div>
                                <div class="chart-body">
                                    <div :class="['chart-tr', {'total' : item.time.indexOf('合计') >= 0 }]" v-for="(item, index) in TableData51Qua" :key="index">
                                        <div class="chart-col">{{ item.time }}</div>
                                        <div class="chart-col">{{ formatMoney(item.invoiceAmount, true, true) }} </div>
                                        <div class="chart-col">{{ formatMoney(item.operatingIncome, true, true) }}</div>
                                        <div class="chart-col">{{ item.ratio }}</div>
                                    </div> 
                                </div> 
                            </div>
                        </div>
                        <div class="risk-tip" v-if="incomeCompareRisk.msg">{{ incomeCompareRisk.msg }}</div>
                    </div>
                    <div class="service-wrap service-last" id="conact">
                        <div class="service-box">
                            <div class="service-code">
                                <img :src="coverData.qrCode" alt="">
                            </div>
                            <div class="service-txt">
                                <div>声明：</div>
                                <div>本报告仅供根据相关数据进行分析得出，供提示参考。</div>
                                <div>如需要年度财税专家咨询服务（付费），请扫描左侧二维码咨询。</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog 
            class="report-progress" 
            title="" 
            v-model="loadingVisible" 
            width="298px" 
            height="200px" 
            :modal="false" 
            :align-center="true" 
            :show-close="false" 
            destroy-on-close
        >
            <div class="custom-loading">
                <div style="font-size: 14px; font-weight: normal; line-height: 30px; text-align: left;">{{ loadingTitle }}</div>
                <el-progress :percentage="percentage" :stroke-width="16" style="height: 20px;margin-top:10px;"  />
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts">
export default {
    name: "InvoiceRiskAnaly",
};
</script>
<script setup lang="ts">
import { nextTick, ref, onUnmounted, watch, reactive, onActivated, computed} from "vue";
import * as echarts from 'echarts/core';
import { BarChart,PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  // 数据集组件
  DatasetComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
  LegendComponent
} from 'echarts/components';
import { LabelLayout, UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import { downloadPDF } from "../htmlToPdf"
import type { 
    ReportCover, 
    ReportRiskMsgItem,
    ReportSPCompareItem, 
    ReportTRAmountItem,
    ReportTUDCompareItem,
    ReportMutInvoiceItem,
    ReportSPOutItem,
    ReportSUnMatchItem,
    ReportSPGoodsItem,
    ReportSWInvoiceItem,
    ReportRedInvalidItem,
    ReportRIInvoiceItem,
    ReportCSItem,
    ReportZTItem,
    ReportAmountItem,
    ReportIncomeTimeItem
} from "@/views/Invoice/types";
import { formatMoney } from "@/util/format";
import { useCorsagentIframeStoreHook } from "@/store/modules/corsagent";

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  LegendComponent,
  BarChart,
  PieChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);
const isErp = ref(window.isErp);
const props = defineProps({
    ReportData: {
        type: Object,
        default: () => {},
    },
});
const emit = defineEmits(["backHistoryList"]);
//返回
const goBack = () => {
    chartDispose();
    emit("backHistoryList");
}
//风险等级常量
const ristLevelList: { 
    [key: number]: string 
} = {
    0: '无风险',
    1: '低风险',
    2: '中风险',
    3: '高风险',
}
//页面瞄点跳转
function goTo(id: string) {
    const targetElement = document.getElementById(id) as HTMLElement;
    targetElement.scrollIntoView({ behavior: 'smooth' });
}
//封面
const coverData = reactive<ReportCover>({
    companyName: "",
    reportRangeDate: "",
    createTime: "",
    qrCode: ""
});
//echart实例资源释放
function chartDispose() {
    let div = document.getElementById('report-range-sale') as HTMLElement;
    let existingChart = echarts.getInstanceByDom(div);
    if (existingChart) {
        existingChart.dispose();
    }
    let div1 = document.getElementById('pillar1') as HTMLElement;
    let div2 = document.getElementById('pillar2') as HTMLElement;
    let existingChart1 = echarts.getInstanceByDom(div1);
    let existingChart2 = echarts.getInstanceByDom(div2);
    if (existingChart1) {
        existingChart1.dispose();
    }
    if (existingChart2) {
        existingChart2.dispose();
    }
}

//1.1报告期进销项对比分析
const TableData11 = ref<Array<ReportSPCompareItem>>([]);
let myChart: any;
function echart1() {
    let div = document.getElementById('report-range-sale') as HTMLElement;
    myChart = echarts.init(div);
    let xData: any = [];
    let salesData: any = [];
    let purchaseData: any = [];
    TableData11.value.forEach((item) => {
        if(item.month.indexOf('合计') < 0) {
            xData.push(item.month);
            if (item.salesInvoiceAmount!==undefined) {
                salesData.push(Math.round(item.salesInvoiceAmount / 10000));
            }
            if (item.purchaseInvoiceAmount!==undefined) {
                purchaseData.push(Math.round(item.purchaseInvoiceAmount / 10000));
            }
        }
    });

    let option = {
        grid: {
            left: '4%', // 调整左边距
            right: '4%', // 调整右边距
            top: '15%', // 可以调整上边距
            bottom: '10%', // 可以调整下边距
        },
        legend: {
            orient: 'horizontal',
            x: 'left',
            y: 'top',
            itemWidth: 8,
            itemHeight: 8,
        },
        tooltip: {},
        xAxis: {
            type: 'category',
            axisLabel: {
                interval: 0, // 强制显示所有标签
                fontSize: salesData.length > 12 ? 10 : 12,
            },
            data: xData,
        },
        yAxis: {},
        series: [
            {
                name: '销项发票 (万元)',
                type: 'bar',
                itemStyle: {
                    color: "#3D7FFF",
                },
                barGap: '20%',
                barCategoryGap: '35%',
                label: {
                    show: true,
                    position: 'top',
                    distance: 0,  
                    fontSize: salesData.length > 12 ? 10 : 12,
                    color:"#333"
                },
                data: salesData,
            },
            {
                name: '进项发票 (万元)',
                type: 'bar',
                itemStyle: {
                    color: "#28B950",
                },
                label: {
                    show: true,
                    position: 'top',
                    distance: 6, 
                    fontSize: salesData.length > 12 ? 10 : 12
                },
                data: purchaseData,
            }
        ]
    };
    option && myChart.setOption(option);
}

//1.2报告期各税率销售、采购额
const TableData12 = ref<Array<ReportTRAmountItem>>([]); 

//1.3报告期上下游发票税额对比分析
let taxUPRisk: ReportRiskMsgItem;
const TableData13 = ref<Array<ReportTUDCompareItem>>([]);

//1.4报告期互开发票风险(最多5条数据+3条总结)
let mutInvoiceRisk: ReportRiskMsgItem;
let TableData14 = ref<Array<ReportMutInvoiceItem>>([]);

//1.5报告期购销两头在外风险（采购）
let dataCake: any = [];
const TableData15 = ref<Array<ReportSPOutItem>>([]);
function handleSaleOutPillarData() {
    dataCake = [];
    if (TableData15.value.length > 0) {
        TableData15.value.forEach((item)=> {
            let obj = {value: 0, name: '', percentage: 0 };
            obj.name = item.province;
            if (item.ratio) {
                obj.percentage = item.ratio;
            }
            if (item.amount) {
                obj.value = item.amount;
            }
            dataCake.push(obj);
        });
    }
}
//1.5报告期购销两头在外风险（销售）
let TableData15Sales = ref<Array<ReportSPOutItem>>([]);
let dataCake1: any = [];
function handlePurchaseOutPillarData() {
    dataCake1 = [];
    if (TableData15Sales.value.length > 0) {
        TableData15Sales.value.forEach((item)=> {
            let obj = {value: 0, name: '', percentage: 0 };
            obj.name = item.province;
            if (item.ratio) {
                obj.percentage = item.ratio;
            }
            if (item.amount) {
                obj.value = item.amount;
            }
            dataCake1.push(obj);
        });
    }
}
//饼图
const colors:string[] = ["#F86602", "#F7B500", "#28B950", "#3D7FFF", "#35CBCA", "#975FE4", "#5470C6", "#91CC75", "#FAC858", "#fAB6B6",
    "#73C0DE", "#C29300", "#FC8452", "#9A60B4", "#EA7CCC", "#366FF9", "#6ED773", "#FF7214", "#00BDAE", "#FBA285", "#FFCF00",
    "#409EFF", "#FF821C", "#FF991E", "#C45656", "#F3D19E", "#FF7500", "#44B449", "#AA6FB4", "#D1EDC4", "#E6A23C", "#529B2E", "#D46FF9", "#EE48F0"
]
let myChart1: any;
let myChart2: any;
function echart2() {
    let div1 = document.getElementById('pillar1') as HTMLElement;
    let div2 = document.getElementById('pillar2') as HTMLElement;
    myChart1 = echarts.init(div1);
    myChart2 = echarts.init(div2);
    let option = {
        title: {
            text: '采购区域占比',
            x: TableData15.value.length > 0 ? '22.5%' : '44.6%',
            y: '45%',
            textStyle: {
                fontSize: '16px'
            }
        },
        tooltip: {},
        legend: {
            orient: 'vertical',
            right: dataCake.length < 9 ? "25%" : "2%",
            top: 'center',
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 16,
            formatter: function(name:string) {
                if(name) {
                    let percentage;
                    for(let i=0; i<dataCake.length; i++) {
                        if(dataCake[i].name === name) {
                            percentage = dataCake[i].percentage;
                        }
                    } 
                    let arr = [name+" "," "+ percentage]
                    return arr.join(" ")
                }
            }
        },
        series: [{
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 4
            },
            label: {
                show: false,
                position: 'center'
            },
            labelLine: {
                show: false
            },
            data: dataCake,
            color: colors,
            center: dataCake.length===0 ? ['50%', '50%']: ['28%', '50%'],
        }]
    };
    let option1 = {
        title: {
            text: '销售区域占比',
            x: TableData15Sales.value.length > 0 ? '22.5%' : '44.6%',
            y: '45%',
            textStyle: {
                fontSize: '16px'
            }
        },
        tooltip: {},
        legend: {
            orient: 'vertical',
            right: dataCake1.length < 9 ? "25%" : "2%",
            top: 'center',
            icon: 'circle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 16,
            formatter: function(name:string) {
                if(name) {
                    let percentage;
                    for(let i=0; i<dataCake1.length; i++) {
                        if(dataCake1[i].name === name) {
                            percentage = dataCake1[i].percentage;
                        }
                    } 
                    let arr = [name+" "," "+ percentage]
                    return arr.join(" ")
                }
            }
        },
        series: [{
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 4
            },
            label: {
                show: false,
                position: 'center'
            },
            labelLine: {
                show: false
            },
            data: dataCake1,
            color: colors,
            center: dataCake1.length===0 ? ['50%', '50%']: ['28%', '50%'],
        }]
    };
    option && myChart1.setOption(option);
    option1 && myChart2.setOption(option1);
}
//1.6报告期发票税号与企业名称不匹配风险(假设最多50)
let TableData16 = ref<Array<ReportSUnMatchItem>>([]);

//1.7销售商品明细
let TableData17 = ref<Array<ReportSPGoodsItem>>([]);
let TableData7Keys: any = [];
//1.7采购商品明细
let TableData17Pur = ref<Array<ReportSPGoodsItem>>([]);
let TableData71Keys: any = [];
//1.8销售商品品类
let TableData18 = ref<Array<ReportSPGoodsItem>>([]);
let TableData72Keys: any = [];
//1.8采购商品品类
let TableData18Pur = ref<Array<ReportSPGoodsItem>>([]);
let TableData73Keys: any = [];
function handleSPGoodsData(
    obj: { [key: string]: { name: string; goodsRatio: string }[] }, 
    arr: Array<ReportSPGoodsItem>, 
        index: number
) {
    // 将对象的键按照从小到大的顺序进行排序
    let sortedKeys = Object.keys(obj).sort();
    // 创建一个新的对象，按照排序后的键重新设置键值
    let newObj: { [key: string]: { name: string; goodsRatio: string }[] } = {};
        sortedKeys.forEach(key => {
        newObj[key] = obj[key];
    });
    Object.assign(obj, newObj);
    let keys = Object.keys(obj);
    switch(index) {
        case 1: 
            TableData7Keys = keys;
            break;
        case 2: 
            TableData71Keys = keys;
            break;
        case 3: 
            TableData72Keys = keys;
            break;
        case 4: 
            TableData73Keys = keys;
            break;
    }
    for(let i=0; i<5; i++) {
        if(keys.length === 1) {
            arr.push({
                name: obj[keys[0]][i].name, 
                currcentGoodsRatio: obj[keys[0]][i].goodsRatio,  
            })
        } else if(keys.length === 2) {
            arr.push({
                name: obj[keys[0]][i].name, 
                currcentGoodsRatio: obj[keys[0]][i].goodsRatio, 
                lastGoodsRatio: obj[keys[1]][i].goodsRatio
            })
        } else {
            arr.push({
                name: obj[keys[0]][i].name, 
                currcentGoodsRatio: obj[keys[0]][i].goodsRatio, 
                lastGoodsRatio: obj[keys[1]][i].goodsRatio, 
                beforeGoodsRatio: obj[keys[2]][i].goodsRatio, 
            })
        }
    }
}

//1.9报告期进项发票中敏感业务发票
const sensitiveWordsMsg = ref("");
const TableData19 = ref<Array<ReportSWInvoiceItem>>([]);

//2.1报告期红冲、作废分析
let redInvalidRisk: ReportRiskMsgItem;
const TableData21 = ref<Array<ReportRedInvalidItem>>([]);

//2.2报告期前十红冲发票明细
let TableData22 = ref<Array<ReportRIInvoiceItem>>([]);
//2.3报告期前十作废发票明细
const TableData23 = ref<Array<ReportRIInvoiceItem>>([]);

//2.4报告期前十大客户分析
let customerRisk: ReportRiskMsgItem;
const TableData24 = ref<Array<ReportCSItem>>([]);

//3.1报告期零税额发票分析
let zeroTaxRisk: ReportRiskMsgItem;
const TableData31 = ref<Array<ReportZTItem>>([]);
//3.2报告期前十大供应商分析
let supplierRisk: ReportRiskMsgItem;
const TableData32 = ref<Array<ReportCSItem>>([]);

//4.1报告期销售回款分析
const TableData41 = ref<Array<ReportAmountItem>>([]);
//4.2报告期采购回款分析
const TableData42 = ref<Array<ReportAmountItem>>([]);
//五、报告期销售发票与报表经营收入对比分析
let incomeCompareRisk: ReportRiskMsgItem;
const TableData51 = ref<Array<ReportIncomeTimeItem>>([]);
const TableData51Qua = ref<Array<ReportIncomeTimeItem>>([]); 


let origin = 1431.8;  //每页pdf高度在浏览器中占的高度
let titleHeight = 78; //主标题高度
let subtitleHeight = 40; //副标题高度
let chartHead = 38; //每行36+table整边框线
let vh = origin; //初始化

//页码
let pageCount:any = [];
let count = 3;
let obj:any = {};
function assignPage(id: string) {
    obj[id] = count;
    pageCount.push(obj);
    obj = {};
}
function restPage() {
    pageCount = [];
    count = 3;
    obj = {};
}
//公共分割
function commonSplit(id: string, vh: number, hasTitle: boolean, father?: string) {
    let blkheight = 40; //各二级目录之间间距
    let wrap =  document.querySelector(`${id}`) as HTMLElement;
    let txt = document.querySelector(`${id} .page-txt`) as HTMLElement;
    let txtHeight = 0;
    if(txt)  {
        txtHeight = txt.offsetHeight + 10; //10margin
    }
    let teableBody = document.querySelector(`${id} .chart-body`) as HTMLElement;
    let table =  document.querySelector(`${id} .chart-wap`) as HTMLElement;
    let tableHeight = 0;
    if(table) {
        tableHeight = table.offsetHeight + 20; //20margin
    }
    //主
    if (hasTitle) {
        blkheight = 16; //主标题和副标题直接的间距
        if (vh > titleHeight) {
            vh = vh - titleHeight;
        } else {
            let fatherEle =  document.querySelector(`${father}`) as HTMLElement;
            let title =  document.querySelector(`${father} .page-title`) as HTMLElement;
            let div = emptyCreateEle(vh);
            fatherEle.insertBefore(div, title);
            vh = origin - titleHeight;
            count++;
        }
    } 
    //二级标题
    assignPage(id);
    if(vh - (blkheight + subtitleHeight) > 0) {
        vh = vh - (blkheight + subtitleHeight);
    } else {
        let subtitle =  document.querySelector(`${id} .page-subtitle`) as HTMLElement;
        let div = emptyCreateEle(vh);
        wrap.insertBefore(div, subtitle);
        vh = origin - (blkheight + subtitleHeight);
        count++;
        assignPage(id);
    }
    //提示语
    if(vh > txtHeight) {
        vh = vh - txtHeight;
    } else {
        let div = emptyCreateEle(vh);
        wrap.insertBefore(div, txt);
        vh = origin - txtHeight;
        count++;
    }
    //表格
    if(tableHeight < vh) {
        vh = vh - tableHeight;
    } else {
        if((chartHead + 20) < vh) {
            vh = vh - (chartHead + 20);
        } else {
            let div = emptyCreateEle(vh);
            wrap.insertBefore(div, table);
            vh = origin - (chartHead + 20);
            count++;
        }
        let tr =  document.querySelectorAll(`${id} .chart-wap .chart-body .chart-tr`) as NodeListOf<HTMLElement>;
        for (let i = 0; i < tr.length; i++) { 
            let offset = i === tr.length - 1 ? 1 : 0; 
            if (tr[i].offsetHeight + offset < vh) { 
                vh -= tr[i].offsetHeight + offset; 
            } else { 
                TableCreateEle(vh, teableBody, tr[i]); 
                vh = origin - (tr[i].offsetHeight + offset); 
                count++; 
            } 
        }
    }
    //风险提示语
    let riskTip = document.querySelector(`${id} .risk-tip`) as HTMLElement;
    let riskTipHeight = 0;
    if(riskTip) {
        riskTipHeight = riskTip.offsetHeight + 10;
        if(riskTipHeight < vh) {
            vh = vh - riskTipHeight;
        } else {
            let div = emptyCreateEle(vh);
            wrap.insertBefore(div, riskTip);
            vh = origin - riskTipHeight;
            count++;
        }
    }
    return vh;
}
//表格添加空行
function TableCreateEle(vh: number, parent: HTMLElement, child: HTMLElement) {
    if (count%5===0 && vh>1.3) vh = vh-1.3;
    let div = document.createElement("div");
    div.style.height = vh + "px";
    div.style.width = 1000 + "px";
    div.style.marginLeft = "-1px";
    div.style.marginRight = "-1px";
    div.style.background = "#fff";
    div.classList.add("chart-tr");
    div.classList.add("add");
    parent.insertBefore(div, child);
}
//其他添加空白块
function emptyCreateEle(vh: number) {
    if (count%5===0 && vh>1.5) vh = vh-1.5;
    let div = document.createElement("div");
    div.style.height = vh + "px";
    div.classList.add("add");
    return div;
}

function page1Split() {
    vh = origin;
    let blkheight = 16; //主标题和副标题直接的间距
    //总结
    let summary = document.getElementById("summary") as HTMLElement;
    let summaryHeight = 0;
    if (summary) {
        summaryHeight = summary.offsetHeight;
    }
    vh = vh - summaryHeight;
    //第一章主目录
    vh = vh - titleHeight; 
    //1.1
    assignPage("#section11");
    let section11 =  document.querySelector("#section11") as HTMLElement;
    let txt = document.querySelector("#section11 .page-txt") as HTMLElement;
    let txtHeight = 0;
    if (txt)  {
        txtHeight = txt.offsetHeight + 10; //10margin
    }
    vh = vh - (blkheight + subtitleHeight + txtHeight); 
    //1.1echart图
    let chartEle = document.querySelector("#section11 .chart-wap.border") as HTMLElement;
    let chartEleHeight = 383; //363内容+20margin
    if (vh > chartEleHeight) {
        vh = vh - chartEleHeight;
    } else {
        let div = emptyCreateEle(vh);
        section11.insertBefore(div, chartEle);
        vh = origin - chartEleHeight;
        count++;
    }
    //表格
    let table11Ele = document.querySelector("#section11 .chart-wap.table") as HTMLElement;
    let table11EleBody = document.querySelector("#section11 .chart-wap.table .chart-body") as HTMLElement;
    let table11EleHeight = 0;
    if (table11Ele) {
        table11EleHeight = table11Ele.offsetHeight + 20;
    }
    if (vh > table11EleHeight) {
        vh = vh - table11EleHeight;
    } else {
        if((chartHead + 20) < vh) {
            vh = vh - (chartHead + 20);
        } else {
            let div = emptyCreateEle(vh);
            chartEle.insertBefore(div, table11Ele);
            vh = origin - (chartHead + 20);
            count++;
        }
        let tr =  document.querySelectorAll("#section11 .chart-wap .chart-body .chart-tr") as NodeListOf<HTMLElement>;
        for (let i = 0; i < tr.length; i++) { 
            let offset = i === tr.length - 1 ? 1 : 0; 
            if (tr[i].offsetHeight + offset < vh) { 
                vh -= tr[i].offsetHeight + offset; 
            } else { 
                TableCreateEle(vh, table11EleBody, tr[i]); 
                vh = origin - (tr[i].offsetHeight + offset); 
                count++; 
            } 
        }
    }
    //1.2
    vh = commonSplit("#section12", vh, false);
    //1.3
    vh = commonSplit("#section13", vh, false);
    //1.4
    vh = commonSplit("#section14", vh, false);
    //1.5
    vh = page15Split(vh);
    //1.6
    vh = commonSplit("#section16", vh, false);
    //1.7~1.8
    vh = page17Split(vh);
    //1.9
    vh = commonSplit("#section19", vh, false);
}
function page15Split(vh: number) {
    let blkheight = 40;
    //1.5
    assignPage("#section15");
    let section15 =  document.querySelector("#section15") as HTMLElement;
    let txt = document.querySelector("#section15 .page-txt") as HTMLElement;
    let txtHeight = 0;
    if (txt)  {
        txtHeight = txt.offsetHeight + 10; //10margin
    }

    if (vh > (blkheight + subtitleHeight)) {
        vh = vh - (blkheight + subtitleHeight);
    } else {
        let subtitle =  document.querySelector("#section15 .page-subtitle") as HTMLElement;
        let div = emptyCreateEle(vh);
        section15.insertBefore(div, subtitle);
        vh = origin - (blkheight + subtitleHeight);
        count++;
        assignPage("#section15");
    }
    if(vh > txtHeight) {
        vh = vh - txtHeight;
    } else {
        let div = emptyCreateEle(vh);
        section15.insertBefore(div, txt);
        vh = origin - txtHeight;
        count++;
    }

    //采购
    let tablePuchaseWrap = document.querySelector("#section15 .chart-wap.purchase") as HTMLElement;
    let tablePuchaseEle = document.querySelector("#section15 .chart-wap.purchase .chart-table") as HTMLElement;
    let tablePuchaseBodyEle = document.querySelector("#section15 .chart-wap.purchase .chart-body") as HTMLElement;
    let tablePuchaseEleHeight = 0;
    if (tablePuchaseEle) {
        tablePuchaseEleHeight = tablePuchaseEle.offsetHeight + 20;
    }
    if (vh > tablePuchaseEleHeight) {
        vh = vh - tablePuchaseEleHeight;
    } else {
        if((chartHead + 20) < vh) {
            vh = vh - (chartHead + 20);
        } else {
            let div = emptyCreateEle(vh);
            tablePuchaseWrap.insertBefore(div, tablePuchaseEle);
            vh = origin - (chartHead + 20);
            count++;
        }
        let tr =  document.querySelectorAll("#section15 .chart-wap.purchase .chart-body .chart-tr") as NodeListOf<HTMLElement>;
        for (let i = 0; i < tr.length; i++) { 
            let offset = i === tr.length - 1 ? 1 : 0; 
            if (tr[i].offsetHeight + offset < vh) { 
                vh -= tr[i].offsetHeight + offset; 
            } else { 
                TableCreateEle(vh, tablePuchaseBodyEle, tr[i]); 
                vh = origin - (tr[i].offsetHeight + offset); 
                count++; 
            } 
        }
    }
    let pillarChase = document.querySelector("#section15 .chart-wap.purchase .wrap-flex-gap") as HTMLElement;
    let pillarChaseHeight = 0;
    if (pillarChase) {
        pillarChaseHeight = pillarChase.offsetHeight + 15;
    }
    if (vh > pillarChaseHeight) {
        vh = vh - pillarChaseHeight;
    } else {
        let div = emptyCreateEle(vh);
        tablePuchaseWrap.insertBefore(div, pillarChase);
        vh = origin - pillarChaseHeight;
        count++;
    }
    //销售
    let tableSalesWrap = document.querySelector("#section15 .chart-wap.salse") as HTMLElement;
    let tableSalesBodyEle = document.querySelector("#section15 .chart-wap.salse .chart-body") as HTMLElement;
    let tableSalesEle = document.querySelector("#section15 .chart-wap.salse .chart-table") as HTMLElement;
    let tableSalesEleHeight = 0;
    if (tableSalesEle) {
        tableSalesEleHeight = tableSalesEle.offsetHeight + 30;
    }
    if (vh > tableSalesEleHeight) {
        vh = vh - tableSalesEleHeight;
    } else {
        if((chartHead + 30) < vh) {
            vh = vh - (chartHead + 30);
        } else {
            let div = emptyCreateEle(vh);
            tableSalesWrap.insertBefore(div, tableSalesEle);
            vh = origin - (chartHead + 30);
            count++;
        }
        let tr =  document.querySelectorAll("#section15 .chart-wap.salse .chart-body .chart-tr") as NodeListOf<HTMLElement>;
        for (let i = 0; i < tr.length; i++) { 
            let offset = i === tr.length - 1 ? 1 : 0; 
            if (tr[i].offsetHeight + offset < vh) { 
                vh -= tr[i].offsetHeight + offset; 
            } else { 
                TableCreateEle(vh, tableSalesBodyEle, tr[i]); 
                vh = origin - (tr[i].offsetHeight + offset); 
                count++; 
            } 
        }
    }
    let pillarSales = document.querySelector("#section15 .chart-wap.salse .wrap-flex-gap") as HTMLElement;
    let pillarSalesHeight = 0;
    if (pillarSales) {
        pillarSalesHeight = pillarSales.offsetHeight + 15;
    }
    if (vh > pillarSalesHeight) {
        vh = vh - pillarSalesHeight;
    } else {
        let div = emptyCreateEle(vh);
        tableSalesWrap.insertBefore(div, pillarSales);
        vh = origin - pillarSalesHeight;
        count++;
    }
    return vh;
}
function page17Split(vh: number) {
    //1.7
    assignPage("#section17");
    let section16 =  document.querySelector("#section16") as HTMLElement;
    let section17 =  document.querySelector("#section17") as HTMLElement;
    let section17Height = 0;
    if (section17)  {
        section17Height = section17.offsetHeight; 
    }
    if (vh > section17Height) {
        vh = vh - section17Height;
    } else {
        let div = emptyCreateEle(vh);
        section16.appendChild(div);
        vh = origin - section17Height;
        count++;
        assignPage("#section17");
    }
    //1.8
    assignPage("#section18");
    let section18 =  document.querySelector("#section18") as HTMLElement;
    let section18Height = 0;
    if (section18)  {
        section18Height = section18.offsetHeight; 
    }
    if (vh > section18Height) {
        vh = vh - section18Height;
    } else {
        let div = emptyCreateEle(vh);
        section17.appendChild(div);
        vh = origin - section18Height;
        count++;
        assignPage("#section18");
    }
    return vh;
}
function page2Split() {
    vh = commonSplit("#section21", vh, true, "#pages2");
    vh = commonSplit("#section22", vh, false);
    vh = commonSplit("#section23", vh, false);
    vh = commonSplit("#section24", vh, false);
}
function page3Split() {
    vh = commonSplit("#section31", vh, true, "#pages3");
    vh = commonSplit("#section32", vh, false);
}
function page4Split() {
    vh = commonSplit("#section41", vh, true, "#pages4");
    vh = commonSplit("#section42", vh, false);
}
function page5Split() {
    let blkheight = 16; //主标题和副标题直接的间距
    let serviceHeight = 250; //服务联系人

    let txt = document.querySelector("#section51 .page-txt") as HTMLElement;
    let txtHeight = 0;
    if(txt)  {
        txtHeight = txt.offsetHeight + 10; //10margin
    }
    let parent = document.querySelector("#section51") as HTMLElement;
    let monthBody = document.querySelector("#section51 .month .chart-body") as HTMLElement;
    let quartBody = document.querySelector("#section51 .quart .chart-body") as HTMLElement;

    let month =  document.querySelector("#section51 .month") as HTMLElement;
    let monthHeight = 0;
    if(month) {
        monthHeight = month.offsetHeight + 20; //20margin
    }
    let quart =  document.querySelector("#section51 .quart") as HTMLElement;
    let quartHeight = 0;
    if(quart) {
        quartHeight = quart.offsetHeight + 20; //20margin
    }
    let riskTip = document.querySelector("#section51 .risk-tip") as HTMLElement;
    let riskTipHeight = 0;
    if(riskTip) {
        riskTipHeight = riskTip.offsetHeight + 10;
    }
    if (vh > titleHeight) {
        vh = vh - titleHeight;
    } else {
        let fatherEle =  document.querySelector("#pages5") as HTMLElement;
        let title =  document.querySelector("#pages5 .page-title") as HTMLElement;
        let div = emptyCreateEle(vh);
        fatherEle.insertBefore(div, title);
        vh = origin - titleHeight;
        count++;
    }
    assignPage("#section51");
    if (vh - (blkheight + subtitleHeight) > 0) {
        vh = vh - (blkheight + subtitleHeight);
    } else {
        let subtitle =  document.querySelector("#section51 .page-subtitle") as HTMLElement;
        let div = emptyCreateEle(vh);
        parent.insertBefore(div, subtitle);
        vh = origin - (blkheight + subtitleHeight);
        count++;
        assignPage("#section51");
    }
    if(vh > txtHeight) {
        vh = vh - txtHeight;
    } else {
        let div = emptyCreateEle(vh);
        parent.insertBefore(div, txt);
        vh = origin - txtHeight;
        count++;
    }

    if(monthHeight < vh) {
        vh = vh - monthHeight;
    } else {
        if((chartHead + 20) < vh) {
            vh = vh - (chartHead + 20) ;
        } else {
            let head =  document.querySelector("#section51 .month .chart-table") as HTMLElement;
            let div = emptyCreateEle(vh);
            month.insertBefore(div, head);
            vh = origin - (chartHead + 20) ;
        }
        let tr =  document.querySelectorAll("#section51 .month .chart-body .chart-tr") as NodeListOf<HTMLElement>;
        for (let i = 0; i < tr.length; i++) { 
            let offset = i === tr.length - 1 ? 1 : 0; 
            if (tr[i].offsetHeight + offset < vh) { 
                 vh -= tr[i].offsetHeight + offset; 
            } else { 
                TableCreateEle(vh, monthBody, tr[i]); 
                vh = origin - (tr[i].offsetHeight + offset); 
            } 
        }  
    }

    if(quartHeight < vh) {
        vh = vh - quartHeight;
    } else {
        if((chartHead + 20) < vh) {
            vh = vh - (chartHead + 20);
        } else {
            let head =  document.querySelector("#section51 .quart .chart-table") as HTMLElement;
            let div = emptyCreateEle(vh);
            quart.insertBefore(div, head);
            vh = origin - (chartHead + 20);
        }
        let tr =  document.querySelectorAll("#section51 .quart .chart-body .chart-tr") as NodeListOf<HTMLElement>;
        for (let i = 0; i < tr.length; i++) { 
            let offset = i === tr.length - 1 ? 1 : 0; 
            if (tr[i].offsetHeight + offset < vh) { 
                 vh -= tr[i].offsetHeight + offset; 
            } else { 
                TableCreateEle(vh, quartBody, tr[i]); 
                vh = origin - (tr[i].offsetHeight + offset); 
            } 
        }  
    }

    if(riskTipHeight < vh) {
        vh = vh - riskTipHeight;
    } else {
        let div = emptyCreateEle(vh);
        parent.insertBefore(div, riskTip);
        vh = origin - riskTipHeight;
    }

    if(vh > serviceHeight) {
        let div = emptyCreateEle(vh-serviceHeight);
        parent.appendChild(div);
    } else {
        let div = emptyCreateEle(vh);
        parent.appendChild(div);
    }
}

function splitPage() {
    return new Promise((resolve) => {
        page1Split();
        page2Split();
        page3Split();
        page4Split();
        page5Split();
        calcNum();
        resolve(true);
    });
}

//目录页码
const directory:Record<string, number> = reactive({
    "#section11": 3,
    "#section12": 3,
    "#section13": 3,
    "#section14": 3,
    "#section15": 3,
    "#section16": 3,
    "#section17": 3,
    "#section18": 3,
    "#section19": 3,
    "#section21": 3,
    "#section22": 3,
    "#section23": 3,
    "#section24": 3,
    "#section31": 3,
    "#section32": 3,
    "#section41": 3,
    "#section42": 3,
    "#section51": 3
});
//计算页码
function calcNum() {
    for(let key in directory) {
        let result = pageCount.filter((item:any)=>{
            return Object.keys(item).some(name => name===key);
        });
        directory[key] = result[result.length-1][key];
    }
    restPage();
}
//1.7和1.8部分某行换行导致最后一行高度需要重新计算赋值
const calLastLineHeight = ((id: string, type: string)=>{
    let table = document.querySelector(`${id} .chart-wap`) as HTMLElement;
    let trs = document.querySelectorAll(`${id} ${type} .chart-body .chart-tr`) as NodeListOf<HTMLElement>;
    let tableHeight = 0;
    table && (tableHeight = table.offsetHeight - 39); //36+2+1(head高+2条外边线+body边线)
    let result = 0;
    result = tableHeight;
    for(let i=0; i<trs.length-1; i++) {
        result = result - trs[i].offsetHeight;
    }
    trs[trs.length-1].style.height = result + "px";
})
//报告期间
const reportRange = ref<Array<string>>([]);
//数据初始化赋值
watch(
    ()=> props.ReportData,
    (val)=> {
        if(val) {
            //封面
            coverData.companyName = val.coverData.companyName;
            coverData.reportRangeDate = val.coverData.reportRangeDate;
            coverData.qrCode = val.coverData.qrCode;
            coverData.createTime = val.coverData.createTime;
            //1.1报告期进销项对比分析
            TableData11.value = val.salesPurchaseInvoiceContrastData;
            //1.2报告期各税率销售、采购额
            TableData12.value = val.reportTaxRateAndAmountData;
            //1.3报告期上下游发票税额对比分析
            taxUPRisk = val.taxUpDownContrastData.ristMsg;
            TableData13.value = val.taxUpDownContrastData.taxUpDownContrastList;
            //1.4报告期互开发票风险
            mutInvoiceRisk = val.mutualInvoicingData.ristMsg;
            TableData14.value = val.mutualInvoicingData.mutualInvoicingList;
            //1.5报告期购销两头在外风险（购买）
            TableData15.value = val.purchaseOutsideData;
            handleSaleOutPillarData();
            //1.5报告期购销两头在外风险（销售）
            TableData15Sales.value = val.salesOutsideData;
            handlePurchaseOutPillarData();
            //1.6报告期发票税号与企业名称不匹配风险(假设最多50)
            TableData16.value = val.supplierUnMatchData;
            //1.7销售商品明细
            TableData17.value = [];
            handleSPGoodsData(val.salesGoodsDetailData, TableData17.value, 1);
            //1.7采购商品明细
            TableData17Pur.value = [];
            handleSPGoodsData(val.purchaseGoodsDetailData, TableData17Pur.value, 2);
            //1.8销售商品品类
            TableData18.value = [];
            handleSPGoodsData(val.salesGoodsClassData, TableData18.value, 3);
            //1.8采购商品品类
            TableData18Pur.value = [];
            handleSPGoodsData(val.purchaseGoodsClassData, TableData18Pur.value, 4);
            //1.9报告期进项发票中敏感业务发票
            sensitiveWordsMsg.value = val.sensitiveWordsData.msg;
            TableData19.value = val.sensitiveWordsData.sensitiveWordsInvoiceList;
            //2.1报告期红冲、作废分析
            redInvalidRisk = val.redInvalidData.ristMsg;
            TableData21.value = val.redInvalidData.redInvalidList;
            //2.2报告期前十红冲发票明细
            TableData22.value = val.top10RedData;
            //2.3报告期前十作废发票明细
            TableData23.value = val.top10InvalidData;
            //2.4报告期前十大客户分析
            customerRisk = val.customerData.ristMsg;
            TableData24.value = val.customerData.customerSupplierList;
            //3.1报告期零税额发票分析
            zeroTaxRisk = val.zeroTaxData.ristMsg;
            TableData31.value = val.zeroTaxData.zeroTaxList;
            //3.2报告期前十大供应商分析
            supplierRisk = val.supplierData.ristMsg;
            TableData32.value = val.supplierData.customerSupplierList;
            //4.1报告期销售回款分析
            TableData41.value = val.backAmountData;
            //4.2报告期采购回款分析
            TableData42.value = val.payAmountData;
            //五、报告期销售发票与报表经营收入对比分析
            incomeCompareRisk = val.salesInvoiceIncomeContrastData.ristMsg;
            TableData51.value = val.salesInvoiceIncomeContrastData.monthData;
            TableData51Qua.value = val.salesInvoiceIncomeContrastData.quarterData;
            
            //报告期间
            reportRange.value = coverData.reportRangeDate.split(/\s|至/);
            formatRange();
        }
    }
)
function formatRange() {
    if (reportRange.value[0]) {
        let index = reportRange.value[0].indexOf("-");
        if (index !== -1) {
            reportRange.value[0] = reportRange.value[0].slice(0, index) + reportRange.value[0].slice(index+1);
        }
    }
    if (reportRange.value[reportRange.value.length -1]) {
        let index = reportRange.value[reportRange.value.length -1].indexOf("-");
        if (index !== -1) {
            reportRange.value[reportRange.value.length -1] = 
                reportRange.value[reportRange.value.length -1].slice(0, index) + 
                reportRange.value[reportRange.value.length -1].slice(index+1);
        }
    }
}
const reportRef = ref();
const loadingVisible = ref(false);
const loadingTitle = ref("");
const percentage = ref(10);
let timer = 0;
watch(()=>loadingVisible.value,
    (val)=>{
        if(val) {
            timer = setInterval(() => {
                if (percentage.value < 100) {
                    percentage.value += 10;
                } else {
                    percentage.value = 0;
                }
            }, 500);
        } else {
            clearInterval(timer);
        }
    }
)
//导出pdf
function exportPdf() {
    if (isErp.value) {
        useCorsagentIframeStoreHook().pushNoticeUrl("");
    }
    const fileName = coverData.companyName + "_发票风险报告" + coverData.reportRangeDate;
    percentage.value = 10;
    loadingVisible.value = true;
    loadingTitle.value = "报告导出中...";
    count = 3;
    setTimeout(()=>{
        splitPage().then(() => {
            downloadPDF(directory, fileName, loadingVisible);
        }).finally(() =>{
            count = 3; 
        });
    })
}
//初始化图表
function initPage() {
    nextTick(()=> {
        echart1();
        echart2();
        splitPage().then(() => {
            // 查找所有拥有 "add" 类名的元素
            const elements = document.querySelectorAll('.add');
            // 遍历并移除元素
            elements.forEach(element => {
                element.remove();
            });
            calLastLineHeight("#section17", ".sales-detail");
            calLastLineHeight("#section17", ".purchase-detail");
            calLastLineHeight("#section18", ".sales-class");
            calLastLineHeight("#section18", ".purchase-class");
        });
    });
}
defineExpose({
    initPage,
});
onUnmounted(()=> {
    myChart && myChart.dispose();
    myChart1 && myChart1.dispose();
    myChart2 && myChart2.dispose();
});
let reportScrollTop = 0;
const pageScroll = ()=>{
    let outer = document.getElementById("pageScroll") as HTMLElement;
    reportScrollTop = outer.scrollTop;
}
onActivated(()=>{
    let outer = document.getElementById("pageScroll") as HTMLElement;
    if(outer) {
        outer.scrollTop = reportScrollTop;
    }
})

</script>

<style lang="less" scoped>
.report-button {
    display: flex;
    text-align: right; 
    width: 1050px;
    padding: 20px 0;
}
.report-outer {
    margin: 20px auto;
    padding: 20px 0 50px;
    font-family: PingFangSC, PingFang SC;
    text-align: left;
    width: 1050px;
    border: 1px solid var(--border-color);
    div {
        box-sizing: border-box;
    }
}
.report-line {
    width: 100%;
    border-bottom: 1px solid var(--border-color);
}
.report-warp {
    position: relative;
    margin: auto;
    width: 1000px; 
    > img {
        display: block;
        width: 100%;
        height: auto;
    }
    .report-box {
        position: absolute;
        top: 298px;
        left: 0;
        right: 0;
        text-align: center;
        .report-title {
            font-size: 56px;
            line-height: 78px;
            // font-weight: 600;
            color: var(--white);
            letter-spacing: 5px;
        }
        .report-company {
            margin-top: 16px;
            font-size: 32px;
            line-height: 36px;
            color: var(--white);
        }
        .report-date {
            margin: 48px auto 0;
            width: 360px;
            text-align: left;
            font-size: 22px;
            line-height: 32px;
            color: var(--white);
        }
    }
}
.header {
    width: 1000px;
    margin: auto; 
    padding: 0 20px;
    height: 50px; 
    display: flex; 
    justify-content: space-between; 
    color: #3C7EFE; 
    background-color: rgba(#3D7FFF, .03);
    align-items: center;
    border-bottom: 2px solid #3881F8;
    .header-name {
        font-weight: bold; 
        font-size: 20px;
    }
}
.service-wrap {
    position: absolute;
    bottom: 50px;
    left: 130px;
    right: 130px;
    .service-box {
        padding: 16px 0 16px 30px;
        display: flex;
        align-items: center;
        height: 130px;
        background-color: rgba(255, 255, 255, .3);
        border-radius: 4px;
        box-sizing: border-box;
    }
    .service-code {
        width: 76px;
        height: 76px;
        > img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
    }
    .service-txt {
        flex: 1;
        min-width: 0;
        margin-left: 10px;
        font-size: 18px;
        line-height: 25px;
        color: var(--white);
    }
    &.service-last {
        position: static;
        margin: 80px auto 0;
        width: 740px;
        .service-box {
            background: #fafafa;
        }
        .service-txt {
            color: #666666;
        }
    }
}
.scale {
    transform: scale(1.1);
}
.directory {
    margin: auto;
    width: 1000px; 
    padding: 56px 0;
    box-sizing: border-box;
    .directory-title {
        font-size: 36px;
        line-height: 52px;
        font-weight: 600;
    }
    .directory-subtitle {
        margin-top: 40px;
        font-size: 24px;
        line-height: 32px;
        font-weight: 600;
    }
    .directory-theme {
        margin-top: 24px;
        display: flex;
        align-items: center;
        padding-left: 40px;
        .theme-txt {
            font-size: 20px;
            color: #3D7FFF;
            cursor: pointer;
            border-bottom: 1px solid #3d7fff;
        }
        .theme-line {
            margin: 0 20px;
            flex: 1;
            min-width: 0;
            border-top: 1px dashed #999;
        }
        .theme-page {
            font-size: 16px;
            color: #999;
        }
    }
}
#pages {
    width: 1000px;
    margin: auto;
}
.page-gap {
    margin-top: 30px;
    height: 10px;
    background: #e8e8e8;
    width: 100%;
}
.page-content {
    width: 1000px;
    margin: auto;
}
.page-title {
    padding-top: 40px;
    display: flex;
    align-items: center;
    &:before {
        margin-right: 10px;
        content: "";
        display: block;
        width: 6px;
        height: 22px;
        background: #3D7FFF;
    }
    > span {
        flex: 1;
        min-width: 0;
        font-weight: 600;
        font-size: 26px;
        line-height: 38px;
    }
}
.page-blk {
    padding-top: 16px;
}
.page-blk + .page-blk {
    padding-top: 40px;
}
.chart-wap + .page-blk {
    padding-top: 40px;
}
.page-subtitle {
    font-weight: 600;
    font-size: 16px;
    padding: 8px 0;
    height: 40px;
    line-height: 24px;
    box-sizing: border-box;
    &.risk {
        padding: 8px 12px;
        color: var(--white);
    }
    &.low {
        background: #FFCE45;   
    }
    &.mid {
        background: #FF991E;
    }
    &.high {
        background: #FF3333;
    }
}
.page-txt {
    margin-top: 10px;
    font-size: 16px;
    line-height: 22px;
}
.total-risk {
    margin-top: 16px;
    display: flex;
    border: 1px solid #ddd;
    .risk-box {
        width: 33.3%;
        height: 116px;
        font-size: 26px;
        line-height: 116px;
        font-weight: 600;
        text-align: center;
    }
    .risk-box + .risk-box {
        border-left: 1px solid #ddd;
    }
    .risk-high {
        background: rgba(255, 0, 0, .04);
        color: #FF3333;
    }
    .risk-mid {
        background: rgba(255, 117, 0, .04);
        color: #FF7500;
    }
    .risk-low {
        background: rgba(247, 181, 0, .04);
        color: #F7B500;
    }
}
.chart-title {
    padding: 10px 0;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
}
.chart-wap {
    width: 1000px;
    margin: 20px auto 0;
    &.wrap-flex {
        display: flex;
        .chart-table {
            width: 485px;
        }
        .wrap-flex-gap {
            margin-top: 0;
            margin-left: 30px;
        }
    }
    .wrap-flex-gap {
        margin-top: 15px;
        width: 485px;
        &.all {
            width: 100%;
        }
    }
    &.border {
        border: 1px solid #ddd;
        box-sizing: border-box;
    }
}
.chart-table {
    border: 1px solid #E5E5E5;
    box-sizing: border-box;
    text-align: center;
    .chart-tr {
        display: flex;
        box-sizing: border-box;
        min-height: 36px;
        &.head {
            background: #F7F9FE;
            font-weight: 600;
            .chart-col.col-title {
                width: 100%;
            }
        }
        &.bold {
            font-weight: 600;
        }
        &:nth-child(2n +2) {
            background: #FCFDFF;
        }
        &.total {
            background: #FFFAF7;
            font-weight: 600;
        }
        &.empty {
            margin: 0 -1px;
            min-height: 37px;
            background-color: var(--white);
            border-top: 1px solid #e5e5e5;
            .chart-col + .chart-col {
                border-left: none;
            }
        }
        &.nodata {
            .chart-col {
                text-align: center;
                width: 100%;
            }
        }
    }
    .chart-body {
        border-top: 1px solid #E5E5E5;
    }
    .chart-tr + .chart-tr {
        border-top: 1px solid #E5E5E5;
    }
    .chart-col {
        padding: 8px 6px;
        width: 25%;
        font-size: 12px;
        line-height: 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        word-break: break-all;
    }
    .chart-col + .chart-col {
        border-left: 1px solid #E5E5E5;
    }
    &.table-col2 {
        .chart-col {
            width: 50%;
        }
    }
    &.table-col3 {
        .chart-col {
            width: 33.3%;
        }
    }
    &.table-col6 {
        .chart-col {
            width: 16.67%;
        }
    }
    &.table-col7 {
        .chart-col {
            width: 14.28%;
        }
    }
    &.table-col10 {
        .chart-col {
            width: 10%;
        }
    }
}
.pillar-box {
    width: 100%;
    height: 258px;
    border: 1px solid #e5e5e5;
    box-sizing: border-box;
    &.all {
        width: 100%;
    }
    .chart-table + & {
        border-top: 0;
    }
}
.risk-tip {
    margin-top: 10px;
    padding: 10px 16px;
    background: rgba(255, 117, 0, .05);
    font-size: 14px;
    line-height: 18px;
    color: #FF5B00;
    font-weight: 500;
    word-break: break-all;
}
:deep(.report-progress) {
    .custom-loading {
        padding: 20px;
        transform: translateY(-4px);
        border-radius: 4px;
        background-color: var(--white);
    }
    .el-dialog__header {
        height: 0;
    }
}
</style>