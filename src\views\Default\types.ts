import type { InjectionKey } from "vue";

export interface IFinanceIndexSheetItem {
    pid: number;
    asid: number;
    statementID: number;
    lineID: number;
    lineName: string;
    lineType: number;
    entryType: number;
    lineNumber: number;
    note: string;
    parentID: number;
    expand: number;
    amount: number;
    initalAmount: number;
    priority: number;
    coumType: number;
}

export enum BannerEnum {
    Free = 2051,
    FreeAgent = 2052,
    Pro = 2053,
    ProAgent = 2054,
    FreeTop = 2071,
    FreeAgentTop = 2072,
    ProTop = 2073,
    ProAgentTop = 2074,
}

export const menuClickKey: InjectionKey<() => void> = Symbol();