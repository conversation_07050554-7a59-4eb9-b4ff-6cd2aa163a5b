<template>
    <el-dialog class="custom-confirm dialogDrag" center width="440px" title="提示" v-model="offsetDisPlay" @closed="handleResetOffset">
        <div class="offset-content" v-dialogDrag>
            <div class="offset-content-main">
                <div class="confirm-label">该凭证已红冲，还需要再次红冲吗？</div>
                <div class="offset-voucher">
                    <span>已红冲凭证：</span>
                    <span class="offset" v-for="(item, index) in offsetList" :key="index">
                        <span :class="{ link: checkPermission(['voucher-canedit']) }" @click="toVoucherPage(item)">
                            {{ formatOffsetName(item) }}
                        </span>
                        <span v-show="index < offsetList.length - 1">，</span>
                    </span>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button mr-20" @click="handleConfirm">继续红冲</a>
                <a class="button" @click="offsetDisPlay = false">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, toRef } from "vue";
import type { IRf } from "../types";
import { checkPermission } from "@/util/permission";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { getUrlSearchParams, globalWindowOpenPage } from "@/util/url";
import { dayjs } from "element-plus";

const emit = defineEmits(["update:modelValue", "confirmOffset", "closed"]);
const offsetDisPlay = ref(false);
const offsetList = ref<IRf[]>([]);
const voucherGroupList = toRef(useVoucherGroupStore(), "voucherGroupList");

const formatOffsetName = (item: IRf) => {
    const date = dayjs(item.vdate).format("YYYYMM");
    const vgName = voucherGroupList.value.find((vg) => vg.id.toString() === item.vgid)?.name ?? "";
    return date + vgName + "-" + item.vnum;
};
const toVoucherPage = (item: IRf) => {
    if (!checkPermission(["voucher-canedit"])) return;
    const url = "/Voucher/VoucherPage?" + getUrlSearchParams({ pid: item.pid, vid: item.vid, from: "offset" });
    globalWindowOpenPage(url, "查看凭证");
    offsetDisPlay.value = false;
};
const handleShowRedVoucher = (rfList: IRf[]) => {
    offsetList.value = rfList ?? [];
    offsetDisPlay.value = true;
};
const handleResetOffset = () => {
    offsetList.value = [];
    emit("closed");
};
const handleConfirm = () => {
    offsetDisPlay.value = false;
    emit("confirmOffset");
};

function close() {
    offsetDisPlay.value = false;
}
defineExpose({ handleShowRedVoucher, close });
</script>

<style lang="less" scoped>
.offset-content {
    .offset-content-main {
        padding: 40px 70px;
        .confirm-label {
            font-size: 16px;
            margin-bottom: 14px;
        }
        .offset-voucher {
            .offset {
                display: inline-block;
            }
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
