export interface ISearchParams {
    period_s: number;
    period_e: number;
    // 这里传递的是code
    sbj_id_s: string;
    sbj_id_e: string;
    sbj_leval_s: number;
    sbj_leval_e: number;
    // 币别
    fcid: number;
    /** 显示辅助核算 */
    assistAccount: number;
    /** 余额为0不显示 */
    IsBalanceZero: number;
    /** 无发生额不显示本期合计、本年累计 */
    hiddenTotal: number;
    /** 显示数量金额 */
    showNumber: number;    
    /** 无发生额且余额为0不显示 */
    NoAmountIncurredAndBalanceZero:number,
    /** 联查总账科目列表 */
    SubjectIDList: string;
}

export interface IRows {
    asub_code: string;
    asub_name: string;
    credit: number;
    debit: number;
    direction: string;
    full_asub_name: string;
    period: string;
    title: string;
    total: number;
}

export interface ItableData {
    total: number;
    rows: Array<IRows>;
}

export interface IFcList {
    asId: number;
    code: string;
    id: number;
    isBaseCurrency: boolean;
    name: string;
    preName: string;
    rate: number;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}
export interface IAsubCodeLength {
    asid:number;
    codeLength:Array<number>;
    firstAsubLength:number;
    firstCodeLength:number;
    forthAsubLength:number;
    forthCodeLength:number;
    preName:string;
    secondAsubLength:number;
    secondCodeLength:number;
    thirdAsubLength:number;
    thirdCodeLength:number;
}
