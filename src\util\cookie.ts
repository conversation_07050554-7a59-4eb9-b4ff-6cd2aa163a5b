import { isLemonClient } from "./lmClient";

export const getCookie = (name: string) => {
    if (isLemonClient()) {
        const d = localStorage.getItem(name);
        if (d) {
            const r = JSON.parse(d);
            if (r.create + r.expires * 24 * 60 * 60 * 1000 > Date.now()) return r.data;
            localStorage.removeItem(name);
            return null;
        }
    }
    const cookieArray = document.cookie.split("; ");
    for (let i = 0; i < cookieArray.length; i++) {
        const cookiePair = cookieArray[i].split("=");
        if (cookiePair[0] === name) {
            return cookiePair[1];
        }
    }
    return null;
};

const isFirefox = /Firefox\/(\d+)\./.exec(navigator.userAgent);

export const setCookie = (name: string, value: string, time: string) => {
    if (isLemonClient()) {
        localStorage.setItem(name, JSON.stringify({ create: Date.now(), data: value, expires: 30 }));
        return;
    }
    /*********用法*********************************/
    //这是有设定过期时间的使用示例：
    //s20是代表20秒
    //h是指小时，如12小时则是：h12
    //d是天数，30天则：d30
    //setCookie("name", "hayden", "s20");
    /**********************************************/
    const strsec = getsec(time);
    const exp = new Date();
    exp.setTime(exp.getTime() + strsec * 1);
    document.cookie = name + "=" + encodeURI(value) + ";expires=" + exp.toUTCString() + ";path=/";
    if (isFirefox) {
        // 火狐的BUG，如果一个Cookie过期一次，那么这个Cookie再次设置，第一次会不生效
        setTimeout(() => {
            document.cookie = name + "=" + encodeURI(value) + ";expires=" + exp.toUTCString() + ";path=/";
        }, 500);
    }
};

function getsec(str: string) {
    const str1 = parseInt(str.substring(1, str.length));
    const str2 = str.substring(0, 1);
    if (str2 == "s") {
        return str1 * 1000;
    } else if (str2 == "h") {
        return str1 * 60 * 60 * 1000;
    } else if (str2 == "d") {
        return str1 * 24 * 60 * 60 * 1000;
    }
    return 0;
}
