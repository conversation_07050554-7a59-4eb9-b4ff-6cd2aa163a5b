<template>
    <el-table
        :class="isErp ? 'erp-table' : ''"
        :data="props.tableData"
        border
        :empty-text="emptyText"
        fit
        stripe
        scrollbar-always-on
        highlight-current-row
        class="custom-table"
        row-key="lineID"
        :row-class-name="setTitleRowStyle"
        @header-dragend="headerDragend"
    >
        <el-table-column 
            label="项目" 
            min-width="280px" 
            align="left" 
            headerAlign="center"
            prop="lineName"
            :width="getColumnWidth(setModule, 'lineName')"
        >
            <template #default="scope">
                <div>
                    <div :class="assertNameClass(scope.row)" :title="scope.row.lineName">
                        <span>{{ scope.row.lineName }}</span>
                    </div>
                </div>
            </template>
        </el-table-column>
        <el-table-column 
            label="行次" 
            min-width="60" 
            align="left" 
            header-align="left" 
            prop="lineNumber" 
            :formatter="rowNumberFormatter"
            :width="getColumnWidth(setModule, 'lineNumber')"
        >
        </el-table-column>
        <el-table-column 
            label="本年累计金额" 
            min-width="349" 
            align="right" 
            header-align="center"
        >
            <el-table-column 
                label="金额" 
                min-width="164" 
                align="right" 
                header-align="right"
                prop="Amount"
                :width="getColumnWidth(setModule, 'Amount')"
            >
                <template #default="scope">
                    <TableAmountItem
                        v-if="scope.row.lineNumber"
                        :lineName="scope.row.lineName"
                        :amount="scope.row.initalAmount==''?'0.00%':scope.row.initalAmount"
                        :formula="calcFormula(scope.row, 0)"
                        :line-number="scope.row.lineNumber"
                        :negative-highlighted="false"

                    ></TableAmountItem>
                    <!-- <div v-if="scope.row.initalAmount == ''" style="position: relative;">
                        <span>0.00%</span>
                    </div> -->
                </template>
            </el-table-column>
            <el-table-column 
                label="同比变化" 
                prop="initalAmountBefore" 
                min-width="164" 
                align="right" 
                header-align="right"
                :width="getColumnWidth(setModule, 'initalAmountBefore')"
            >
            </el-table-column>
        </el-table-column>

        <el-table-column 
            label="本期金额" 
            min-width="348" 
            align="right" 
            header-align="center"
        >
            <el-table-column 
                label="金额" 
                min-width="164" 
                align="right" 
                header-align="right"
                rop="initalAmount"
                :width="getColumnWidth(setModule, 'initalAmount')"
            >
                <template #default="scope">
                    <TableAmountItem
                        v-if="scope.row.lineNumber"
                        :amount="scope.row.initalAmount==''?'0.00%':scope.row.amount"
                        :formula="calcFormula(scope.row, 1)"
                        :line-number="scope.row.lineNumber"
                        :negative-highlighted="false"
                    ></TableAmountItem>
                    <!-- <div v-if="scope.row.initalAmount == ''">
                        <span>0.00%</span>
                    </div> -->
                </template>
            </el-table-column>
            <el-table-column 
                label="同比变化" 
                prop="amountBefore" 
                min-width="164" 
                align="right" 
                header-align="right"
                :width="getColumnWidth(setModule, 'amountBefore')"
            > </el-table-column>
            <el-table-column 
                label="环比变化" 
                prop="amountLastMonth" 
                min-width="164" 
                align="right" 
                header-align="right"
                :resizable="false"
            > </el-table-column>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "FinancialTable";
const isErp = ref(window.isErp);

const props = defineProps<{
    tableData: Array<any>;
    // periodRef: any ,
    // searchInfo: any
    emptyText: string;
}>();

//判定assertnameclass
function assertNameClass(row: any) {
    let className: string = "";
    let sign = [
        "一、",
        "二、",
        "三、",
        "四、",
        "五、",
        "六、",
        "七、",
        "八、",
        "九、",
        "十、",
        "(一)",
        "(二)",
        "(三)",
        "(四)",
        "(五)",
        "(六)",
        "(七)",
        "(八)",
        "(九)",
        "(十)",
        "（一）",
        "（二）",
        "（三）",
        "（四）",
        "（五）",
        "（六）",
        "（七）",
    ];

    sign.forEach((v) => {
        if (row.lineName.indexOf(v) > -1) {
            className = "level1";
        }
    });
    return className ? className : "level2";
}
const hasNumberTitle = (value: string) => {
    const chinaNumber = ["（一）", "（二）", "（三）", "（四）", "（五）", "（六）", "（七）"];
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.indexOf(chinaNumber[i]) > -1) return true;
    }
    return false;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle(data.row.lineName)) {
        return "highlight-title-row";
    }
}
//行次
function rowNumberFormatter(_row: any, _column: any, cellValue: any) {
    if (cellValue === 0) {
        return "";
    } else {
        return cellValue;
    }
}

//
function calcFormula(row: any, columnIndex: number) {
    let formula: string;
    switch (columnIndex) {
        case 0:
            formula = row.note?.split("|")[0].replace(/期末/g, "年初") ?? "";
            break;
        case 1:
            formula = row.note?.split("|")[1] ?? "";
            break;
        default:
            formula = "";
            break;
    }
    return formula;
}

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";

:deep(.note-popover-item) {
    display: block;
}
</style>
