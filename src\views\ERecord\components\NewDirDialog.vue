<template>
    <el-dialog :title="isEdit === 2 ? '编辑' : '新建文件夹'" v-model="props.newDirDialogShow" center width="440px" class="custom-confirm dialogDrag">
        <div class="relate-voucher-content" v-dialogDrag>
            <div class="form-main">
                <div class="form-item">
                    <span class="form-label">文件夹名称：</span>
                    <span class="form-content">
                        <input type="text" autofocus maxlength="30" placeholder="请输入文件夹名称" v-model="newDirName" @input="limitNewDirNameInput" />
                    </span>
                </div>
            </div>
            <div v-if="isErp" class="buttons">
                <a class="button solid-button" @click="newDirConfirm">确认</a>
                <a class="button" @click="() => $emit('reload-table-data')">取消</a>
            </div>
            <div v-else class="buttons">
                <a class="button mr-10" @click="() => $emit('reload-table-data')">取消</a>
                <a class="button solid-button" @click="newDirConfirm">确认</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { getGlobalToken } from "@/util/baseInfo";
import { onMounted } from "vue";

const props = defineProps({
    fid: {
        type: Number,
        required: true,
    },
    newDirDialogShow:{
        type:Boolean,
        default:false
    }
});
const emit = defineEmits<{
    (e: "reload-table-data"): void;
}>();

let newDirName = ref("");
let isEdit = ref(1);
let oldName='';
const isErp=ref(window.isErp);
const editDataInit = (name: string, type: number) => {
    oldName=name;
    newDirName.value = name;
    isEdit.value = type;
};

defineExpose({
    editDataInit,
});


const limitNewDirNameInput=(e:Event)=>{
    if(newDirName.value.length===30) {
        ElNotify({ type: "warning", message: "文件夹名称不可多于30个字" });
    }
}
const newDirConfirm = () => {
    if (newDirName.value.trim() === "") {
        ElNotify({ type: "warning", message: "文件夹名称不能为空" });
        return;
    }else if(newDirName.value.trim()===oldName.trim()) {
        ElNotify({ type: "warning", message: "文件夹名称未发生变化！" });
        return;
    }
    request({
        url: isEdit.value === 2 ? window.jLmDiskHost + "/Services/Doc/ModifyName.ashx" : window.jLmDiskHost + "/Services/Doc/CreateDirectory.ashx",
        method: "post",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        data:
            isEdit.value === 2
                ? { fid: props.fid, name: newDirName.value.trim(), appasid: getGlobalToken() }
                : { fid: props.fid, dir: newDirName.value.trim(), appasid: getGlobalToken() },
    })
        .then((data: any) => {
            if (data.Succeed) {
                ElNotify({ type: "success", message: `文件夹${isEdit.value === 2 ? "名称修改" : "创建"}成功` });
                emit("reload-table-data");
                window.dispatchEvent(new CustomEvent("reloadERecordDirList"));
            } else {
                ElNotify({ type: "warning", message: data.Message });
                emit("reload-table-data");
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "文件夹创建时发生异常" });
        })
        .finally(() => {
            newDirName.value = "";
            isEdit.value = 1;
        });
};
</script>

<style lang="less" scoped>
@import "@/style/ERecord/MainTopBox.less";
</style>
