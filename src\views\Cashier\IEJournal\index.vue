<template>
    <div class="content">
        <div class="title">收支类别日记账</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <Select 
                        v-model="searchInfo.ie_id" 
                        :teleported="false" 
                        style="margin-right: 20px" 
                        :filterable="true"
                        :filter-method="ieTypeFilterMethod"
                    >
                        <ElOption
                            v-for="item in showIETypeList"
                            :key="item.subkey"
                            :value="item.subkey"
                            :label="item.label"
                        ></ElOption>
                    </Select>
                    <DatePicker :clearable="true" v-model:startPid="searchInfo.startPid" v-model:endPid="searchInfo.endPid" />
                    <span class="ml-10 noWrap">账户：</span>
                    <CDAccountFilter
                        v-model:accountOptions="accountOptions"
                        :options="optionsList"
                        v-model:selectedList="selectedList"
                        :maxWidth="168"
                        :showDisabledAccount="showDisabledAccount"
                        :isJump="true"
                    />
                    <span class="ml-10 noWrap">币别：</span>
                    <CurrencyFilter v-model:fcid="searchInfo.fcid" :fcList="fcList"></CurrencyFilter>
                    <a class="button solid-button ml-10" @click="handleSearchClick">查询</a>
                </div>
                <div class="main-tool-right">
                    <el-checkbox class="mr-10" v-model="showAll" label="显示所有信息" />
                    <Dropdown
                        btnTxt="打印列表"
                        class="print"
                        :width="isErp ? 96 : 84"
                        :downlistWidth="isErp ? 96 : 84">
                        <li @click="handlePrint(0,getPrintOrExportParams(),printApi)">直接打印</li>
                        <li @click="printListSettings">打印设置</li>
                    </Dropdown>
                    <a class="button ml-10" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :loading="loading"
                    :use-normal-scroll="true"
                    :columns="columns"
                    :data="tableData"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :scrollbarShow="true"
                    :tableName="setModule"
                >
                    <template #fcName>
                        <el-table-column 
                            label="币别" 
                            min-width="80" 
                            align="left" 
                            header-align="left"
                            prop="fc_name"
                            :width="getColumnWidth(setModule, 'fc_name')"
                        >
                            <template #default="scope">
                                <span>{{ getFcCode(scope.row.description, currentFcid, fcList) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #income>
                        <el-table-column 
                            label="收入（借方）" 
                            :min-width="165" 
                            align="right" 
                            header-align="right"
                            prop="income_standard"
                            :width="getColumnWidth(setModule, 'income_standard')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.income_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #incomeFc>
                        <el-table-column 
                            label="收入（借方）" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'incomeFc')"
                                align="right"
                                header-align="right"
                                prop="incomeFc"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.income) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'income')"
                                align="right"
                                header-align="right"
                                prop="income"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.income_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #expenditure>
                        <el-table-column 
                            label="支出（贷方）" 
                            :min-width="165" 
                            align="right" 
                            header-align="right"
                            prop="expenditure_standard"
                            :width="getColumnWidth(setModule, 'expenditure_standard')"
                        >
                            <template #default="scope">
                                <span>{{ GetNumber(scope.row.expenditure_standard) }}</span>
                            </template>
                        </el-table-column>
                    </template>
                    <template #expenditureFc>
                        <el-table-column 
                            label="支出（贷方）" 
                            header-align="center"
                        >
                            <el-table-column
                                label="原币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditureFc')"
                                align="right"
                                header-align="right"
                                prop="expenditureFc"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.expenditure) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本币"
                                :min-width="90"
                                :width="getColumnWidth(setModule, 'expenditure')"
                                align="right"
                                header-align="right"
                                prop="expenditure"
                            >
                                <template #default="scope">
                                    <span>{{ GetNumber(scope.row.expenditure_standard) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #lineSn>
                        <el-table-column label="日记账序号" min-width="155px" align="left" header-align="left" :resizable="false">
                            <template #default="scope">
                                <span v-if="!scope.row.line_sn_name.startsWith('<a')">{{ scope.row.line_sn_name }}</span>
                                <span class="link" @click="goToDetail(scope.row.line_sn_name,scope.row.created_date)" v-else>
                                    {{ getText(scope.row.line_sn_name) }}
                                </span>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <JouralPrint
    v-model:printDialogShow="printDialogVisible"
    title="日记账打印"
    :printData="printInfo"
    :dir-disabled="showAll"
    :otherOptions="otherOptions"
    @currentPrint="handlePrint(3,getPrintOrExportParams(),printApi)" />
</template>

<script lang="ts">
export default {
    name: "IEJournal",
};
</script>
<script setup lang="ts">
import { ref, reactive, watch, computed, onActivated, onUnmounted, watchEffect, nextTick } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { request } from "@/util/service";
import { usePagination } from "@/hooks/usePagination";
import { ElNotify } from "@/util/notify";
import { setColumns, GetNumber } from "@/views/Cashier/CDJournal/utils";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";
import { Option } from "@/components/SelectCheckbox/types";
import { getATagParams, getText, getFcCode } from "@/views/Cashier/Report/utils";
import type { ISCurrcy } from "@/views/Cashier/Report/types";
import usePrint from "@/hooks/usePrint";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableData, IIETypeList } from "./types";

import Table from "@/components/Table/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import Select from "@/components/Select/index.vue";
import ElOption from "@/components/Option/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import JouralPrint from "@/components/PrintDialog/index.vue";
import { getGlobalLodash } from "@/util/lodash";
import { commonFilterMethod } from "@/components/Select/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import type { IBankAccountItem } from "@/views/Cashier/CDAccount/utils";
import { getShowDisabledAccount } from "@/views/Cashier/CDAccount/utils";
import CDAccountFilter from "@/views/Cashier/components/CDAccountFilter.vue";
import CurrencyFilter from "@/views/Cashier/components/CurrencyFilter.vue";
import { useCDAccountStore } from "@/store/modules/cdAccountList";
import { useCurrencyStore } from "@/store/modules/currencyList";
import { getSearchInfoCD } from "@/views/Cashier/Report/utils";

const _ = getGlobalLodash()
const isErp = ref(window.isErp)
const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const cdAccountStore = useCDAccountStore();
const currencyStore = useCurrencyStore();

const setModule = "IEJournal";
const showAll = ref(false);
const loading = ref(false);
const IETypeList = ref<IIETypeList[]>([]);
const tableData = ref<ITableData[]>([]);
const selectedList = ref<number[]>([]);
const columns = ref<IColumnProps[]>([]);
const optionsList = ref<Array<IBankAccountItem>>([]);
const searchInfo = reactive({
    startPid: "",
    endPid: "",
    ie_id: "",
    CD_IDS: "",
    fcid: -1,
});

watch([() => searchInfo.startPid, () => searchInfo.endPid], ([val1, val2]) => {
    if (val1 + "" === "null") {
        searchInfo.startPid = "";
    }
    if (val2 + "" === "null") {
        searchInfo.endPid = "";
    }
});
const fcList = ref<ISCurrcy[]>([]);
const accountOptions = ref<Option[]>([]);
const getOptionsList = () => {
    optionsList.value = [...cdAccountStore.cdAccountList];
    fcList.value = [...currencyStore.fcListOptions];
    nextTick(() => {
        if (searchInfo.CD_IDS === "ALL") {
            selectedList.value = accountOptions.value.map((i) => i.id);
        } else {
            selectedList.value = searchInfo.CD_IDS.split(",").map((i) => Number(i));
        }
    });
};
const getIETypeList = () => {
    request({ url: "/api/IEType/List" }).then((res: any) => {
        if (res.state != 1000) return;
        IETypeList.value = res.data.rows.filter((item: any) => item.haschild !== 1).map((v: any) => {
            return {
                ...v,
                label: v.value1 + ' - ' + v.value2,
            }
        });
    });
};
const showDisabledAccount = ref(false);
const handleInit = () => {
    const { IE_ID, Date_s, Date_e, CD_IDS, fcid, showDisabled } = route.query;
    if (IE_ID) searchInfo.ie_id = IE_ID as string;
    if (Date_s) searchInfo.startPid = Date_s as string;
    if (Date_e) searchInfo.endPid = Date_e as string;
    if (CD_IDS) searchInfo.CD_IDS = CD_IDS as string;
    if (fcid) searchInfo.fcid = Number(fcid as string);
    if (showDisabled) showDisabledAccount.value = showDisabled === "true";
    Promise.all([getIETypeList(), getOptionsList()]).then(() => {
        handleSearch();
    });
};

const handleSearchClick = () => {
    if (paginationData.currentPage !== 1) {
        paginationData.currentPage = 1;
    } else {
        handleSearch();
    }
};
const currentFcid = ref(-1);
const handleSearch = () => {
    if (searchInfo.startPid === "" || searchInfo.endPid === "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return;
    }
    loading.value = true;
    request({ url: "/api/Journal/PagingListForIE?" + getUrlSearchParams(getParams()) })
        .then((res: any) => {
            columns.value = setColumns(showAll.value, false, true, setModule, searchInfo.fcid > 1);
            if (res.state != 1000) return;
            tableData.value = res.data.rows;
            paginationData.total = res.data.total;
            currentFcid.value = searchInfo.fcid;
        })
        .finally(() => ((loading.value = false), (hasInit = true)));
};

const judgeCanPrintOrExport = () => {
    if (tableData.value.length === 0 || tableData.value.length === 1) {
        ElNotify({ type: "warning", message: "报表数据为空" });
        return false;
    }
    if (searchInfo.startPid == "" || searchInfo.endPid == "") {
        ElNotify({ type: "warning", message: "起止日期不能为空" });
        return false;
    }
    return true;
};

const printApi = computed(() => {
    return "/api/Journal/PrintIETable?ieId="+searchInfo.ie_id;
});
const {
  printDialogVisible,
  printInfo,
  otherOptions,
  updataPritnInfo,
  handlePrint
} = usePrint("joural", printApi.value, {}, true, true ,judgeCanPrintOrExport);
const printListSettings = () => {
    printDialogVisible.value = true;
};
onActivated(() => {
    printInfo.value = updataPritnInfo()
});
const handleExport = () => {
    if (!judgeCanPrintOrExport()) return;
    const params = getPrintOrExportParams();
    globalExport("/api/Journal/ExportIETable?" + getUrlSearchParams(params));
};
const goToDetail = (name: string,created_date:string) => {
    const path = "/Cashier/IEJournal";
    const params = { ...getATagParams(name) };
    params.from = path;
    params.CREATED_DATE = created_date;
    globalWindowOpenPage("/Cashier/JournalPage?" + getUrlSearchParams(params), params.JOURNAL_TYPE === "INCOME" ? "收款凭据" : "付款凭据");
};

watch(showAll, () => {
    //勾选显示所有，默认禁用，横向
    if(showAll.value) printInfo.value.direction = 1
    handleSearch();
});

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    if (!hasInit) return;
    handleSearch();
});

watch(selectedList, () => {
    searchInfo.CD_IDS = getSearchInfoCD(selectedList.value, optionsList.value);
});

const getParams = () => {
    return {
        ie_id: searchInfo.ie_id,
        date_s: searchInfo.startPid,
        Date_e: searchInfo.endPid,
        cdAccIds: searchInfo.CD_IDS,
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
        fcid: searchInfo.fcid,
        showDisableAccount: getShowDisabledAccount("cashReport"),
    };
};
const getPrintOrExportParams = () => {
    return {
        showAll: showAll.value ? 1 : 0,
        ieId: searchInfo.ie_id,
        date_s: searchInfo.startPid,
        date_e: searchInfo.endPid,
        cd_acc_ids: searchInfo.CD_IDS,
        fcid: searchInfo.fcid,
        showDisableAccount: getShowDisabledAccount("cashReport"),
    };
};

const routeQueryParams = ref<any>(null);

let hasInit = false;
onActivated(() => {
    const thisRouteQueryParams = route.query;
    const isEqualRouterParams = _.isEqual(thisRouteQueryParams, routeQueryParams.value);
    if (!isEqualRouterParams) {
        hasInit = false;
        paginationData.currentPage = 1;
        handleInit();
    }
    routeQueryParams.value = thisRouteQueryParams;
});

onBeforeRouteLeave((to, from, next) => {
    routeQueryParams.value = from.query;
    next();
});

onUnmounted(() => {
    routeQueryParams.value = null;
});

const showIETypeList = ref<Array<IIETypeList>>([]);
watchEffect(() => { 
    showIETypeList.value = JSON.parse(JSON.stringify(IETypeList.value));  
});
function ieTypeFilterMethod(value: string) {
    showIETypeList.value = commonFilterMethod(value, IETypeList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/Common.less";
@import "@/style/SelfAdaption.less";
.main-content {
    .main-top {
        padding: 16px 20px;
        .main-tool-left {
            .detail-el-select(140px, 30px);
        }
    }
    .main-center {
        padding: 20px;
        padding-top: 0;
        :deep(.el-table) {
            .el-scrollbar__bar.is-horizontal {
                display: block !important;
            }
        }
    }
}
</style>
