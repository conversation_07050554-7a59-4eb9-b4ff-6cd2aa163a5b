<template>
    <div class="content">
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentSearchText }}</template>
                        <div class="line-item first-item">
                            <div class="line-item-title">科目类别：</div>
                            <div class="line-item-field">
                                <el-select 
                                    v-model="searchInfo.asubType" 
                                    :teleported="false"
                                    :filterable="true"
                                    :filter-method="asubTypeFilterMethod"
                                >
                                    <el-option
                                        v-for="item in showAsubTypeList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">起始科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="startSubjectPickerRef"
                                    asubImgRight="16px"
                                    v-model="searchInfo.sbj_id_s"
                                    :showDisabled="true"
                                    :is-by-id="true"
                                    :defaultMaxWidth="280"
                                />
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">结束科目：</div>
                            <div class="line-item-field">
                                <SubjectPicker
                                    ref="endSubjectPickerRef"
                                    asubImgRight="16px"
                                    v-model="searchInfo.sbj_id_e"
                                    :showDisabled="true"
                                    :is-by-id="true"
                                    :defaultMaxWidth="280"
                                />
                            </div>
                        </div>
                        <div class="line-item">
                            <div class="line-item-title">科目级别：</div>
                            <div class="line-item-field">
                                <el-input-number
                                    v-model="searchInfo.sbj_leval_s"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                                <div class="ml-10 mr-10">至</div>
                                <el-input-number
                                    v-model="searchInfo.sbj_leval_e"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.onlyShowEmptyItemAsub" label="只显示未设置流量项目的科目"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleQuickSearch">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <div class="search-content ml-10">
                        <el-input
                            clearable
                            v-model="searchInfo.searchText"
                            placeholder="输入科目编码/名称搜索"
                            @keydown.enter="handleQuickSearch"
                        />
                        <div class="search-icon" @click.stop.self="handleQuickSearch"></div>
                    </div>
                </div>
                <div class="main-tool-right">
                    <a
                        class="button solid-button large-2 ml-10"
                        v-permission="['cashflowstatement-canedit']"
                        @click="handleOpenCashFlowDialog"
                        >现金等价物设置</a
                    >
                    <a class="button ml-10" v-permission="['cashflowstatement-canedit']" @click="importDialogDisplay = true">导入</a>
                    <a class="button ml-10 mr-10" v-permission="['cashflowstatement-canexport']" @click="handleExport">导出</a>
                    <Dropdown :btnTxt="'打印'" :downlistWidth="102" v-permission="['cashflowstatement-canprint']">
                        <li @click="handlePrint(0,getSearchParams())">当前报表数据</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                </div>
            </div>
            <div class="main-center">
                <Table
                    ref="tableContentRef"
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :data="tableData"
                    :columns="columns"
                    :page-is-show="true"
                    :show-overflow-tooltip="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    :scrollbar-show="true"
                    :row-class-name="formatRowClassName"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    @cell-click="handleCashFlowEntryRowClick"
                    @cell-mouse-enter="handleCellMouseEnter"
                    @cell-mouse-leave="removeTips"
                    :tableName="setModule"
                >
                    <template #mainTableEntry>
                        <el-table-column label="主表项目" align="left" header-align="center">
                            <el-table-column
                                label="流入"
                                min-width="200px"
                                align="left"
                                header-align="center"
                                :show-overflow-tooltip="true"
                                prop="mainInItemId"
                                :width="getColumnWidth(setModule, 'mainInItemId')"
                            >
                                <template #default="scope">
                                    <template v-if="currentCashFlowEntryIndex === scope.row.index && !checkIsCashAsub(scope.row)">
                                        <Select
                                            ref="mainInSelectRef"
                                            v-model="editInfo.mainInItemId"
                                            placeholder=" "
                                            IconClearRight="20px"
                                            :clearable="true"
                                            :filterable="true"
                                            :circleClose="true"
                                            @focus="handleInEdit"
                                            @blur="handleBlur"
                                            :filter-method="mcItemInFilterMethod"
                                        >
                                            <Option
                                                v-for="item in showMCItemListIn"
                                                :key="item.itemId"
                                                :value="item.itemId"
                                                :label="item.itemName"
                                            ></Option>
                                        </Select>
                                    </template>
                                    <template v-else>
                                        <div class="default-cell">
                                            <span>{{ scope.row.mainInItemName || "" }}</span>
                                            <el-icon v-if="!checkIsCashAsub(scope.row) && checkPermission(['cashflowstatement-canedit'])"
                                                ><ArrowDown
                                            /></el-icon>
                                        </div>
                                    </template>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="流出"
                                min-width="200px"
                                align="left"
                                header-align="center"
                                :show-overflow-tooltip="true"
                                prop="mainOutItemId"
                                :width="getColumnWidth(setModule, 'mainOutItemId')"
                                :resizable="accountStandard === AccountStandard.CompanyStandard"
                            >
                                <template #default="scope">
                                    <template v-if="currentCashFlowEntryIndex === scope.row.index && !checkIsCashAsub(scope.row)">
                                        <Select
                                            ref="mainOutSelectRef"
                                            v-model="editInfo.mainOutItemId"
                                            placeholder=" "
                                            IconClearRight="20px"
                                            :clearable="true"
                                            :filterable="true"
                                            :circleClose="true"
                                            @focus="handleInEdit"
                                            @blur="handleBlur"
                                            :filter-method="mcItemOutFilterMethod"
                                        >
                                            <Option
                                                v-for="item in showMCItemListOut"
                                                :key="item.itemId"
                                                :value="item.itemId"
                                                :label="item.itemName"
                                            ></Option>
                                        </Select>
                                    </template>
                                    <template v-else>
                                        <div class="default-cell">
                                            <span>{{ scope.row.mainOutItemName || "" }}</span>
                                            <el-icon v-if="!checkIsCashAsub(scope.row) && checkPermission(['cashflowstatement-canedit'])"
                                                ><ArrowDown
                                            /></el-icon>
                                        </div>
                                    </template>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #secondTableEntry>
                        <el-table-column 
                            label="附表项目" 
                            min-width="200px" 
                            align="left" 
                            header-align="center"
                            prop="subItemId"
                            :width="getColumnWidth(setModule, 'subItemId')"
                            :resizable="false"
                        >
                            <template #default="scope">
                                <template v-if="currentCashFlowEntryIndex === scope.row.index && !checkIsCashAsub(scope.row)">
                                    <Select
                                        ref="subSelectRef"
                                        v-model="editInfo.subItemId"
                                        placeholder=" "
                                        IconClearRight="20px"
                                        :clearable="true"
                                        :filterable="true"
                                        :circleClose="true"
                                        @focus="handleInEdit"
                                        @blur="handleBlur"
                                        :filter-method="subItemFilterMethod"
                                    >
                                        <Option
                                            v-for="item in showSubItemList"
                                            :key="item.itemId"
                                            :value="item.itemId"
                                            :label="item.itemName"
                                        ></Option>
                                    </Select>
                                </template>
                                <template v-else>
                                    <div class="default-cell">
                                        <span>{{ scope.row.subItemName || "" }}</span>
                                        <el-icon v-if="!checkIsCashAsub(scope.row) && checkPermission(['cashflowstatement-canedit'])"
                                            ><ArrowDown
                                        /></el-icon>
                                    </div>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
            </div>
        </div>
    </div>
    <el-dialog
        title="现金等价物设置"
        class="custom-confirm dialogDrag"
        center
        v-model="cashFlowDialogDisplay"
        width="600px"
        @closed="cashFlowDialogHide"
    >
        <div class="cash-flow-dialog-content" v-dialogDrag>
            <div class="cash-flow-dialog-main" @mouseleave="canAddCashAsub = false" @mouseenter="canAddCashAsub = true">
                <Table
                    ref="tableRef"
                    height="200px"
                    add-sub-field="index"
                    :data="cashFlowDialogTableData"
                    :columns="cashFlowDialogColumns"
                    :not-delete-row="minCashAsubLength"
                    :custom-subtract="true"
                    :custom-add="true"
                    :has-add-sub="canAddCashAsub"
                    :use-normal-scroll="true"
                    :first-row-not-show="true"
                    @row-click="handleCashAsubRowClick"
                    @handle-add="handleAddCashAsub"
                    @handle-subtract="handleSubtractCashAsub"
                    :tableName="setModuleDialog"
                    :show-overflow-tooltip="false"
                >
                    <template #asubCode>
                        <el-table-column 
                            label="科目编码" 
                            :min-width="120" 
                            align="left" 
                            header-align="left" 
                            prop="asubCode" 
                            :width="getColumnWidth(setModuleDialog, 'asubCode')"
                        > 
                            <template #default="{ row }">
                                <template
                                    v-if="currentCashAsubIndex === row.index && row.index !== 0 && currentCashAsubLabel === 'asubCode'"
                                >
                                    <SelectV2
                                        v-model="row.asubId"
                                        :options="mapSubjectList"
                                        placeholder=" "
                                        :clearable="true"
                                        :filterable="true"
                                        :toolTipOptions="{ dynamicWidth: true }"
                                        @change="handleSelectAsub"
                                    />
                                </template>
                                <ToolTip
                                    v-else
                                    :content="row.asubCode || ''"
                                    :dynamicWidth="true"
                                    :line-clamp="1"
                                    :teleported="true"
                                    placement="right"
                                    :offset="-8"
                                >
                                    {{ row.asubCode || "" }}
                                </ToolTip>
                            </template>
                        </el-table-column>
                    </template>
                    <template #asubName>
                        <el-table-column label="科目名称" :min-width="240" align="left" header-align="left" prop="asubName" :resizable="false">
                            <template #default="{ row }">
                                <template
                                    v-if="currentCashAsubIndex === row.index && row.index !== 0 && currentCashAsubLabel === 'asubName'"
                                >
                                    <SelectV2
                                        v-model="row.asubId"
                                        :options="showMapSubjectList"
                                        placeholder=" "
                                        :clearable="true"
                                        :filterable="true"
                                        :toolTipOptions="{ dynamicWidth: true }"
                                        @change="handleSelectAsub"
                                        :remote="true"
                                        :filter-method="mapSubjectFilterMethod"
                                        :isSuffixIcon="true"
                                        @visible-change="mapSubjectVisibleChange"
                                    />
                                </template>
                                <ToolTip
                                    v-else
                                    :content="row.asubName || ''"
                                    :dynamicWidth="true"
                                    :line-clamp="1"
                                    :teleported="true"
                                    placement="right"
                                    :offset="-8"
                                >
                                    {{ row.asubName || "" }}
                                </ToolTip>
                            </template>
                        </el-table-column>
                    </template>
                </Table>
                <div class="tip">
                    <img src="@/assets/Icons/warn.png" />
                    <span>库存现金和银行存款默认为现金科目，无需设置哦~</span>
                </div>
            </div>
            <div class="buttons">
                <a class="button solid-button" @click="handleSave">保存</a>
                <a class="button ml-10" @click="handleCancel">取消</a>
            </div>
        </div>
    </el-dialog>
    <ImportSingleFileDialog
        importTitle="现金流量预设导入"
        v-model:import-show="importDialogDisplay"
        importUrl="/api/StandardCashFlowStatement/ImportAsubItemList"
        :downloadTemplate="handleDownloadTemplate"
        :uploadSuccess="uploadSuccess"
    ></ImportSingleFileDialog>
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="现金流量预设打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getSearchParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    // 现金流量预设
    name: "CashflowAssumption",
};
</script>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch, toRef, nextTick, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { useAccountSetStore, AccountSubjectType, AccountStandard } from "@/store/modules/accountSet";
import { usePagination } from "@/hooks/usePagination";
import usePrint from "@/hooks/usePrint";
import { getUrlSearchParams, globalExport, globalPrint } from "@/util/url";
import { ElNotify } from "@/util/notify";
import { useAccountSubjectStore } from "@/store/modules/accountSubject";
// import { ElConfirm } from "@/util/confirm";
import { cloneDeep } from "lodash";
import { checkPermission } from "@/util/permission";

import type { IAsubCodeLength } from "@/views/AccountBooks/SubsidiaryLedger/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IAsubItem, IAsubItemString, IAsubTypeList, IMapTableData, ISearchBack } from "./types";

import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import Table from "@/components/Table/index.vue";
import ImportSingleFileDialog from "@/components/ImportSingleFileDialog/index.vue";
import Select from "@/components/Select/index.vue";
import Option from "@/components/Option/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import SelectV2 from "@/components/SelectV2/index.vue";
import ToolTip from "@/components/ToolTip/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import StatementsPrint from "@/components/PrintDialog/index.vue";

const setModule = "CashFlowAssum";
const setModuleDialog = "CashFlowAssumDialog";
const tableContentRef = ref<InstanceType<typeof Table>>();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const tableRef = ref<InstanceType<typeof Table>>();
const mainInSelectRef = ref<InstanceType<typeof Select>>();
const mainOutSelectRef = ref<InstanceType<typeof Select>>();
const subSelectRef = ref<InstanceType<typeof Select>>();
const startSubjectPickerRef = ref<InstanceType<typeof SubjectPicker>>();
const endSubjectPickerRef = ref<InstanceType<typeof SubjectPicker>>();

const accountStandard = useAccountSetStore().accountSet!.accountingStandard as unknown as AccountStandard;
const subjectList = toRef(useAccountSubjectStore(), "accountSubjectListWithoutDisabled");
const mapSubjectList = computed(() =>
    subjectList.value
        .filter((item) => !cashAsubCode.some((asubCode) => item.asubCode.startsWith(asubCode)))
        .map((item) => ({ value: item.asubId.toString(), label: item.asubCode + " " + item.asubAAName }))
);

const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const maxCodelength = ref(4);
const searchInfo = reactive({
    asubType: 0,
    onlyShowEmptyItemAsub: false,
    sbj_leval_s: 1,
    sbj_leval_e: 1,
    sbj_id_s: "",
    sbj_id_e: "",
    searchText: "",
});

const asubTypeList = computed<Array<IAsubTypeList>>(() => {
    if (accountStandard === AccountStandard.LittleCompanyStandard) {
        return [
            { label: "全部", value: 0 },
            { label: "资产", value: AccountSubjectType.Asset },
            { label: "负债", value: AccountSubjectType.Debit },
            { label: "权益", value: AccountSubjectType.Owe },
            { label: "成本", value: AccountSubjectType.Cost },
            { label: "损益", value: AccountSubjectType.Income },
        ];
    } else if (accountStandard === AccountStandard.CompanyStandard) {
        return [
            { label: "全部", value: 0 },
            { label: "资产", value: AccountSubjectType.Asset },
            { label: "负债", value: AccountSubjectType.Debit },
            { label: "共同", value: AccountSubjectType.Common },
            { label: "权益", value: AccountSubjectType.Owe },
            { label: "成本", value: AccountSubjectType.Cost },
            { label: "损益", value: AccountSubjectType.Income },
        ];
    } else if (accountStandard === AccountStandard.FolkComapnyStandard) {
        return [
            { label: "全部", value: 0 },
            { label: "资产", value: AccountSubjectType.Asset },
            { label: "负债", value: AccountSubjectType.Debit },
            { label: "净资产", value: AccountSubjectType.NetWorth },
            { label: "收入", value: AccountSubjectType.Revenue },
            { label: "费用", value: AccountSubjectType.Expenses },
        ];
    }
    return [{ label: "全部", value: 0 }];
});

const currentSearchText = ref("");
const tableData = ref<Array<IMapTableData>>([]);
const columns = computed<Array<IColumnProps>>(() => {
    const column: Array<IColumnProps> = [
        { label: "科目编码", prop: "asubCode", minWidth: 80, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'asubCode') },
        { label: "科目名称", prop: "asubName", minWidth: 300, align: "left", headerAlign: "left", width: getColumnWidth(setModule, 'asubName') },
        { slot: "mainTableEntry" },
    ];
    if (accountStandard === AccountStandard.CompanyStandard) {
        column.push({ slot: "secondTableEntry" });
    }
    return column;
});
const loading = ref(false);
async function handleLoadTableData() {
    const queryParams = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    const params = getSearchParams();
    loading.value = true;
    await request({
        url: "/api/StandardCashFlowStatement/GetAsubItemPagingList?" + getUrlSearchParams(queryParams),
        method: "post",
        data: params,
    })
        .then((res: IResponseModel<ISearchBack>) => {
            loading.value = false;
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "获取数据失败" });
                return;
            }
            tableData.value = res.data.data.map((item, index) => ({
                ...item,
                index,
                mainInItemId: !item.mainInItemId ? "" : item.mainInItemId.toString(),
                mainOutItemId: !item.mainOutItemId ? "" : item.mainOutItemId.toString(),
                subItemId: !item.subItemId ? "" : item.subItemId.toString(),
            }));
            paginationData.total = res.data.count;
        })
        .catch(() => {
            loading.value = false;
            ElNotify({ type: "warning", message: "获取数据失败" });
        });
}
function checkCanSearch() {
    if (searchInfo.sbj_id_s && searchInfo.sbj_id_e && Number(searchInfo.sbj_id_s) > Number(searchInfo.sbj_id_e)) {
        ElNotify({ message: "亲，起始科目不能大于结束科目哦", type: "warning" });
        return false;
    }
    if (searchInfo.sbj_leval_s > searchInfo.sbj_leval_e) {
        ElNotify({ message: "亲，起始科目级别不能大于结束科目级别哦", type: "warning" });
        return false;
    }
    return true;
}
function handleSearch() {
    if (!checkCanSearch()) return;
    setcurrentSearchText();
    handleClose();
    resetEditInfo();
    handleLoadTableData();
}
function handleQuickSearch() {
    paginationData.currentPage === 1 ? handleSearch() : (paginationData.currentPage = 1);
}
function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.asubType = 0;
    searchInfo.sbj_leval_s = 1;
    searchInfo.sbj_leval_e = 1;
    searchInfo.sbj_id_s = "";
    searchInfo.sbj_id_e = "";
    searchInfo.onlyShowEmptyItemAsub = false;
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "cashflowAssumption",
    "/api/StandardCashFlowStatement/PrintAsubItemList",
    {},
    false,
    false,
);

function handleExport() {
    const params = getSearchParams();
    globalExport("/api/StandardCashFlowStatement/ExportAsubItemList?" + getUrlSearchParams(params));
}

function getAsubCodeLength() {
    request({
        url: `/api/AccountSubject/GetAsubCodeLength`,
        method: "post",
    }).then((res: IResponseModel<IAsubCodeLength>) => {
        maxCodelength.value = res.data.codeLength.length;
    });
}

function setcurrentSearchText() {
    const asubItem = asubTypeList.value.find((item) => item.value === searchInfo.asubType);
    currentSearchText.value = asubItem!.label + "科目";
}

interface ICashAsubBase {
    asubCode: string;
    asubName: string;
    order: number;
}
interface ICashAsubBack extends ICashAsubBase {
    asubId: number;
}
interface ICashAsubList extends ICashAsubBase {
    asubId: string;
    index: number;
}
const cashFlowDialogDisplay = ref(false);
const cashFlowDialogTableData = ref<Array<ICashAsubList>>([]);
const cashFlowDialogColumns = ref<Array<IColumnProps>>([
    { label: "序号", prop: "order", minWidth: 60, align: "left", headerAlign: "left", width: getColumnWidth(setModuleDialog, 'order') },
    { slot: "asubCode" },
    { slot: "asubName" },
]);
let cashFlowCanDisplay = true;
function handleSetCashAsub() {
    if (!cashFlowCanDisplay) return;
    cashFlowCanDisplay = false;
    const asubs = cloneDeep(cashEquivalentList.value);
    const data = asubs.map((item, index) => ({ ...item, asubId: item.asubId.toString(), index }));
    data.length < 10 && data.push(getCashAsubTemplate());
    cashFlowDialogTableData.value = data;
    initTableData();
    cashFlowCanDisplay = true;
}
function handleOpenCashFlowDialog() {
    handleSetCashAsub();
    cashFlowDialogDisplay.value = true;
}
const currentCashAsubIndex = ref(-1);
const currentCashAsubLabel = ref("");
function handleCashAsubRowClick(row: any, column: any) {
    currentCashAsubIndex.value = row.index;
    currentCashAsubLabel.value = column.property;
}
const canAddCashAsub = ref(true);
function getCashAsubTemplate() {
    return { asubId: "", asubCode: "", asubName: "", order: 0, index: 0 };
}
function handleAddCashAsub(index: number) {
    if (cashFlowDialogTableData.value.length >= 10) {
        ElNotify({ type: "warning", message: "亲，最多只能添加10个现金等价物哦~" });
        return;
    }
    const template = getCashAsubTemplate();
    template.order = cashFlowDialogTableData.value[index].order + 1;
    cashFlowDialogTableData.value.splice(index + 1, 0, template);
    initTableData();
}
const minCashAsubLength = 2;
function handleSubtractCashAsub(index: number) {
    if (cashFlowDialogTableData.value.length === minCashAsubLength) {
        cashFlowDialogTableData.value[index].asubId = "";
        cashFlowDialogTableData.value[index].asubCode = "";
        cashFlowDialogTableData.value[index].asubName = "";
        currentCashAsubIndex.value = -1;
        return;
    }
    cashFlowDialogTableData.value.splice(index, 1);
    initTableData();
}
function initTableData() {
    cashFlowDialogTableData.value.forEach((item, i) => ((item.index = i), (item.order = i + 1)));
    tableRef.value?.initEditData(cashFlowDialogTableData.value);
}
function handleSelectAsub(asubId: string) {
    const asubItem = subjectList.value.find((item) => item.asubId.toString() === asubId);
    if (asubItem) {
        cashFlowDialogTableData.value[currentCashAsubIndex.value].asubCode = asubItem.asubCode;
        cashFlowDialogTableData.value[currentCashAsubIndex.value].asubName = asubItem.asubName;
    } else {
        cashFlowDialogTableData.value[currentCashAsubIndex.value].asubCode = "";
        cashFlowDialogTableData.value[currentCashAsubIndex.value].asubName = "";
        cashFlowDialogTableData.value[currentCashAsubIndex.value].asubId = "";
    }
}
function cashFlowDialogHide() {
    cashFlowDialogTableData.value.length = 0;
    currentCashAsubIndex.value = -1;
    currentCashAsubLabel.value = "";
}
let isSaving = false;
function handleSave() {
    if (isSaving) return;
    const params: Array<{ asubId: number; order: number }> = [];
    for (let i = 0; i < cashFlowDialogTableData.value.length; i++) {
        const item = cashFlowDialogTableData.value[i];
        if (!item.asubId || !item.asubCode || !item.asubName || params.some((_) => _.asubId === Number(item.asubId))) continue;
        params.push({ asubId: Number(item.asubId), order: params.length + 1 });
    }
    isSaving = true;
    request({ url: "/api/StandardCashFlowStatement/SaveCashAsub", method: "post", data: params })
        .then(async (res: IResponseModel<boolean>) => {
            isSaving = false;
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "保存失败" });
                return;
            }
            ElNotify({ type: "success", message: "保存成功" });
            handleCancel();
            await handleGetCashEquivalent();
            handleSearch();
        })
        .catch(() => {
            isSaving = false;
            ElNotify({ type: "warning", message: "保存失败" });
        });
}
function handleCancel() {
    cashFlowDialogDisplay.value = false;
}

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    handleSearch();
});

const asubItemList = ref<Array<IAsubItemString>>([]);
const mainCashFlowItemList = ref<Array<IAsubItemString>>([]);
const subCashFlowItemList = ref<Array<IAsubItemString>>([]);
async function handleGetStandardCashFlowStatementItemList() {
    await request({
        url: "/api/StandardCashFlowStatement/GetStandardCashFlowStatementItemList",
        method: "post",
    })
        .then((res: IResponseModel<Array<IAsubItem>>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "获取数据失败" });
                return;
            }
            const formatData = res.data.map((item, index) => ({ ...item, itemId: item.itemId.toString(), isBusinessAsub: index < 7 }));
            asubItemList.value = formatData;
            mainCashFlowItemList.value = formatData.filter((item) => item.itemType === 1);
            subCashFlowItemList.value = formatData.filter((item) => item.itemType === 2);
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "获取数据失败" });
        });
}

const cashEquivalentList = ref<Array<ICashAsubBack>>([]);
async function handleGetCashEquivalent() {
    await request({
        url: "/api/StandardCashFlowStatement/GetCashAsubList",
        method: "post",
    })
        .then((res: IResponseModel<Array<ICashAsubBack>>) => {
            if (res.state !== 1000) {
                ElNotify({ type: "warning", message: res.msg || "获取数据失败" });
                return;
            }
            cashEquivalentList.value = res.data;
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "获取数据失败" });
        });
}
const cashAsubCode = ["1001", "1002"];
const unEditAsubList = computed(() => {
    let disabledList: string[] = [];
    const cashEquivalentAsubCode = cashEquivalentList.value.map((item) => item.asubCode);
    disabledList = [...cashAsubCode, ...cashEquivalentAsubCode];
    return disabledList;
});
function checkIsCashAsub(row: IMapTableData) {
    return unEditAsubList.value.some((item) => row.asubCode?.startsWith(item));
}
function formatRowClassName({ row }: { row: IMapTableData }) {
    if (checkIsCashAsub(row)) return "disabled";
    return "";
}

function handleCellMouseEnter(row: IMapTableData, cloumn: any, cell: any, event: any) {
    if (!checkIsCashAsub(row)) return;
    createTips(event, row.asubId);
}

onMounted(async () => {
    getAsubCodeLength();
    handleReset();
    await handleGetStandardCashFlowStatementItemList();
    await handleGetCashEquivalent();
    handleSearch();
});

const importDialogDisplay = ref(false);
function handleDownloadTemplate() {
    globalExport("/api/StandardCashFlowStatement/ExportAsubItemImportTemplate");
}
function uploadSuccess(res: IResponseModel<boolean>) {
    if (res.state !== 1000) {
        ElNotify({ type: "warning", message: res.msg || "导入失败" });
        return;
    }
    ElNotify({ type: "success", message: "导入成功" });
    importDialogDisplay.value = false;
    handleQuickSearch();
}

function getSearchParams() {
    return {
        searchText: searchInfo.searchText || "",
        asubType: searchInfo.asubType,
        startAsubId: searchInfo.sbj_id_s ? Number(searchInfo.sbj_id_s) : 0,
        endAsubId: searchInfo.sbj_id_e ? Number(searchInfo.sbj_id_e) : 0,
        startAsubLevel: searchInfo.sbj_leval_s,
        endAsubLevel: searchInfo.sbj_leval_e,
        onlyShowEmptyItemAsub: searchInfo.onlyShowEmptyItemAsub,
    };
}

const currentCashFlowEntryIndex = ref(-1);
function handleCashFlowEntryRowClick(row: IMapTableData, column: any) {
    if (!checkPermission(["cashflowstatement-canedit"])) return;
    if (row.index !== currentCashFlowEntryIndex.value && currentCashFlowEntryIndex.value !== -1) {
        allowBlur = false;
        handleSubmit();
        return;
    }
    if (row.index === currentCashFlowEntryIndex.value) return;
    if (checkIsCashAsub(row)) return;
    const { label } = column;
    if (!["流入", "流出", "附表项目"].includes(label)) return;
    editInfo.mainInItemId = row.mainInItemId;
    editInfo.mainOutItemId = row.mainOutItemId;
    editInfo.subItemId = row.subItemId;
    cacheLastParams.mainInItemId = row.mainInItemId;
    cacheLastParams.mainOutItemId = row.mainOutItemId;
    currentCashFlowEntryIndex.value = row.index;
    nextTick().then(() => {
        if (label === "流入") {
            mainInSelectRef.value?.focus();
        } else if (label === "流出") {
            mainOutSelectRef.value?.focus();
        } else if (label === "附表项目") {
            subSelectRef.value?.focus();
        }
    });
}
// function handleMainInItemIdChange(id: string) {
//     const cashItem = mainCashFlowItemList.value.find((item) => item.itemId === id);
//     if (cashItem) {
//         const difference = !!editInfo.mainOutItemId && id !== editInfo.mainOutItemId;
//         if (difference) {
//             handleDifferenceWithInAndOut("in", cacheLastParams.mainInItemId);
//         } else {
//             cacheLastParams.mainInItemId = id;
//         }
//     } else {
//         editInfo.mainInItemId = "";
//         cacheLastParams.mainInItemId = "";
//     }
// }
// function handleMainOutItemIdChange(id: string) {
//     const cashItem = mainCashFlowItemList.value.find((item) => item.itemId === id);
//     if (cashItem) {
//         const difference = !!editInfo.mainInItemId && id !== editInfo.mainInItemId;
//         if (difference) {
//             handleDifferenceWithInAndOut("out", cacheLastParams.mainOutItemId);
//         } else {
//             cacheLastParams.mainInItemId = id;
//         }
//     } else {
//         editInfo.mainOutItemId = "";
//         cacheLastParams.mainOutItemId = "";
//     }
// }
// function handleDifferenceWithInAndOut(change: "in" | "out", lastValue: string) {
//     handleInEdit();
//     function handleChangeItemId(confirm: boolean) {
//         if (confirm) {
//             change === "in" ? (editInfo.mainOutItemId = editInfo.mainInItemId) : (editInfo.mainInItemId = editInfo.mainOutItemId);
//         } else {
//             change === "in" ? (editInfo.mainInItemId = lastValue) : (editInfo.mainOutItemId = lastValue);
//         }
//         change === "in" ? mainInSelectRef.value?.blur() : mainOutSelectRef.value?.blur();
//         confirm && handleSubmit();
//         handleInEdit();
//     }
//     ElConfirm("您已按流入和流出分别设置了主表项目，修改后将统一应用到流入和流出项目，是否修改？", false, () => {}, "提示", {
//         confirmButtonText: "是",
//         cancelButtonText: "否",
//     }).then((r: boolean) => {
//         handleChangeItemId(r);
//     });
// }
let isEditing = false;
let allowBlur = true;
const editInfo = reactive({
    mainInItemId: "",
    mainOutItemId: "",
    subItemId: "",
});
const cacheLastParams = reactive({
    mainInItemId: "",
    mainOutItemId: "",
});
function resetEditInfo() {
    currentCashFlowEntryIndex.value = -1;
    editInfo.mainInItemId = "";
    editInfo.mainOutItemId = "";
    editInfo.subItemId = "";
    isEditing = false;
    allowBlur = true;
}
function handleInEdit() {
    const timer = setTimeout(() => {
        isEditing = true;
        clearTimeout(timer);
    });
}
function handleBlur() {
    isEditing = false;
    const timer = setTimeout(() => {
        clearTimeout(timer);
        if (isEditing || !allowBlur) return;
        handleSubmit();
    }, 250);
}
let isRequesting = false;
function handleSubmit() {
    if (isRequesting) return;
    const row = tableData.value[currentCashFlowEntryIndex.value];
    if (!row) return;
    const params = {
        asubId: row.asubId,
        mainInItemId: !editInfo.mainInItemId ? 0 : Number(editInfo.mainInItemId),
        mainOutItemId: !editInfo.mainOutItemId ? 0 : Number(editInfo.mainOutItemId),
        subItemId: !editInfo.subItemId ? 0 : Number(editInfo.subItemId),
    };
    isRequesting = true;
    request({ url: "/api/StandardCashFlowStatement/SaveAsubItem", method: "post", data: params })
        .then((res: IResponseModel<boolean>) => {
            isRequesting = false;
            if (res.state !== 1000 || !res.data) {
                ElNotify({ type: "warning", message: res.msg || "保存失败" });
                return;
            }
            ElNotify({ type: "success", message: "保存成功" });
            handleSearch();
        })
        .catch(() => {
            isRequesting = false;
            ElNotify({ type: "warning", message: "保存失败" });
        });
}

let tipDoom: any = null;
function createTips(el: any, id: number) {
    const tooltipDom = document.createElement("div");
    const domWidth = 300;
    const offsetWidth = 20;
    const viewportWidth = window.innerWidth + offsetWidth;
    let left = el.clientX + domWidth > viewportWidth ? viewportWidth - domWidth - offsetWidth : el.clientX;
    const cell = el.target.querySelector(".cell.el-tooltip");
    const overflow = cell.scrollWidth > cell.clientWidth;
    const {width, left: _left} = cell.getBoundingClientRect();
    if (left + domWidth > _left + width && overflow) {
        left = _left + width - domWidth;
    }
    tooltipDom.style.cssText = `
        display: inline-block;
        position: absolute;
        top: ${el.clientY + 5}px;
        left: ${left}px;
        padding: 10px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid var(--border-color);
        border-radius: 3px;
        text-align: left;
        line-height: 12px;
        background-color: var(--white);
        z-index: 99;
      `;
    tooltipDom.innerHTML = "现金及现金等价物的科目无需设置主附表项目哦~";
    tooltipDom.setAttribute("id", `tooltip-${id}`);
    tipDoom = tooltipDom;
    document.body.appendChild(tooltipDom);
}
function removeTips() {
    if (tipDoom) {
        document.body.removeChild(tipDoom);
        tipDoom = null;
    }
}

const showMCItemListIn = ref<Array<any>>([]);
const showMCItemListOut = ref<Array<any>>([]);
const showSubItemList = ref<Array<any>>([]);
const showMapSubjectList = ref<Array<any>>([]);
const showAsubTypeList = ref<Array<IAsubTypeList>>([]);
watchEffect(() => {
    showMCItemListIn.value = JSON.parse(JSON.stringify(mainCashFlowItemList.value));
    showMCItemListOut.value = JSON.parse(JSON.stringify(mainCashFlowItemList.value));
    showSubItemList.value = JSON.parse(JSON.stringify(subCashFlowItemList.value));
    showMapSubjectList.value = JSON.parse(JSON.stringify(mapSubjectList.value));
    showAsubTypeList.value = JSON.parse(JSON.stringify(asubTypeList.value));
});
function mcItemInFilterMethod(value: string) {
    showMCItemListIn.value = commonFilterMethod(value, mainCashFlowItemList.value, 'itemName');
}
function mcItemOutFilterMethod(value: string) {
    showMCItemListOut.value = commonFilterMethod(value, mainCashFlowItemList.value, 'itemName');
}
function subItemFilterMethod(value: string) {
    showSubItemList.value = commonFilterMethod(value, subCashFlowItemList.value, 'itemName');
}
function mapSubjectFilterMethod(value: string) {
    showMapSubjectList.value = commonFilterMethod(value, mapSubjectList.value, 'label');
}
function mapSubjectVisibleChange(visible: boolean) {
    if (!visible) {
        showMapSubjectList.value = JSON.parse(JSON.stringify(mapSubjectList.value));
    }
}
function asubTypeFilterMethod(value: string) {
    showAsubTypeList.value = commonFilterMethod(value, asubTypeList.value, 'label');
}
</script>

<style lang="less" scoped>
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.main-center {
    flex: 1 !important;
    position: relative;
    :deep(.custom-table) {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table {
            input[type="text"] {
                border: none;
            }
        }
        .el-select {
            width: 100%;
            .el-input__wrapper {
                padding-right: 4px;
                .el-icon {
                    margin: 0;
                }
            }
            .el-input__suffix {
                margin-left: 18px;
            }
        }
        .el-table__row.disabled {
            .cell {
                color: #aaa !important;
            }
        }
        .default-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            & > span {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .el-icon {
                margin-right: 4px;
            }
        }
    }
    .cell-tip {
        position: absolute;
        padding: 10px;
        font-size: 12px;
        font-weight: 500;
        border: 1px solid var(--border-color);
        border-radius: 3px;
        text-align: left;
        line-height: 12px;
        background-color: var(--white);
        z-index: 99;
    }
}
.line-item-field {
    :deep(.el-select) {
        width: 298px;
    }
}
.search-content {
    position: relative;
    :deep(.el-input) {
        .el-input__suffix {
            position: absolute;
            right: 26px;
        }
    }
}
.search-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    height: 16px;
    width: 15px;
    background: url("@/assets/Icons/search-icon.png") no-repeat;
    background-size: 15px 16px;
    cursor: pointer;
}
.cash-flow-dialog-content {
    :deep(.el-table) {
        .el-table__empty-block {
            min-height: 260px;
        }
    }
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
.cash-flow-dialog-main {
    padding: 20px;
    padding-bottom: 0;
    :deep(.el-table) {
        input[type="text"] {
            border: none;
        }
        .el-select {
            width: 100%;
        }
    }
    .tip {
        display: flex;
        align-items: center;
        padding: 10px 0;
        font-size: var(--h5);
        color: #aaa;
        span {
            margin-left: 5px;
        }
    }
}
</style>
