<template>
    <el-dialog v-model="batchAddAsubsDialogShow" title="请选择要添加的科目" width="798px" class="batch-add-asubs-dialog custom-confirm dialogDrag">
        <div class="dialog-content" v-dialogDrag>
            <div class="batch-add-asubs-dialog-container">
                <div class="left-container">
                    <el-radio-group class="asub-tabs" v-model="activeName" style="margin-bottom: 30px">
                        <el-radio-button v-for="(item, index) in tabList" :label="item.name" :key="index">{{ item.label }}</el-radio-button>
                    </el-radio-group>
                    <div class="asub-table">
                        <Table
                            class="subject-table"
                            ref="accountSubjectTableRef"
                            :data="tableData[activeName].data"
                            :columns="Columns"
                            :fit="true"
                            height="280"
                            row-key="asubId"
                            :scrollbar-show="true"
                            :highlight-current-row="false"
                            :show-overflow-tooltip="true"
                            @selection-change="tableSelectionChange"
                            @row-click="tableRowClick"
                            :tableName="setModule"
                        >
                            <template #selection>
                                <el-table-column type="selection" width="30" align="center" header-align="left" :reserve-selection="true" />
                            </template>
                        </Table>
                    </div>
                </div>
                <div class="right-container">
                    <Table
                        class="subject-table-right"
                        :data="tableRightList"
                        :columns="rightColumn"
                        height="280"
                        :scrollbar-show="true"
                        :highlight-current-row="false"
                        :show-overflow-tooltip="true"
                    >
                        <template #checkedList>
                            <el-table-column label="已选择的科目" width="auto" align="left" header-align="center" :resizable="false">
                                <template #default="scope">
                                    <div>{{ scope.row.asubCode }}&nbsp;{{ scope.row.asubName }}</div>
                                    <img
                                        src="@/assets/Statements/remove-btn.png"
                                        class="delete-btn"
                                        @click="removeCheckAsub(scope.row, scope.row.asubId)"
                                    />
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                </div>
            </div>
            <div class="buttons erp-buttons">
                <a class="button solid-button mr-10" @click="asubAddSure">确定</a>
                <a
                    class="button"
                    @click="
                        batchAddAsubsDialogShow = false;
                        resetDialog();
                    "
                    >取消</a
                >
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { AccountStandard, useAccountSetStore } from "@/store/modules/accountSet";
import Table from "@/components/Table/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ITableTreeData, ITableDataItem, ITabListItem } from "@/views/Settings/AccountSubject/types";
import { getAccountSubjectTabList, asubTypeCode } from "@/views/Settings/AccountSubject/utils";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "CusStateAddAsubject";
const props = defineProps({
    modelValue: {
        type: Boolean,
        required: true,
    },
});
const emits = defineEmits(["update:modelValue", "addAsubSure"]);
const batchAddAsubsDialogShow = computed({
    get() {
        return props.modelValue;
    },
    set(value) {
        emits("update:modelValue", value);
    },
});
const tabList = ref<ITabListItem[]>([]);
let tableData = reactive<any>({
    aasj: {
        data: [],
        state: "astate",
    },
    dasj: {
        data: [],
        state: "dstate",
    },
    cmasj: {
        data: [],
        state: "cmstate",
    },
    oasj: {
        data: [],
        state: "ostate",
    },
    casj: {
        data: [],
        state: "cstate",
    },
    iasj: {
        data: [],
        state: "istate",
    },
    nasj: {
        data: [],
        state: "nastate",
    },
    resj: {
        data: [],
        state: "restate",
    },
    exsj: {
        data: [],
        state: "exstate",
    },
});
const Columns = ref<IColumnProps[]>([
    { slot: "selection", width: 36, headerAlign: "center", align: "center" },
    { 
        label: "科目编码", 
        prop: "asubCode", 
        minWidth: 60, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "asubCode")
    },
    { 
        label: "科目名称", 
        prop: "asubName", 
        minWidth: 60, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "asubName") 
    },
    { 
        label: "助记码", 
        prop: "acronym", 
        minWidth: 60, 
        align: "left", 
        headerAlign: "left",
        width: getColumnWidth(setModule, "acronym")  
    },
    {
        label: "余额方向",
        prop: "direction",
        align: "left",
        minWidth: 40,
        headerAlign: "left",
        formatter: (row: any) => {
            if (row.direction == 1) {
                return "借";
            } else if (row.direction == 2) {
                return "贷";
            } else {
                return "未知";
            }
        },
        width: getColumnWidth(setModule, "direction")
    },
    {
        label: "状态",
        prop: "status",
        align: "left",
        minWidth: 35,
        headerAlign: "left",
        formatter: (row: any) => {
            if (row.status == 0) {
                return "启用";
            } else if (row.status == 1) {
                return "不启用";
            } else {
                return "未知";
            }
        },
        resizable: false,
    },
]);

tabList.value = getAccountSubjectTabList();

const activeName = ref("aasj");
let tableAllData: ITableTreeData | { [key: string]: ITableDataItem[] } = reactive({});
let loading = ref<boolean>(false);
function getTableData(searchInfo?: string) {
    loading.value = true;
    request({
        url: `/api/AccountSubject/Tree?asType=20&showDisabled=true&searchInfo=${searchInfo || ""}`,
        method: "get",
    }).then((res: IResponseModel<ITableTreeData>) => {
        tableAllData = {};
        loading.value = false;
        if (res.state === 1000) {
            tableAllData = res.data;
            tableData[activeName.value].data = (tableAllData as any)[activeName.value];
        }
    });
}
getTableData();
watch(activeName, () => {
    tableData[activeName.value].data = (tableAllData as any)[activeName.value];
});
const tableRightList = ref<ITableDataItem[]>([]);
const rightColumn = ref([{ slot: "checkedList" }]);
const accountSubjectTableRef = ref();
const tableSelectionChange = (currentRow: ITableDataItem[]) => {
    tableRightList.value = currentRow;
};
const tableRowClick = (row: ITableDataItem, column: any, event: any) => {
    let selected = tableRightList.value.some((item: ITableDataItem) => item.asubId === row.asubId);
    accountSubjectTableRef.value?.getTable()?.toggleRowSelection(row, !selected);
};
const removeCheckAsub = (row: ITableDataItem, asubId: number) => {
    tableRightList.value = tableRightList.value.filter((v: ITableDataItem) => v.asubId !== asubId);
    accountSubjectTableRef.value?.getTable()?.toggleRowSelection(row, false);
};
const asubAddSure = () => {
    batchAddAsubsDialogShow.value = false;
    emits("addAsubSure", tableRightList.value);
    resetDialog();
};
const resetDialog = () => {
    activeName.value = "aasj";
    accountSubjectTableRef.value?.getTable()?.clearSelection();
};
</script>

<style lang="less" scoped>
.batch-add-asubs-dialog {
    .batch-add-asubs-dialog-container {
        margin-bottom: 15px;
        .left-container {
            display: inline-block;
            vertical-align: top;
            margin-right: 16px;
            text-align: left;
            margin-top: 15px;
            width: 546px;
            .asub-tabs {
                font-size: 0;
                overflow: hidden;
                border-radius: 2px;
                display: inline-block;
                vertical-align: top;
                &.el-radio-group {
                    margin-bottom: 0 !important;
                }
            }
            .asub-table {
                margin-top: 8px;
                .table.subject-table {
                    border-bottom: none;
                }
                :deep(.el-table__inner-wrapper) {
                    height: 278px !important;
                    border-bottom: 1px solid var(--el-border-color-lighter);
                }
            }
        }
        .right-container {
            display: inline-block;
            vertical-align: top;
            text-align: left;
            width: 204px;
            margin-top: 55px;
            height: 280px;
            // border: 1px solid var(--table-border-color);
            box-sizing: border-box;
            .subject-table-right {
                :deep(.el-table__empty-block) {
                    width: 100% !important;
                    min-height: 0;
                }
                :deep(.cell) {
                    div {
                        display: inline-block;
                        max-width: 163px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        vertical-align: top;
                    }
                    .delete-btn {
                        background-size: 100%;
                        background-repeat: no-repeat;
                        width: 12px;
                        height: 12px;
                        float: right;
                        cursor: pointer;
                        margin-top: 2.5px;
                        text-align: center;
                    }
                }
            }
        }
    }
}
</style>
