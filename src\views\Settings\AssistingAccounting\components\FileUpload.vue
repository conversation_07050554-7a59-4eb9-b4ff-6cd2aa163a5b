<template>
    <div class="upload-box">
        <div class="title">批量上传辅助核算项目</div>
        <div class="help-main">
            <div style="margin-top: 60px">
                <a v-show="!isErp && !isHideBarcode" class="help" @click="globalWindowOpen('https://help.ningmengyun.com/#/jz/videoPlayer?qType=130270120')">
                    不会操作？点此观看视频
                </a>
            </div>
            <div class="help-title mt-20">
                <div class="first-step">
                    <p>1.请选择下面任意一种方式导入{{aaTypeName}}</p>
                    <p class="mt-10 ml-10">(1)在{{ aaTypeName }}辅助核算列表导出所需数据，确认后直接导入</p>
                    <p class="mt-10 ml-10">(2)点击下载模板，按照模板格式进行数据整理再导入</p>
                </div>
            </div>
            <div class="downLoad mt-10">
                <a class="link ml-10" @click="downLoad">下载模板</a>
            </div>
            <div class="help-title mt-20">2.选择文件导入</div>
            <div class="mt-10">
                <label class="file-button">
                    <input @change="onFileSelected($event)" ref="selectedFileRef" type="file" accept=".xls,.xlsx" />
                    <a class="link ml-10">选取文件</a>
                </label>
                <span class="ml-20 file-name">{{ fileName }}</span>
            </div>
            <div class="buttons mt-10" style="border-top: none">
                <a class="button solid-button" @click="uploadFile">{{ btnText }}</a>
                <a class="button ml-10" @click="cancelHandle">取消</a>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { request } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { globalExport, globalWindowOpen } from "@/util/url";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";

const props = defineProps<{ aaType: number; aaTypeName: string }>();
const fileName = ref("");
const selectedFile = ref<File | null>(null);
const selectedFileRef = ref();
let isUploading = ref(false);
const isErp = ref(window.isErp);
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const btnText = ref(isErp.value ? "导入" : "上传");
const emit = defineEmits(["cancelImport", "successImport"]);

const fileClear = () => {
    fileName.value = "";
    selectedFile.value = null;
    if (selectedFileRef.value) selectedFileRef.value!.value = "";
};


const onFileSelected = (event: Event) => {
    if (isUploading.value) {
        ElNotify({ type: "warning", message: "上传中，请稍后..." });
        return;
    }
    const input = event.target as HTMLInputElement;
    const file: File = (input.files as FileList)[0];
    if (!file) {
        fileClear();
        return;
    }
    fileName.value = file.name;
    selectedFile.value = file;
};
const lockUpload = () => {
    isUploading.value = true;
    btnText.value = isErp.value ? "导入中..." : "上传中...";
};
const unlockUpload = () => {
    isUploading.value = false;
    btnText.value = isErp.value ? "导入" : "上传";
};
const uploadFile = () => {
    if (isUploading.value) {
        ElNotify({ type: "warning", message: "上传中请稍后..." });
        return;
    }
    if (!selectedFile.value) {
        ElNotify({ type: "warning", message: "请选择文件" });
        return;
    }
    lockUpload();
    const formData = new FormData();
    formData.append("file", selectedFile.value);
    const baseUrlPath = "/api/AssistingAccounting/" + getUrlPath() + "Import";
    const url = props.aaType > 10007 ? baseUrlPath + "?aaType=" + props.aaType : baseUrlPath;
    request({
        url,
        data: formData,
        method: "post",
        headers: { "Content-Type": "multipart/form-data" },
    })
        .then((result: any) => {
            if (result.state === 1000) {
                if (result.data == "ERPY") {
                    ElNotify({ type: "success", message: "导入成功，部分项目的部门或负责人不存在，导入后部门或负责人默认为空。" });
                    cancelHandle();
                    emit("successImport");
                } else if (result.data == "Y") {
                    ElNotify({ type: "success", message: "导入成功。" });
                    cancelHandle();
                    emit("successImport");
                }
            } else if (result.state === 2000) {
                const res: string = result.msg;
                if (res == "ERPY") {
                    ElNotify({ type: "success", message: "导入成功，部分项目的部门或负责人不存在，导入后部门或负责人默认为空。" });
                    cancelHandle();
                    emit("successImport");
                } else if (res == "nofile") {
                    ElNotify({ type: "warning", message: "请选择文件！" });
                } else if (res == "error") {
                    ElNotify({ type: "warning", message: "文件格式不正确，请检查您的文件！" });
                } else if (res == "checkFile") {
                    ElNotify({ type: "warning", message: "亲，请使用正确的导入模板导入！" });
                } else if (res == "stockType") {
                    ElNotify({ type: "warning", message: "亲，请输入已有的存货类别！" });
                } else if (res == "REPEAT") {
                    ElNotify({ type: "warning", message: "您的辅助核算项目中存在重复的编号！请修正后再次导入！" });
                } else if (res == "LENGTH") {
                    ElNotify({ type: "warning", message: (props.aaTypeName || "") + "编码长度最多18位，请修改后再导入哦~" });
                } else if (res == "NameLength") {
                    ElNotify({ type: "warning", message: (props.aaTypeName || "") + "名称长度最多256位，请修改后再导入哦~" });
                } else if (res == "NULL") {
                    ElNotify({ type: "warning", message: "你的数据不正确或存在辅助核算项目编码或名称为空的数据！" });
                } else if (res.includes("SNFomatErr")) {
                    if (res.indexOf("_") > -1) {
                        const line = res.split("_")[1];
                        ElNotify({
                            type: "warning",
                            message: `第${line}行，${props.aaTypeName || ""}编码不是数字和字母组合，请修改后重试~`,
                        });
                    } else {
                        ElNotify({ type: "warning", message: "你的数据中核算条目编码不是由数字和字母组成，请修正后重试！" });
                    }
                } else if (res == "typeerror") {
                    ElNotify({ type: "warning", message: "您的数据包含换行符'\\\"等特殊字符！请修正后重试！" });
                } else if (res == "N") {
                    ElNotify({ type: "warning", message: "导入失败,请检查您的数据！" });
                } else if (res == "NoteLength") {
                    ElNotify({ type: "warning", message: "备注长度最多1024位，请修改后再导入哦~" });
                } else if (res.indexOf("duplicateAANum") > -1) {
                    ElNotify({ type: "warning", message: res.split("|")[1] });
                } else if (res !== "") {
                    ElNotify({ type: "warning", message: res });
                }
                if (res.length === 0) {
                    ElNotify({ type: "warning", message: "对不起,未知错误！" });
                }
            }
        })
        .catch(() => {
            ElNotify({ type: "warning", message: "出错了，请使用模板再次尝试！" });
        })
        .finally(() => {
            setTimeout(() => {
                unlockUpload();
            }, 500);
        });
};
const cancelHandle = () => {
    fileClear();
    emit("cancelImport");
};
const downLoad = () => {
    let urlPath = "";
    switch (props.aaType) {
        case 10001: {
            urlPath = "CustomerImportTemplate";
            break;
        }
        case 10002: {
            urlPath = "VendorExportTemplate";
            break;
        }
        case 10003: {
            urlPath = "EmployeeExportTemplate";
            break;
        }
        case 10004: {
            urlPath = "DepartmentExportTemplate";
            break;
        }
        case 10005: {
            urlPath = "ProjectExportTemplate";
            break;
        }
        case 10006: {
            urlPath = "StockExportTemplate";
            break;
        }
        default: {
            urlPath = "CustomExportTemplate?aaType=" + props.aaType;
            break;
        }
    }
    globalExport("/api/AssistingAccounting/" + urlPath);
};
const getUrlPath = () => {
    let urlPath = "";
    switch (props.aaType) {
        case 10001: {
            urlPath = "Customer";
            break;
        }
        case 10002: {
            urlPath = "Vendor";
            break;
        }
        case 10003: {
            urlPath = "Employee";
            break;
        }
        case 10004: {
            urlPath = "Department";
            break;
        }
        case 10005: {
            urlPath = "Project";
            break;
        }
        case 10006: {
            urlPath = "Stock";
            break;
        }
        default: {
            urlPath = "Custom";
            break;
        }
    }
    return urlPath;
};
</script>

<style lang="less" scoped>
.first-step {
    text-align: left;
    > p {
        padding: 0;
        left: 0;
    }
}
.upload-box {
    width: 1000px;
    background-color: var(--white);
    margin: 0 auto;
    padding-bottom: 60px;
    font-size: var(--font-size);
    .help-main {
        overflow: hidden;
        div {
            display: flex;
            box-sizing: border-box;
            padding-left: 272px;
            align-items: center;
            &:last-child {
                padding: 0px;
                display: block;
            }
        }
        a {
            &.help {
                color: var(--red);
                &:hover {
                    text-decoration: underline;
                }
            }
        }
        .file-name {
            display: inline-block;
            max-width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
