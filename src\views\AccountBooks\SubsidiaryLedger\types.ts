export interface ITableData {
    v_date: string;
    vg_name: string;
    asub_code: string;
    asub_name: string;
    asub_names: string;
    description: string;
    debit: number;
    debit_price?: number;
    debit_qut?: number;
    credit: number;
    credit_price?: number;
    credit_qut?: number;
    direction: string;
    total: number;
    total_price?: number;
    total_qut?: number;
    contrastsubject: string;
    infoType: number;
    p_id: number;
    v_id: number;
}

export interface IResTableData {
    rows: Array<ITableData>;
    total: number;
    unit: string;
    aa_names: string;
}
// tree节点
export interface ITree {
    id: string;
    text: string;
    attributes: ITreeAttributes;
    children: ITree[];
}
export interface ITreeAttributes {
    aaname: string;
    acronym: string;
    code: string;
    ifassist: string;
    ifnum: string;
    name: string;
}

// 请求传递的参数
export interface ISearchParams {
    IsSearch?: number;
    AS_ID?: number;
    period_s: string | number;
    period_e: number | string;
    sbj_id: string;
    sbj_leval_s?: number;
    sbj_leval_e?: number;
    assistAccount?: boolean;
    contrastSubject?: number;
    IsBalanceZero?: number;
    NoAmountIncurredAndBalanceZero?: number;
    HiddenTotal?: number;
    showNumber: number;
    fcid: number;
    sortColumn: string;
}

export interface IFcList {
    asId: number;
    preName: string;
    id: number;
    code: string;
    name: string;
    rate: number;
    isBaseCurrency: boolean;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}

export interface IAsubCodeLength {
    asid: number;
    codeLength: Array<number>;
    firstAsubLength: number;
    firstCodeLength: number;
    forthAsubLength: number;
    forthCodeLength: number;
    preName: string;
    secondAsubLength: number;
    secondCodeLength: number;
    thirdAsubLength: number;
    thirdCodeLength: number;
}
