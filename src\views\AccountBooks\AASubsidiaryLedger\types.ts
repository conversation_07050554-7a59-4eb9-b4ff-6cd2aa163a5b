export interface Itree {
    id: string;
    text: string;
    attributes: any;
    children?: Itree[];
    parentData?: any;
    treeId?: string;
}

export interface ISearchParams {
    period_s: number;
    period_e: number;
    sbj_id: string;
    sbj_leval_s: number;
    sbj_leval_e: number;
    showsbj: number;
    isBalanceZero: number;
    hiddenTotal: number;
    aae_id: number | string;
    aae_pid: number | string;
    aa_type: number | string;
    fcid: number | string;
    direction?: number;
    ifpage: string;
    basepage: number; // 打印封面 1 0
    faname: string;
    quantity: boolean | number;
    sortColumn: string;
    page: number;
    rows: number;
}

export interface ITableData {
    v_date: string;
    vg_name: string;
    asub_code: string;
    asub_name: string;
    asub_names: string;
    description: string;
    debit: number;
    debit_price?: number;
    debit_qut?: number;
    credit: number;
    credit_price?: number;
    credit_qut?: number;
    direction: string;
    total: number;
    total_price?: number;
    total_qut?: number;
    p_id: number;
    v_id: number;
}
export interface IResTableData {
    rows: Array<ITableData>;
    total: number;
    unit: string;
}
export interface IAssistingAccount {
    /** asid */
    asid: number;
    /** 辅助核算类别 */
    aatype: number;
    /** 辅助核算目标id */
    aaeid: number;
    /** 辅助核算目标编码 */
    aanum: string;
    /** 辅助核算目标名称 */
    aaname: string;
    /**  */
    value01: string;
    /** 是否启用 */
    status: number;
    /**  */
    uscc: string;
    /** 创建人  */
    createdBy: number;
    /** 创建时间 */
    createdDate: string;
    /**  */
    preName: string;
}
export interface ISelectList {
    id: number;
    label: string;
}
export interface IAsubCodeLength {
    asid: number;
    codeLength: Array<number>;
    firstAsubLength: number;
    firstCodeLength: number;
    forthAsubLength: number;
    forthCodeLength: number;
    preName: string;
    secondAsubLength: number;
    secondCodeLength: number;
    thirdAsubLength: number;
    thirdCodeLength: number;
}

export interface ICurrency {
    asId: number;
    code: string;
    id: number;
    isBaseCurrency: boolean;
    name: string;
    preName: string;
    rate: number;
    rateDecimal: string;
    rateSeparator: string;
    status: number;
}

export interface IAssistingAccountType {
    asid: number;
    aatypeId: number;
    aatypeName: string;
    status: number;
    preName: string;
}

export interface ISearchInfo {
    period_s: number; // 会计期间开始
    period_e: number; // 会计期间结束
    sbj_id: string; // 科目
    sbj_leval_s: number; // 科目级别
    sbj_leval_e: number; // 科目级别
    sortColumn: string; // 排序方式
    showsbj: boolean; // 显示科目
    isBalanceZero: boolean; // 余额为0不显示
    hiddenTotal: boolean; // 无发生额不显示本期合计、本年累计
    aa_type: string | number; // 辅助类别
    quantity: boolean; // 显示数量金额
    fcid: number; // 币别
    aae_id: string; // 辅助项目
    faname: string;
    aae_pid: string;
    startMonth: string;
    endMonth: string;
}
