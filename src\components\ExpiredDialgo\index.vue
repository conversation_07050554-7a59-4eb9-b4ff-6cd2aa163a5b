<template>
    <div class="expired-dialog" v-if="expiredDialogShow">
        <div class="expired-dialog-container">
            <div class="expired-dialog-title">
                <img src="@/assets/ExpiredDialgo/trial7-dialog-icon.png" />
                <div>您的专业版试用已过期！</div>
            </div>
            <img class="expired-dialog-close" src="@/assets/ExpiredDialgo/trial7-dialog-close.png" @click="expiredDialogShow = false" />
            <div class="txt">
                <div>过期后资金、发票、工资、资产、关联进销存等专业版功能</div>
                <div>不支持新增数据，建议您升级至专业版!</div>
            </div>
            <div class="btn" @click="buyProHandle">升级专业版</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { buyPro } from "@/util/proUtils";

const expiredDialogShow = ref(true);
const buyProHandle = ()=> {
    expiredDialogShow.value = false;
    buyPro();
}
</script>

<style scoped lang="less">
.expired-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--shadow-color);
    z-index: 9999;
    & .expired-dialog-container {
        width: 630px;
        height: 401px;
        background-color: var(--white);
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        border-radius: 16px;
        & .expired-dialog-title {
            background-image: url("@/assets/ExpiredDialgo/trial7-dialog-bg.png");
            background-repeat: no-repeat;
            background-size: 100%;
            margin: -14px -14px 0;
            height: 163px;
            display: flex;
            align-items: center;
            justify-content: center;
            & img {
                width: 33px;
                height: 33px;
                margin-right: 8px;
            }
            & div {
                font-size: 24px;
                font-weight: 600;
                color: #735524;
                line-height: 16px;
            }
        }
        & .expired-dialog-close {
            height: 33px;
            width: 33px;
            cursor: pointer;
            position: absolute;
            top: 12px;
            right: 12px;
        }
        & .txt {
            font-size: 18px;
            font-weight: 500;
            color: #563e17;
            line-height: 30px;
            padding-left: 43px;
            margin-top: 21px;
        }
        & .btn {
            width: 200px;
            height: 48px;
            text-align: center;
            line-height: 48px;
            align-self: center;
            margin-top: 87px;
            font-size: 20px;
            font-weight: 600;
            color: #6c4b15;
            background: linear-gradient(177deg, #fff7e8 0%, #f4dbb2 100%);
            border-radius: 24px;
            cursor: pointer;
        }
    }
}
</style>
