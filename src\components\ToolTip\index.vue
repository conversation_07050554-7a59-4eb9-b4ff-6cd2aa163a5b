<template>
  <el-tooltip
    v-bind="tooltipProps"
    :visible="visible"
    class="custom-tooltip">
    <template #content>
      <div class="tooltip-content">
        {{ actualContent }}
      </div>
    </template>

    <span
      ref="tooltipRef"
      class="content-wrapper"
      :class="[props.class]"
      :style="contentStyle"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @input="handleInput">
      <slot />
    </span>
  </el-tooltip>
</template>

<script setup lang="ts">
  import type { TooltipProps } from "./type"

  //目前只为溢出气泡
  const props = withDefaults(defineProps<TooltipProps>(), {
    content: "",
    maxWidth: 300,
    fontSize: 14,
    lineClamp: 1,
    isInput: false,
    shieldDistance: 0,
    teleported: false,
    placement: "top",
    offset: 12,
    class: "",
    effect: "light",
    dynamicWidth: true,
    popperClass: "",
  })

  const visible = ref(false)
  const tooltipRef = ref<HTMLElement | null>(null)
  const isInputting = ref(false)

  const tooltipProps = computed(() => ({
    placement: props.placement,
    offset: props.offset,
    effect: props.effect,
    popperClass: props.popperClass,
    teleported: props.teleported,
    appendTo: props.appendTo,
  }))

  const contentStyle = computed(() => ({
    "-webkit-line-clamp": props.lineClamp,
    fontSize: `${props.fontSize}px`,
    maxWidth: props.maxWidth ? `${props.maxWidth}px` : undefined,
  }))

  const calculateTextWidth = (text: string, fontSize: number): number => {
    const span = document.createElement("span")
    span.style.cssText = `
      display: inline;
      width: auto;
      visibility: hidden;
      white-space: nowrap;
      font-size: ${fontSize}px;
    `
    span.textContent = text

    document.body.appendChild(span)
    const width = span.getBoundingClientRect().width
    document.body.removeChild(span)

    return width
  }

  const handleMouseEnter = (event: MouseEvent) => {
    if (isInputting.value) return

    if (props.isInput) {
      checkInputOverflow()
    } else {
      checkTextOverflow(event)
    }
  }

  const checkInputOverflow = () => {
    const input = tooltipRef.value?.querySelector(".el-input__inner") || tooltipRef.value?.querySelector("input")

    if (!input) return

    const { offsetWidth, scrollWidth } = input as HTMLElement
    visible.value = scrollWidth > offsetWidth
    actualContent.value = (input as HTMLInputElement).value.toString()
  }
  const actualContent = ref("")
  const checkTextOverflow = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    const childElement = target.firstElementChild as HTMLElement
    actualContent.value = childElement.textContent?.trim() || props.content
    const textWidth = calculateTextWidth(actualContent.value, props.fontSize)
    const maxWidth = props.dynamicWidth ? childElement?.getBoundingClientRect().width : props.maxWidth

    const computedStyle = window.getComputedStyle(childElement)
    const contentWidth = maxWidth - parseFloat(computedStyle.paddingLeft) - parseFloat(computedStyle.paddingRight)
    visible.value = textWidth > contentWidth
  }

  const handleMouseLeave = () => {
    isInputting.value = false
    visible.value = false
  }

  const handleInput = (event: Event) => {
    const target = event.target as HTMLElement
    if (target.tagName === "INPUT") {
      isInputting.value = true
      visible.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .custom-tooltip {
    --tooltip-max-width: 300px;

    max-width: var(--tooltip-max-width);
    text-align: left;

    .tooltip-content {
      max-width: var(--tooltip-max-width);
      white-space: normal;
      color: var(--el-text-color-primary);
      overflow-wrap: break-word;
      word-break: break-all;
    }
  }

  .content-wrapper {
    display: -webkit-box;
    max-width: 100%;
    white-space: normal;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
  }
</style>
