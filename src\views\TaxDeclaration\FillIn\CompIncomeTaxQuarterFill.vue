<!-- 增值税月报 -->
<template>
  <category-tabs
    class="main-content-body"
    :tab-list="tabList"
    :show-total="true"
    @tab-click="handleTabClick">
    <template #content="{ activeIndex, tabData }">
      <div class="declaration-form">
        <div class="form-title">{{ tabData.describe || "中华人民共和国企业所得税月（季）度与缴纳税申报表" }}</div>
        <div
          class="form-subtitle"
          v-if="tabData.describe">
          {{ tabData.describe || "（A类）" }}
        </div>
        <div class="form-info">
          <div class="info">
            <div
              v-for="(item, index) in formHeaderInfo"
              :key="index">
              <span class="label">{{ item.label }}：</span>
              <span class="value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="native-table">
        <table>
          <tbody>
            <tr>
              <th colspan="3">纳税人识别号（统一社会信用代码）</th>
              <th colspan="2">
                <div style="width: 100%"></div>
              </th>
              <th colspan="3">纳税人名称</th>
              <th colspan="2">
                <span>--</span>
              </th>
            </tr>
            <tr>
              <th colspan="3">预缴方式</th>
              <td colspan="7">
                <div class="radio-group">
                  <el-radio-group>
                    <el-radio label="1">按照实际利润额预缴</el-radio>
                    <el-radio label="2">按照上一纳税年度应纳税所得额平均额预缴</el-radio>
                    <el-radio label="3">按照税务机关确定的其他方法预缴</el-radio>
                  </el-radio-group>
                </div>
              </td>
            </tr>
            <tr>
              <th colspan="3">企业类型</th>
              <td colspan="7">
                <div class="radio-group">
                  <el-radio-group>
                    <el-radio label="1">一般企业</el-radio>
                    <el-radio label="2">跨地区经营汇总纳税企业总机构</el-radio>
                    <el-radio label="3">跨地区经营汇总纳税企业分支机构</el-radio>
                  </el-radio-group>
                </div>
              </td>
            </tr>
            <tr>
              <th colspan="3">跨省总机构行政区划</th>
              <td colspan="2"></td>
              <td colspan="5">
                <div class="hint">提示: 总机构在外省的分支机构申报时，请先选择跨省总机构行政区划</div>
              </td>
            </tr>

            <tr>
              <th colspan="10">优惠及附报事项有关信息</th>
            </tr>
            <tr>
              <th rowspan="2">项目</th>
              <th colspan="2">一季度</th>
              <th colspan="2">二季度</th>
              <th colspan="2">三季度</th>
              <th colspan="2">四季度</th>
              <th rowspan="2">季度平均值</th>
            </tr>
            <tr>
              <th>季初</th>
              <th>季末</th>
              <th>季初</th>
              <th>季末</th>
              <th>季初</th>
              <th>季末</th>
              <th>季初</th>
              <th>季末</th>
            </tr>

            <tr>
              <td>从业人数</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>资产总额（万元）</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <th>国家限制或禁止行业</th>
              <td colspan="4">
                <div class="radio-group">
                  <el-radio-group>
                    <el-radio label="1">是</el-radio>
                    <el-radio label="2">否</el-radio>
                  </el-radio-group>
                </div>
              </td>
              <th>小型微利企业</th>
              <td colspan="4">
                <div class="radio-group">
                  <el-radio label="1">是</el-radio>
                  <el-radio label="2">否</el-radio>
                </div>
              </td>
            </tr>
            <tr>
              <th>代码</th>
              <th colspan="6">附报事项说明</th>
              <th colspan="3">金额或选项</th>
            </tr>
            <tr>
              <td>K01002</td>
              <td colspan="6">扶贫捐赠支出全额扣除（本年累计，元）</td>
              <td colspan="3">0.00</td>
            </tr>
            <tr>
              <td>Y01001</td>
              <td colspan="6">软件集成电路企业优惠政策适用类型</td>
              <td colspan="3">
                <el-checkbox label="原政策"></el-checkbox>
                <el-checkbox label="新政策"></el-checkbox>
              </td>
            </tr>

            <tr>
              <th colspan="10">预缴税款计算表</th>
            </tr>
            <tr>
              <th>行次</th>
              <th colspan="7">项目</th>
              <th colspan="2">本年累计金额</th>
            </tr>
            <tr>
              <td>1</td>
              <td colspan="7">营业收入</td>
              <td colspan="2">
                <checkable-input />
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td colspan="7">营业成本</td>
              <td colspan="2">
                <checkable-input />
              </td>
            </tr>
            <tr>
              <td>3</td>
              <td colspan="7">利润总额</td>
              <td colspan="2">
                <checkable-input />
              </td>
            </tr>
            <tr>
              <td>4</td>
              <td colspan="7">加：特定企业特定业务(以前年度亏损)</td>
              <td colspan="2">
                <checkable-input />
              </td>
            </tr>
            <tr>
              <td>5</td>
              <td colspan="7">减：不征税收入</td>
              <td colspan="2">
                <checkable-input />
              </td>
            </tr>
            <tr>
              <td>6</td>
              <td colspan="7">
                减：资产加速折旧、摊销（从第2020D）
                <a
                  href="#"
                  class="link">
                  填写
                </a>
              </td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>7</td>
              <td colspan="7">
                减：免税收入、减计收入、加计扣除（7.1+7.2+...）
                <a
                  href="#"
                  class="link"
                  @click.prevent="showTaxPreferenceDialog('deduction')">
                  填写优惠
                </a>
                <el-icon
                  v-if="taxPreferenceDetails.length > 0"
                  class="expand-icon"
                  @click="showTaxPreferenceDetails = !showTaxPreferenceDetails">
                  <arrow-down v-if="showTaxPreferenceDetails" />
                  <arrow-right v-else />
                </el-icon>
              </td>
              <td colspan="2">{{ taxPreferenceAmount }}</td>
            </tr>

            <!-- 优惠项明细，可展开收起 -->
            <template v-if="showTaxPreferenceDetails && taxPreferenceDetails.length > 0">
              <tr
                v-for="(item, index) in taxPreferenceDetails"
                :key="index">
                <td align="right">7.{{ index + 1 }}</td>
                <td colspan="7">{{ item.name }}</td>
                <td colspan="2">
                  {{ item.amount }}
                </td>
              </tr>
            </template>

            <tr>
              <td>8</td>
              <td colspan="7">
                减：所得减免（8.1+8.2+...）
                <a
                  href="#"
                  class="link">
                  填写优惠
                </a>
              </td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>9</td>
              <td colspan="7">减：弥补以前年度亏损</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>10</td>
              <td colspan="7">实际利润额（3+4-5-6-7-8-9）：按照上一纳税年度应纳税所得额平均额预缴的填写预缴税款</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>11</td>
              <td colspan="7">税率(25%)</td>
              <td colspan="2">25%</td>
            </tr>
            <tr>
              <td>12</td>
              <td colspan="7">应纳所得税额（10×11）</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>13</td>
              <td colspan="7">
                减：减免所得税额（13.1+13.2+...）
                <a
                  href="#"
                  class="link">
                  填写优惠
                </a>
              </td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>13.1</td>
              <td colspan="7">符合条件的小型微利企业减免企业所得税</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>14</td>
              <td colspan="7">减：本年实际已预缴所得税额</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>15</td>
              <td colspan="7">减：特定业务预缴（退）所得税额</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
            <tr>
              <td>16</td>
              <td colspan="7">本期应补（退）所得税额（12-13-14-15）：税务机关确定的本期应纳所得税额</td>
              <td colspan="2"><input-field value="0.00" /></td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- 免税收入、减计收入、加计扣除弹窗 -->
      <el-dialog
        v-model="taxPreferenceDialogVisible"
        title="免税、减计收入、加计扣除类型选项表"
        width="80%">
        <el-table
          ref="tableRef"
          :data="preferenceTableData"
          style="width: 100%"
          height="400"
          border
          :headerCellStyle="headerCellStyle"
          :span-method="getSpanMethod"
          @selection-change="handleSelectionChange">
          <el-table-column
            type="selection"
            width="55"
            align="center"
            label="选择"
            :selectable="(row) => !(parseFloat(row.amount) > 0)"></el-table-column>
          <el-table-column
            prop="index"
            label="序号"
            width="60"
            align="center"></el-table-column>
          <el-table-column
            prop="code"
            label="代码"
            width="100"
            align="center"></el-table-column>

          <el-table-column prop="type"></el-table-column>
          <el-table-column
            prop="name"
            label="优惠事项"></el-table-column>

          <el-table-column
            prop="amount"
            label="本年累计金额"
            width="180"
            align="center">
            <template #default="scope">
              <checkable-input
                @change="() => handleInputChange(scope.row)"
                v-model="scope.row.amount"></checkable-input>
            </template>
          </el-table-column>
        </el-table>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="taxPreferenceDialogVisible = false">取消</el-button>
            <el-button
              type="primary"
              @click="confirmTaxPreference">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </category-tabs>
</template>
<script setup lang="ts">
  import { ref, computed } from "vue"
  import CategoryTabs from "../components/CategoryTabs.vue"

  // 接口回传数据
  const tabList = ref([
    { declareName: "********中华人民共和国企业所得税月（季）度预缴纳税申报表" },
    {
      declareName: "A201020固定资产加速折旧（扣除）优惠明细表",
    },
    { declareName: "技术成果投资入股企业所得税递延纳税备案表" },
  ])

  const formHeaderInfo = ref([
    { label: "税款所属期", value: "2024-10-01至2024-12-31" },
    { label: "填表日期", value: "2025-01-10" },
    { label: "金额单位", value: "元，至角分" },
  ])

  // 优惠弹窗相关
  const taxPreferenceDialogVisible = ref(false)
  const currentPreferenceType = ref("")
  const selectedPreferences = ref([])
  const taxPreferenceAmount = ref("0.00") // 优惠项汇总金额
  const taxPreferenceDetails = ref([]) // 优惠项明细
  const showTaxPreferenceDetails = ref(true) // 控制明细展开收起

  // 免税收入列表
  const exemptIncomeList = ref([
    { index: 1, code: "MSSR010", name: "国债利息收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    { index: 2, code: "MSSR021", name: "股股息红利等权益性投资收益免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    {
      index: 3,
      code: "MSSR022",
      name: "通过沪港通投资且连续持有H股满12个月取得的股息红利所得免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    {
      index: 4,
      code: "MSSR023",
      name: "通过深港通投资且连续持有H股满12个月取得的股息红利所得免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    {
      index: 5,
      code: "MSSR024",
      name: "持有创新企业CDR取得的股息红利所得免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    { index: 6, code: "MSSR025", name: "免税收入 永续债利息收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    { index: 7, code: "MSSR030", name: "符合条件的非营利组织的收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    {
      index: 8,
      code: "MSSR040",
      name: "投资者从证券投资基金分配中取得的收入免征企业所得税",
      amount: "0.00",
      selected: false,
      type: "免税收入",
    },
    { index: 9, code: "MSSR050", name: "取得的地方政府债券利息收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    { index: 10, code: "MSSR100", name: "取得的基础研究收入免征企业所得税", amount: "0.00", selected: false, type: "免税收入" },
    {
      index: 11,
      code: "MSSR999",
      name: "其他: 免税收入类系列明优惠 减免税代码:",
      amount: "0.00",
      selected: false,
      hasExtraInput: true,
      type: "免税收入",
    },
  ])

  // 减计收入列表
  const reducedIncomeList = ref([
    { index: 12, code: "JJSR010", name: "取得铁路债券利息收入减半征收企业所得税", amount: "0.00", selected: false, type: "减计收入" },
    {
      index: 13,
      code: "JJSR020",
      name: "取得的社区家庭服务收入在计算应纳所得税额时减计收入",
      amount: "0.00",
      selected: false,
      type: "减计收入",
    },
    {
      index: 14,
      code: "JJSR030",
      name: "综合利用资源生产产品取得的收入在计算应纳所得税额时减计收入",
      amount: "0.00",
      selected: false,
      type: "减计收入",
    },
    {
      index: 15,
      code: "JJSR999",
      name: "其他: 免税收入类系列明优惠 减免税代码:",
      amount: "0.00",
      selected: false,
      hasExtraInput: true,
      type: "减计收入",
    },
  ])

  // 加计扣除列表
  const additionalDeductionList = ref([
    {
      index: 16,
      code: "JJKC014",
      name: "企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除(集成电路和工业母机企业按120%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 17,
      code: "JJKC015",
      name: "企业开发新技术、新产品、新工艺发生的研究开发费用加计扣除(按100%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 18,
      code: "JJKC024",
      name: "企业为获得创新性、创造性和突破性的产品进行创意设计活动发生的相关费用加计扣除（集成电路和工业母机企业按120%加计扣除）",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 19,
      code: "JJKC025",
      name: "企业为获得创新性、创造性和突破性的产品进行创意设计活动发生的相关费用加计扣除(按100%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
    {
      index: 20,
      code: "JJKC031",
      name: "企业投入基础研究支出加计扣除(按100%加计扣除)",
      amount: "0.00",
      selected: false,
      type: "加计扣除",
    },
  ])

  // 合并所有优惠项目到一个表格数据中
  const preferenceTableData = computed(() => {
    return [...exemptIncomeList.value, ...reducedIncomeList.value, ...additionalDeductionList.value]
  })

  const headerCellStyle = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex === 4 && rowIndex === 0) {
      column.colSpan = 2
    }
    if (columnIndex === 3 && rowIndex === 0) {
      return { display: "none" }
    }
  }

  const getSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex === 3) {
      if (rowIndex === 0) {
        return { rowspan: exemptIncomeList.value.length, colspan: 1 }
      } else if (rowIndex === exemptIncomeList.value.length) {
        return { rowspan: reducedIncomeList.value.length, colspan: 1 }
      } else if (rowIndex === reducedIncomeList.value.length + exemptIncomeList.value.length) {
        return { rowspan: additionalDeductionList.value.length, colspan: 1 }
      }
      return { rowspan: 0, colspan: 1 }
    }
    return { rowspan: 1, colspan: 1 }
  }

  // 显示优惠弹窗
  const showTaxPreferenceDialog = (type: string) => {
    currentPreferenceType.value = type
    taxPreferenceDialogVisible.value = true
  }

  // 处理选择变更
  const handleSelectionChange = (selection) => {
    selectedPreferences.value = selection
  }

  // 确认优惠选择
  const confirmTaxPreference = () => {
    // 计算所有选中项目的金额总和
    let totalAmount = 0
    const selectedItems = []

    selectedPreferences.value.forEach((item) => {
      const amount = parseFloat(item.amount || "0")
      totalAmount += amount
      selectedItems.push({
        index: item.index,
        code: item.code,
        name: item.name,
        amount: item.amount,
        type: item.type,
      })
    })

    // 更新表格中对应行的金额
    taxPreferenceAmount.value = totalAmount.toFixed(2)

    // 保存选中的优惠项目明细
    taxPreferenceDetails.value = selectedItems

    // 关闭弹窗
    taxPreferenceDialogVisible.value = false
  }

  // 处理金额变化
  const tableRef = ref(null)
  const handleInputChange = (row) => {
    console.log("触发金额变化")
    const amount = parseFloat(row.amount || 0)

    // 使用 nextTick 确保在 DOM 更新后再操作表格选择状态

    // 如果金额大于0，自动勾选并禁用取消勾选
    if (amount > 0) {
      // 标记为已选中
      row.selected = true
      // 直接勾选当前行，而不是遍历所有行
    }
    nextTick(() => {
      if (tableRef.value) {
        preferenceTableData.value.forEach((item) => {
          if (item.selected) {
            tableRef.value.toggleRowSelection(item, true)
          }
        })
      }
    })
  }
</script>
<style scoped lang="scss">
  @use "@/style/TaxDeclaration/index.scss" as *;
  @include colspan-width(10);
  .declaration-form {
    .form-title {
      margin-bottom: 4px;
      font-size: var(--h3);
      text-align: center;
    }

    .form-subtitle {
      margin-bottom: 14px;
      font-weight: 400;
      font-size: var(--h4);
      color: var(--dark-grey);
      text-align: center;
    }

    .form-info {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .info {
        flex: 3;
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;

        div {
          width: 33.3%;
          box-sizing: border-box;

          span {
            line-height: 20px;
            font-size: var(--h4);
          }
        }
      }
      span {
        flex: 1;
        align-self: flex-end;
        line-height: 32px;
        font-size: 14px;
        text-align: right;
      }
    }
  }

  .main-table {
    :deep(.el-tabs) {
      z-index: 999;
      height: auto;
      position: sticky;
      top: 0;
      background-color: var(--white);

      .el-tabs__header {
        margin-bottom: 12px;
      }
    }

    .title {
      display: inline-block;
      padding: 8px 16px;
      margin-bottom: 15px;
      border: 1px solid var(--button-border-color);
    }

    .el-divider--horizontal {
      margin: 20px 0;
    }
  }

  .appendix-2,
  .appendix-4 {
    table {
      table-layout: fixed;
    }
  }

  .appendix-5 {
    width: 100%;
    overflow-x: auto;
    table {
      width: max-content;
    }
  }
  .appendix-6 {
    table {
      width: 100%;
      border-right: 0;

      :deep(.el-select) {
        width: 95% !important;
        float: right;
      }
    }

    tr {
      position: relative;
      &.dynamic-row {
        .minus {
          display: none;
          position: absolute;
          top: 50%;
          left: 2px;
          transform: translateY(-50%);
        }
        &:hover .minus {
          display: block;
        }
      }
    }

    th {
      width: 16.7%;
    }

    .el-icon {
      font-size: var(--h4);
      vertical-align: sub;
      cursor: pointer;
    }
  }
</style>
