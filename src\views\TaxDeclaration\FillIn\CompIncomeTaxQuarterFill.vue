<!-- 增值税月报 -->
<template>
  <category-tabs
    class="main-content-body"
    :tab-list="tabList"
    :show-total="true"
    v-model:active-index="activeIndex">
    <template #content="{ activeIndex, tabData }">
      <div
        v-if="activeIndex == 0"
        class="table1">
        <MainTable
          :tab-data="tabData"
          @jump-to-table2="jumpToTable2" />
      </div>

      <div
        v-if="activeIndex == 1"
        class="table2">
        <AdditionalFirst :tab-data="tabData" />
      </div>

      <div
        v-if="activeIndex == 2"
        class="table3">
        <AdditionalSecond :tab-data="tabData" />
      </div>
    </template>
  </category-tabs>
</template>
<script setup lang="ts">
  import CategoryTabs from "../components/CategoryTabs.vue"
  import { TabObj } from "../components/types"

  import MainTable from "./components/CompIncomeTaxQuarterFill/MainTable.vue"
  import AdditionalFirst from "./components/CompIncomeTaxQuarterFill/AdditionalFirst.vue"
  import AdditionalSecond from "./components/CompIncomeTaxQuarterFill/AdditionalSecond.vue"
  // 接口回传数据
  const tabList = ref<TabObj[]>([
    {
      title: "中华人民共和国企业所得税月（季）度预缴纳税申报表",
      describe: "中华人民共和国企业所得税月（季）度与缴纳税申报表",
    },
    {
      title: "《A201020固定资产加速折旧(扣除)优惠明细表》",
      describe: "《A201020固定资产加速折旧(扣除)优惠明细表》",
    },
    {
      title: "《技术成果投资入股企业所得税递延纳税备案表》",
      describe: "《技术成果投资入股企业所得税递延纳税备案表》",
    },
  ])

  const activeIndex = ref(0)
  // 跳转至表格二
  const jumpToTable2 = () => {
    console.log("跳转至表格二dwadwa")
    activeIndex.value = 1
  }
</script>
<style scoped lang="scss"></style>
