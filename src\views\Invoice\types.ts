import type { VoucherTemplateEntryModel } from "@/api/VoucherTemplate";
import type { IFileInfo } from "@/components/UploadFileDialog/types";

export interface ITableItem {
    account?: string | null;
    adValoremTaxTotals?: string;
    address: string | null;
    amount: number | null;
    amountText: string | null;
    billId: number;
    billType: number;
    businessType: number;
    businessTypeText: string;
    code: string | null;
    entryDate: string;
    entryDateText: string;
    invoiceCategory: number;
    invoiceDate: string | null;
    invoiceDateText: string;
    invoiceId: number;
    invoiceStatus?: string;
    invoiceType: number;
    invoiceTypeText: string | null;
    isInvalid: number;
    isOffseted: boolean;
    isCheckOut: boolean;
    issueDate: string;
    issueDateText: string;
    name: string | null;
    erpNameId: string;
    nameId: number;
    note: string | null;
    number: string | null;
    pid: number;
    pidText: string;
    projectId: number;
    projectName: string;
    tax: number;
    taxRate: string;
    taxRate2: string;
    taxText: string;
    taxpayerNumber: number | null;
    vid: number;
    vidText: string;
    vnum: number;
    vgId: number;
    vtId: number;
    vtName: string | null;
    attachFileCount: number;
    invoiceSource: number;
    verifiedBills?: VerifiedBill[];
    groupBills?: GroupedBill[]; 
}
export interface IPagingList {
    count: number;
    data: ITableItem[];
}
export interface VerifiedBill {
    billType: number;
    billId: number;
    billNo: string;
    billName: string;
}
export interface BillItem {  
    billId: number; 
    billNo: string;
}
export interface GroupedBill {  
    billType: number;  
    billName: string;  
    bills: BillItem[];  
}
export interface IInvoiceTypeListItem {
    IN_ID: string;
    IN_NAME: string;
}
export interface ISearchInfoType {
    startDate: string;
    endDate: string;
    startEntryDate: string;
    endEntryDate: string;
    number?: string;
    invoiceType?: string;
    invoiceVoucherStatus?: string;
    invoiceStatus: string;
    invoiceSource?: string;
    name: string;
    invoiceIssueDateStatus?: string;
    issueStartDate?: string | "";
    issueEndDate?: string | "";
    businessTypes: number[];
    appasid?: string;
    startVDate: string; //凭证开始日期
    endVDate: string; //凭证结束日期
    vgId?: number; //凭证类型
    startVNum?: string; //开始凭证号
    endVNum?: string; //结束凭证号
    invoiceBillStatus?: string; //是否生成单据
    taxRate?: string; //税率
    note: string; //备注
    invoiceTypes: number[]; //发票种类多选列表
}
export interface ISearchParams {
    startDate?: string;
    endDate?: string;
    startEntryDate?: string;
    endEntryDate?: string;
    number?: string;
    invoiceType?: string;
    invoiceVoucherStatus?: string;
    invoiceStatus?: string;
    invoiceSource?: string;
    name?: string;
    invoiceIssueDateStatus?: string;
    issueStartDate?: string | "";
    issueEndDate?: string | "";
    businessTypes?: string;
    appasid?: string;
    pageIndex: number;
    pageSize: number;
    invoiceIds?: string;
    startVDate?: string;
    endVDate?: string;
    vgId?: string;
    startVNum?: string;
    endVNum?: string;
    invoiceBillStatus?: string;
    taxRate?: string;
    note?: string;
    invoiceTypes?: string;
    sortField?: number;
    sortOrder?: number;
}
export interface IAddInvoiceForm {
    invoiceId: string | number;
    invoiceCategory?: string;
    erpNameId: string;
    projectId: number | null;
    invoiceType?: string | number;
    entryDate: string;
    issueDate?: string;
    invoiceDate: string;
    code: string;
    number: string;
    isInvalid: number | string;
    name: string;
    taxpayerNumber: string;
    address: string;
    account: string;
    note: string;
    businessType: string;
    invoiceEntries: Array<IAddTableEnterItem>;
    amount: string;
    tax: string;
    adValoremTaxTotals?: string;
    vin?: string; //车架号,
    machineNumbers?: string; //机器编号,
    competentTaxAuthoritiesCode?: string; //主管税务机关代码,
    vehicleType?: string; //车辆类型
    brandModel?: string; //厂牌型号
    engineNUM?: string; //发动机号
    vehicleLicence?: string; //车牌照号
    attachFileCount: number; //附件数量
    attachFiles?: string; //附件,
    isCheckOut: boolean; //是否结账
    invoiceSource: number; //发票来源
}

export interface IInvoiceModel {
    isCheckOut: boolean; //是否结账
    invoiceData: IEditOneData;
}
export interface IEditOneData {
    account: string;
    adValoremTaxTotals: number;
    address: string;
    amount: number;
    asId: number;
    businessType: number;
    createdBy: number;
    createdDate: string;
    entryDate: string;
    code?: string;
    entryDateText: string;
    erpNameId: string;
    invoiceCategory: number;
    invoiceDate: string;
    invoiceDateText: string;
    invoiceEntries: IEditOneDataEntries[];
    invoiceId: number;
    invoiceType: number;
    invoiceTypeName: string;
    isDetail: boolean;
    isInvalid: number;
    issueDate: string | null;
    issueDateText: string;
    modifiedBy: number;
    modifiedDate: string;
    name: string;
    note: string;
    number: string;
    oppositeAccount: string;
    oppositeAddress: string;
    oppositeName: string;
    oppositeTaxpayerNumber: string;
    pid: number;
    projectId: number | null;
    tax: number;
    taxpayerNumber: string;
    vid: number;
    vin?: string;
    machineNumbers?: string;
    competentTaxAuthoritiesCode?: string;
    vehicleType?: string;
    brandModel?: string;
    engineNUM?: string;
    vehicleLicence?: string;
    attachFiles?: string; //附件,
}
export interface IAddTableEnterItem {
    index?: number;
    message?: string;
    name: string;
    model: string;
    unit: string;
    quality: string | number | null;
    amount: string | number;
    taxRate: string | number;
    tax: string | number;
    taxProject: number;
    taxCountingMethod: number;
    code?: string;
}
export interface IEditOneDataEntries {
    amount: number | string;
    asId?: number;
    invoiceId?: number;
    sn: number;
    model: string;
    name: string;
    quality: number | string;
    tax: number | string;
    taxCountingMethod: number;
    code?: string;
    taxProject: number;
    taxRate: number | string;
    unit: string;
}
export interface IGetTaxConfigurationInfo {
    taxCountingMethodInfo: { [key: number]: string };
    taxProjectInfo: { [key: number]: string };
    taxRateInfo: { [key: number]: string };
}
export interface VoucherTemplateModel {
    hasErrorVoucherLines: boolean;
    vgId: number;
    voucherLines: Array<VoucherTemplateEntryModel>;
    vtId: number;
    vtName: string;
    vtType: number;
}
export interface ITableDataRow {
    businessTypeModel: IBusinessTypeModel;
    voucherTemplateModel: VoucherTemplateModel;
}

export interface IBusinessTypeModel {
    asId: number;
    code: string;
    id: number;
    invoiceCategory: number;
    name: string;
    templateId: number;
}

export interface IInvoicesGenerateBillModel {
    idFromScm: number;
    successCount: number;
    dropCount: number;
    isRefund: boolean;
}
export interface IInvoicesGenerateBillModelErp {
    idFromScm: number;
    positiveCount: number;
    negativeCount: number;
    dropCount: number;
    isRefund: boolean;
}
export interface IBusinessTypeItem {
    label: string;
    value: string;
}
export interface IEntriesSelectItem {
    label: string;
    value: number;
}
export interface ISearchDate {
    endDate: string;
    startDate: string;
}
export interface ICalculationRes {
    balanceTax: number | 0;
    balanceTaxText: string;
    purchaseTax: number | 0;
    purchaseTaxText: string;
    salesTax: number | 0;
    salesTaxText: string;
}
export interface ICalculationRes2 {
    balanceTax: number | 0;
    balanceTaxText: string;
    purchaseTax: number | 0;
    purchaseTaxText: string;
    salesTax: number | 0;
    salesTaxText: string;
    taxRetained: number | 0;
    taxRetainedText: string;
    issueStartDate: string;
    issueEndDate: string;
    isCustomTaxRetained: boolean;
}
export interface IInvoiceTaskValue {
    success: boolean;
    result: string;
    message: string;
    info: string;
    taskId: number;
    reportId: number;
}

export interface IInvoiceTaskDisplay {
    asId: number;
    createdBy: number;
    createdByName: string;
    createdDate: string;
    startDate: number;
    endDate: number;
    modifiedDate: string;
    purchaseTotal: number;
    purchaseTax: number;
    purchaseAmount: number;
    purchaseCount: number;
    salesTotal: number;
    salesTax: number;
    salesAmount: number;
    salesCount: number;
    invoiceTaskStatus: number;
    invoiceTaskRemark: string;
    deductionTaskStatus: number;
    deductionTaskRemark: string;
    fileTaskStatus: number;
    fileTaskRemark: string;
    status: number;
    remark: string;
}

export interface IInvoiceTaskResult {
    asId: number;
    startDate: number;
    endDate: number;
    createdBy: number;
    createdDate: string;
    modifiedDate: string;
    status: number;
    remark: string;

    invoiceTaskStatus: number;
    invoiceTaskRemark: string;
    purchaseInvalidTotal: number;
    purchaseInvalidTax: number;
    purchaseInvalidAmount: number;
    purchaseInvalidCount: number;
    purchaseNormalTotal: number;
    purchaseNormalTax: number;
    purchaseNormalAmount: number;
    purchaseNormalCount: number;
    purchaseRedTotal: number;
    purchaseRedTax: number;
    purchaseRedAmount: number;
    purchaseRedCount: number;
    salesInvalidTotal: number;
    salesInvalidTax: number;
    salesInvalidAmount: number;
    salesInvalidCount: number;
    salesNormalTotal: number;
    salesNormalTax: number;
    salesNormalAmount: number;
    salesNormalCount: number;
    salesRedTotal: number;
    salesRedTax: number;
    salesRedAmount: number;
    salesRedCount: number;

    deductionTaskStatus: number;
    deductionTaskRemark: string;
    deductionCount: number;
    deductionAmount: number;

    fileTaskStatus: number;
    fileTaskRemark: string;
    fileCount: number;

    createdByName: string;
    taxLoginType: number;
}

export interface IInvoiceTaskResultList {
    count: number;
    data: IInvoiceTaskResult[];
}

export interface IImportResultTableData {
    invoiceId: number;
    no: number;
    fileName: string;
    number: string;
    importStatus: number;
    importStatusText: string;
    reason: string;
    handComplete: boolean;
    graphCode: string;
    guidno: string;
}

export interface IImportSuccessData {
    invoiceIds: number[];
    startDate: string;
    endDate: string;
}

export interface IResoneStockItem {
    aaAcronym: string;
    aaName: string;
    aaNum: string;
    aaType: number;
    aaeId: number;
    displayOrder: number;
    startDate: string | null;
    endDate: string | null;
    note: string;
    option: boolean;
    status: number;
    stockModel: string;
    stockType: string;
    unit: string;
}
export interface IInvoiceStockItem {
    name: string;
    value: number;
    model: string;
    unit: string;
    num: string;
    label?: string;
}
export interface IProjectItem {
    department: string;
    owner: string;
    mobilePhone: string;
    startDate: string;
    endDate: string;
    note: string;
    aaType: number;
    aaeId: number;
    aaNum: string;
    aaName: string;
    aaAcronym: string;
    status: number;
    displayOrder: number;
    option: boolean;
}
export interface IResoneProjectItem {
    aaeid: number;
    aaname: string;
    aanum: string;
    aatype: number;
    asid: number;
    createdBy: number;
    createdDate: string;
    preName: string;
    status: number;
    uscc: string;
    value01: string;
}
export interface QuickAddInvoiceForm {
    invoiceCode: string;
    invoiceNum: string;
    invoiceDate: string;
    invoiceMoney: string;
}
export interface IResoneQuickAddInvoice {
    asId: number;
    invoiceCategory: number;
    invoiceDate: string;
    invoiceDateText: string;
    invoiceId: number;
    invoiceType: number;
    invoiceTypeName: string;
}
export interface IInvoiceAttachFileList {
    result: boolean;
    parentId: number;
    data: IFileInfo[];
}
export interface IHistoryItem {
    reportId: number;
    reportDate: string;
    invoiceDate: string;
    userName: string;
    status: number;
    FailedMsg: string;
}
export interface IHistoryPagingList {
    count: number;
    data: IHistoryItem[];
}
export interface ReportCover {
    companyName: string;
    reportRangeDate: string;
    createTime: string;
    qrCode: string;
}
export interface ReportSPCompareItem {
    month: string;
    salesInvoiceAmount?: number;
    salesInvoiceTax?: number;
    purchaseInvoiceAmount?: number;
    purchaseInvoiceTax?: number;
    grossMargin?: string;
}
export interface ReportTRAmountItem {
    year: string;
    taxRate: string;
    salesInvoiceAmount: number;
    purchaseInvoiceAmount: number;
}
export interface ReportRiskMsgItem {
    ristLevel: number;
    msg: string;
}
export interface ReportTUDCompareItem {
    month: string;
    taxRatio?: number;
    ratioEvaluate?: string;
    referenceScope?: string;
}
export interface ReportMutInvoiceItem {
    name: string;
    salesInvoiceAmount?: number;
    purchaseInvoiceAmount?: number;
    subtotal?: number;
}
export interface ReportSPOutItem {
    province: string;
    ratio?: number;
    amount?: number;
}
export interface ReportSUnMatchItem {
    num: number | string;
    taxpayerNumber?: string;
    invoiceSupplierName?: string;
    goodsName?: string;
    amount?: number;
    verifySupplierName?: string;
}
export interface ReportSPGoodsItem {
    name: string;
    currcentGoodsRatio: string;
    lastGoodsRatio?: string;
    beforeGoodsRatio?: string;
}
export interface ReportSWInvoiceItem {
    num: number | string;
    invoiceDate?: string;
    number?: string;
    supplierName?: string;
    goodsName?: string;
    amount?: number;
}
export interface ReportRedInvalidItem {
    month: string;
    amount?: number;
    redAmount?: number;
    monthLastRedAmount?: number;
    invalidAmount?: number;
    monthLastInvalidAmount?: number;
}
export interface ReportRIInvoiceItem {
    num: number | string;
    invoiceCategoryDes?: string;
    invoiceTypeDes?: string;
    name?: string;
    code?: string;
    number?: string;
    allElectricNumber?: string;
    invoiceDate?: string;
    amount?: number;
    taxAmount?: number;
}
export interface ReportCSItem {
    name: string;
    amount?: number;
    ratio?: number;
    duration?: string;
    // isZeroSocialStaffNum?: string;
    regStatus?: string;
    companyOrgType?: string;
}
export interface ReportZTItem {
    month: string;
    amount?: number;
    zeroTaxInvoiceAmount?: number;
}
export interface ReportAmountItem {
    num: number | string;
    name?: string;
    invoiceAmount?: number;
    amount?: number;
}
export interface ReportIncomeTimeItem {
    time: string;
    invoiceAmount?: number;
    operatingIncome?: number;
    ratio?: number;
}

export interface InvoiceLineSn {
    invoiceId: number;
    invoiceDateText: string;
}
export interface ICashierInvoiceTableItem {
    // 往来单位
    opposite_party: string;
    // 销项发票张数与金额
    salesInvoiceNum: number;
    salesInvoiceAmount: number;
    // 进项发票
    purchaseInvoiceNum: number;
    purchaseInvoiceAmount: number;
    // 收支数量和金额
    incomeNum: number;
    incomeAmount: number;
    // 支出数量和金额
    expenditureNum: number;
    expenditureAmount: number;
    // 现金金额
    cashierAmount?: number;
    // 未开发票金额
    noSalesInvoiceAmount: number;
    // 未收款金额
    noIncomeAmount: number;
    // 未收发票金额
    noPurchaseeNum: number;
    // 未付发票金额
    noExpenditureAmount: number;
}

export interface ICashierInvoiceInfoItem {
    ac_id: number;
    ac_name: string;
    ac_no: string;
    cashierDate: string;
    expenditureAmount: string;
    incomeAmount: string;
    inv_id: number;
    invoiceDate: string;
    invoiceNumber: string;
    invoiceNumberName: string;
    j_type: number;
    line_sn: string;
    line_sn_name: string;
    opposite_party: string;
    purchaseInvoiceAmount: string;
    salesInvoiceAmount: string;
    InvoiceCategory: string;
}

// 自定义匹配存货
export interface ICustomStockItemApi {
    emodel: string;
    ename: string;
    stockAAEId: number;
    stockModel: string;
    stockName: string;
    stockUnit: string;
}

export interface ICustomStockItem extends ICustomStockItemApi {
    modelValue: number | string;
}
export interface AAListWithAcronymModel {
    aa_name: string;
    aaeid: number;
    asId: number;
    endDate: null | string;
    note: string;
    startDate: null | string;
    stockModel: string;
    stockType: string;
    unit: string;
    userSn: number;
}
export interface AAListWithAcronymItem {
    aaacronym: string;
    aaeid: number;
    aaname: string;
    aanum: string;
    aanumLen: number;
    aatype: number;
    asid: number;
    model: null | AAListWithAcronymModel;
    status: number;
}
export interface IFSearchItem {  
    name: string;
    note: string;
    number: string;
    invoiceTypes: number[];  
    businessTypes: number[];
    invoiceStatus: string;  
}
export interface IEisPermissionRes {
    hasInvoiceIssuePermission: boolean;
    companyId:string,
    companyName:string,
    adminPhoneNumber:string,
}
