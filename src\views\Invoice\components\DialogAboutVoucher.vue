<template>
<div v-if="display">
    <el-dialog 
        v-model="display" 
        center width="832" 
        class="about-voucehr-dialog dialogDrag" 
        title="关联凭证" 
        @close="resetDialogInfo"
        :draggable="false"
    >
        <div class="about-voucehr-content" v-dialogDrag>
            <div class="about-voucehr-main">
                <div class="voucehr-main-top mb-10">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">
                                <el-select :teleported="false" v-model="selectorType">
                                    <el-option label="会计期间：" value="0" />
                                    <el-option label="凭证日期：" value="1" />
                                </el-select>
                            </div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="startMonth"
                                    v-model:endPid="endMonth"
                                    v-show="selectorType === '0'"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :disabledDateStart="disabledDateMonth"
                                    :disabledDateEnd="disabledDateMonth"
                                />
                                <DatePicker
                                    v-model:startPid="startDate"
                                    v-model:endPid="endDate"
                                    v-model:periodInfo="periodInfoDate"
                                    v-show="selectorType === '1'"
                                    :clearable="true"
                                    :disabled-date-start="disabledDate"
                                    :disabled-date-end="disabledDate"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证字：</div>
                            <div class="line-item-field small">
                                <Select 
                                    :teleported="false" 
                                    v-model="searchInfo.vgId"
                                    :filterable="true"
                                    :filter-method="voucherGroupFilterMethod"
                                >
                                    <ElOption 
                                        v-for="item in showVoucherGroupList" 
                                        :key="item.id" 
                                        :value="item.id + ''" 
                                        :label="item.title" 
                                    />
                                </Select>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">制单人：</div>
                            <div class="line-item-field small">
                                <Select 
                                    :teleported="false" 
                                    v-model="searchInfo.prepareBy"
                                    :filterable="true"
                                    :filter-method="userNameFilterMethod"
                                    :remote="true"
                                    :remoteShowSuffix="true"
                                >
                                    <ElOption 
                                        v-for="(item, index) in showUserNameList" 
                                        :key="index" 
                                        :label="item.label" 
                                        :value="item.value" 
                                    />
                                </Select>
                            </div>
                        </div>
                        <div class="line-item input" v-show="checkNeeded">
                            <div class="line-item-title">是否审核：</div>
                            <div class="line-item-field small">
                                <el-select :teleported="false" v-model="searchInfo.approvalStatus">
                                    <el-option label="全部" value="-1" />
                                    <el-option label="已审核" value="2" />
                                    <el-option label="未审核" value="1" />
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">摘要：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.description" style="width: 298px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">备注：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.note" style="width: 298px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目：</div>
                            <div class="line-item-field subject">
                                <SubjectPicker
                                    ref="asubRef"
                                    v-model="searchInfo.asubCode"
                                    :need-asub-name="false"
                                    :show-disabled="true"
                                    asubImgRight="16px"
                                ></SubjectPicker>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">金额：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.startAmount" style="width: 132px" clearable />
                                <span class="mr-10 ml-10">至</span>
                                <el-input v-model="searchInfo.endAmount" style="width: 132px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">凭证号：</div>
                            <div class="line-item-field">
                                <el-input v-model="searchInfo.startVNum" style="width: 132px" clearable />
                                <span class="mr-10 ml-10">至</span>
                                <el-input v-model="searchInfo.endVNum" style="width: 132px" clearable />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">排序依据：</div>
                            <div class="line-item-field">
                                <el-radio-group v-model="searchInfo.sortColumn">
                                    <el-radio label="0">凭证号排序</el-radio>
                                    <el-radio label="1">凭证日期排序</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">排序方式：</div>
                            <div class="line-item-field">
                                <el-radio-group v-model="searchInfo.sortType">
                                    <el-radio label="0">升序</el-radio>
                                    <el-radio label="1">降序</el-radio>
                                </el-radio-group>
                            </div>
                        </div>
                        <div class="buttons left">
                            <a class="button solid-button" @click="handleConfirm">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                </div>
                <div v-loading="loadingVal" element-loading-text="正在加载数据...">
                    <Table
                        class="voucher-table"
                        ref="voucherTableRef"
                        :data="tableData"
                        :columns="columns"
                        :empty-text="emptyText"
                        :height="300"
                        :page-is-show="true"
                        :layout="paginationData.layout"
                        :page-sizes="paginationData.pageSizes"
                        :page-size="paginationData.pageSize"
                        :currentPage="paginationData.currentPage"
                        :total="paginationData.total"
                        :show-overflow-tooltip="false"
                        :scrollbarShow="true"
                        @current-change="handleCurrentChange"
                        @size-change="handleSizeChange"
                        @refresh="handleRerefresh"
                        @scroll="handleScroll"
                        :tableName="setModule"
                    >
                        <template #date>
                            <el-table-column
                                label="凭证日期"
                                min-width="85"
                                prop="date"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'date')"
                            >
                                <template #default="scope">
                                    <div class="collapse-cell">
                                        <div :class="{ suggest: scope.row.suggest }"></div>
                                        <Tooltip
                                            class="test"
                                            :content="dayjs(scope.row.vdate).format('YYYY-MM-DD')" 
                                            :dynamicWidth="true" 
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ dayjs(scope.row.vdate).format("YYYY-MM-DD") }}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #num>
                            <el-table-column
                                label="凭证字号"
                                min-width="80"
                                prop="num"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'num')"
                            >
                                <template #default="scope">
                                    <div class="collapse-cell">
                                        <Tooltip
                                            class="test"
                                            :content="scope.row.vgName + '-' + scope.row.vnum" 
                                            :dynamicWidth="true" 
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ scope.row.vgName + "-" + scope.row.vnum}}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #descript>
                            <el-table-column
                                label="摘要"
                                min-width="120"
                                prop="descript"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'descript')"
                            >
                                <template #default="scope">
                                    <div v-show="scope.row.expend || lineIndex < 2" 
                                        v-for="(voucherLine, lineIndex) in scope.row.voucherLines" 
                                        :key="lineIndex"
                                        class="collapse-txt collapse-cell"
                                    >
                                        <Tooltip
                                            class="test"
                                            :content="voucherLine.description" 
                                            :dynamicWidth="true" 
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ voucherLine.description }}</div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="scope.row.expend" class="collapse-txt collapse-cell">
                                        <Tooltip 
                                            class="test"
                                            :content="'合计：' + digitUppercase(calcTotalMoney(scope.row.voucherLines, 'debit'))" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">合计：{{ digitUppercase(calcTotalMoney(scope.row.voucherLines, "debit")) }}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #subject>
                            <el-table-column
                                label="科目"
                                min-width="120"
                                prop="subject"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'subject')"
                            >
                                <template #default="scope">
                                    <div v-show="scope.row.expend || lineIndex < 2" 
                                        v-for="(voucherLine, lineIndex) in scope.row.voucherLines" 
                                        :key="lineIndex"
                                        class="collapse-txt collapse-cell"
                                    >
                                        <Tooltip 
                                            class="test"
                                            :content="voucherLine.asubName" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ voucherLine.asubName }}</div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="scope.row.expend" class="collapse-txt"></div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #debit>
                            <el-table-column
                                label="借方金额"
                                min-width="85"
                                prop="debit"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'debit')"
                            >
                                <template #default="scope">
                                    <div v-show="scope.row.expend || lineIndex < 2" 
                                        v-for="(voucherLine, lineIndex) in scope.row.voucherLines" 
                                        :key="lineIndex"
                                        class="collapse-txt collapse-cell"
                                    >
                                        <Tooltip 
                                            :content="formatMoney(voucherLine.debit)" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                            >
                                            <div class="textOverflow">{{ formatMoney(voucherLine.debit) }}</div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="scope.row.expend" class="collapse-txt collapse-cell">
                                        <Tooltip 
                                            :content="formatMoney(calcTotalMoney(scope.row.voucherLines, 'debit'))" 
                                            :dynamicWidth="true" 
                                            placement="right"
                                            :hide-after="0"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ formatMoney(calcTotalMoney(scope.row.voucherLines, "debit")) }}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #credit>
                            <el-table-column
                                label="贷方金额"
                                min-width="85"
                                prop="credit"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'credit')"
                            >
                                <template #default="scope">
                                    <div v-show="scope.row.expend || lineIndex < 2" 
                                        v-for="(voucherLine, lineIndex) in scope.row.voucherLines" 
                                        :key="lineIndex"
                                        class="collapse-txt collapse-cell"
                                    >
                                        <Tooltip 
                                            :content="formatMoney(voucherLine.credit)" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ formatMoney(voucherLine.credit) }}</div>
                                        </Tooltip>
                                    </div>
                                    <div v-show="scope.row.expend" class="collapse-txt collapse-cell">
                                        <Tooltip 
                                            :content="formatMoney(calcTotalMoney(scope.row.voucherLines, 'credit'))" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ formatMoney(calcTotalMoney(scope.row.voucherLines, "credit")) }}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #create>
                            <el-table-column
                                label="制单人"
                                min-width="75"
                                prop="create"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'create')"
                            >
                                <template #default="scope">
                                    <div class="collapse-cell">
                                        <Tooltip 
                                            :content="scope.row.preparedBy" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :offset="-4"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ scope.row.preparedBy }}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #check>
                            <el-table-column
                                label="审核人"
                                min-width="75"
                                prop="check"
                                align="left"
                                header-align="left"
                                :width="getColumnWidth(setModule, 'check')"
                            >
                                <template #default="scope">
                                    <div class="collapse-cell">
                                        <Tooltip 
                                            :content="scope.row.approvedBy" 
                                            :dynamicWidth="true"
                                            placement="right"
                                            :offset="-2"
                                            :fontSize="12"
                                        >
                                            <div class="textOverflow">{{ scope.row.approvedBy }}</div>
                                        </Tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                        <template #operation>
                            <el-table-column
                                label="操作"
                                min-width="85"
                                align="left"
                                header-align="left"
                                :resizable="false"
                            >
                                <template #default="scope">
                                    <div class="collapse-cell">
                                        <span class="link" @click="handleExpend(scope.row)">{{ scope.row.expend ? "收起" : "展开" }}</span>
                                        <span class="link ml-10" @click="getVourcher(scope.row.pid, scope.row.vid)">关联</span>
                                    </div>
                                </template>
                            </el-table-column>
                        </template>
                    </Table>
                    <div class="table-tip mt-10">单据关联凭证后，单据附件将自动添加到凭证附件中</div>
                </div>
            </div>
            <div class="buttons bt">
                <a class="button" @click="resetDialogInfo">取消</a>
            </div>
        </div>
    </el-dialog>
</div>
</template>

<script setup lang="ts">
import { ref, toRef, reactive, computed, onBeforeMount, watch, watchEffect } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney, digitUppercase } from "@/util/format";
import { useVoucherGroupStore } from "@/store/modules/voucherGroup";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { dayjs } from "element-plus";
import { getUrlSearchParams } from "@/util/url";
import type { IVoucherModel, IVoucherLine } from "@/views/Voucher/VoucherList/types";
import type { IPeriod } from "@/api/period";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import DatePicker from "@/components/DatePicker/index.vue";
import SubjectPicker from "@/components/Picker/SubjectPicker/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { PeriodStatus } from "@/api/period";
import type { InvoiceLineSn } from "../types";
import { ElNotify } from "@/util/notify";
import type { IVoucherSetting } from '@/components/Dialog/GenerateVoucherSetting/type'
import Table from "@/components/Table/index.vue";
import { usePagination } from "@/hooks/usePagination";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import ElOption from "@/components/Option/index.vue";
import Select from "@/components/Select/index.vue";
import { initStartOrEndMonth, getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { commonFilterMethod } from "@/components/Select/utils";
import { handleTrialExpired, ExpiredToBuyDialogEnum } from "@/util/proUtils";

const setModule = "InvoiceAboutVoucher";
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();
const emit = defineEmits([
    "about-vourcher",
]);

interface IVoucherItem extends IVoucherModel {
    expend: boolean;
    suggest: boolean;
    height: number;
}
const periodStore = useAccountPeriodStore();
const periodList = computed(() => {
    let list = periodStore.periodList.filter((p) => p.status < PeriodStatus.CheckOut).sort((p1, p2) => p2.pid - p1.pid);
    return list.map((item) => {
        return {
            ...item,
            time: item.year + "" + String(item.sn).padStart(2, "0")
        };
    });
})
// const periodList = ref<IPeriod[]>([]);
// async function getPeriodList() {
//     await request({ 
//         url: "/api/Period/ListWithStatus",
//         method: "get",
//     }).then((res: IResponseModel<IPeriod[]>)=>{
//         periodList.value = res.data.filter((p) => p.status < PeriodStatus.CheckOut).sort((p1, p2) => p2.pid - p1.pid);
//     })
// }
//未结账开始日期
const minCheckOutDate = dayjs(new Date(
    periodStore.periodList.filter((p) => p.status < PeriodStatus.CheckOut)[0]?.startDate || ""
)).format("YYYY-MM-DD");

const userNameList = ref<Array<any>>([]);
const voucherGroupList = toRef(useVoucherGroupStore(), "voucherGroupList");
const checkNeeded = computed(() => useAccountSetStore().accountSet!.checkNeeded === 1);
const selectorType = ref<"0" | "1">("0");
const startPid = ref(0);
const endPid = ref(0);
const startMonth = ref("");
const endMonth = ref("");
const startDate = ref("");
const endDate = ref("");
const periodInfoDate = ref("");
const currentPeriodInfo = ref("");
// const periodInfo = computed(() => {
//     if (startPid.value === 0 || startPid.value === 0) {
//         return "----年--月";
//     }
//     if (startPid.value === endPid.value) {
//         return getPeriodInfoByPID(startPid.value);
//     } else {
//         return getPeriodInfoByPID(startPid.value) + "—" + getPeriodInfoByPID(endPid.value);
//     }
// });
const searchInfo = reactive({
    vgId: "",
    prepareBy: "",
    approvalStatus: "-1",
    description: "",
    note: "",
    asubCode: "",
    startAmount: "",
    endAmount: "",
    startVNum: "",
    endVNum: "",
    sortColumn: "0",
    sortType: "0",
});

const emptyText = ref("");
const columns = [
    { slot: "date" },
    { slot: "num" },
    { slot: "descript" },
    { slot: "subject" },
    { slot: "debit" },
    { slot: "credit" },
    { slot: "create" },
    { slot: "check" },
    { slot: "operation" },
];
const voucherTableRef = ref();
const handleScroll = () => {
    const tableBody = voucherTableRef.value?.$el.querySelector(".el-scrollbar__wrap");
    let scrollTop = tableBody.scrollTop;
    if (scrollTop > 0) {
        let  popperList = tableBody.querySelectorAll(".el-popper");
        if (popperList.length > 0) {
            popperList.forEach((item: HTMLElement)=> {
                item.style.display = "none";
            })
        }
    }
}
const loadingVal = ref(false);
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
function handleConfirm() {
    startPid.value = periodList.value.find((item) => item.time === startMonth.value)?.pid || 0;
    endPid.value = periodList.value.find((item) => item.time === endMonth.value)?.pid || 0;
    if (startPid.value > endPid.value) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哟！" });
        return;
    }
    if (dayjs(startDate.value).valueOf() > dayjs(endDate.value).valueOf()) {
        ElNotify({ type: "warning", message: "亲，开始日期不能晚于结束日期哟！" });
        return;
    }
    if (!startDate.value || !endDate.value) {
        ElNotify({ type: "warning", message: "亲，起止日期不能为空！" });
        return;
    }
    handleClose();
    paginationData.currentPage = 1;
    handleSearch();
}
async function handleSearch() { 
    setCurrentPeriodInfo();
    const params = { 
        ...searchInfo, 
        startPId: startPid.value,
        endPId: endPid.value,
        startDate: startDate.value,
        endDate: endDate.value,
        onlyTempVoucher: false, 
        searchInfo: "", 
        selectorType: selectorType.value, 
        pageIndex: paginationData.currentPage, 
        pageSize: paginationData.pageSize 
    };
    loadingVal.value = true;
    await request({ 
        url: "/api/Voucher/PagingList?" + getUrlSearchParams(params) 
    }).then(
        (res: IResponseModel<{ count: number; data: Array<IVoucherModel> }>) => {
            if (res.state !== 1000) return;
            tableData.value = res.data.data.map((item: IVoucherModel) => {
                return { ...item, expend: false, suggest: false, height: calcTableItemHeight(item.voucherLines.length, false) };
            });
            paginationData.total = res.data.count;
            if (res.data.data.length === 0) {
                emptyText.value = "暂无数据";
            }
        }
    ).finally(()=>{
        loadingVal.value = false;
    });
}

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    handleSearch();
});

function handleExpend(item: IVoucherItem) {
    item.expend = !item.expend;
    item.height = calcTableItemHeight(item.voucherLines.length, item.expend);
}
function handleClose() {
    containerRef.value?.handleClose();
}
function handleReset() {
    searchInfo.vgId = "";
    searchInfo.prepareBy = "";
    searchInfo.approvalStatus = "-1";
    searchInfo.description = "";
    searchInfo.note = "";
    searchInfo.asubCode = "";
    searchInfo.startAmount = "";
    searchInfo.endAmount = "";
    searchInfo.startVNum = "";
    searchInfo.endVNum = "";
    searchInfo.sortColumn = "0";
    selectorType.value = "0";
    searchInfo.sortType = "0";
    startPid.value = saveStartPid;
    endPid.value = saveEndPid;
}

// function getPeriodInfoByPID(pid: number) {
//     for (let i = 0; i < periodList.value.length; i++) {
//         const item = periodList.value[i];
//         if (item.pid === pid) {
//             return getPeriodInfo(item);
//         }
//     }
//     return "";
// }
// function getPeriodInfo(period: IPeriod) {
//     return period.year + "年" + period.sn + "月";
// }
function setCurrentPeriodInfo() {
    currentPeriodInfo.value = selectorType.value === "0" ? getCurrentPeriodInfo(periodList.value, startPid.value, endPid.value) : periodInfoDate.value;
}
function disabledDate(time: Date) {
    const now = new Date();
    const accountStartDate = dayjs(minCheckOutDate).valueOf();
    now.setFullYear(now.getFullYear() + 5);
    return time.getTime() < accountStartDate || time.getTime() > dayjs(now).valueOf();
}
function disabledDateMonth(time: Date) {
    const start = periodList.value[periodList.value.length - 1]?.time ?? new Date();
    const end = periodList.value[0]?.time ?? new Date();
    const asStartDate = dayjs(start).valueOf();
    const asEndDate = dayjs(end).valueOf();
    return time.getTime() < asStartDate || time.getTime() > asEndDate;
}
function calcTotalMoney(voucherLines: Array<IVoucherLine>, type: "debit" | "credit") {
    const result: number = voucherLines.reduce((prev, cur) => {
        prev += cur[type] - 0;
        return prev;
    }, 0);
    return result;
}
const defaultLineHeight = 37;
function calcTableItemHeight(length: number, expend: boolean) {
    if (!expend) return defaultLineHeight * 2;
    return defaultLineHeight * (length + 1);
}
function resetDialogInfo() {
    display.value = false;
    voucherTableRef.value?.setScrollTop(0);
    invoiceIds.value = [];
    recommendId.value = 0;
    paginationData.currentPage = 1;
    handleReset();
}
async function handleGetUserNameList() {
    userNameList.value = [{ value: "", label: "全部" }];
    await request({ url: "/api/Voucher/GetVoucherPreparedBys", method: "post" }).then((res: IResponseModel<Array<string>>) => {
        if (res.state !== 1000) return;
        let list = res.data.map((item) => {
            return {
                value: item,
                label: item,
            }
        });
        userNameList.value = [...userNameList.value, ...list];
    });
}
onBeforeMount(() => {
    handleGetUserNameList();
});

const display = ref(false);
const tableData = ref<Array<IVoucherItem>>([]);
function findPeriodItem(date: string) {
    return periodList.value.find(
        (item) => item.year + item.sn.toString().padStart(2, "0") === dayjs(date).format("YYYYMM")
    );
}

let saveStartPid:number = 0; //保存一份初始值，重置时用
let saveEndPid:number = 0;
function defaultDate(invoiceLineList: Array<InvoiceLineSn>) {
    let startItem;
    let startIndex:number = 0;
    let startList:Array<InvoiceLineSn> = JSON.parse(JSON.stringify(invoiceLineList));
    for(let i=0; i<startList.length; i++) {
        startItem = findPeriodItem(startList[i].invoiceDateText);
        startIndex = i;
        if (startItem) break;
    }
    let endItem;
    let endIndex:number = 0;
    let endList:Array<InvoiceLineSn> = JSON.parse(JSON.stringify(invoiceLineList.reverse()));
    for(let i=0; i<endList.length; i++) {
        endItem = findPeriodItem(endList[i].invoiceDateText);
        endIndex = i;
        if (endItem) break;
    }
    if (startItem) {
        startPid.value = startItem.pid;
        startDate.value = dayjs(startList[startIndex].invoiceDateText).format("YYYY-MM-DD");
    } else {
        startPid.value = periodList.value[0].pid;
        if (endItem) {
            startDate.value = dayjs(endList[endIndex].invoiceDateText).format("YYYY-MM-DD");
        } else {  
            let time = dayjs(minCheckOutDate).valueOf() > dayjs(new Date()).valueOf() ? minCheckOutDate : new Date();  
            startDate.value = dayjs(time).format("YYYY-MM-DD");
        }
    }
    if (endItem) {
        endPid.value = endItem.pid;
        endDate.value = dayjs(endList[endIndex].invoiceDateText).format("YYYY-MM-DD");
    } else {
        endPid.value = periodList.value[0].pid;
        let time = dayjs(minCheckOutDate).valueOf() > dayjs(new Date()).valueOf() ? minCheckOutDate : new Date();
        endDate.value = dayjs(time).format("YYYY-MM-DD");
    }
    let result = initStartOrEndMonth(periodList.value, Number(startPid.value), Number(endPid.value));
    startMonth.value = result.startMonth;
    endMonth.value = result.endMonth;
    saveStartPid = startPid.value;
    saveEndPid = endPid.value;
}
const invoiceIds = ref<Array<number>>([]);
//打开关联凭证列表弹窗
async function handleOpenDialog(
    invoiceLineList: Array<InvoiceLineSn>, 
    invoiceCategory: string, 
    hasRecommend: boolean, 
    VoucherSettings?: IVoucherSetting
) {
    display.value = true;
    invoiceLineList.forEach((item)=>{
        invoiceIds.value.push(item.invoiceId);
    })
    // await getPeriodList();
    defaultDate(invoiceLineList);
    await handleSearch();
    if (hasRecommend) {
        await getRecommendVoucher(invoiceCategory, JSON.parse(JSON.stringify(VoucherSettings)));
    }
    if (recommendId.value) {
        const index = tableData.value.findIndex((item) => item.vid === recommendId.value);
        if (index !== -1) {
            const item = tableData.value[index];
            item.suggest = true;
            const changeItem = tableData.value.splice(index, 1)[0];
            tableData.value.unshift(changeItem);
        }
    }
}
//点击关联
let timer: any;
const getVourcher = async (pid: number, vid: number) => {
    let isTrilExpired = await handleTrialExpired({ msg: ExpiredToBuyDialogEnum.relateVoucher });
    if (isTrilExpired) {
        return;
    }
    if (timer) {
        clearTimeout(timer);
        return;
    }
    getVourcherFn(pid, vid);
    timer = setTimeout(() => {
        timer = 0;
    }, 200); 
};
const getVourcherFn = (pid:number, vid: number) => {
    const data = {
        invoiceIds: invoiceIds.value,
        pId: pid,
        vId: vid,
        isNeedSaveToVoucher: true,
    };
    request({ 
        url: "/api/InvoiceVoucher/BatchBindVoucher",
        method: "post",
        data: data
    }).then((res: IResponseModel<{ successCount: number; skipCount: number }>) => {
        if (res.state === 1000) {
            let msg = `成功：${res.data.successCount}，跳过：${res.data.skipCount} (已生成凭证的数据已跳过)`;
            ElNotify({
                type: "success",
                message: msg,
            });
            emit("about-vourcher", res.data.successCount);
        } else {
            ElNotify({
                type: "warning",
                message: res.msg,
            });
        }
    }).finally(()=>{
        resetDialogInfo();
    });
}
//推荐凭证
const recommendId = ref(0);
async function getRecommendVoucher(invoiceCategory: string, VoucherSettings:IVoucherSetting) {
    await request({ 
        url: "/api/InvoiceVoucher/GetRecommendedVoucher", 
        method: "post", 
        data: {
            invoiceIds: invoiceIds.value,
            invoiceCategory: invoiceCategory,
            VoucherSettings: VoucherSettings
        } 
    }).then((res: IResponseModel<any>) => {
        if (res.state === 1000) {
            if (res.data.isMatch) {
                recommendId.value = res.data.voucher.vid;
            }
        }
    });
}
defineExpose({ handleOpenDialog });

const voucherGroupListAll = ref<Array<any>>([]);
const showVoucherGroupList = ref<Array<any>>([]);
const showUserNameList = ref<Array<any>>([]);
watchEffect(() => {
    voucherGroupListAll.value = JSON.parse(JSON.stringify(voucherGroupList.value));
    voucherGroupListAll.value.unshift({ id: "", title: "全部" });
    showVoucherGroupList.value = JSON.parse(JSON.stringify(voucherGroupListAll.value));
    showUserNameList.value = JSON.parse(JSON.stringify(userNameList.value));
});
function voucherGroupFilterMethod(value: string) {
    showVoucherGroupList.value = commonFilterMethod(value, voucherGroupListAll.value, 'title');
}
function userNameFilterMethod(value: string) {
    showUserNameList.value = commonFilterMethod(value, userNameList.value, 'label');
}
</script>

<style lang="less" scoped>
.about-voucehr-content {
    .buttons.bt {
        border-top: 1px solid var(--border-color);
    }
    .collapse-cell {
        padding: 0 8px;
    }
    .suggest {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 0;
        left: 0;
        background-image: url("@/assets/Invoice/suggest.png");
        background-repeat: no-repeat;
        background-size: 40px 40px;
        background-position: -2px -2px;
    }
    .collapse-txt {
        height: 36px;
        line-height: 36px;
    }
    .collapse-txt + .collapse-txt {
        border-top: 1px solid var(--border-color);
    }
    
    :deep(.custom-table) {
        &.voucher-table {
            tbody tr td .cell {
                padding: 0;
            }
            .el-table__cell {
                position: static;
            }
            tbody tr:first-child {
                position: relative;
            }
        }
    }
    .about-voucehr-main {
        padding: 10px;
        .title-cell-item,
        .table-cell-item {
            box-sizing: border-box;
            &.date,
            &.operate,
            &.debit,
            &.credit {
                width: 85px;
            }
            &.num {
                width: 80px;
            }
            &.desc,
            &.asub {
                width: 120px;
            }
            &.create,
            &.check {
                width: 75px;
            }
        }
        .table-title {
            border: 1px solid var(--border-color);
            display: inline-flex;
            align-items: center;
            background-color: rgb(232, 232, 232);
            height: 40px;
            border-bottom: none;
            .title-cell-item {
                height: 100%;
                line-height: 40px;
                border-right: 1px solid var(--border-color);
                padding-left: 5px;
                &.operate {
                    border-right: none;
                }
            }
        }
        .table-item {
            border-bottom: 1px solid var(--border-color);
            border-top: none;
            display: flex;
            align-items: center;
            font-size: 12px;
            .pl-5 {
                padding: 0 5px;
            }
            .table-cell-item {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                border-right: 1px solid var(--border-color);
                overflow: hidden;
                &.operate {
                    border-right: none;
                    flex-direction: row;
                    align-items: center;
                    justify-content: flex-start;
                    span.link {
                        color: var(--main-color);
                        font-size: 12px;
                    }
                }
                & > div {
                    box-sizing: border-box;
                    height: 37px;
                    line-height: 37px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 100%;
                    white-space: nowrap;
                    border-bottom: 1px solid var(--border-color);
                }

                &.expend {
                    & > div {
                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }

                &:not(.expend) {
                    & > div {
                        &:nth-child(2) {
                            border-bottom: none;
                        }
                    }
                }
            }
            &:first-child {
                border-bottom: 1px solid var(--border-color);
            }
            &.suggest {
                border-top: none;
                background-image: url("@/assets/INvoice/suggest.png");
                background-repeat: no-repeat;
                background-size: 40px 40px;
                background-position: -2px -2px;
            }
        }
        .table-tip {
            padding-left: 20px;
            line-height: 20px;
            background: url("@/assets/Icons/warn.png") no-repeat 2px 2px;
        }
    }
}
.period-container {
    display: flex;
    align-items: center;
}
.line-item-field {
    :deep(.el-select) {
        width: 132px;
    }
}
.line-item-title {
    :deep(.el-select) {
        width: 97px;
        margin-left: -12px;
        .el-input__wrapper {
            padding: 0 5px 0 10px;
        }
        .el-icon {
            margin-left: 0;
        }
        .el-input__inner {
            text-overflow: initial;
        }
    }
}
.textOverflow {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

</style>
<style lang="less">
.about-voucehr-dialog {
    margin-top: 80px;
}
</style>
