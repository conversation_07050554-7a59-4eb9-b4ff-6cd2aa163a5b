<template>
    <el-dialog v-model="display" title="选择模板" center width="600px" class="custom-confirm dialogDrag">
        <div class="vouchertemplate-container" v-dialogDrag>
            <div class="searchinfo">
                <search
                    :placeholder="'输入模板名称搜索'"
                    :width="200"
                    :height="30"
                    @search="reloadVoucherTemplate"
                    ref="searchRef"
                ></search>
            </div>
            <div class="vouchertemplate-content">
                <div class="table-container">
                    <Table 
                        ref="tableRef" 
                        :data="voucherTemplates" 
                        :columns="columns" 
                        :height="259" 
                        :loading="loading" 
                        @row-dblclick="onDblSelect" 
                        @row-click="onSelect"
                    ></Table>
                </div>
                <div class="move-buttons">
                    <a
                        class="button move-button prev"
                        :class="{ disabled: selectedIndex === -1 || selectedIndex <= 0, erp: isErp }"
                        @click="moveVoucherTemplate(1)"
                        >前移</a
                    >
                    <a
                        class="button move-button next"
                        :class="{ disabled: selectedIndex === -1 || selectedIndex >= voucherTemplates.length - 1, erp: isErp }"
                        @click="moveVoucherTemplate(2)"
                        >后移</a
                    >
                </div>
            </div>
            <div class="buttons">
                <a class="button" @click="display = false">取消</a>
                <a class="button solid-button ml-10" @click="selectVoucherTemplate()">确定</a>
            </div>
            <a class="link edit-vouchertemplate" @click="editVoucherTemplate()">编辑模板</a>
        </div>
    </el-dialog>
</template>
<style lang="less" scoped>
.vouchertemplate-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    position: relative;

    .searchinfo {
        padding: 10px 10px 0 10px;
    }

    .vouchertemplate-content {
        display: flex;
        align-items: center;

        .table-container {
            padding: 10px 20px 5px 10px;
            width: 488px;
            box-sizing: border-box;
        }

        .move-buttons {
            display: flex;
            flex-direction: column;

            .move-button {
                display: flex;
                align-items: center;
                justify-content: center;
                border-color: #b5e0b7;

                &.prev,
                &.next {
                    &::before {
                        content: " ";
                        width: 8px;
                        height: 9px;
                        margin-right: 8px;
                        background-size: 100%;
                        background-repeat: no-repeat;
                    }
                }

                &.prev {
                    &::before {
                        background-image: url("@/assets/voucher/move-up.png");
                    }

                    &.erp {
                        &::before {
                            background-image: url("@/assets/voucher/erp-move-up.png");
                        }
                    }
                }

                &.next {
                    margin-top: 10px;
                    margin-bottom: 24px;

                    &::before {
                        background-image: url("@/assets/voucher/move-down.png");
                    }

                    &.erp {
                        &::before {
                            background-image: url("@/assets/voucher/erp-move-down.png");
                        }
                    }
                }

                &:hover,
                &:active {
                    border-color: var(--main-color);
                    background-color: #f4fff5;
                    color: var(--font-color);
                }

                &.disabled {
                    border-color: rgba(181, 224, 183, 0.5);
                    color: var(--border-color);
                    background-color: var(--white);

                    &.prev {
                        &::before {
                            background-image: url("@/assets/voucher/move-up-disabled.png");
                        }

                        &.erp {
                            &::before {
                                background-image: url("@/assets/voucher/erp-move-up-disabled.png");
                            }
                        }
                    }

                    &.next {
                        &::before {
                            background-image: url("@/assets/voucher/move-down-disabled.png");
                        }

                        &.erp {
                            &::before {
                                background-image: url("@/assets/voucher/erp-move-down-disabled.png");
                            }
                        }
                    }
                }
            }
        }
    }

    .buttons {
        padding: 10px;
        display: flex;
        justify-content: center;
        border-top: 1px solid var(--border-color);
    }

    .edit-vouchertemplate {
        position: absolute;
        bottom: 76px;
        right: 32px;
    }
}

:deep(.el-scrollbar__bar) {
    &.is-vertical {
        display: block !important;
        opacity: 1;
    }
}

body[erp] {
    .custom-confirm .el-dialog__body .buttons {
        display: flex;
        justify-content: flex-end;
    }
    .vouchertemplate-container {
        .searchinfo {
            padding-left: 30px;
        }
        .vouchertemplate-content {
            margin-left: 20px;
            .move-buttons {
                .move-button.disabled {
                    color: #b7b7b7;
                    border-color: #e9e9e9;
                    background-color: #f5f5f5;
                }
            }
        }
    }
}
</style>
<script setup lang="ts">
import search from "@/components/SearchInfo/index.vue";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import Table from "@/components/Table/index.vue";
import { ref } from "vue";
import type { VoucherTemplateModel } from "@/components/Voucher/types";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { globalWindowOpenPage } from "@/util/url";

const emit = defineEmits<{
    (e: "onSelect", vtId: number): void,
    (e: "exitFullScreen"): void
}>();
const isErp = ref(window.isErp);
const display = ref(false);
const loading = ref(false);
const columns = ref<IColumnProps[]>([
    {
        label: "模板类型",
        prop: "vtTypeName",
        align: "left",
        headerAlign: "center",
        minWidth: 228,
        resizable: false,
    },
    {
        label: "模板名称",
        prop: "vtName",
        align: "left",
        headerAlign: "center",
        minWidth: 228,
        resizable: false,
    },
]);
const voucherTemplates = ref<Array<VoucherTemplateModel>>(new Array<VoucherTemplateModel>());
const selectedIndex = ref(-1);
const searchRef = ref<InstanceType<typeof search>>();
const tableRef= ref()
const tableContainerScrollTop= ref(0)
const onSelect = (val: any, column: any, event: any) => {
    for (let i = 0; i < voucherTemplates.value.length; i++) {
        if (voucherTemplates.value[i] === val) {
            tableContainerScrollTop.value = 0
            selectedIndex.value = i;
            break;
        }
    }
};

const onDblSelect = (val: any, column: any, event: any) =>{
    onSelect(val, column, event);
    selectVoucherTemplate();
}

function showVoucherTemplates() {
    display.value = true;
    searchRef.value?.clear();
    reloadVoucherTemplate("");
}

function reloadVoucherTemplate(searchInfo: string) {
    loading.value = true;
    request({
        url: "/api/VoucherTemplate/List?searchInfo=" + searchInfo,
        method: "get",
    }).then((res: IResponseModel<Array<VoucherTemplateModel>>) => {
        if (res.state === 1000) {
            voucherTemplates.value = res.data;
            selectedIndex.value = -1;
        }
        loading.value = false;
    });
}

function moveVoucherTemplate(moveType: 1 | 2) {
    if (selectedIndex.value === -1) {
        return;
    }
    let oppositeVtId = -1;
    if (moveType === 1) {
        if (selectedIndex.value <= 0) {
            return;
        } else {
            oppositeVtId = voucherTemplates.value[selectedIndex.value - 1].vtId;
        }
    }
    if (moveType === 2) {
        if (selectedIndex.value >= voucherTemplates.value.length - 1) {
            return;
        } else {
            oppositeVtId = voucherTemplates.value[selectedIndex.value + 1].vtId;
        }
    }
    loading.value = true;
    request({
        url: `/api/VoucherTemplate/Move?vtId=${
            voucherTemplates.value[selectedIndex.value].vtId
        }&oppositeVtId=${oppositeVtId}&moveType=${moveType}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000 && res.data) {
            let row = voucherTemplates.value.splice(selectedIndex.value, 1)[0];
            const tableContainer = tableRef.value?.$el.querySelector(".el-scrollbar__wrap");
            if (moveType === 1 || moveType === 2) {
                const indexOffset = moveType === 1 ? -1 : 1;
                voucherTemplates.value.splice(selectedIndex.value + indexOffset, 0, row);
                selectedIndex.value += indexOffset;
                if (tableContainerScrollTop.value) {
                    tableContainer.scrollTop = tableContainerScrollTop.value + (moveType === 1 ? -38 : 38);
                } else {
                    tableContainer.scrollTop += (moveType === 1 ? -38 : 38);
                }
            }
            tableContainerScrollTop.value=tableContainer.scrollTop
        }
    }).finally(() => {
        loading.value = false;
    });
}

function selectVoucherTemplate() {
    if (selectedIndex.value === -1) {
        ElNotify({
            message: "亲，您还没有选择模板哟！",
            type: "warning",
        });
        return;
    }
    emit("onSelect", voucherTemplates.value[selectedIndex.value].vtId);
    display.value = false;
}

function editVoucherTemplate() {
    emit('exitFullScreen')
    globalWindowOpenPage("/Settings/VoucherTemplate", "凭证模板");
}
function close() {
    display.value = false;
}
defineExpose({
    showVoucherTemplates,
    close,
});
</script>
