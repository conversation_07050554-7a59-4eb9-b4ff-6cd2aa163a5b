<template>
    <div class="content">
        <div class="title">总账对账差异明细</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between">
                <div class="main-tool-left">
                    <div>
                        期间：<span>{{ periodInfo }}</span>
                    </div>
                    <div class="ml-10">
                        账户：<span>{{ spanAcName }}</span>
                    </div>
                    <div class="ml-10">
                        币别：<span>{{ spanFcName }}</span>
                    </div>
                    <div class="ml-10">
                        科目：<span>{{ spanAsubName }}</span>
                    </div>
                    <ErpRefreshButton></ErpRefreshButton>
                </div>
                <div class="main-tool-right">
                    <a v-permission="['cdcheckdetail-canprint']" class="button mr-10" @click="handlePrint">打印</a>
                    <a v-permission="['cdcheckdetail-canexport']" class="button" @click="handleExport">导出</a>
                </div>
            </div>
            <div class="main-center">
                <Table
                    :class="isErp ? 'erp-table' : ''"
                    :data="tableData"
                    :columns="columns"
                    :page-is-show="true"
                    :loading="loading"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :scrollbarShow="true"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #voucher>
                        <el-table-column label="凭证" header-align="center">
                            <el-table-column
                                label="日期"
                                min-width="85px"
                                align="left"
                                header-align="left"
                                prop="voucherDate"
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'voucherDate')"
                            />
                            <el-table-column 
                                label="凭证号"
                                min-width="80px" 
                                align="left" 
                                header-align="left" 
                                :show-overflow-tooltip="true"
                                prop="voucherNo"
                                :width="getColumnWidth(setModule, 'voucherNo')"
                            >
                                <template #default="scope">
                                    <a
                                        class="link"
                                        v-if="GetVoucherText(scope.row.pId, scope.row.vId)"
                                        @click="goToCheckVoucher(scope.row.pId, scope.row.vId)"
                                    >
                                        {{ scope.row.voucherNo }}
                                    </a>
                                    <span v-else>{{ scope.row.voucherNo }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="摘要"
                                min-width="100px"
                                align="left"
                                header-align="left"
                                prop="description"
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'description')"
                            />
                            <el-table-column
                                label="借方"
                                min-width="100px"
                                align="right"
                                header-align="right"
                                prop="debit"
                                :show-overflow-tooltip="true"
                                :formatter="formatValueNUmber"
                                :width="getColumnWidth(setModule, 'debit')"
                            />
                            <el-table-column
                                label="贷方"
                                min-width="100px"
                                align="right"
                                header-align="right"
                                prop="credit"
                                :show-overflow-tooltip="true"
                                :formatter="formatValueNUmber"
                                :width="getColumnWidth(setModule, 'credit')"
                            />
                            <el-table-column
                                label="余额"
                                min-width="100px"
                                align="right"
                                header-align="right"
                                prop="total"
                                :show-overflow-tooltip="true"
                                :formatter="formatValueNUmber"
                                :width="getColumnWidth(setModule, 'total')"
                            />
                        </el-table-column>
                    </template>
                    <template #journal>
                        <el-table-column label="日记账" header-align="center">
                            <el-table-column
                                label="日期"
                                min-width="80px"
                                align="left"
                                header-align="left"
                                prop="cdDate"
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'cdDate')"
                            />
                            <el-table-column
                                label="日记账序号"
                                min-width="93px"
                                align="left"
                                header-align="left"
                                prop="lineSnName"
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'lineSnName')"
                            >
                                <template #default="scope">
                                    <a v-if="checkPermission([journalPermission])" class="link" @click="goToJournal(scope.row)">
                                        {{ scope.row.lineSnName }}
                                    </a>
                                    <span v-else>{{ scope.row.lineSnName }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="摘要"
                                min-width="80px"
                                align="left"
                                header-align="left"
                                prop="summary"
                                :show-overflow-tooltip="true"
                                :width="getColumnWidth(setModule, 'summary')"
                            />
                            <el-table-column 
                                label="凭证号" 
                                min-width="80px" 
                                align="left" 
                                header-align="left" 
                                :show-overflow-tooltip="true"
                                prop="cdVoucherNo"
                                :width="getColumnWidth(setModule, 'cdVoucherNo')"
                            >
                                <template #default="scope">
                                    <a
                                        class="link"
                                        v-if="GetVoucherText(scope.row.cdPid, scope.row.cdVid)"
                                        @click="goToCheckVoucher(scope.row.cdPid, scope.row.cdVid)"
                                    >
                                        {{ scope.row.cdVoucherNo }}
                                    </a>
                                    <span v-else>{{ scope.row.cdVoucherNo }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="收入"
                                min-width="100px"
                                align="right"
                                header-align="right"
                                prop="income"
                                :show-overflow-tooltip="true"
                                :formatter="formatValueNUmber"
                                :width="getColumnWidth(setModule, 'income')"
                            />
                            <el-table-column
                                label="支出"
                                min-width="100px"
                                align="right"
                                header-align="right"
                                prop="expenditure"
                                :show-overflow-tooltip="true"
                                :formatter="formatValueNUmber"
                                :width="getColumnWidth(setModule, 'expenditure')"
                            />
                            <el-table-column
                                label="余额"
                                min-width="100px"
                                align="right"
                                header-align="right"
                                prop="cdTotal"
                                :show-overflow-tooltip="true"
                                :formatter="formatValueNUmber"
                            />
                        </el-table-column>
                    </template>
                    <template #balance>
                        <el-table-column
                            label="差额"
                            header-align="right"
                            align="right"
                            min-width="100px"
                            prop="balance"
                            :show-overflow-tooltip="true"
                            :formatter="formatValueNUmber"
                            :resizable="false"
                        />
                    </template>
                </Table>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
export default {
    name: "CDCheckDetail",
};
</script>
<script setup lang="ts">
import { ref, watch, onActivated, onUnmounted } from "vue";
import { useRoute, onBeforeRouteLeave } from "vue-router";
import { request, type IResponseModel } from "@/util/service";
import { formatMoney } from "@/util/format";
import { checkPermission } from "@/util/permission";
import { usePagination } from "@/hooks/usePagination";
import { getDaysInMonth } from "@/views/Voucher/VoucherList/utils";
import { getUrlSearchParams, globalExport, globalPrint, globalWindowOpenPage } from "@/util/url";

import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { IAccountItem, ITableItem, ICDAccountItem } from "./types";

import Table from "@/components/Table/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "CDCheckDetail";
const columns = ref<Array<IColumnProps>>([{ slot: "voucher" }, { slot: "journal" }, { slot: "balance" }]);

const route = useRoute();
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const isErp = ref(window.isErp);
const loading = ref(false);
const asub = ref("");
const spanAcName = ref("");
const spanFcName = ref("");
const spanAsubName = ref("");
const periodInfo = ref("");
const periodList = ref<IPeriodData[]>([]);
const startPid = ref("");
const endPid = ref("");
const searchStartPid = ref("");
const searchEndPid = ref("");
const acId = ref("");
const tableData = ref<ITableItem[]>([]);
const accountList = ref<IAccountItem[]>([]);
const cashList = ref<ICDAccountItem[]>([]);
const bankList = ref<ICDAccountItem[]>([]);
const CdAccountList = ref<ICDAccountItem[]>([]);
const journalType = ref("1010");

const getAccountListApi = () => {
    return request({ url: "/api/CDAccount/Table" });
};

const goToCheckVoucher = (pid: number, vid: number) => {
    const params = { pid, vid, fcode: "voucher-canedit" };
    globalWindowOpenPage("/Voucher/VoucherPage?" + getUrlSearchParams(params), "查看凭证");
};
const goToJournal = (row: ITableItem) => {
    const urlPath = journalType.value === "1010" ? "CashJournal" : "DepositJournal";
    const [year, sn] = searchEndPid.value.split("-");
    const searchEndDate = searchEndPid.value + "-" + getDaysInMonth(Number(year), Number(sn));
    const params = {
        cdAccount: acId.value,
        CD_DATE: row.cdDate,
        searchStartDate: searchStartPid.value + "-01",
        searchEndDate,
        from: "CDCheckDetail",
        r: Math.random(),
    };
    const title = journalType.value === "1010" ? "现金日记账" : "银行日记账";
    // {cdAccount: '6', CD_DATE: '2023-09-17', searchStartDate: '2023-09-01', searchEndDate: '2023-10-31'}
    globalWindowOpenPage("/Cashier/" + urlPath + "?" + getUrlSearchParams(params), title);
};

const handleSearch = () => {
    loading.value = true;
    request({ url: "/api/CDCheck/DetailPagingList?" + getUrlSearchParams(getParams()) })
        .then((res: any) => {
            if (res.state == 1000) {
                tableData.value = res.data.rows;
                paginationData.total = res.data.total;
            }
        })
        .finally(() => {
            loading.value = false;
        });
};
const handleExport = () => globalExport("/api/CDCheck/ExportDetail?" + getUrlSearchParams(getParams()));
const handlePrint = () => globalPrint("/api/CDCheck/PrintDetail?" + getUrlSearchParams(getParams()));

const journalPermission = ref("");
const getCdAccountList = () => {
    Promise.all([getCdAccountApi(1010), getCdAccountApi(1020)]).then((res: any) => {
        if (res[0].state != 1000 || res[1].state != 1000) return;
        cashList.value = res[0].data;
        bankList.value = res[1].data;
        CdAccountList.value = [...res[0].data, ...res[1].data];
        const item = CdAccountList.value.find((item) => item.asub === asub.value) as ICDAccountItem;
        spanAcName.value = item?.ac_name || "";
        spanFcName.value = item?.currency_name || "";
        spanAsubName.value = (item?.asub_name || "").split(" ")[1] || "";
        acId.value = item?.ac_id || "";
        journalType.value = item?.ac_type || "1010";
        journalPermission.value = item.ac_type == "1010" ? "cashjournal-canview" : "depositjournal-canview";
        if (paginationData.currentPage !== 1) {
            paginationData.currentPage = 1;
            return;
        }
        handleSearch();
    });
};
const findItemByRoute = (periodInfo: string) => {
    if (periodInfo.includes("-")) {
        const [year, sn] = periodInfo.split("-");
        return periodList.value.find((item) => item.year === Number(year) && item.sn === Number(sn));
    } else if (Number(periodInfo)) {
        return periodList.value.find((item) => item.pid === Number(periodInfo));
    } else {
        return null;
    }
};
const setPeriodInfo = () => {
    const startItem = findItemByRoute(startPid.value);
    const endItem = findItemByRoute(endPid.value);
    const startInfo = startItem ? startItem.year + "年" + (startItem.sn + "").padStart(2, "0") + "月" : "";
    const endInfo = endItem ? endItem.year + "年" + (endItem.sn + "").padStart(2, "0") + "月" : "";
    periodInfo.value = startPid.value === endPid.value ? startInfo : startInfo + "-" + endInfo;
    searchStartPid.value = startItem ? startItem.year + "-" + (startItem.sn + "").padStart(2, "0") : "";
    searchEndPid.value = endItem ? endItem.year + "-" + (endItem.sn + "").padStart(2, "0") : "";
};

watch([() => paginationData.currentPage, () => paginationData.refreshFlag, () => paginationData.pageSize], () => {
    handleSearch();
});

const getParams = () => {
    return {
        ac_id: acId.value,
        fc_code: route.query.fc_code,
        period_s: searchStartPid.value + "-01",
        period_e: searchEndPid.value + "-01",
        show_type: route.query.show_type,
        asubId: asub.value,
        asubName: spanAsubName.value,
        rows: paginationData.pageSize,
        page: paginationData.currentPage,
    };
};
const getCdAccountApi = (acType: 1010 | 1020) => request({ url: "/api/CDAccount/List?acType=" + acType });
const formatValueNUmber = (row: any, column: any, value: string | number) => GetNumber(value + "");
const GetNumber = (number: string) => (number == "" || Number(number).toFixed(2) == "0.00" ? "" : formatMoney(number));
const GetVoucherText = (pid: number, vid: number) => checkPermission(["voucher-canedit"]) && pid != 0 && vid != 0;

//获取期间信息
interface IPeriodData {
    year: number;
    sn: number;
    key: string;
    pid: number;
}
interface IPeriodResponseData {
    year: number;
    sn: number;
    key: string;
    isActived: boolean;
}

const handleInit = () => {
    asub.value = route.query.asubId as string;
    startPid.value = route.query.startPid as string;
    endPid.value = route.query.endPid as string;
    request({
        url: "/api/CDCheck/GetPeriodList",
        method: "post",
    }).then((res: IResponseModel<Array<IPeriodResponseData>>) => {
        if (res.state === 1000) {
            const startPid = res.data[0].sn;
            periodList.value = res.data.map((item, index) => {
                return {
                    year: item.year,
                    sn: item.sn,
                    key: item.key,
                    pid: startPid + index,
                };
            });
            setPeriodInfo();
            getAccountListApi().then((res: any) => {
                if (res.state == 1000) accountList.value = JSON.parse(res.data);
                acId.value = accountList.value.find((item) => item.AC_NAME.split("-")[1] == spanAcName.value)?.AC_ID || "";
                getCdAccountList();
            });
        }
    });
};

let random = "";
onBeforeRouteLeave(() => {
    random = route.query.r as string;
});

onActivated(() => {
    console.log("CDCheckDetail activated",route);
    if (route.query.r !== random) {
        random = route.query.r as string;
        handleInit();
    }
});

onUnmounted(() => {
    random = "";
});
</script>

<style lang="less" scoped>
@import "@/style/SelfAdaption.less";
</style>
