import type { Directive } from "vue";

export const decimalLimit: Directive = {
    mounted(el, binding) {
        let maxDigits = binding.value ?? 2;
        let maxInteger = 9;
        if (Array.isArray(binding.value)) {
            maxDigits = binding.value[1] ?? 2;
            maxInteger = binding.value[0];
        }

        el.addEventListener("input", function (e: Event) {
            const input = e.target as HTMLInputElement;
            const value = input.value;
            // 暂时将数字部分限制到亿位，如果出现需要更大的数字，传入一个数组，第一位为整数位限制，第二位为小数位限制
            // 或者将此指令拆分成两个，分别限制数字部分的长度和小数位数
            // 使用正则表达式匹配小数点后最多指定位数的数字
            const regex = new RegExp(`^-?\\d{0,${maxInteger}}(\\.\\d{0,${maxDigits}})?$`);
            if (!regex.test(value)) {
                const numberList = value.split(".");
                let integer = numberList[0];
                let decimal = numberList[1];
                integer = integer.startsWith("-") ? integer.slice(0, maxInteger + 1) : integer.slice(0, maxInteger);
                decimal = decimal?.slice(0, maxDigits);
                if (isNaN(Number(integer))) {
                    input.value = "";
                    return;
                }
                input.value = decimal ? `${integer}.${decimal}` : integer;
            }
        });
    },
};
