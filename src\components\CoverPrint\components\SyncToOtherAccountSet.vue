<template>
    <el-dialog title="同步到其他账套" center width="428px" class="custom-confirm" v-model="syncDialogShow" @close="openDialog">
        <div class="selected-content">
            <div v-if="props.type === 'AccountBooks'" class="highlight-red" style="line-height: 28px;font-size: 12px;">*将对横向和纵向模版一起同步</div>
            <div class="selected-table-search">
                <SearchInfo :width="336" :height="30" placeholder="请输入账套名称" @search="getAccountData"></SearchInfo>
            </div>
            <div class="selected-table-content">
                <template v-for="item in accountSetList" :key="item.asId">
                    <el-checkbox class="ml-20" v-model="item.checked" :label="item.asName" /> <br />
                </template>
                <div class="emptyText" v-if="accountSetList.length === 0">没有可同步账套</div>
            </div>
            <div class="selected-table-notice">
                <div style="display: inline-block; width: 20px; vertical-align: top; margin-top: 2px">
                    <img src="@/assets/Icons/hint.png" style="height: 16px; width: 16px" />
                </div>
                <div style="display: inline-block;width: 320px;font-size: 12px;line-height: 20px;">
                    <div v-for="item in syncNotice" :key="item">{{ item }}</div>
                </div>
            </div>
        </div>
        <div class="buttons borderTop mt-20">
            <a class="button solid-button mr-10" @click="syncSettings">确定</a>
            <a class="button" @click="cancelSync">取消</a>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import SearchInfo from "@/components/SearchInfo/index.vue";
import { Calendar, Search } from '@element-plus/icons-vue'
import { request, type IResponseModel } from "@/util/service";
import { useAccountSetStore } from "@/store/modules/accountSet";
import { getServiceId } from "@/util/proUtils";
import { ElNotify } from "@/util/notify";
import { getGlobalLodash } from "@/util/lodash";
import { ref } from "vue";

const { cloneDeep } = getGlobalLodash()
const syncDialogShow = defineModel<boolean>("syncDialogShow");
const props = defineProps({
    syncNotice: {
        type: Array<string>,
        default: ["1. 有对应账套凭证打印权限时才可以进行同步", "2. 同步后对应账套的凭证高级打印模板将更新"],
    },
    type: {
        type: String,
        default: "Vouhcer",
    },

});

const emits = defineEmits(["confirmSync"]);

const accountsetStore = useAccountSetStore();
interface IAccountSet {
    asId: number;
    asName: string;
    checked: boolean;
}
const searchText = ref("");
const accountSetList = ref<IAccountSet[]>([]);
const storeAccountSetList = ref<IAccountSet[]>([]);
function getAccountData(searchInfo: string) {
    const functionCode = props.type === "AccountBooks" ? "subsidiaryledger-canprint,generalledger-canprint,aasubsidiaryledger-canprint" : "voucher-canprint";
    let url = `/api/AccountSet/FullListByFunctionCode?searchText=${searchInfo}&functionCode=${functionCode}`;
    if (window.isProSystem) {
        url = window.eHost + "/wb/valveacc_vip_web" + url + `&serviceID=${getServiceId()}`;
    } else if (window.isErp) {
        url = `/api/AccountSet/FullListByFunctionCodeForERP?searchText=${searchInfo
            }&functionCode=${functionCode
            }&serviceId=${getServiceId()}`;
    }
    request({
        url: url,
    }).then((res: any) => {
        if (res.state === 1000) {
            accountSetList.value = res.data.filter((i: any) => i.asId !== accountsetStore.accountSet?.asId);
            accountSetList.value.forEach((i) => {
                i.checked = false;
            });
            if (searchInfo.length === 0) {
                storeAccountSetList.value = cloneDeep(accountSetList.value);
            }
        } else {
            accountSetList.value = [];
        }
    });
}
getAccountData("");

function openDialog() {
    accountSetList.value.forEach((i) => {
        i.checked = false;
    });
}

function syncSettings() {
    if (accountSetList.value.length === 0) {
        ElNotify({
            type: "warning",
            message: "没有可同步的账套",
        });
        return;
    }
    let data = accountSetList.value.filter((x) => x.checked).map((i: any) => i.asId);
    if (data.length === 0) {
        ElNotify({
            type: "warning",
            message: "亲，请先选择同步账套",
        });
        return;
    }
    emits("confirmSync", data);
}
function cancelSync() {
    syncDialogShow.value = false;
}
</script>

<style scoped lang="less">
@import "@/style/Functions.less";

.selected-content {
    padding-left: 34px;

    .selected-table-search {
        position: relative;
        // margin-top: 20px;
        width: 336px;

        :deep(.el-button) {
            margin-top: 20px;
        }
    }

    .selected-table-content {
        width: 336px;
        height: 284px;
        overflow-y: scroll;
        // padding-left: 10px;
        margin-top: 20px;
        // background-color: #f6f6f6;
        .detail-lm-default-scroll(6px);

        .emptyText {
            margin-top: 10px;
        }
    }

    .selected-table-notice {
        margin-top: 20px;
        padding-left: 10px;
        color: #999;
        display: flex;
    }

    // .selected-table-text {
    //     width: 320px;
    //     margin-top: 20px;
    //     padding-left: 10px;
    //     margin-right: 20px;
    //     // color: black;
    // }
}
</style>
