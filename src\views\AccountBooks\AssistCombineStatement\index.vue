<template>
    <div class="content">
        <div class="title">核算项目组合表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template #title>{{ currentPeriodInfo }}</template>
                        <div class="line-item first-item input">
                            <div class="line-item-title">会计期间：</div>
                            <div class="line-item-field">
                                <DatePicker
                                    v-model:startPid="searchInfo.startMonth"
                                    v-model:endPid="searchInfo.endMonth"
                                    :clearable="false"
                                    :editable="false"
                                    :dateType="'month'"
                                    :value-format="'YYYYMM'"
                                    :label-format="'YYYY年MM月'"
                                    :isPeriodList="true"
                                    @getActPid="getActPid"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">主辅助项目：</div>
                            <div class="line-item-field">
                                <VirtualSelectCheckbox
                                    width="298px"
                                    v-model:selectedList="mainAAItemSelectList"
                                    :options="mainAAItemList"
                                    :custom-props="customProps"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">次辅助项目：</div>
                            <div class="line-item-field">
                                <VirtualSelectCheckbox
                                    width="298px"
                                    v-model:selectedList="subAAItemSelectList"
                                    :options="subAAItemList"
                                    :custom-props="customProps"
                                />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目：</div>
                            <div class="line-item-field">
                                <MultipleSubjectPicker v-model:selectedList="subject" />
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title"></div>
                            <div class="line-item-field" style="color: #c5c5c5">例：1001, 1122, 2001-2202</div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">科目级别：</div>
                            <div class="line-item-field">
                                <el-input-number
                                    v-model="searchInfo.sbj_leval_s"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                                <div class="ml-10 mr-10">至</div>
                                <el-input-number
                                    v-model="searchInfo.sbj_leval_e"
                                    :min="1"
                                    :max="maxCodelength"
                                    controls-position="right"
                                    style="width: 132px"
                                ></el-input-number>
                            </div>
                        </div>
                        <div class="line-item input">
                            <div class="line-item-title">余额选项：</div>
                            <div class="line-item-field">
                                <VirtualSelectCheckbox
                                    width="298px"
                                    :options="customTrialBalanceOptions"
                                    v-model:selectedList="customTrialBalanceSelectList"
                                />
                            </div>
                        </div>
                        <div class="line-item input" v-show="fcIsShow">
                            <div class="line-item-title">币别：</div>
                            <div class="line-item-field fcid-select">
                                <el-select 
                                    v-model="searchInfo.fcid" 
                                    style="width: 132px" 
                                    :fit-input-width="true" 
                                    :teleported="false"
                                    :filterable="true"
                                    :filter-method="fcFilterMethod"
                                >
                                    <el-option 
                                        :label="item.label" 
                                        :value="item.id" 
                                        v-for="item in showfcList" 
                                        :key="item.id"
                                    ></el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="line-item single">
                            <div class="line-item-title">
                                <el-checkbox v-model="searchInfo.isBalanceZero" label="余额为0不显示"></el-checkbox>
                            </div>
                        </div>
                        <div class="buttons">
                            <a class="button solid-button" @click="handleSearch(true)">确定</a>
                            <a class="button" @click="handleClose">取消</a>
                            <a class="button" @click="handleReset">重置</a>
                        </div>
                    </SearchInfoContainer>
                    <span class="ml-10">主辅助类别：</span>
                    <div class="ml-10">
                        <el-select 
                            v-model="mainAAType" 
                            style="width: 132px"
                            :filterable="true"
                            :filter-method="assistingTypeFilterMethod"
                        >
                            <el-option
                                v-for="item in showAssistingTypeList"
                                :key="item.aaType"
                                :label="item.aaTypeName"
                                :value="item.aaType"
                            ></el-option>
                        </el-select>
                    </div>
                    <span class="ml-10">次辅助类别：</span>
                    <div class="ml-10">
                        <el-select 
                            v-model="subAAType" 
                            style="width: 132px"
                            :filterable="true"
                            :filter-method="assistingTypeFilterMethod"
                        >
                            <el-option
                                v-for="item in showAssistingTypeList"
                                :key="item.aaType"
                                :label="item.aaTypeName"
                                :value="item.aaType"
                            ></el-option>
                        </el-select>
                    </div>
                    <a class="button ml-10" @click="handleSearch(true)">查询</a>
                </div>
                <div class="main-tool-right">
                    <el-checkbox v-model="searchInfo.isShowSubject" label="显示科目" @change="handleQuickSearch"></el-checkbox>
                        <el-checkbox
                            v-show="searchInfo.isShowSubject"
                            v-model="searchInfo.isShowAllSubject"
                            label="显示非明细科目"
                            @change="handleQuickSearch"
                        ></el-checkbox>
                        <el-checkbox
                            class="mr-10"
                            :disabled="showDataMask"
                            v-model="searchInfo.isShowMainAAtypeSum"
                            label="显示主辅助类别合计"
                            @change="handleQuickSearch"
                        ></el-checkbox>
                    <Dropdown class="mr-10" :btnTxt="'打印'" :downlistWidth="85" v-permission="['trialbalance-canprint']">
                        <li @click="handlePrint(0,getSearchParams())">直接打印</li>
                        <li @click="handlePrint(2)">打印设置</li>
                    </Dropdown>
                    <a class="button ml-10" v-permission="['assistcombinestatement-canexport']" @click="handleExport">导出</a>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div :class="['main-center', { erp: isErp }, { showDataMask: showDataMask }]">
                <AccountBooksTable
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :scrollbarShow="true"
                    :data="tableData"
                    :columns="columns"
                    :empty-text="emptyText"
                    :page-is-show="true"
                    :layout="paginationData.layout"
                    :page-sizes="paginationData.pageSizes"
                    :page-size="paginationData.pageSize"
                    :total="paginationData.total"
                    :currentPage="paginationData.currentPage"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    @refresh="handleRerefresh"
                    :tableName="setModule"
                >
                    <template #initialAount>
                        <el-table-column label="期初余额" align="center" header-align="center">
                            <el-table-column
                                label="借方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="initialDebit"
                                :width="getColumnWidth(setModule, 'initialDebit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].initialDebit) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="贷方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="initialCredit"
                                :width="getColumnWidth(setModule, 'initialCredit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].initialCredit) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #amount>
                        <el-table-column label="本期发生额" align="center" header-align="center">
                            <el-table-column
                                label="借方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="debit"
                                :width="getColumnWidth(setModule, 'debit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].debit) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="贷方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="credit"
                                :width="getColumnWidth(setModule, 'credit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].credit) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #totalAmount>
                        <el-table-column label="期末余额" align="center" header-align="center">
                            <el-table-column
                                label="借方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="totalDebit"
                                :width="getColumnWidth(setModule, 'totalDebit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].totalDebit) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="贷方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="totalCredit"
                                :width="getColumnWidth(setModule, 'totalCredit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].totalCredit) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #yearAmount>
                        <el-table-column label="本年累计发生额" align="center" header-align="center">
                            <el-table-column
                                label="借方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="yearDebit"
                                :width="getColumnWidth(setModule, 'yearDebit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].yearDebit) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="贷方"
                                min-width="120px"
                                align="right"
                                header-align="right"
                                :show-overflow-tooltip="true"
                                prop="yearCredit"
                                :width="getColumnWidth(setModule, 'yearCredit')"
                            >
                                <template #default="scope">
                                    <span>{{ formatMoney(scope.row.items[0].yearCredit) }}</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template #changeout>
                        <el-table-column 
                            label="损益发生额" 
                            min-width="120px" 
                            align="right" 
                            header-align="right" 
                            prop="changeout" 
                            :width="getColumnWidth(setModule, 'changeout')"
                        />
                    </template>
                </AccountBooksTable>
                <DataMask v-if="showDataMask" ref="dataMaskRef" :showLines="5" :hasPage="true" />
            </div>
        </div>
    </div>
    <AccountBooksPrint
        v-model:printDialogShow="printDialogVisible"
        title="核算项目组合表打印"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getSearchParams())"
    />
</template>
<script lang="ts">
export default {
    name: "AssistCombineStatement",
};
</script>
<script setup lang="ts">
import { reactive, ref, provide, toRef, watch, watchEffect, nextTick, computed } from "vue";
import { request, type IResponseModel } from "@/util/service";
import { ElNotify } from "@/util/notify";
import { componentFinishKey } from "@/symbols";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useAssistingAccountingStore } from "@/store/modules/assistingAccouting";
import { usePagination } from "@/hooks/usePagination";
import { trialBalanceOptions, getCloumns } from "./utils";
import { formatMoney } from "@/util/format";
import { getUrlSearchParams, globalFormPost, globalExport, globalPrint } from "@/util/url";
import { getGlobalLodash } from "@/util/lodash";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import { useTrialStatusStore } from "@/store/modules/trialStatus";
import usePrint from "@/hooks/usePrint";

import type { Option } from "@/components/SelectCheckbox/types";
import type { IColumnProps } from "@/components/Table/IColumnProps";
import type { ISelectList } from "@/views/AccountBooks/AASubsidiaryLedger/types";
import type { ITableData, ISearchBack } from "./types";
import type { IAsubCodeLength } from "@/views/AccountBooks/SubsidiaryLedger/types";
import type { IAssistingAccount } from "@/api/assistingAccounting";

import Dropdown from "@/components/Dropdown/index.vue";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import AccountBooksPrint from "@/components/PrintDialog/index.vue";
import AccountBooksTable from "../components/AccountBooksTable.vue";
import MultipleSubjectPicker from "./components/MultipleSubjectPicker.vue";
import VirtualSelectCheckbox from "./components/VirtualSelectCheckbox.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import DataMask from "@/components/DataMask/index.vue";
import { getColumnWidth } from "@/components/ColumnSet/utils";
import DatePicker from "@/components/DatePicker/index.vue";
import { getCurrentPeriodInfo } from "@/components/DatePicker/utils";
import { usePeriodData } from "@/hooks/useDatePickeMonth";
import { commonFilterMethod } from "@/components/Select/utils";
import { useCurrencyStore } from "@/store/modules/currencyList";

const setModule = "AssistCombineState";
const _ = getGlobalLodash()
const trialStatusStore = useTrialStatusStore();
// 是否过期
const isExpired = computed(() => {
    return trialStatusStore.isExpired;
});
// 是否显示遮罩
const showDataMask = computed(() => isExpired.value && tableData.value.length > 5);

const periodStore = useAccountPeriodStore();
const isHideBarcode = ref(useThirdPartInfoStoreHook().isHideBarcode);
const { paginationData, handleCurrentChange, handleSizeChange, handleRerefresh } = usePagination();

const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();

const searchInfo = reactive({
    period_s: Number(periodStore.getPeriodRange().start),
    period_e: Number(periodStore.getPeriodRange().end),
    first_aatype: "",
    second_aatype: "",
    sbj_id: "",
    sbj_leval_s: 1,
    sbj_leval_e: 4,
    fcid: -1,
    isBalanceZero: false,
    isShowSubject: false,
    isShowAllSubject: false,
    isShowMainAAtypeSum: false,
    startMonth: "",
    endMonth: "",
});
const isErp = ref(window.isErp);
const mainAAType = ref(10001);
const subAAType = ref(10005);
const subject = ref<Array<string>>([]);
// const mainAAItemSelectList = ref<Array<number>>([]);
// const subAAItemSelectList = ref<Array<number>>([]);
const customProps = { id: "aaeid", name: "aaname" };

const { periodData } = usePeriodData(searchInfo, searchInfo.period_s, searchInfo.period_e); 
const getActPid = (start: number, end: number) => {
    searchInfo.period_s = start;
    searchInfo.period_e = end;
}
watchEffect(() => {
    searchInfo.sbj_id = subject.value.join(",");
});

const currentPeriodInfo = ref("");
const customTrialBalanceOptions = ref<Array<Option>>(trialBalanceOptions);
const customTrialBalanceSelectList = ref<Array<number>>([1, 2, 3, 4, 5, 6]);
const fcIsShow = ref(false);
const fcList = ref<Array<ISelectList>>([]);
const emptyText = ref(" ");

const dataMaskRef = ref<InstanceType<typeof DataMask>>();

const extraInfo = {
    isHideAACode: false,
}

const { printDialogVisible, handlePrint, printInfo, otherOptions } = usePrint(
    "assistCombineStatement",
    window.jAccountBooksHost + "/api/AssistCombineStatement/Print",
    extraInfo,
    true,
    true,
    printandExportValidator
);

otherOptions.value = [
    { key: "isHideAACode", label: "表体不打印核算项目编码" },
    ...otherOptions.value,
];

function printandExportValidator() {
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    if (mainAAType.value && subAAType.value && mainAAType.value === subAAType.value) {
        ElNotify({ type: "warning", message: "主、次辅助类别不允许为同一辅助核算类别，请重新选择。" });
        return false;
    }
    return true;
}

function handleExport() {
    if (!printandExportValidator()) {
        return false;
    }
    const params = getSearchParams();
    if (isHideBarcode.value) {
        globalExport(window.jAccountBooksHost + "/api/AssistCombineStatement/Export?" + getUrlSearchParams(params));
    } else {
        globalFormPost(window.jAccountBooksHost + "/api/AssistCombineStatement/Export?", params, "export");
    }
}

function handleSearch(changeCurrentPage = true) {
    setTimeout(() => {
        if (showDataMask.value) {
            dataMaskRef.value?.bounce();
            return false;
        }
        if (!checkCanSearch()) return false;
        periodStore.changePeriods(String(searchInfo.period_s), String(searchInfo.period_e));
        currentPeriodInfo.value = getCurrentPeriodInfo(periodData.value, searchInfo.period_s, searchInfo.period_e);
        handleClose();
        if (changeCurrentPage) {
            paginationData.currentPage === 1 ? handleLoadTableData() : (paginationData.currentPage = 1);
        } else {
            handleLoadTableData();
        }
    }, 0);
}

function handleClose() {
    containerRef.value?.handleClose();
}

function handleReset() {
    searchInfo.first_aatype = "";
    searchInfo.second_aatype = "";
    subject.value = [];
    searchInfo.sbj_leval_s = 1;
    searchInfo.sbj_leval_e = maxCodelength.value;
    searchInfo.fcid = -1;
    searchInfo.isBalanceZero = false;
    mainAAItemSelectList.value = mainAAItemList.value.map((item) => item.aaeid);
    subAAItemSelectList.value = subAAItemList.value.map((item) => item.aaeid);
    customTrialBalanceSelectList.value = [1, 2, 3, 4, 5, 6];
    searchInfo.period_s = Number(periodStore.getPeriodRange().start);
    searchInfo.period_e = Number(periodStore.getPeriodRange().end);
}

function handleQuickSearchFn() {
    if (mainAAType.value && subAAType.value && mainAAType.value === subAAType.value) {
        ElNotify({ type: "warning", message: "主、次辅助类别不允许为同一辅助核算类别，请重新选择。" });
        return false;
    }
    handleSearch(false);
}
const handleQuickSearch = _.debounce(handleQuickSearchFn, 300);

function checkCanSearch() {
    if (mainAAType.value && subAAType.value && mainAAType.value === subAAType.value) {
        ElNotify({ type: "warning", message: "主、次辅助类别不允许为同一辅助核算类别，请重新选择。" });
        return false;
    }

    if (searchInfo.period_s > searchInfo.period_e) {
        ElNotify({ type: "warning", message: "亲，开始期间不能大于结束期间哦" });
        return false;
    }

    if (searchInfo.sbj_leval_s && searchInfo.sbj_leval_e && searchInfo.sbj_leval_s > searchInfo.sbj_leval_e) {
        ElNotify({ type: "warning", message: "亲，开始科目级别不能大于结束科目级别哦~" });
        return false;
    }

    if (customTrialBalanceSelectList.value.length === 0) {
        ElNotify({ type: "warning", message: "余额选项至少需要选择一项哦~" });
        return false;
    }

    return true;
}

function getSearchParams() {
    return {
        periodStart: searchInfo.period_s,
        periodEnd: searchInfo.period_e,
        subjectLevelStart: searchInfo.sbj_leval_s,
        subjectLevelEnd: searchInfo.sbj_leval_e,
        subjectIds: searchInfo.sbj_id,
        firstAAType: mainAAType.value,
        secondAAType: subAAType.value,
        firstItem: searchInfo.first_aatype || "",
        secondItem: searchInfo.second_aatype || "",
        items: customTrialBalanceSelectList.value.join(","),
        isBalanceZero: searchInfo.isBalanceZero,
        fcId: searchInfo.fcid,
        showSubject: searchInfo.isShowSubject,
        showFirstAATypeSum: searchInfo.isShowMainAAtypeSum,
        showAllSubject: searchInfo.isShowAllSubject,
    };
}
const tableData = ref<Array<ITableData>>([]);
const columns = ref<Array<IColumnProps>>([]);
const loading = ref(false);
function handleLoadTableData() {
    loading.value = true;
    const params = getSearchParams();
    const urlParams = {
        pageIndex: paginationData.currentPage,
        pageSize: paginationData.pageSize,
    };
    request({
        url: "/api/AssistCombineStatement/GetList?" + getUrlSearchParams(urlParams),
        method: "post",
        data: params,
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    })
        .then((res: IResponseModel<ISearchBack>) => {
            if (res.state !== 1000) {
                loading.value = false;
                ElNotify({ type: "warning", message: res.msg || "获取数据失败，请刷新页面后重试。" });
                return false;
            }

            let isShowSubject = searchInfo.isShowSubject;
            if (res.data.subjects.length === 0 && searchInfo.isShowSubject) {
                isShowSubject = false;
            }
            const rows = getCloumns(
                isShowSubject,
                customTrialBalanceSelectList.value,
                searchInfo.fcid,
                mainAAType.value,
                subAAType.value,
                res.data.subjects
            );
            columns.value = rows;

            if (res.data.subjects.length === 0 && searchInfo.isShowSubject) {
                tableData.value = [];
                paginationData.total = 0;
                emptyText.value = "暂无数据";
            } else {
                tableData.value = res.data.rows;
                paginationData.total = res.data.count || 0;
                emptyText.value = tableData.value.length === 0 ? "暂无数据" : "";
            }

            loading.value = false;
        })
        .catch(() => {
            loading.value = false;
            paginationData.total = 0;
            tableData.value = [];
            emptyText.value = "暂无数据";
            ElNotify({ type: "warning", message: "获取数据失败，请刷新页面后重试。" });
        })
        .finally(() => {
            nextTick(() => {
                if (showDataMask.value) {
                    searchInfo.isShowMainAAtypeSum = true;
                    dataMaskRef.value?.getTableHeight();
                }
            });
        });
}

watch(
    () => searchInfo.isShowSubject,
    (isShowSubject) => {
        if (!isShowSubject) searchInfo.isShowAllSubject = false;
    }
);

const assistingAccountingTypeList = toRef(useAssistingAccountingStore(), "assistingAccountingTypeList");
const assistingAccountingList = toRef(useAssistingAccountingStore(), "assistingAccountingList");
const mainAAItemList = computed(() => {
    return assistingAccountingList.value.filter(item => item.aatype === mainAAType.value);
});

const subAAItemList = computed(() => {
    return assistingAccountingList.value.filter(item => item.aatype === subAAType.value);
});

const mainAAItemSelectList = ref<number[]>([]);
const subAAItemSelectList = ref<number[]>([]);

watch(mainAAItemList, (newList) => {
    if(newList?.length) {
        mainAAItemSelectList.value = newList.map(item => item.aaeid);
    }
}, { immediate: true });

watch(subAAItemList, (newList) => {
    if(newList?.length) {
        subAAItemSelectList.value = newList.map(item => item.aaeid);
    }
}, { immediate: true });

watch(mainAAItemSelectList, (selectedList) => {
    updateFirstAAType(selectedList);
});
watch(subAAItemSelectList, (selectedList) => {
    updateSecondAAType(selectedList);
});

const updateFirstAAType = (selectedList: number[]) => {
    searchInfo.first_aatype = selectedList.length === mainAAItemList.value.length 
        ? "" 
        : selectedList.join(",");
};

const updateSecondAAType = (selectedList: number[]) => {
    searchInfo.second_aatype = selectedList.length === subAAItemList.value.length 
        ? "" 
        : selectedList.join(",");
};

watch([mainAAType, subAAType], ([newMainType, newSubType], [oldMainType, oldSubType]) => {
    if (newMainType !== oldMainType) {
        updateFirstAAType(mainAAItemSelectList.value);
    }
    if (newSubType !== oldSubType) {
        updateSecondAAType(subAAItemSelectList.value);
    }
}, { immediate: true });

async function getCurrencyList() {
    await useCurrencyStore().getCurrencyList();
    fcList.value = [...useCurrencyStore().fcListOptions];
}

async function isExistsFc() {
    await request({ url: "/api/AccountSubject/ExistsFc", method: "post" }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) fcIsShow.value = res.data;
    });
}

const maxCodelength = ref(4);
async function getMaxCodeLength() {
    await request({ url: "/api/AccountSubject/GetAsubCodeLength", method: "post" }).then((res: IResponseModel<IAsubCodeLength>) => {
        if (res.state === 1000) {
            maxCodelength.value = res.data.codeLength.length;
            searchInfo.sbj_leval_e = maxCodelength.value;
        }
    });
}

async function handleInit() {
    await getCurrencyList();
    await isExistsFc();
    await getMaxCodeLength();
    if (periodStore.getPeriodRange().start && periodStore.getPeriodRange().end) {
        searchInfo.period_s = Number(periodStore.getPeriodRange().start) || 0;
        searchInfo.period_e = Number(periodStore.getPeriodRange().end) || 0;
        nextTick().then(() => {
            handleSearch();
        });
        return;
    }
    handleSearch();
}

let childComponentFinishCount = 0;
provide(componentFinishKey, () => {
    childComponentFinishCount++;
    if (childComponentFinishCount === 1) {
        handleInit();
    }
});

watch([() => paginationData.currentPage, () => paginationData.pageSize, () => paginationData.refreshFlag], () => {
    if (showDataMask.value) {
        dataMaskRef.value?.bounce();
        return false;
    }
    handleLoadTableData();
});

const showAssistingTypeList = ref<Array<any>>([]);
const showfcList = ref<Array<ISelectList>>([]);
watchEffect(() => { 
    showAssistingTypeList.value = JSON.parse(JSON.stringify(assistingAccountingTypeList.value));  
    showfcList.value = JSON.parse(JSON.stringify(fcList.value));  
});
function assistingTypeFilterMethod(value: string) {
    showAssistingTypeList.value = commonFilterMethod(value, assistingAccountingTypeList.value, 'aaTypeName');
}
function fcFilterMethod(value: string) {
    showfcList.value = commonFilterMethod(value, fcList.value, 'label');
}

</script>
<style lang="less" scoped>
@import "@/style/Functions.less";
@import "@/style/SelfAdaption.less";

.main-tool-right {
    :deep(.el-checkbox) {
        &:first-of-type {
            margin-right: 20px;
        }
    }
}

.line-item-field {
    :deep(.el-select) {
        width: 298px;
    }
}

.main-center {
    position: relative;
    &.showDataMask {
        :deep(.el-table) {
            .el-scrollbar__view {
                min-height: 0;
                height: 535px;
                overflow: hidden;
            }
            // 底部滚动条样式
            .el-scrollbar__bar {
                &.is-horizontal {
                    height: 8px;
                    bottom: 1px;
                    background-color: #fff;
                    .el-scrollbar__thumb {
                        &:hover {
                            filter: brightness(1.2);
                            opacity: 1;
                        }
                    }
                }
            }
        }
    }
}
</style>
