@import "./AccountBooks.less";
@import "../SelfAdaption.less";
:deep(.el-popper) {
    max-width: 300px;
    text-align: left;
}
.line-item {
    :deep(.el-checkbox) {
        .el-checkbox__label {
            color: var(--font-color) !important;
        }
    }
}
.main-center {
    display: flex;
    width: 100%;
    justify-content: space-between;
    background-color: var(--white);
    box-sizing: border-box;
    .main-left {
        flex: 1;
        min-width: 0;
        // width: calc(100% - 248px);
        margin-right: 14px;
        .main-title {
            margin: 0;
        }
    }
    .main-right {
        width: 234px;
        border: 1px solid var(--border-color);
        background-color: var(--white);
        .main-right-title {
            color: var(--font-color);
            font-size: var(--h5);
            line-height: 36px;
            font-weight: bold;
            padding-left: 10px;
            text-align: left;
        }
        .select-content {
            position: relative;
            margin-bottom: 10px;
            .search-box {
                position: absolute;
                right: 17px;
                top: 7px;
                width: 18px;
                height: 18px;
                background: url("@/assets/Icons/search-erp.png") no-repeat center;
                background-size: 100% 100%;
            }
            input {
                height: 30px;
                width: calc( 100% - 48px);
                // width: 166px;
                border: 1px solid var(--main-color);
                border-radius: 4px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                padding: 0 36px 0 10px;
                outline: none;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .select-info {
                // width: 212px;
                width: calc( 100% - 20px);
                position: absolute;
                top: 32px;
                left: 10px;
                z-index: 9;
                box-sizing: border-box;
                height: 178px;
                border: 1px solid var(--border-color);
                background-color: var(--white);
                overflow-y: auto;
                text-align: left;
                .select-info-inner {
                    text-align: left;
                    color: var(--font-color);
                    font-size: var(--font-size);
                    line-height: 16px;
                    padding: 6px 6px 6px 8px;
                    margin: 0;
                    box-sizing: border-box;
                    max-height: 40px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    cursor: pointer;
                    &:hover {
                        background-color: var(--main-color);
                        color: var(--white);
                    }
                }
            }
        }
        .tree {
            width: 100%;
            // height: 560px;
            overflow: auto;
            text-align: left;
            :deep(.el-tree) {
                display: inline-block;
                min-width: 100%;
                .el-tree-node {
                    text-align: start;
                    & > .el-tree-node__content {
                        &:hover {
                            background-color: var(--table-hover-color) !important;
                        }
                    }
                    &.is-current {
                        & > .el-tree-node__content {
                            background-color: var(--table-title-color);
                        }
                    }
                    &[aria-expanded~="true"] {
                        > .el-tree-node__content {
                            > .el-tree-node__label {
                                background-image: url("@/assets/Icons/folder-open.png");
                            }
                        }
                    }
                    &.is-focusable {
                        > .el-tree-node__content {
                            > .el-tree-node__label {
                                background-image: url("@/assets/Icons/folder.png");
                            }
                        }
                    }
                    &.tree-file {
                        > .el-tree-node__content {
                            > .el-tree-node__label {
                                background-image: url("@/assets/Icons/file.png");
                            }
                        }
                    }
                    .el-tree-node__content {
                        height: 21px;
                        overflow: visible;
                        .el-icon {
                            display: inline-flex;
                            justify-content: center;
                            align-items: center;
                            width: 21px;
                            height: 21px;
                            padding: 0;
                        }
                        .el-tree-node__label {
                            display: inline-block;
                            padding-left: 23px;
                            background-repeat: no-repeat;
                            background-position-y: center;
                            background-position-x: 2px;
                            color: var(--font-color);
                            font-size: var(--h5);
                            line-height: 21px;
                            text-align: left;
                        }
                    }
                    .el-tree-node__children {
                        overflow: visible;
                    }
                }
            }
        }
    }
}
.print-content {
    text-align: center;
    .print-main {
        text-align: left;
        color: var(--font-color);
        font-size: var(--font-size);
    }
}

.no-animation-tree :deep(.el-tree-node__children) {
    transition: none !important;
}

body[erp] {
    .a_nofloat {
        a {
            float: none !important;
            display: inline-block !important;
        }
    }
}
.pd-10 {
    padding:  0 10px;;
}