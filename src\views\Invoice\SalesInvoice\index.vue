<template>
    <SalesInvoice :invoiceCategory="'10070'" :isFetchInvoiceSales="isFetchInvoice"></SalesInvoice>
</template>

<script lang="ts">
export default {
    name: "SalesInvoice",
};
</script>
<script setup lang="ts">
import SalesInvoice from "@/views/Invoice/index.vue";
import { ref, watch, onMounted } from "vue";
import { onBeforeRouteLeave, useRoute } from "vue-router";
const route = useRoute();
const isFetchInvoice = ref(false);
watch(
    () => route.query,
    (val) => {
        if (isLeavingFromInvoice) {
            isLeavingFromInvoice = false;
            return;
        }
        const { fetch } = val;
        if (fetch && route.path === "/Invoice/SalesInvoice") {
            isFetchInvoice.value = true;
        } else {
            isFetchInvoice.value = false;
        }
    }
);

let isLeavingFromInvoice = false;
onBeforeRouteLeave((to, from) => {
    isLeavingFromInvoice = from.path === "/Invoice/SalesInvoice" && from.query.fetch === "true";
});

onMounted(() => {
    const { fetch } = route.query;
    if (fetch) {
        isFetchInvoice.value = true;
    }
});
</script>
