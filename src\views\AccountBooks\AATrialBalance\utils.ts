import type { IColumnProps } from "@/components/Table/IColumnProps";
import { formatMoney, formatQuantity } from "@/util/format";
import { ref } from "vue";
import { cloneDeep } from "lodash";
import { getColumnWidth } from "@/components/ColumnSet/utils";

const setModule = "AATrialBalance";
const isErp=window.isErp;
export const setColumns = (aa_type: string, showYear: boolean, showQuanlity: boolean, isFc: boolean): Array<IColumnProps> => {
    const columns = ref<Array<IColumnProps>>([]);
    const initColumns: IColumnProps[] = [
        { slot: "num" },
        { slot: "name" },
        {
            label: "期初余额",
            headerAlign: "center",
            children: [
                {
                    label: "借方",
                    prop: "s_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_debit")
                },
                {
                    label: "贷方",
                    prop: "s_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_credit")
                },
            ],
        },
        {
            label: "本期发生额",
            headerAlign: "center",
            children: [
                {
                    label: "借方",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "debit")
                },
                {
                    label: "贷方",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "credit")
                },
            ],
        },
        {
            label: "期末余额",
            headerAlign: "center",
            children: [
                {
                    label: "借方",
                    prop: "e_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_debit")
                },
                {
                    label: "贷方",
                    prop: "e_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ],
        },
    ];
    // 有外币
    const initColumnsFc: IColumnProps[] = [
        { slot: "num" },
        { slot: "name" },
        {
            label: "币别",
            prop: "fc_code",
            align: "left",
            headerAlign: "left",
            width: getColumnWidth(setModule, "fc_code")
        },
        {
            label: "期初余额",
            headerAlign: "center",
            children: [
                {
                    label: "借方(原币)",
                    prop: "s_debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_debit_fc")
                },
                {
                    label: "借方(本位币)",
                    prop: "s_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_debit")
                },
                {
                    label: "贷方(原币)",
                    prop: "s_credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_credit_fc")
                },
                {
                    label: "贷方(本位币)",
                    prop: "s_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_credit")
                },
            ],
        },
        {
            label: "本期发生额",
            headerAlign: "center",
            children: [
                {
                    label: "借方(原币)",
                    prop: "debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "debit_fc")
                },
                {
                    label: "借方(本位币)",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "debit")
                },
                {
                    label: "贷方(原币)",
                    prop: "credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "credit_fc")
                },
                {
                    label: "贷方(本位币)",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "credit")
                },
            ],
        },
        {
            label: "期末余额",
            headerAlign: "center",
            children: [
                {
                    label: "借方(原币)",
                    prop: "e_debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_debit_fc")
                },
                {
                    label: "借方(本位币)",
                    prop: "e_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_debit")
                },
                {
                    label: "贷方(原币)",
                    prop: "e_credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_credit_fc")
                },
                {
                    label: "贷方(本位币)",
                    prop: "e_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ],
        },
    ];

    const quanliftColumns = cloneDeep(initColumns);
    quanliftColumns.reduce((prev, cur: IColumnProps) => {
        if (cur.label == "期初余额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "s_debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "s_debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_debit_price")
                },
                {
                    label: "金额",
                    prop: "s_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "s_credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "s_credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_credit_price")
                },
                {
                    label: "金额",
                    prop: "s_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_credit")
                },
            ];
        }
        if (cur.label == "本期发生额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "debit_price")
                },
                {
                    label: "金额",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "credit_price")
                },
                {
                    label: "金额",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "credit")
                },
            ];
        }
        if (cur.label == "期末余额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "e_debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "e_debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_debit_price")
                },
                {
                    label: "金额",
                    prop: "e_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "e_credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "e_credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_credit_price")
                },
                {
                    label: "金额",
                    prop: "e_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ];
        }
        if (cur.label == "本年累计发生额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "t_debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "t_debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_debit_price")
                },
                {
                    label: "金额",
                    prop: "t_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "t_credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "t_credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_credit_price")
                },
                {
                    label: "金额",
                    prop: "t_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_credit")
                },
            ];
        }
        prev = columns.value;
        return prev;
    }, columns.value);
    const quanliftColumnsFc = cloneDeep(initColumns);
    quanliftColumnsFc.splice(2, 0, {
        label: "币别",
        prop: "fc_code",
        align: "left",
        headerAlign: "left",
        width: getColumnWidth(setModule, "fc_code")
    });
    quanliftColumnsFc.reduce((prev, cur: IColumnProps) => {
        if (cur.label == "期初余额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "s_debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "s_debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_debit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "s_debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_debit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "s_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "s_credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "s_credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "s_credit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "s_credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_credit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "s_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "s_credit")
                },
            ];
        }
        if (cur.label == "本期发生额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "debit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "debit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "credit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "credit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "credit")
                },
            ];
        }
        if (cur.label == "期末余额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "e_debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "e_debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_debit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "e_debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_debit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "e_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "e_credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "e_credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "e_credit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "e_credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "e_credit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "e_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    resizable: false,
                },
            ];
        }
        if (cur.label == "本年累计发生额") {
            (cur.children as Array<IColumnProps>)[0].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[1].headerAlign = "center";
            (cur.children as Array<IColumnProps>)[0].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "t_debit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_debit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "t_debit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_debit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "t_debit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_debit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "t_debit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_debit")
                },
            ];
            (cur.children as Array<IColumnProps>)[1].children = [
                {
                    slot: "customizePrompt",
                    label: "数量",
                    prop: "t_credit_qut",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_credit_qut")
                },
                {
                    slot: "customizePrompt",
                    label: "单价",
                    prop: "t_credit_price",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatQuantity(value);
                    },
                    width: getColumnWidth(setModule, "t_credit_price")
                },
                {
                    label: "金额(原币)",
                    prop: "t_credit_fc",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_credit_fc")
                },
                {
                    label: "金额(本位币)",
                    prop: "t_credit",
                    align: "right",
                    headerAlign: "right",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_credit")
                },
            ];
        }
        prev = columns.value;
        return prev;
    }, columns.value);

    if (!showYear && !showQuanlity) {
        return isFc ? initColumnsFc : initColumns;
    }
    if (showQuanlity && !showYear) {
        return isFc ? quanliftColumnsFc : quanliftColumns;
    }

    if (showYear && showQuanlity) {
        const showYear = cloneDeep(quanliftColumns);
        showYear.splice(4, 0, {
            label: "本年累计发生额",
            align: "right",
            headerAlign: "center",
            children: [
                {
                    label: "借方",
                    align: "right",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "t_debit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_debit_qut")
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "t_debit_price",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_debit_price")
                        },
                        {
                            label: "金额",
                            prop: "t_debit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, "t_debit")
                        },
                    ],
                },
                {
                    label: "贷方",
                    align: "right",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "t_credit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_credit_qut")
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "t_credit_price",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_credit_price")
                        },
                        {
                            label: "金额",
                            prop: "t_credit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, "t_credit")
                        },
                    ],
                },
            ],
        });
        const showYearFc = cloneDeep(quanliftColumnsFc);
        showYearFc.splice(4, 0, {
            label: "本年累计发生额",
            align: "right",
            headerAlign: "center",
            children: [
                {
                    label: "借方",
                    align: "right",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "t_debit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_debit_qut")
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "t_debit_price",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_debit_price")
                        },
                        {
                            label: "金额(原币)",
                            prop: "t_debit_fc",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, "t_debit_fc")
                        },
                        {
                            label: "金额(本位币)",
                            prop: "t_debit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, "t_debit")
                        },
                    ],
                },
                {
                    label: "贷方",
                    align: "right",
                    headerAlign: "center",
                    children: [
                        {
                            slot: "customizePrompt",
                            label: "数量",
                            prop: "t_credit_qut",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_credit_qut")
                        },
                        {
                            slot: "customizePrompt",
                            label: "单价",
                            prop: "t_credit_price",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatQuantity(value);
                            },
                            width: getColumnWidth(setModule, "t_credit_price")
                        },
                        {
                            label: "金额(原币)",
                            prop: "t_credit_fc",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, "t_credit_fc")
                        },
                        {
                            label: "金额(本位币)",
                            prop: "t_credit",
                            align: "right",
                            headerAlign: "right",
                            formatter: (row, column, value) => {
                                return formatMoney(value);
                            },
                            width: getColumnWidth(setModule, "t_credit")
                        },
                    ],
                },
            ],
        });
        return isFc ? showYearFc : showYear;
    }
    if (!showQuanlity && showYear) {
        const onlyShowYear = cloneDeep(initColumns);
        onlyShowYear.splice(4, 0, {
            label: "本年累计发生额",
            align: "right",
            headerAlign: "center",
            children: [
                {
                    label: "借方",
                    prop: "t_debit",
                    align: "right",
                    headerAlign: "center",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_debit")
                },
                {
                    label: "贷方",
                    prop: "t_credit",
                    align: "right",
                    headerAlign: "center",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_credit")
                },
            ],
        });
        const onlyShowYearFc = cloneDeep(initColumnsFc);
        onlyShowYearFc.splice(4, 0, {
            label: "本年累计发生额",
            align: "right",
            headerAlign: "center",
            children: [
                {
                    label: "借方(原币)",
                    prop: "t_debit_fc",
                    align: "right",
                    headerAlign: "center",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_debit_fc")
                },
                {
                    label: "借方(本位币)",
                    prop: "t_debit",
                    align: "right",
                    headerAlign: "center",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_debit")
                },
                {
                    label: "贷方(原币)",
                    prop: "t_credit_fc",
                    align: "right",
                    headerAlign: "center",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_credit_fc")
                },
                {
                    label: "贷方(本位币)",
                    prop: "t_credit",
                    align: "right",
                    headerAlign: "center",
                    formatter: (row, column, value) => {
                        return formatMoney(value);
                    },
                    width: getColumnWidth(setModule, "t_credit")
                },
            ],
        });
        return isFc ? onlyShowYearFc : onlyShowYear;
    }

    return isFc ? initColumnsFc : initColumns;
};
interface ITableDataBack {
    columns: any[];
    rows: any[];
    total: number;
}

export const setTransverseColumns = (res: ITableDataBack, showYear: boolean, showQuanlity: boolean) => {
    if (showQuanlity) {
        const columnsP = res.columns[0].map((item: any) => {
            const prop = item.field ? item.field.toLowerCase() : "";
            let columnsItem: IColumnProps;
            if (item.field === "AA_NAME") {
                columnsItem = { slot: "name" };
            } else {
                columnsItem = {
                    label: item.title,
                    prop: prop,
                    minWidth: item.width,
                    width: getColumnWidth(setModule, prop),
                    align: item.align,
                    headerAlign: prop ? item.titleAlign : "center",
                    col:showYear?8: 6,
                };
            }
            return columnsItem;
        });
        const columns = res.columns[1].map((item: any) => {
            const prop = item.field ? item.field.toLowerCase() : "";
            let columnsItem: IColumnProps;
            if (item.field === "AA_NAME") {
                columnsItem = { slot: "name" };
            } else {
                columnsItem = {
                    label: item.title,
                    prop: prop,
                    minWidth: item.width,
                    width: getColumnWidth(setModule, prop),
                    align: item.align,
                    headerAlign: prop ? (isErp?'right':item.titleAlign) : "center",
                    col: item.colspan,
                };
                // if (item.formatter == "getShortData") {
                //     columnsItem.formatter = (row, column, value) => {
                //         return getShortData(value);
                //     };
                // } else if (item.formatter == "formatMoney") {
                //     columnsItem.formatter = (row, column, value) => {
                //         return formatMoney(value);
                //     };
                // }
            }
            return columnsItem;
        });
        const columnsTwo = res.columns[2].map((item: any) => {
            const prop: string = item.field ? item.field.toLowerCase() : "";
            const columnsItem: IColumnProps = {
                label: item.title,
                prop: prop,
                minWidth: item.width,
                width: getColumnWidth(setModule, prop),
                align: item.align,
                headerAlign: isErp?'right':"center",
                formatter: (row, column, value) => {
                    const format = prop.includes("_qut") || prop.includes("_price") ? formatQuantity : formatMoney;
                    return format(value);
                },
            };
            if (columnsItem.prop?.includes("_qut") || columnsItem.prop?.includes("_price")) {
                columnsItem.slot = "customizePrompt";
            }
            return columnsItem;
        });

        columns.forEach((item: any, index: number) => {
            item.children = columnsTwo.splice(0, item.col);
        });
        const childrenCol = columnsP.filter((item: any) => !item.prop).slice(1);

        childrenCol.forEach((item: any, index: number) => {
            item.children = columns.splice(0, item.col);
        });

        return columnsP;
    }

    const columns = res?.columns[0].map((item: any) => {
        const prop = item.field ? item.field.toLowerCase() : "";
        let columnsItem: IColumnProps;
        if (item.field === "AA_NAME") {
            columnsItem = { slot: "name" };
        } else {
            columnsItem = {
                label: item.title,
                prop: prop,
                minWidth: item.width,
                width: getColumnWidth(setModule, prop),
                align: item.align,
                headerAlign: prop ? item.titleAlign : "center",
                col: item.colspan,
            };
            // if (item.formatter == "getShortData") {
            //     columnsItem.formatter = (row, column, value) => {
            //         return getShortData(value);
            //     };
            // } else if (item.formatter == "formatMoney") {
            //     columnsItem.formatter = (row, column, value) => {
            //         return formatMoney(value);
            //     };
            // }
        }
        return columnsItem;
    });
    const columnsTwo = res?.columns[1].map((item: any) => {
        const prop = item.field ? item.field.toLowerCase() : "";
        const columnsItem: IColumnProps = {
            label: item.title,
            prop: prop,
            minWidth: item.width,
            width: getColumnWidth(setModule, prop),
            align: item.align,
            headerAlign: "center",
            formatter: (row, column, value) => {
                return formatMoney(value);
            },
        };
        return columnsItem;
    });
    const childrenCol = columns?.filter((item: any) => !item.prop);

    childrenCol?.forEach((item: any, index: number) => {
        item.children = columnsTwo.splice(0, item.col);
    });

    return columns;
};

export function extractParams(str: string): { AA_TYPE: string; AAE_ID: string; period_s: string; period_e: string } {
    const regex = /AA_TYPE=(\d+)&AAE_ID=([\-]?\d+)&period_s=(\d+)&period_e=(\d+)/;
    const match = str.match(regex);
    if (match) {
        const [, AA_TYPE, AAE_ID, period_s, period_e] = match;
        return { AA_TYPE, AAE_ID, period_s, period_e };
    }
    return { AA_TYPE: "", AAE_ID: "", period_s: "", period_e: "" };
}
