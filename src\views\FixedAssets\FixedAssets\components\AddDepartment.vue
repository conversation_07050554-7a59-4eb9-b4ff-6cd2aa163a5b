<template>
    <el-dialog v-model="showAddDepartment" title="创建新部门" center width="576px" @close="close" class="dialogDrag">
        <div class="departmentAddContent" v-dialogDrag>
            <el-form ref="departemntRef" :show-message="false" :model="departmentAddForm">
                <el-row class="isRow">
                    <el-form-item label="部门编号：" :required="true" prop="departmentCode">
                        <el-input v-model="departmentAddForm.departmentCode" class="input-class"></el-input>
                    </el-form-item>
                    <el-form-item label="部门名称：" :required="true" prop="departmentName">
                        <el-input 
                            v-model="departmentAddForm.departmentName" 
                            class="input-class" 
                            @input="handleInput($event, 'departmentName', 256, '部门名称')"
                        ></el-input>
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="负责人：" prop="departmentManager">
                        <el-input v-model="departmentAddForm.departmentManager" class="input-class"></el-input>
                    </el-form-item>
                    <el-form-item label="手机：" prop="departmentManagerPhone">
                        <el-input 
                            v-model="departmentAddForm.departmentManagerPhone" 
                            class="input-class"
                            @input="handleInput($event, 'departmentManagerPhone', 11, '手机号')"
                        ></el-input>
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="成立日期：" prop="departmentEstablishDate">
                        <el-date-picker
                            v-model="departmentAddForm.departmentEstablishDate"
                            style="width: 160px; height: 32px"
                            :disabled-date="disabledEstablishDate"
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="撤销日期：" prop="departmentCancelDate">
                        <el-date-picker
                            v-model="departmentAddForm.departmentCancelDate"
                            style="width: 160px; height: 32px"
                            :disabled-date="disabledCancelDate"
                        ></el-date-picker>
                    </el-form-item>
                </el-row>
                <el-row class="isRow">
                    <el-form-item label="备注：" prop="departmentRemark">
                        <el-input 
                            v-model="departmentAddForm.departmentRemark" 
                            class="input-class" 
                            @input="handleInput($event, 'departmentRemark', 256, '备注')"
                        ></el-input>
                    </el-form-item>
                </el-row>
            </el-form>
        </div>
        <div class="buttons">
            <a class="button solid-button" @click="SaveDepartment">保存</a>
            <a class="button ml-10" @click="handleCancel">取消</a>
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive, watchEffect } from "vue";
import { ElNotify } from "@/util/notify";
import { isNull } from "@/util/format";
import { isNumberOrLetter } from "@/util/validator";
import { dayjs } from "element-plus";

// 新增部门表格类型
export interface IDepartmentAddForm {
    departmentCode: string;
    departmentName: string;
    departmentManager: string;
    departmentManagerPhone: string;
    departmentEstablishDate: string;
    departmentCancelDate: string;
    departmentRemark: string;
}

const props = withDefaults(defineProps<{ isShow: boolean; departmentCode: string }>(), {
    isShow: false,
    departmentCode: "",
});
const emits = defineEmits<{
    (e: "cancel"): void;
    (e: "save", data: IDepartmentAddForm): void;
    (e: "close"): void;
}>();

const departemntRef = ref();
const showAddDepartment = ref(false);
const departmentAddForm = reactive<IDepartmentAddForm>({
    departmentCode: "",
    departmentName: "",
    departmentManager: "",
    departmentManagerPhone: "",
    departmentEstablishDate: "",
    departmentCancelDate: "",
    departmentRemark: "",
});

// 禁用日期
function disabledEstablishDate(time: Date) {
    return time.getTime() > dayjs(departmentAddForm.departmentCancelDate).valueOf();
}

function disabledCancelDate(time: Date) {
    return time.getTime() < dayjs(departmentAddForm.departmentEstablishDate).valueOf();
}

const checkMobileLength = (s: string) => {
    return s.length === 11;
};

const SaveDepartment = () => {
    if (!departmentAddForm.departmentCode) {
        ElNotify({
            type: "warning",
            message: "请录入部门编号！",
        });
        return;
    }
    if (!isNumberOrLetter(departmentAddForm.departmentCode) || departmentAddForm.departmentCode.length > 18) {
        ElNotify({ type: "warning", message: "编码为不超过18位的字母或数字组合！" });
        return false;
    }
    if (!departmentAddForm.departmentName) {
        ElNotify({
            type: "warning",
            message: "请录入部门名称！",
        });
        return;
    }
    if (departmentAddForm.departmentName.length > 256) {
        ElNotify({ message: "亲，名称不能超过256个字哦！", type: "warning" });
        return;
    }
    if (departmentAddForm.departmentRemark.length > 1024) {
        ElNotify({ message: "亲，备注不能超过1024个字哦！", type: "warning" });
        return;
    }
    if (!isNull(departmentAddForm.departmentManagerPhone) && !checkMobileLength(departmentAddForm.departmentManagerPhone)) {
        ElNotify({
            type: "warning",
            message: "请录入正确的手机号码！",
        });
        return false;
    }
    emits("save", departmentAddForm);
};
const handleCancel = () => {
    resetFields();
    emits("cancel");
};

const resetFields = () => {
    departemntRef.value?.resetFields();
};
defineExpose({
    resetFields,
});
watchEffect(() => {
    showAddDepartment.value = props.isShow;
    departmentAddForm.departmentCode = props.departmentCode;
});
const close = () => {
    emits("close");
};
function handleInput(value: string, prop: keyof IDepartmentAddForm, limitSize: number, name: string) {
    if (name === "手机号") {
        value = value.replace(/[^\d]/g, "");
    }

    if (value.length > limitSize) {
        ElNotify({ type: "warning", message: `亲，${name}不能超过${limitSize}个字符！` });
        departmentAddForm[prop] = value.slice(0, limitSize);
    } else {
        departmentAddForm[prop] = value;
    }
}
</script>
<style lang="less" scoped>
.departmentAddContent {
    padding: 20px 0 10px 26px;
    .isRow {
        height: 42px;
        & :deep(.el-form-item) {
            & .el-form-item__label {
                width: 102px;
                text-align: right;
            }
        }
    }
}
:deep(.isRow) {
    & .el-form-item__content {
        padding-top: 0px !important;
    }
    & .el-date-editor {
        & .el-input__prefix {
            position: absolute;
            right: 0;
        }
    }
}
.input-class {
    width: 160px; 
    height: 32px;
}
</style>
