<template>
    <div class="content">
        <div class="title">{{ props.aatype == "10005" ? "项目" : "部门" }}利润表</div>
        <div class="main-content">
            <div class="main-top main-tool-bar space-between split-line">
                <div class="main-tool-left">
                    <SearchInfoContainer ref="containerRef">
                        <template v-slot:title>{{ parentPeriodInfo }}</template>
                        <div class="setting-option">
                            <div class="sheet">
                                <span class="select-label">日期:</span>
                                <Select
                                    v-model="state.statementTaxType"
                                    :teleported="false"
                                    @focus="forcedShutdown = true"
                                    @blur="forcedShutdown = false"
                                >
                                    <el-option label="月报" value="1" />
                                    <el-option label="季报" value="2" />
                                </Select>
                            </div>
                            <div class="sheet" style="margin-top: 10px">
                                <span class="select-label">会计期间:</span>
                                <Select
                                    v-model="state.pid"
                                    :teleported="false"
                                    @blur="forcedShutdown = false"
                                    @focus="forcedShutdown = true"
                                    @change="periodChange"
                                >
                                    <el-option v-for="item in accountantList" :label="item.label" :value="item.pid" :key="item.pid" />
                                </Select>
                            </div>
                            <div class="sheet" style="margin-top: 10px">
                                <span class="select-label">核算项目:</span>
                                <div class="accounting-item">
                                    <SelectCheckbox
                                        width="260px"
                                        :options="optionDataComputed"
                                        :useElIcon="true"
                                        v-model:selectedList="state.aaeIdsList"
                                        v-model:forced-shutdown="forcedShutdown"
                                    >
                                    <template v-slot:cancel-button>
                                            <div class="cancel-button" @click="clearSelectedList">全部取消</div>
                                        </template>
                                    </SelectCheckbox>
                                </div>
                            </div>
                            <div class="sheet" style="margin-top: 10px">
                                <el-checkbox-group v-model="state.periodTypeList" style="display: flex; flex-direction: column">
                                    <el-checkbox :label="1" name="type" :value="1">显示本期金额</el-checkbox>
                                    <el-checkbox :label="2" name="type" :value="2">显示本年累计金额</el-checkbox>
                                    <el-checkbox :label="4" name="type" :value="4">显示上年同期累计</el-checkbox>
                                </el-checkbox-group>
                            </div>
                            <div class="buttons" style="display: block">
                                <a class="button solid-button" @click="btnSearch">确定</a>
                                <a class="button" @click="cancel">取消</a>
                                <a class="button" @click="reset">重置</a>
                            </div>
                        </div>
                    </SearchInfoContainer>
                    <help :aatype="props.aatype === '10005' ? '项目' : '部门'" class="ml-10" />
                    <ErpRefreshButton></ErpRefreshButton>
                    <div v-show="causeOfUnevenness" class="uneven-prompt">
                        <Tooltip :content="causeOfUnevenness" :max-width="500" :line-clamp="1" placement="bottom" :teleported="true">
                            <img v-show="isErp" src="@/assets/Erp/tip-erp.png" alt="" class="uneven-prompt-icon ml-10" />
                            <span :class="['highlight-red uneven-prompt-text', { 'pl-10': !isErp }]">{{ causeOfUnevenness }}</span>
                        </Tooltip>
                    </div>
                </div>
                <div class="main-tool-right">
                    <Dropdown
                        btnTxt="导出"
                        class="export large-2"
                        :width="104"
                        :downlistWidth="106"
                        v-permission="[props.aatype == '10005'?'incomeassiststatementxm-canexport':'incomeassiststatementbm-canexport']"
                    >
                        <li @click="searchNowDAta">当前查询数据</li>
                        <li @click="searchAllData">所有项目数据</li>
                    </Dropdown>
                    <RefreshButton></RefreshButton>
                </div>
            </div>
            <div class="main-center">
                <!-- style="min-height: 600px" -->
                <el-table
                    :class="isErp ? 'erp-table' : ''"
                    v-loading="loading"
                    element-loading-text="正在加载数据..."
                    :data="tableData"
                    border
                    fit
                    :empty-text="emptyText"
                    :scrollbar-always-on="true"
                    stripe
                    highlight-current-row
                    class="custom-table"
                    row-key="lineID"
                    :row-class-name="setTitleRowStyle"
                    @header-dragend="headerDragend"
                >
                    <el-table-column 
                        label="项目" 
                        header-align="center"
                        prop="proName"
                        :min-width="400"
                        :width="getColumnWidth(setModule, 'proName')"
                    >
                        <template #default="scope">
                            <div
                                :class="[
                                    assertNameClass(scope.row),
                                    scope.row.indentation && assertNameClass(scope.row) === 'level2' ? 'pl-56' : '',
                                ]"
                                :title="scope.row.proName"
                            >
                                <span>{{ scope.row.proName }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column 
                        prop="lineNumber" 
                        label="行次" 
                        min-width="68"
                        :width="getColumnWidth(setModule, 'lineNumber')"
                    >
                    </el-table-column>
                    <template v-if="state.aaeIdListLength !== 0 && state.showColumns.length >= 2">
                        <el-table-column
                            v-for="(item, index) in tableData[0]?.colums"
                            :key="item.assitEntry.aanum"
                            :label="item.assitEntry.aaname"
                        >
                            <el-table-column
                                label="本期金额"
                                min-width="135"
                                header-align="right"
                                align="right"
                                v-if="state.showColumns.includes(1)"
                                :prop="item.assitEntry.aanum + 'init' "
                                :width="getColumnWidth(setModule, item.assitEntry.aanum + 'init')"
                            >
                                <template #default="scope">
                                    <TableAmountItem
                                        :amount="scope.row.colums[index].data.Amount.toString()"
                                        :formula="calcFormula(scope.row.note, index)"
                                        :line-number="scope.row.lineNumber"
                                    >
                                    </TableAmountItem>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="本年累计金额"
                                min-width="135"
                                header-align="right"
                                align="right"
                                v-if="state.showColumns.includes(2)"
                                :prop="item.assitEntry.aanum + 'year' "
                                :width="getColumnWidth(setModule, item.assitEntry.aanum + 'year')"
                            >
                                <template #default="scope">
                                    <TableAmountItem
                                        :amount="scope.row.colums[index].data.InitialAmount.toString()"
                                        :formula="calcFormula(scope.row.note, (scope.row.note.split('|').length - 1) / 2 + index)"
                                        :line-number="scope.row.lineNumber"
                                    ></TableAmountItem>
                                </template>
                            </el-table-column>
                            <el-table-column
                                label="上年同期累计"
                                min-width="135"
                                header-align="right"
                                align="right"
                                v-if="state.showColumns.includes(4)"
                                :prop="item.assitEntry.aanum + 'lastyear' "
                                :width="getColumnWidth(setModule, item.assitEntry.aanum + 'lastyear')"
                            >
                                <template #default="scope">
                                    <TableAmountItem
                                        :amount="scope.row.colums[index].data.beforeyearInitialAmount?.toString()"
                                        :formula="scope.row?.beforeyearNoteArr?.[index]"
                                        :line-number="scope.row.lineNumber"
                                    >
                                    </TableAmountItem>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </template>
                    <template v-if="state.aaeIdListLength !== 0 && state.showColumns.length === 1">
                        <template v-for="(item, index) in tableData[0]?.colums" :key="item.aaeid">
                            <el-table-column
                                :label="item.assitEntry.aaname"
                                min-width="135"
                                header-align="left"
                                align="right"
                                v-if="state.showColumns.includes(1)"
                                :prop="item.assitEntry.aaeid + 'Amount' "
                                :width="getColumnWidth(setModule, item.assitEntry.aaeid + 'Amount')"
                            >
                                <template #default="scope">
                                    <TableAmountItem
                                        :amount="scope.row.colums[index].data.Amount.toString()"
                                        :formula="calcFormula(scope.row.note, index)"
                                        :line-number="scope.row.lineNumber"
                                    >
                                    </TableAmountItem>
                                </template>
                            </el-table-column>
                            <el-table-column
                                :label="item.assitEntry.aaname"
                                min-width="135"
                                header-align="left"
                                align="right"
                                v-if="state.showColumns.includes(2)"
                                :prop="item.assitEntry.aaeid + 'InitialAmount' "
                                :width="getColumnWidth(setModule, item.assitEntry.aaeid + 'InitialAmount')"
                            >
                                <template #default="scope">
                                    <TableAmountItem
                                        :amount="scope.row.colums[index].data.InitialAmount.toString()"
                                        :formula="calcFormula(scope.row.note, (scope.row.note.split('|').length - 1) / 2 + index)"
                                        :line-number="scope.row.lineNumber"
                                    ></TableAmountItem>
                                </template>
                            </el-table-column>
                            <el-table-column
                                :label="item.assitEntry.aaname"
                                min-width="135"
                                header-align="left"
                                align="right"
                                v-if="state.showColumns.includes(4)"
                                :prop="item.assitEntry.aaeid + 'beforeyear' "
                                :width="getColumnWidth(setModule, item.assitEntry.aaeid + 'beforeyear')"
                            >
                                <template #default="scope">
                                    <TableAmountItem
                                        :amount="scope.row.colums[index].data.beforeyearInitialAmount?.toString()"
                                        :formula="scope.row?.beforeyearNoteArr?.[index]"
                                        :line-number="scope.row.lineNumber"
                                    >
                                    </TableAmountItem>
                                </template>
                            </el-table-column>
                        </template>
                    </template>
                    <template v-if="state.aaeIdListLength === 0">
                        <el-table-column
                            v-if="state.showColumns.includes(1)"
                            label="本期金额"
                            min-width="270"
                            header-align="center"
                            :resizable="state.showColumns.length !== 1"
                        ></el-table-column>
                        <el-table-column
                            v-if="state.showColumns.includes(2)"
                            label="本年累计金额"
                            min-width="270"
                            header-align="center"
                            :resizable="state.showColumns.length !== 2"
                        ></el-table-column>
                        <el-table-column
                            v-if="state.showColumns.includes(4)"
                            label="上年同期累计"
                            min-width="270"
                            header-align="center"
                            :resizable="false"
                        ></el-table-column>
                    </template>
                </el-table>
            </div>
        </div>
        <el-dialog center title="提示" width="450px" v-model="dialogShow" @close="confirmClick" class="custom-confirm dialogDrag">
            <div class="dialog-content" v-dialogDrag>
                <div class="dialog-content-body">
                    {{ `暂无${props.aatype === "10005" ? "项目" : "部门"}信息，请先设置${props.aatype === "10005" ? "项目" : "部门"}！` }}
                </div>
                <div class="buttons">
                    <a class="button solid-button" @click="confirmClick"> 确定</a>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { ElNotify } from "@/util/notify";
import { request, type IResponseModel } from "@/util/service";
import { getUrlSearchParams, globalWindowOpenPage, closeCurrentTab, globalExport } from "@/util/url";
import type { IPeriod } from "@/api/period";
import SearchInfoContainer from "@/components/SearchInfoContainer/index.vue";
import TableAmountItem from "@/views/Statements/components/TableAmountItem/index.vue";
import help from "./components/Help.vue";
import SelectCheckbox from "@/components/SelectCheckbox/index.vue";
import ErpRefreshButton from "@/components/ErpRefreshButton/index.vue";
import { Option } from "@/components/SelectCheckbox/types";
import type { ITableDataItem, IAssitEntry } from "./types";
import { getGlobalLodash } from "@/util/lodash";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import Select from "@/components/Select/index.vue";
import { checkPermission } from "@/util/permission";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import Tooltip from "@/components/Tooltip/index.vue";
import { getColumnWidth, saveColWidth } from "@/components/ColumnSet/utils";

const setModule = "IncomeAssit";
const props = defineProps<{
    aatype: string;
}>();
const isErp = ref(window.isErp);
const _ = getGlobalLodash()
const periodStore = useAccountPeriodStore();
const route = useRoute();
const loading = ref(false);
const emptyText = ref(" ");
const tableData = ref<ITableDataItem[]>([]);
const optionData = ref<IAssitEntry[]>([]);
const showOption = ref(false);
let accountantList = ref();
const containerRef = ref<InstanceType<typeof SearchInfoContainer>>();
const forcedShutdown = ref(false);
const state = reactive({
    periodTypeList: [1, 2],
    aaeIdsList: [] as number[],
    statementTaxType: "1",
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    showColumns: [1, 2],
    aaeIdListLength: 0,
});
// 判断 periodType
const periodTypeHandle = (periodTypeList: number[]) => {
    let length = periodTypeList.length;

    return length === 1
        ? periodTypeList[0]
        : length === 2
        ? periodTypeList[0] | periodTypeList[1]
        : periodTypeList[0] | periodTypeList[1] | periodTypeList[2];
};
const optionDataComputed = computed(() =>
    optionData.value.map((i) => {
        return new Option(Number(i.aaeid), i.aaname);
    })
);
const dialogShow = ref(false);
const confirmClick = () => {
    if (dialogShow.value) {
        if (checkPermission(["assistingaccount-canedit"])) {
            globalWindowOpenPage(`/Settings/AssistingAccounting?newItem=1&aaType=${props.aatype}`, "辅助核算");
        } else {
            ElNotify({ message: "请联系账套管理员设置！", type: "warning" });
            closeCurrentTab();
            globalWindowOpenPage("/Default/Default", "首页");
        }
    }
};
const getOptionList = () => {
    loading.value = true;
    // 获取项目列表
    // 获取分页项目列表
    request({
        url:
            props.aatype === "10005"
                ? `/api/AssistingAccounting/ProjectList?showAll=${true}`
                : `/api/AssistingAccounting/DepartmentList?showAll=${true}&onlyLeaf=${true}`,
        method: "get",
    })
        .then((res: IResponseModel<IAssitEntry[]>) => {
            if (res.state === 1000) {
                if (res.data.length) {
                    dialogShow.value = false;
                    optionData.value = res.data;
                    state.aaeIdsList = res.data.map((item: IAssitEntry) => item.aaeid).slice(0, 50);
                } else {
                    dialogShow.value = true;
                    //   ElConfirm(
                    //         `暂无${props.aatype === "10005" ? "项目" : "部门"}信息，请先设置${props.aatype === "10005" ? "项目" : "部门"}！`,
                    //         true
                    //     ).then(() => {
                    //         if (checkPermission(["assistingaccount-canedit"])) {
                    //             globalWindowOpenPage(`/Settings/AssistingAccounting?newItem=1&aaType=${props.aatype}`, "辅助核算");
                    //         } else {
                    //             ElNotify({ message: "请联系账套管理员设置！", type: "warning" });
                    //             closeCurrentTab();
                    //             globalWindowOpenPage("/Default/Default", "首页");
                    //         }
                    //     });
                }
            }
        })
        .then(() => {
            getTableList();
        });
};
onMounted(() => {
    getOptionList();
    window.addEventListener("updateAssistingAccounting", () => {
        getOptionList();
    });
});
onUnmounted(() => {
    window.removeEventListener("updateAssistingAccounting", () => {
        getOptionList();
    });
});
let parentPeriodInfo = ref("");
// 获取时间list
let monthDate = ref<IPeriod[]>([]);
let quarterlyDate = ref<any[]>([]);
let monthPid = ref(0);
let quarterPid = ref(0);
getPeriodInit();
function getPeriodInit() {
    let periodListArr = periodStore.periodList;
    monthDate.value = _.cloneDeep(periodListArr);
    monthPid.value = Number(periodStore.getCurrentPeriod());
    quarterlyDate.value = getQuarterlyDateList(_.cloneDeep(periodListArr));
    quarterPid.value = quarterlyDate.value.filter((item: any) => item.pid <= Number(periodStore.getCurrentPeriod())).reverse()[0]?.pid;
    getAccountantList();
}
// 点击全部取消清空勾选
function clearSelectedList() {
    state.aaeIdsList = []
}

function getAccountantList() {
    if (state.statementTaxType === "1") {
        accountantList.value = monthDate.value
            .map((item: IPeriod) => {
                return {
                    ...item,
                    label: item.year + "年" + item.sn + "月",
                };
            })
            .reverse();
        state.pid = monthPid.value;
    } else {
        accountantList.value = quarterlyDate.value
            .map((item: IPeriod) => {
                return {
                    ...item,
                    label: item.year + "年第" + Math.floor(item.sn % 3 === 0 ? item.sn / 3 : item.sn / 3 + 1) + "季度",
                };
            })
            .reverse();
        state.pid = quarterPid.value;
    }

    handlePeriodChange(state.pid);
}
function periodChange(val: number) {
    if (state.statementTaxType === "1") {
        monthPid.value = val;
    } else {
        quarterPid.value = val;
    }
    state.pid = state.statementTaxType === "1" ? monthPid.value : quarterPid.value;
}
watch(() => state.statementTaxType, getAccountantList);
function getQuarterlyDateList(periodsList: IPeriod[]): any[] {
    let quarterGroup: IPeriod[] = [];
    quarterGroup = periodsList.filter((item: IPeriod) => item.sn % 3 === 0);

    if (periodsList[periodsList.length - 1].sn % 3 !== 0) {
        quarterGroup.push(periodsList[periodsList.length - 1]);
    }
    return quarterGroup;
}
function handlePeriodChange(val: number) {
    let target = accountantList.value.find((item: IPeriod) => item.pid === val);
    parentPeriodInfo.value = target.label;
}
const getTableListApi = (initData?: any) => {
    const searchData = {
        statementType: Number(state.statementTaxType),
        aatype: props.aatype,
        aaeids: state.aaeIdsList.join(),
        statementPeriodType: periodTypeHandle(state.periodTypeList),
        pid: state.pid,
    };
    state.aaeIdListLength = state.aaeIdsList.length;
    return request({
        url: "/api/AssistIncomeSheet",
        method: "get",
        params: { ...searchData, ...initData },
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        },
    });
};
const getTableList = () => {
    if (state.periodTypeList.length === 0) {
        ElNotify({
            type: "warning",
            message: "请选择需要显示的数据",
        });
        return;
    }
    loading.value = true;

    if (!state.periodTypeList.includes(4)) {
        getTableListApi()
            .then((res: IResponseModel<ITableDataItem[]>) => {
                if (res.state === 1000) {
                    tableData.value = [];
                    let parent = null;
                    for (let index = 0; index < res.data.length; index++) {
                        const element = res.data[index];
                        if (element.expand === 1) {
                            element.children = [];
                            parent = element;
                        } else if (element.fold === 1) {
                            (parent?.children as ITableDataItem[]).push(element);
                            continue;
                        }
                        if (index !== 0 && res.data[index - 1].proName.includes("其中：") && element.fold === res.data[index - 1].fold) {
                            element.indentation = true;
                        }
                        tableData.value.push(element);
                    }
                    state.showColumns = state.periodTypeList;
                    loading.value = false;
                    checkAsubNotInEquations();
                    if (!tableData.value.length) {
                        emptyText.value = "暂无数据";
                    }
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                    loading.value = false;
                }
            })
            .catch((error) => {
                loading.value = false;
                console.log(error);
            });
    } else {
        Promise.all([getTableListApi(), getTableListApi({ pid: state.pid - 12 < 0 ? 0 : state.pid - 12 })])
            .then((res: IResponseModel<ITableDataItem[]>[]) => {
                tableData.value = [];
                let parent = null;
                for (let index = 0; index < res[0].data.length; index++) {
                    const element = res[0].data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        (parent?.children as ITableDataItem[]).push(element);
                        continue;
                    }
                    tableData.value.push(element);
                }

                tableData.value = tableData.value.map((item, index) => {
                    const equalLine = res[1].data.find(cur=>cur.lineNumber === item.lineNumber) as ITableDataItem;
                    item.colums = item.colums.map((columnItem, columnIndex) => {
                        columnItem.data.beforeyearInitialAmount = equalLine.colums[columnIndex].data.InitialAmount;
                        return columnItem;
                    });
                    const arr = equalLine.note.split("|").reverse().slice(1).reverse();
                    item.beforeyearNoteArr = arr.slice(arr.length / 2);
                    return item;
                });
                state.showColumns = state.periodTypeList;
                loading.value = false;
                checkAsubNotInEquations();
                if (!tableData.value.length) {
                    emptyText.value = "暂无数据";
                }
            })
            .catch((error) => console.log(error));
    }
};
const btnSearch = () => {
    periodStore.changePeriods(String(state.pid));
    state.pid = Number(periodStore.getCurrentPeriod());
    if (state.aaeIdsList.length > 80) {
        ElNotify({
            type: "warning",
            message: "您筛选的辅助核算项目已超过80个，如需查询更多数据，请分开查询哦~",
        });
        return false;
    }
    getTableList();
    cancel();
    handlePeriodChange(state.pid);
};

const cancel = () => containerRef.value?.handleClose();

const reset = () => {
    state.aaeIdsList = optionData.value.map((item: IAssitEntry) => item.aaeid).slice(0, 50);
    state.periodTypeList = [1, 2];
    state.statementTaxType = "1";
    state.pid = Number(periodStore.getCurrentPeriod());
};

const mouseoverHandle = () => (showOption.value = true);

const mouseoutHandle = () => (showOption.value = false);

const searchNowDAta = (importData?: any) => {
    const params = {
        pid: state.pid,
        aaeids: importData.isAll ? -1 : state.aaeIdsList.join(),
        aatype: props.aatype,
        statementPeriodType: periodTypeHandle(state.periodTypeList),
        statementType: state.statementTaxType,
    };
    if (!params.aaeids) {
        ElNotify({
            type: "warning",
            message: "请选择核算项目",
        });
        return false;
    }
    if (importData && importData.isAll) {
        importData = {};
    }
    globalExport("/api/AssistIncomeSheet/Export?" + getUrlSearchParams({ ...params, ...importData }));
};

const searchAllData = () => searchNowDAta({ isAll: true });

let causeOfUnevenness = ref("");
function checkAsubNotInEquations() {
    // 检查是否有科目未添加进报表公式
    request({
        url: `/api/IncomeSheet/CheckAsubNotInEquations?pId=${state.pid}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                causeOfUnevenness.value = res.data;
            } else {
                console.log(res.msg);
            }
        })
        .catch((error) => {
            console.log(error);
        });
}
//判定 assertnameclass
let isFristChild = false;
const assertNameClass = (row: ITableDataItem) => {
    let className: string = "";
    let sign = [
        "一、",
        "二、",
        "三、",
        "四、",
        "五、",
        "六、",
        "七、",
        "八、",
        "九、",
        "十、",
        "(一)",
        "(二)",
        "(三)",
        "(四)",
        "(五)",
        "(六)",
        "(七)",
        "(八)",
        "(九)",
        "(十)",
        "（一）",
        "（二）",
        "（三）",
        "（四）",
    ];

    sign.forEach((v) => {
        if (row.proName.indexOf(v) > -1) {
            className = "level1";
        }
    });
    if (row.expand === 1) {
        isFristChild = true;
    } else if (row.fold) {
        if (isFristChild) {
            className = "level3";
        } else {
            className = "level3 pl-60";
        }
        isFristChild = false;
    }
    return className ? className : "level2";
};
const hasNumberTitle = (value: string) => {
    const chinaNumber = ["一、", "二、", "三、", "四、", "五、", "六、", "七、", "八、", "九、", "十、"];
    for (let i = 0; i < chinaNumber.length; i++) {
        if (value.indexOf(chinaNumber[i]) > -1) return true;
    }
    return false;
};
function setTitleRowStyle(data: any) {
    if (hasNumberTitle(data.row.proName)) {
        return "highlight-title-row";
    }
}
const calcFormula = (note: string, columnIndex: number) => note.split("|")[columnIndex];

const headerDragend = (newWidth: number, oldWidth: number, column: any) => {
    //列宽拖动保存浏览器
    saveColWidth(setModule, column.width, column.property);
};
</script>

<style lang="less" scoped>
@import "@/style/Statements/IncomeAssistStatement.less";
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";

body[erp] .select-checkbox .check-box .cancel-button {
    color: #3d7fff !important;
}

.cancel-button {
    position: absolute;
    right: 0;
    top: 0;
    padding: 5px 10px;
    background-color: #fff;
    color: #6ed773;
    border: none;
    cursor: pointer;
    z-index: 1;
}
.custom-confirm {
    .buttons {
        border-top: 1px solid var(--border-color);
    }
}
</style>
