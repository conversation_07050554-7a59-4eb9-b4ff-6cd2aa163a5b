{"name": "lemonacc.h5", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "axios": "^1.2.5", "big.js": "^6.2.1", "dayjs": "^1.11.9", "echarts": "5.5.1", "element-plus": "2.4.4", "html2canvas": "1.4.1", "js-sha1": "^0.6.0", "jspdf": "^2.4.0", "lodash": "^4.17.21", "mitt": "3.0.1", "modern-screenshot": "4.5.5", "nprogress": "^0.2.0", "pinia": "2.0.30", "pinyin-pro": "3.26.0", "sortablejs": "1.15.2", "spark-md5": "3.0.2", "tesseract.js": "5.1.1", "viewerjs": "1.11.7", "vue": "3.4.15", "vue-router": "^4.1.6", "vue-smooth-dnd": "^0.8.1", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-draggable-resizable": "^1.6.5", "vue3-smooth-dnd": "0.0.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@types/node": "^18.11.12", "@types/nprogress": "^0.2.0", "@types/sortablejs": "1.15.8", "@types/spark-md5": "^3.0.4", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "less": "^4.1.3", "less-loader": "^11.1.0", "npm-run-all": "^4.1.5", "typescript": "~4.7.4", "vite": "^4.0.0", "vue-tsc": "^1.0.12"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "description": "## Project setup ``` npm install ```", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}