{"name": "lemonacctax", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build-only": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.0.0", "axios": "^1.7.2", "big.js": "^7.0.1", "dayjs": "^1.11.13", "element-plus": "^2.9.7", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-router": "^4.3.2"}, "devDependencies": {"@eslint/js": "^9.4.0", "@types/node": "^20.14.2", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "^5.0.4", "@vue/eslint-config-typescript": "^11.0.0", "eslint": "^8.22.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "globals": "^15.3.0", "prettier": "^3.3.1", "sass": "^1.86.0", "sass-loader": "^16.0.5", "typescript": "^5.2.2", "typescript-eslint": "^7.12.0", "unplugin-auto-import": "^0.17.6", "vite": "^5.2.0", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^2.0.6"}}