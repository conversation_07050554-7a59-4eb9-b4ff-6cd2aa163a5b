<template>
    <el-dialog v-model="EnterLockedAccountSet" title="输入账套密码" width="438px" center :show-close="false">
        <div class="accountset-lock-dialog-content" id="EnterLockedAccountSet">
            <table cellpadding="0" cellspacing="0">
                <tr>
                    <td class="tb-title">账套：</td>
                    <td id="enterLockedAccountSetAsName" class="tb-field" style="line-height:40px">
                        <Tooltip :content=asName placement="right" :lineClamp="1" :maxWidth="180">
                            <div>{{ asName }}</div>
                        </Tooltip>
                    </td>
                </tr>
                <tr>
                    <td class="tb-title">输入密码：</td>
                    <td class="tb-field">
                        <el-input type="password" id="enterLockedAccountSetPwd" placeholder="请输入密码" v-model="password" show-password />
                    </td>
                </tr>
            </table>
            <div class="buttons">
                <a id="enterLockedAccountSetConfirm" class="button solid-button" @click="onConfirmClick">确认</a>
                <a class="button ml-10" @click="onCancelClick">取消</a>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ElNotify } from "@/util/notify";
import { request } from "@/util/service";
import { change2newasid } from "@/views/Settings/AccountSets/utils";
import { ref } from "vue";
import Tooltip from "@/components/Tooltip/index.vue";
const props = withDefaults(
    defineProps<{
        asId: number;
        asName: string;
        confirm: Function;
        cancel: Function;
        defaultOpen?: boolean;
        closeAfterConfirm?: boolean;
    }>(),
    {
        defaultOpen: true,
        closeAfterConfirm: true,
    }
);
const EnterLockedAccountSet = ref(props.defaultOpen);
const password = ref("");

const onConfirmClick = () => {
    if (!password.value) {
        ElNotify({
            type: "warning",
            message: "亲，请输入密码！",
        });
        return;
    }
    request({
        url: `/api/AccountSetOnlyAuth/EnterLockedAccountSet?asId=${props.asId}&lockPassword=${password.value}`,
        method: "post",
    }).then((res: any) => {
        if (res.state === 1000) {
            if (res.data) {
                change2newasid(props.asId);
                props.confirm();
                props.closeAfterConfirm && (EnterLockedAccountSet.value = false);
            } else {
                ElNotify({
                    type: "error",
                    message: "密码错误！",
                });
            }
        } else if (res.state === 2000) {
            ElNotify({
                type: "error",
                message: res.msg,
            });
        } else {
            ElNotify({
                type: "error",
                message: "保存失败！",
            });
        }
    });
};

const onCancelClick = () => {
    props.cancel();
    EnterLockedAccountSet.value = false;
};
</script>

<style scoped lang="less">
.accountset-lock-dialog-content {
    padding-top: 34px;
    text-align: center;
    & table {
        margin: 0 auto;
        & tr {
            height: 40px;
        }
        & td.tb-title {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: 20px;
            text-align: right;
        }
        & td.tb-field {
            color: var(--font-color);
            font-size: var(--font-size);
            line-height: var(--line-height);
            min-width: 180px;
            max-width: 180px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            & input[type="password"] {
                width: 180px;
                height: 30px;
                border: 1px solid var(--input-border-color);
                outline: none;
                padding: 0;
                padding-left: 10px;
                padding-right: 10px;
                color: var(--font-color);
                font-size: var(--font-size);
                line-height: 30px;
                box-sizing: border-box;
                border-radius: var(--input-border-radius);
            }
        }
    }
    & .buttons {
        border-top: 1px solid var(--border-color);
        text-align: center;
        padding: 10px 0;
        margin-top: 51px;
    }
}
</style>
