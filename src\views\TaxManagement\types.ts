// 定义一个接口来描述单元格数据的结构
export interface CategoryItem {
  projectName?: string
  confirmStartDate?: string
  confirmEndDate?: string
  [key: string]: any // 允许其他属性
}

export interface TaxTypeItem {
  typeId?: number
  /** 上级Typeid */
  parentCode?: string
  /** 账套id */
  asId: number
  /** 征收项目 */
  projectCode: string
  /** 征收项目name */
  projectName: string
  /** 申报种类id */
  declarationType?: number
  /** 申报种类 name */
  declarationTypeName?: string
  /** 申报期限/纳税期限 0=(月,月) 1=(季,季) 2=(年,年) 3=(半年,半年) 4=(次,次) */
  taxPeriod?: number
  /** 纳税期限名称 */
  taxPeriodName?: string
  /** 申报选项 0=(申报,申报) 1=(无需申报,无需申报) */
  option?: number
  /** 申报选项名称 */
  optionName?: string
  /** 认定有效期起 */
  confirmStartDate: string
  /** 认定有效期止 */
  confirmEndDate: string
  /** 来源 0=(FromTaxBureau,税局获取) 1=(FromUser,手动新增) */
  source: number
  /** 来源名称 */
  sourceName?: string
  /** 排序 */
  order?: number
  /** 征收品目数量 */
  categoryCount?: number
  /** 申报记录是否填写 */
  existDeclareRecord?: boolean

  [key: string]: any // 允许其他属性
}

export interface FocusState {
  projectCode: boolean
  declarationType: boolean
  taxPeriod: boolean
  option: boolean
  confirmStartDate: boolean
  confirmEndDate: boolean
}

export interface ITaxTypeEditParams {
  code: string
  period: number
  isContinue: boolean
}
