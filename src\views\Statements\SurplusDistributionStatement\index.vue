<template>
    <div class="content narrow-content">
        <div class="title">盈余及盈余分配表</div>
        <ContentSlider :slots="slots" :current-slot="currentSlot">
            <template #main>
                <div class="main-content">
                    <div class="main-top main-tool-bar space-between split-line">
                        <div class="main-tool-left">
                            <PaginationPeriodPicker v-model="searchInfo.pid" ref="periodRef" />
                            <CheckOutTooltip v-if="periodIsCheckOut"></CheckOutTooltip>
                        </div>
                        <div class="main-tool-right">
                            <Dropdown :btnTxt="'打印'" :downlistWidth="102" v-permission="['surplusdistributionstatement-canprint']">
                                <li @click="handlePrint(0,getDefaultParams())">当前报表数据</li>
                                <li @click="handlePrint(1)">批量打印</li>
                                <li @click="handlePrint(2)">打印设置</li>
                            </Dropdown>
                            <div class="button large-1 dropdown ml-10" v-permission="['surplusdistributionstatement-canexport']">
                                <div style="width: 100%; text-align: center">导出</div>
                                <div class="downlist" style="width: 100%">
                                    <div class="downlist-buttons large">
                                        <div @click="handleExport(0)">当前报表数据</div>
                                        <div @click="handleExport(1)">批量导出</div>
                                    </div>
                                </div>
                            </div>
                            <a
                                class="button ml-10"
                                v-if="!isHideBarcode"
                                v-permission="['surplusdistributionstatement-canshare']"
                                @click="handleShare"
                                >微信分享</a
                            >
                            <a
                                class="button ml-10 solid-button"
                                v-show="resetFormulaButton"
                                v-permission="['surplusdistributionstatement-canedit']"
                                @click="resetFormula"
                                >重置公式</a
                            >
                            <RefreshButton></RefreshButton>
                        </div>
                    </div>
                    <SurplusTable
                        v-loading="loading"
                        element-loading-text="正在加载数据..."
                        :tableData="tableData"
                        :empty-text="emptyText"
                        :periodRef="periodRef"
                        @changeSlot="changeSlot"
                        :searchInfo="searchInfo"
                        :lineIDList="lineIDList"
                        @colunmOrderChange="handleColunmOrderChange"
                    ></SurplusTable>
                </div>
            </template>
            <template #edit>
                <div class="slot-content align-center">
                    <EditEquation
                        ref="editRef"
                        class="edit-content"
                        :statement-id="editData.statementID"
                        :line-id="editData.lineId"
                        :pid="editData.pid"
                        :title="editData.title"
                        month-title="期末数"
                        year-title="年初数"
                        :value-type-options="valueTypeOptions"
                        @handle-submit-success="handleEditSubmitSuccess"
                        @handle-cancel="handleEditCancel"
                    ></EditEquation>
                </div>
            </template>
        </ContentSlider>
    </div>
    <!-- 打印导出弹窗 -->
    <PrintOrExportDialog
        :show-dialog="showDialog"
        :type="dialogType"
        :pid="searchInfo.pid"
        :fromStatement="35"
        @close="handleDialogClose"
    />
    <StatementsPrint
        v-model:printDialogShow="printDialogVisible"
        title="盈余及盈余分配表打印"
        :customNum="6"
        :dirShow="false"
        :printData="printInfo"
        :otherOptions="otherOptions"
        @currentPrint="handlePrint(3,getDefaultParams())"
    ></StatementsPrint>
</template>

<script lang="ts">
export default {
    name: "SurplusDistributionStatement",
};
</script>
<script setup lang="ts">
import { request, type IResponseModel } from "@/util/service";
import PaginationPeriodPicker from "@/components/Picker/PaginationPeriodPicker/index.vue";
import ContentSlider from "@/components/ContentSlider/index.vue";
import SurplusTable from "./components/SurplusTable.vue";
import { globalPrint, globalExport, getUrlSearchParams } from "@/util/url";
import { useAccountSetStore, ReportTypeEnum } from "@/store/modules/accountSet";
import EditEquation from "@/views/Statements/components/EditEquation/index.vue";
import StatementsPrint from "@/components/PrintDialog/index.vue";
import type { IValueTypeOption } from "../types";
import { getPeriodsApi } from "@/api/period";
import { ref, reactive, watch, nextTick, computed } from "vue";
import { useAccountPeriodStore } from "@/store/modules/accountPeriod";
import { useRoute } from "vue-router";
import type { ISurplusDistribution } from "./types";
import { ElConfirm } from "@/util/confirm";
import { ElNotify } from "@/util/notify";
import { share } from "@/views/Statements/utils";
import PrintOrExportDialog from "@/views/Statements/components/BatchPrintOrExportDialog/index.vue";
import RefreshButton from "@/components/RefreshButton/index.vue";
import Dropdown from "@/components/Dropdown/index.vue";
import { useThirdPartInfoStoreHook } from "@/store/modules/thirdpart";
import usePrint from "@/hooks/usePrint";
import CheckOutTooltip from "@/views/Statements/components/CheckOutTooltip/index.vue";
import { PeriodStatus } from "@/api/period";

const route = useRoute();
const periodStore = useAccountPeriodStore();
const colunmOrderChange = ref("0");
const slots = ["main", "edit"];
const currentSlot = ref("main");
const searchInfo = reactive({
    pid: route.query.pid ? Number(route.query.pid) : Number(periodStore.getCurrentPeriod()),
    classification: false,
});

const accountStandard = useAccountSetStore().accountSet!.accountingStandard;

const periodRef = ref<InstanceType<typeof PaginationPeriodPicker>>();
const periodIsCheckOut = computed(() => {
    return periodRef.value?.periodStatus === PeriodStatus.CheckOut;
});

const isHideBarcode = useThirdPartInfoStoreHook().isHideBarcode;
const tableData = ref<any[]>([]);
const reportStatementId =
    Number(useAccountSetStore().accountSet?.accountingStandard) * 1000 + ReportTypeEnum["SurplusDistributionStatement"];
// 获取时间list
let accountantList = ref();
let parentPeriodInfo = ref();
getPeriodsApi().then((res) => {
    if (res.state === 1000) {
        accountantList.value = res.data.reverse();
        searchInfo.pid = accountantList.value[0].pid;
        handlePeriodChange(searchInfo.pid);
    }
});
function handlePeriodChange(val: any) {
    let target = accountantList.value.find((item: any) => item.pid === val);
    parentPeriodInfo.value = target.year + "年" + target.sn + "月";
}

// 打印导出
const handleDialogClose = () => {
    showDialog.value = false;
};

function getDefaultParams() {
    return {
        pid: searchInfo.pid,
        colunmOrderChange: colunmOrderChange.value,
    };
}
const { printDialogVisible, dialogType, showDialog, handlePrint, printInfo, otherOptions } = usePrint(
    "surplusDistributionStatement",
    `/api/SurplusDistributionStatement/Print${accountStandard === 5 ? "2023" : ""}`,
    {},
    false,
    false,
)

//导出
const handleExport = (exportType: number) => {
    if (exportType === 0) {
        globalExport(
            `/api/SurplusDistributionStatement/Export${accountStandard === 5 ? "2023" : ""}?`+ getUrlSearchParams(getDefaultParams())
        );
    } else {
        // 批量导出
        dialogType.value = "export";
        showDialog.value = true;
    }
};
const handleColunmOrderChange = (data: string) => {
    colunmOrderChange.value = data;
};
//微信分享
const shareReportHost = ref("");
function handleShare() {
    request({
        url: `/api/SurplusDistributionStatement/Share${accountStandard === 5 ? "2023" : ""}?PId=${searchInfo.pid}`,
        method: "post",
    })
        .then((res: IResponseModel<string>) => {
            if (res.state === 1000) {
                shareReportHost.value =
                    window.accountSrvHost +
                    "/api/WxPay/MakeQRCode.ashx?data=" +
                    window.shareReportHost +
                    "/ShareReport/" +
                    res.data +
                    "&CurrentSystemType=1";
                share(shareReportHost.value);
            } else {
                console.log(res.msg);
            }
        })
        .catch((err) => {
            console.log(err);
        });
}
// 检查是否有自定义公式
let resetFormulaButton = ref<boolean>();
function isHasEquation() {
    request({
        url: `/api/StatementEquation/HasCustomEqutions?statementId=${editData.statementID}`,
        method: "post",
    }).then((res: IResponseModel<boolean>) => {
        if (res.state === 1000) {
            resetFormulaButton.value = res.data;
        }
    });
}
const editRef = ref<InstanceType<typeof EditEquation>>();
const editData = reactive({
    statementID: reportStatementId,
    lineId: 0,
    pid: 0,
    title: "",
});
// 重置公式
function resetFormula() {
    ElConfirm(
        "确认删除此报表所有自定义公式？<div style='margin-top: 10px;'>重置公式仅影响未结账期间的报表数据</div>",
        false,
        () => {},
        "重置此报表公式"
    ).then((r: boolean) => {
        if (r) {
            request({
                url: `/api/StatementEquation/ResetEqutions?statementId=${editData.statementID}`,
                method: "post",
            }).then((res: IResponseModel<boolean>) => {
                if (res.state === 1000) {
                    ElNotify({
                        type: "success",
                        message: "已经成功重置",
                    });
                    handleSearch();
                    isHasEquation();
                    checkEquations();
                } else {
                    ElNotify({
                        type: "warning",
                        message: res.msg,
                    });
                }
            });
        }
    });
}
//改变滑块
function changeSlot(data: any) {
    editData.statementID = data.statementID;
    editData.lineId = data.lineId;
    editData.pid = searchInfo.pid;
    editData.title = data.title;
    nextTick().then(() => {
        editRef.value?.init();
        currentSlot.value = "edit";
    });
}

//编辑
const valueTypeOptions: IValueTypeOption[] = [
    { value: "4", label: "发生额" },
    { value: "3", label: "贷方发生额" },
];
function handleEditSubmitSuccess() {
    handleSearch();
    searchLineId();
    isHasEquation();
    currentSlot.value = "main";
}
function handleEditCancel() {
    currentSlot.value = "main";
}
let loading = ref(false);
let emptyText = ref("");
function handleSearch() {
    loading.value = true;
    emptyText.value = " ";
    // 获取盈余及盈余分配表
    request({
        url: `/api/SurplusDistributionStatement${accountStandard === 5 ? "/2023" : ""}`,
        params: { PId: searchInfo.pid },
        method: "get",
    })
        .then((res: IResponseModel<ISurplusDistribution[]>) => {
            loading.value = false;
            tableData.value = [];
            let parent = null;
            if (res.state === 1000 && res.data) {
                for (let index = 0; index < res.data.length; index++) {
                    const element = res.data[index];
                    if (element.expand === 1) {
                        element.children = [];
                        parent = element;
                    } else if (element.fold === 1) {
                        parent?.children!.push(element);
                        continue;
                    }
                    tableData.value.push(element);
                }
            }
            if (!tableData.value.length) {
                emptyText.value = "暂无数据";
            }
        })
        .catch((error) => {
            console.log(error);
        });
}
const lineIDList = ref();
function searchLineId() {
    request({
        url: `/api/SurplusDistributionStatement/Formula?PId=${searchInfo.pid}`,
        method: "get",
    }).then((res: IResponseModel<string>) => {
        if (res.state === 1000) {
            lineIDList.value = res.data;
        }
    });
}
// 检查是否有科目未添加进报表公式
// checkEquations();
function checkEquations() {
    request({
        url: `/api/SurplusDistributionStatement/CheckAsubNotInEquations`,
        params: { pId: searchInfo.pid },
        method: "post",
    }).then((res: IResponseModel<string>) => {});
}
watch(
    searchInfo,
    () => {
        handleSearch();
        isHasEquation();
        searchLineId();
    },
    { immediate: true }
);
</script>

<style scoped lang="less">
@import "@/style/Statements/Statements.less";
@import "@/style/SelfAdaption.less";
.edit-content {
    width: 1100px !important;
}
</style>
